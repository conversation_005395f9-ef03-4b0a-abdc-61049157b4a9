SYMFONY_DEPRECATIONS_HELPER=disabled
# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration
###> symfony/framework-bundle ###
APP_ENV=test
APP_DEBUG=0
APP_SECRET=4e29263680c0f0083182bfa74dc877b9
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
TRUSTED_HOSTS='^(localhost|nicolas|paul\.int\.alienor\.xyz)$'
TRUSTED_PROXIES=127.0.0.1,REMOTE_ADDR
###< symfony/framework-bundle ###
###> symfony/mailer ###
# MAILER_DSN=smtp://localhost
###< symfony/mailer ###
###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# For a PostgreSQL database, use: "postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=11&charset=utf8"
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
DATABASE_URL=mysql://root:root@mariadb:3306/anetdb-test?serverVersion=mariadb-10.11.3&charset=utf8mb4&collate=utf8mb4_unicode_ci
###< doctrine/doctrine-bundle ###
###> knplabs/knp-snappy-bundle ###
WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf
WKHTMLTOIMAGE_PATH=/usr/local/bin/wkhtmltoimage
###< knplabs/knp-snappy-bundle ###
###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=eduprat
###< lexik/jwt-authentication-bundle ###
###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN=^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$
###< nelmio/cors-bundle ###
###> symfony/swiftmailer-bundle ###
# For Gmail as a transport, use: "gmail://username:password@localhost"
# For a generic SMTP server, use: "smtp://localhost:25?encryption=&auth_mode="
# Delivery is disabled by default via "null://localhost"
MAILER_URL=sendmail://localhost
###< symfony/swiftmailer-bundle ###
###> friendsofsymfony/elastica-bundle ###
ELASTICSEARCH_URL=http://elasticsearch:9200/
###< friendsofsymfony/elastica-bundle ###
###> CUSTOM ###
DOMAIN=localhost
SUBDOMAIN_EXTRANET=eduprat
SUBDOMAIN_CRM=crm
ALIENOR_USER_CONTACT_SENDER="{\"<EMAIL>\":\"Eduprat Formations\"}"
#ALIENOR_USER_CONTACT_SENDER="{\"<EMAIL>\":\"<EMAIL>\"}"
EDUPRAT_EMAIL_SENDER="{\"<EMAIL>\":\"Eduprat Formations\"}"
EDUPRAT_ACTALIANS_RECIPIENT=<EMAIL>
ROUTER_REQUEST_CONTEXT_HOST='nicolas.int.alienor.xyz'
#ROUTER_REQUEST_CONTEXT_HOST='localhost'
ROUTER_REQUEST_CONTEXT_SCHEME=https
ALIENOR_ELASTICSEARCH_HOST=https://aqui-es-swarm.alienor.net/es/
ALIENOR_ELASTICSEARCH_PORT=443
ALIENOR_ELASTICSEARCH_INDEX_PREFIX=eduprat_dev
ALIENOR_ELASTICSEARCH_SCHEME=https
ALIENOR_ELASTICSEARCH_USERNAME=eduprat
ALIENOR_ELASTICSEARCH_PASSWORD=NzQxOWU5
ALIENOR_ELASTIC_DOCTRINE_ENABLED_SYNCHRO=false

DASHBOARD_URL='http://web303-local.dev.alienor.net/app_dev.php/'
EVALUATION_MIGRATION_DATE='2020-01-01'
EVALUATION_QUALITE_EMAIL="{\"<EMAIL>\":\"Eduprat Formations\"}"
ALIENOR_ELASTICA_CUSTOM_CONFIG=
E360LEARNING_CID=59d20fc71258a3718109c82d
E360LEARNING_KEY=5f9d530e305d4f22949fda46c60919a4
E360LEARNING_API=fc1245e5768c41989cf27ba529aab65b
ELEARNING_URL='https://www.elearning.com'
ELEARNING_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NTY4ZGZhZm9wZWpta3ZmbGHDtiIsIm5hbWUiOiJyYW5kb21lZCB0b2tlbiIsImlhdCI6MTUxNjIzOTAyMn0.zANR3Hk7wHZGt_Pmo8SBFExE9goIESOWB1_klUJYJ8Y
ALIENOR_REQUETEUR_ADRESSE_WS_URL='http://aqui-symfony.alienor.net/getAdresse'
ALIENOR_REQUETEUR_ADRESSE_CLIENT=eduprat
ALIENOR_REQUETEUR_ADRESSE_CLE=EDUPRATuhdoainwuRia41dza4Dad12dazQ
CARTO_FTP_HOST=************
CARTO_FTP_LOGIN=export-eduprat-bdd
CARTO_FTP_PASS=sRerAFZey7
ZEFID_ENRICHISSEMENT_HOST=null
INSCRIPTIONS_HOST=mail-eduprat.alienor.net
INSCRIPTIONS_EMAIL=<EMAIL>
INSCRIPTIONS_PASSWORD=Eduprat2023!
### INSCRIPTIONS_FOLDER="[\"INBOX/Archives DPC/Inscription &AOA- traiter\"]" ###
INSCRIPTIONS_FOLDER="[\"INBOX/Archives DPC/DEV-ALIENOR\"]"
INSCRIPTIONS_ERROR_EMAIL="{\"<EMAIL>\":\"Thomas Choquet\"}"
HONORAIRES_MIGRATION_DATE='2019-01-01'
JWT_TOKEN_TTL=360000000
API_NOTIFICATION_ENDPOINT="https://eduprat-api.preprod.dev.heurisko.fr"
LDAP_HOST=ldap
LDAP_BASE_DN=dc=eduprat-ldap,dc=alienor,dc=net
LDAP_BASE_GROUP=people
LDAP_DN_ADMIN=admin
LDAP_DN_PASSWORD=password
EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT1=<EMAIL>
EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT2=<EMAIL>
DEV_EMAIL=<EMAIL>
CRM_SECTIONS="[\"rapport\", \"analysis\", \"analysis_participants\", \"suivi_commercial\"]"
###< CUSTOM ###
API_VITRINE_KEY=JnNrTSY2V3uSpP90DFS9jdp6A8_zMYp1k
API_KEY=JoiQWRtaW4iLCJJc3N_1ZXIiOiJJc3N1Z
PARCOURS_V3_MIGRATION_DATE='2022-01-24'
PARCOURS_ELEARNING_MIGRATION_DATE='2022-09-19'
LEAD_MFM_REFERENT_ID=30234

# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
MAILER_DSN=sendmail://localhost

IS_PLAYWRIGHT_ENV=true
SIB_CALL_API=false
API_ADRESS_ACTIVE=false
EDUPRAT_COORDINATEUR_PERSON_EL_ID=1
