FROM gitlab.alienor.net:5050/dev-docker/web_php:8.3-dev

# ~~~~~~ CONF USER ~~~~~~

ENV UID_USER=1000
ENV GID_USER=1000

RUN usermod -u ${UID_USER} ${APACHE_RUN_USER} && groupmod -g ${GID_USER} ${APACHE_RUN_GROUP}

RUN chown -R www-data:www-data /var/www/.ssh/
RUN chown -R www-data:www-data /var/www/.cache
RUN chown -R www-data:www-data /var/www/.composer

# ~~~~~~ CONF PHP ~~~~~~

RUN echo "memory_limit = -1\n\
max_execution_time = 240\n\
max_input_time = 300\n\
max_input_vars = 6000\n\
upload_max_filesize = 16M\n\
post_max_size = 16M\n\
display_errors = Off\n\
date.timezone = Europe/Paris" > /usr/local/etc/php/php.ini

RUN sed -i '/^LogFormat/s/%h/%{X-Forwarded-For}i/' /etc/apache2/apache2.conf

RUN echo "ServerTokens Prod\n" >> /etc/apache2/apache2.conf
RUN echo "ServerSignature Off\n" >> /etc/apache2/apache2.conf

# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
# ~~ AJOUT POUR FAIRE FONCTIONNER DU SSL  ~~
# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

RUN curl -o /etc/ssl/private/ssl-cert-snakeoil.key https://www.groupe-aquitem.fr/keys/privkey.pem && \
curl -o /etc/ssl/certs/ssl-cert-snakeoil.pem https://www.groupe-aquitem.fr/keys/fullchain.pem && \
a2ensite default-ssl && \
a2enmod ssl

# [OPTION] ~~~~~~ INSTALL PHP EXTENSIONS IMAP ~~~~~~
ENV PHP_OPENSSL yes
RUN apt-get update && apt-get install -y libc-client-dev libkrb5-dev
RUN docker-php-ext-configure imap --with-kerberos --with-imap-ssl && docker-php-ext-install imap && docker-php-ext-enable imap

# ~~~~~~ EXTRAS ~~~~~~

RUN echo 'alias sf="sudo -HEu www-data php bin/console"\n\
alias svi="sudo -HEu www-data vi"\n\
alias svi="sudo -u www-data vi"\n\
alias sw="sudo -HE -u www-data"\n\
alias sf="sudo -HE -u www-data php bin/console"\n\
alias oups="chown -Rf www-data:www-data /var/www/html"\n\
alias amysql="mysql -hmariadb -uuserdb -ppassdb anetdb"\n\
alias amysqldump="mysqldump -hmariadb -uuserdb -ppassdb anetdb"\n\
function expose () { echo https://"$1".prox.alienor.xyz && ssh -fNT -o StrictHostKeyChecking=no -o ServerAliveInterval=60 -R "$1".prox.alienor.xyz:80:localhost:80 prox.alienor.xyz; }\n\' >> ~/.bashrc

# ~~~~~~ FONTS ~~~~~~

RUN apt-get update && apt-get install -y fonts-liberation fontconfig-config fontconfig fonts-dejavu-core fonts-droid-fallback fonts-noto-mono gsfonts libfont-afm-perl libfontenc1
#RUN pecl install xdebug-3.1.0 && docker-php-ext-enable xdebug
#RUN echo "XDEBUG_MODE=coverage" >>  /usr/local/etc/php/php.ini

RUN docker-php-ext-install sysvmsg sysvsem sysvshm

# EDUPRAT GENERATION PDF

RUN mkdir -p /usr/share/man/man1
RUN apt-get update && apt-get install -y libxrender1 libfontconfig1 libssl-dev pdftk poppler-utils
