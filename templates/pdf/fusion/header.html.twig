{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>
        h1 {
            font-size: 20px;
            color: #109199;
            text-align: center;
            margin-bottom: 2em;
            line-height: 2cm;
        }
        #header {
            background: url({{ absolute_url(asset('img/logo-form.png')) }}) no-repeat 0.5cm 0cm transparent;
            background-size: 2cm 2cm;
            position: relative;
            height: 2cm;
        }
        #body {
            padding-left: 3cm;
            padding-right: 0.5cm;
            margin-top: 2cm;
        }
        .pageWidth {
            background: url({{ absolute_url(asset('img/bg-restitution-form2.png')) }}) repeat-y bottom left transparent;
            height: 23cm;
        }
    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div id="header">
                <h1>Document de traçabilité</h1>
            </div>
            <div id="body">
                <p><b>Référence DPC :</b> {{ formation.programme.reference }}</p>
                <p><b>Titre :</b> {{ formation.programme.title }}</p>
                <p><b>Format :</b> {% if formation.isAudit and formation.isPredefined %}Formation continue{% else %}Non présentiel avec réunion présentielle{% endif %}</p>
                <p><b>Intervenant :</b>
                    {% for person in formation.formateursPersons %}
                        {{ person.civility }} {{ person.firstname }} {{ person.lastname }}{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </p>
            </div>
        </div>
    </div>
</div>
</html>
