{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>
        h1 {
            font-size: 20px;
            color: #109199;
            text-align: center;
            margin-bottom: 2em;
        }
        .page-break {
            page-break-after: always;
        }
        #header {
            background: url({{ absolute_url(asset('img/logo-form.png')) }}) no-repeat 0.5cm 0cm transparent;
            background-size: 2cm 2cm;
            position: relative;
            height: 2cm;
        }
        #body {
            padding-left: 3cm;
            padding-right: 0.5cm;
        }
    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div id="header">
                <h1>Sommaire</h1>
            </div>
            <div id="body">
            {% for s in summary %}
                <div class="clearfix"><span class="text-left">{{ s.title }}</span><p class="float-right">{{ s.pages[0] }}{% if s.pages[0] != s.pages[1] %} - {{ s.pages[1] }}{% endif %}</p></div>
                {% if loop.index % 19 == 0 %}<div class="page-break"></div>{% endif %}
            {% endfor %}
            </div>
        </div>
    </div>
</div>
</html>
