<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/evaluation.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>
        body {
            padding: 0;
            width: 21cm;
            font-size: 14px;
        }

        #logo img {
            width: 120px;
        }

        #form-container {
            padding: 20px 45px;
        }

        h1 {
            font-size: 26px;
            margin-top: 0.5em;
            margin-bottom: 0;
            letter-spacing: inherit;
        }

        h2 {
            font-size: 20px;
        }

        h3 {
            font-size: 18px;
            margin-top: 1em;
        }

        h4 {
            font-size: 14px;
            margin-left: 0.75em;
        }

        li {
            list-style-type: none;
            margin-left: 0.75em;
            line-height: 1.2em;
        }

        li::before {
            content: "●";
            color: #459494;
            margin-right: 4px;
            font-size: 8px;
        }

        #spiderweb-title {
            font-family: stimupratbold, sans-serif;
            font-size: 26px;
        }

        .group-padding {
            padding-top: 0.25cm;
        }

        #evaluation-info {
            margin: 0 0 1cm 0;
            font-size: 14px;
        }

        #evaluation-info .bold {
            font-size: 16px;
        }

        .bold {
            font-weight: normal;
        }

        .text-group-small {
            font-size: 14px;
        }

        tr{
            background-color:white !important;
            border: none;
        }

        table tbody, table tfoot, table thead {
            border: none;
        }

        table {
            margin-bottom: 0px;
        }

        td {
            text-align:center;
        }

        th {
            font-size: 13px
        }

        .align-left {
            text-align: left;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="bg-form reporting-container" id="form-container">
            <header>
                <div class="pull-left">
                    {% if participation is defined and participation is not null %}
                        <div>{{ "evaluation.header.periode" | trans }}</div>
                        <p class="bold">{{ participation.formation.startDate|date('d/m/Y') }}
                            - {{ participation.formation.endDate|date('d/m/Y') }}</p>
                    {% endif %}
                    {% if programme is defined %}
                        <p>N° DPC: <span class="bold blue">{{ programme.reference }}</span></p>
                    {% endif %}
                </div>
                <div class="pull-right">
                    <div id="logo">
                        <a href="{{ url('eduprat_audit_index') }}"><img src="{{ asset('img/eduprat-new-logo-web.png') }}" alt=""></a>
                    </div>
                </div>
            </header>
            <div class="clearfix"></div>
            <div>
                <hr class="mbm">
                {% if evaluation_title is defined %}
                    <h1 class="text-center">{% block box_title %}{{ evaluation_title|raw }}{% endblock box_title %}</h1>
                {% endif %}
                {% if evaluation_subtitle is defined %}
                    <h2 class="text-center">{% block box_subtitle %}{{ evaluation_subtitle }}{% endblock box_subtitle %}</h2>
                {% endif %}
                <hr>
            </div>
            <div>
                <p class="text-right">{{ reporting.answersCount }} personnes ont répondu à ce questionnaire</p>
            </div>
            {% if programme is defined %}
                <div id="evaluation-info" class="nbi">
                    <div class="bold">FORMATION {{ programme.title }}</div>
                    {% if formation is defined %}
                        <div>{{ formation.startDate | date('d.m.Y') }}{% if formation.startDate != formation.endDate %} - {{ formation.endDate | date('d.m.Y') }}{% endif %} à {{ formation.city }}</div>
                        <div class="bold blue">
                            {% if former is defined %}
                                {{ former.person.civility }} {{ former.person.fullname }}
                            {% else %}
                                {% for former in formation.formateurs %}
                                    {{ former.person.civility }} {{ former.person.fullname }}{% if not loop.last %}, {% endif %}
                                {% endfor %}
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            {% endif %}
            {#{% if person is defined %}#}
            {#<div id="evaluation-info" class="nbi">#}
            {#<div class="bold">{{ person.fullname }}</div>#}
            {#{% if year is defined %}#}
            {#<div>Année {{ year }}</div>#}
            {#{% endif %}#}
            {#</div>#}
            {#{% endif %}#}
            <div>
                {% block box_questions %}
                    {% for question in reporting.reporting %}
                        {% if reporting.summary[question.identifier] is defined %}
                            {% if reporting.summary[question.identifier].count > 0 %}
                                {% if question.title is defined %}
                                    <div class="nbi {% if not loop.first %}group-padding{% endif %}">
                                    <h3>{{ ("evaluation.global." ~ question.title ~ ".title")|trans }}
                                        <span class="float-right mrs">{% if reporting.summary[question.identifier].avg > 0 %}{{ reporting.summary[question.identifier].avg }}/5 {% else %}NA{% endif %}<span class="text-group-small">Note moyenne</span></span>
                                    </h3>
                                    <div class="row">
                                    <div class="columns small-12">
                                    <h4>
                                        <span class="blue">{{ "evaluation.questions"|trans }}</span> {{ ("evaluation.global." ~ question.title ~ ".label")|trans }}
                                    </h4>
                                {% endif %}
                                <li>{{ question.label|trans }} <span class="float-right mrs bold">{% if reporting.summary[question.question].avg > 0 %}{{ reporting.summary[question.question].avg }}/5 {% else %}NA{% endif %}</span></li>
                                {% if question.close is defined and question.close == true %}
                                    </div>
                                    </div></div>
                                {% endif %}
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                {% endblock %}
                {% if reporting.showRadar is not defined or reporting.showRadar %}
                    <br>
                    <hr>
                    <div class="nbi group-padding">
                        <p id="spiderweb-title">{{ "evaluation.titles.radar"|trans }}</p>
                        <div id="spiderweb"></div>
                    </div>
                {% endif %}
                {% if syntheseDatas is defined and syntheseDatas.answeredPoeple > 0 %}
                    <h2>POURCENTAGE D’ATTEINTE DES OBJECTIFS – BASÉ SUR LES RETOURS DES PARTICIPANTS </h2>
                    <p class="text-right"> {{ syntheseDatas.answeredPoeple }} personnes ont répondu à ce questionnaire
                    
                    {% if syntheseDatas.connaissances|length > 0 %}
                        <h3>{{ "admin.formation.autoEval.expectedKnowledge" | trans }}</h3>
                        <table>
                            <tbody>
                                <tr>
                                    <th width="52%"></th>
                                    <th width="16%">{{ "admin.formation.synthese.acquis" | trans }}</th>
                                    <th width="16%">{{ "admin.formation.synthese.enAcquisition" | trans }}</th>
                                    <th width="16%">{{ "admin.formation.synthese.nonAcquis" | trans }}</th>
                                </tr>
                                {% for connaissance,value in syntheseDatas.connaissances %}
                                    <tr>
                                        <td class="align-left"><li>{{ connaissance }}</li></td>
                                        <td>{{ value[2]|round }}%</td>
                                        <td>{{ value[1]|round }}%</td>
                                        <td>{{ value[0]|round }}%</td>
                                    </tr>
                                {% endfor %}
                            <tbody>
                        </table>
                    {% endif %}

                    {% if syntheseDatas.competences|length > 0 %}
                        <h3>{{ "admin.formation.autoEval.expectedSkills" | trans }}</h3>
                        <table>
                            <tbody>
                                <tr>
                                    <th width="52%"></th>
                                    <th width="16%">{{ "admin.formation.synthese.acquis" | trans }}</th>
                                    <th width="16%">{{ "admin.formation.synthese.enAcquisition" | trans }}</th>
                                    <th width="16%">{{ "admin.formation.synthese.nonAcquis" | trans }}</th>
                                </tr>
                                {% for competence,value in syntheseDatas.competences %}
                                    <tr>
                                        <td class="align-left"><li>{{ competence }}</li></td>
                                        <td>{{ value[2]|round }}%</td>
                                        <td>{{ value[1]|round }}%</td>
                                        <td>{{ value[0]|round }}%</td>
                                    </tr>
                                {% endfor %}
                            <tbody>
                        </table>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="{{ absolute_url(asset('admin/plugins/jQuery/jquery-2.2.3.min.js')) }}"></script>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
<script src="{{ absolute_url(asset('/js/radarChart.js')) }}"></script>
<script>

	var radarData = JSON.parse('{{ reporting.radarData|json_encode|escape('js') }}');
	radarData[0] = radarData[0].filter(function (r) {
		return !r.hasOwnProperty("show") || r.show;
    });
	var margin = {top: 120, right: 100, bottom: 150, left: 100};
	var width = 519;
	var height = 350;
	var maxValue = 100;
	var color = d3.scale.ordinal().range(["#3c8dbc"]);
	var radarChartOptions = {
		w: width,
		h: height,
		margin: margin,
		maxValue: maxValue,
		levels: 4,
		roundStrokes: true,
		labelFactor: 1.1,
		lineBackground: true,
		dotRadius: 0,
		wrapWidth: 150,
		color: color,
		legends: ["Moyenne des réponses"],
		gradients: [["#ffeb3b", "#ff5722"]]
	};

	//Call function to draw the Radar chart
	RadarChart("#spiderweb", radarData, radarChartOptions);

</script>
</body>
</html>