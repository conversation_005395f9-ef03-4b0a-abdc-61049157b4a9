<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff; 
        }

        table {
            font-size: 0.85em;
            width: 80%;
            margin: auto;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }    

        .wrapleft {
            width: 80%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .wrapright {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
            text-align: right;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

        .details {
            margin: 5em 0 0 0;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row mtm mbm">
                <div class="column large-12">
                    <div><span class="bold">{{ "evaluationCoordinator.evalCoordinator.programmeTitle" | trans }}</span> : {{ programme.title }}</div>
                </div>

                <div class="column large-12">
                    <div><span class="bold">{{ "evaluationCoordinator.evalCoordinator.coordinator" | trans }}</span> : {{ coordinator.fullname }}</div>
                </div>

                {# <div class="column large-12">
                    <div><span class="bold">{{ "evaluationCoordinator.evalProgramme.city" | trans }}</span> : {{ programme.city }}</div>
                </div> #}

                {# <div class="column large-12">
                    <div><span class="bold">{{ "evaluationCoordinator.evalProgramme.date" | trans }}</span> :             
                        {% if programme.startDate|date('d/m/Y') == programme.endDate|date('d/m/Y') %}
                            Le {{ programme.startDate | date('d/m/Y') }}
                        {% else %}
                            Du {{ programme.startDate | date('d/m/Y') }} au {{ programme.endDate | date('d/m/Y') }}
                        {% endif %}
                    </div>
                </div> #}

                <div class="column large-12 mtm mbm">
                    <div><span class="bold">{{ "evaluationCoordinator.evalProgramme.formers" | trans }}</span> :                      
                        <div>{% if programme.formateurs|length %}
                            {% for formateur in programme.formateurs %}
                                {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                            {% endfor %}
                        {% else %}
                            /
                        {% endif %}</div>                       
                    </div>
                </div>

                <div class="column large-12 mtm mbm">
                    <div><span class="bold">{{ "evaluationCoordinator.evalProgramme.durationParticipants" | trans }}</span> :                       
                        {% for formation in programme.formations %}
                            <div>{{ formation.reference }} : {{ formation.programme.durationPresentielle + formation.programme.durationNotPresentielle }}h / {{participants[formation.id]}} {{ "evaluationCoordinator.evalProgramme.participants" | trans }}</div>
                        {% endfor %}
                    </div>
                </div>

                
            </div>

            <div class="row mtm mbm">
                <div class="column large-12 details">
                    <table class="table table-bordered table-hover mbm">
                        <thead>
                        <tr>
                            <th>{{ "admin.user.evaluation.question" | trans }}</th>
                            <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% if evaluationC|length %}
                        {% for i in 1..evaluationC|length %}
                            {% if ("evaluationCoordinator.coordinator.question." ~ i ~".label") != "comment" %}
                                <tr>
                                    <td>{{ ("evaluationCoordinator.coordinator.question." ~ i ~".label") | trans }}</td>
                                    <td>
                                        {% if evaluationC[i] is defined %}
                                            {{ evaluationC[i].count }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if evaluationC[i] is defined %}
                                            {{ evaluationC[i].avg | round(2) }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                        {% endif %}
                        </tbody>
                    </table>

                    <h5 class="mtm">{{ ("evaluationCoordinator.coordinator.question.5.label") | trans }}</h5>
                    <p>
                        <em>{{ commentaire }}</em>
                    </p>
                </div>
            </div>

             <div class="row mtm mbm">
                <table class="table table-bordered table-hover">
                    <thead>
                    <tr>
                        <th>{{ "admin.user.evaluation.question" | trans }}</th>
                        <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                        <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% if evaluationP|length %}
                    {% for i in 1..evaluationP|length %}
                        <tr>
                            <td>{{ ("evaluation.programme.question." ~ i ~".label") | trans }}</td>
                            <td>
                                {% if evaluationP[i] is defined %}
                                    {{ evaluationP[i].count }}
                                {% else %}
                                    0
                                {% endif %}
                            </td>
                            <td>
                                {% if evaluationP[i] is defined %}
                                    {{ evaluationP[i].avg | round(2) }}
                                {% else %}
                                    0
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                    {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>
