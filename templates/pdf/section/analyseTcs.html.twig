{% set preSufix = formation.hasFormPost ? "pré" : "" %}
{% if showTitle is not defined or showTitle == true %}
    <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Analyse par item</p>
{% endif %}
{% if auditId == 2 and not formation.vignette %}
    {% include "pdf/section/legend.html.twig" %}
{% endif %}
{% set hasDescription = false %}
{% set questionnaireTcs =  participation.formation.questionnaireTCS %}
{% set nbExpertCompletRepondant = questionnaireTcs.nbExpertCompletRepondant %}
{% set questionGroups = questionnaireTcs.groupeQuestionsTCS %}
{% for questionGroup in questionGroups %}
    {% if forPdf is defined %}
         <div class="bold">
            <p style="margin-top:1rem">TCS n°{{ loop.index }}</p>
            <p style="background-color:#898a8d; color:white; padding:0.5rem"> {{ questionGroup.description|striptags|raw }} </p>
        </div>
    {% else %}
        <p class="bold"><u>TCS n°{{ loop.index }} :</u> {{ questionGroup.description|striptags|raw  }} </p>
    {% endif %}

    {% for question in questionGroup.questionsTCS %}
        <div class="mll mbl">
            {% if forPdf is defined %}
                <span class="eduprat bold" style="font-size:14px;">QUESTION {{ loop.index }}</span>
                <div class="tcs-rendu-question" style="margin-top:4px; font-size: 14px;"> Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}</div>
            {% else %}
                <div class="eduprat tcs-rendu-question"><u><b>QUESTION {{ loop.index }} :</b></u> Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}</div>
            {% endif %}
            <div class="tcs-results tcs-results-analyse">
                 {% set participationAnswerTCS = participation.getAnswerTo(question) %}

                {% if forPdf is defined %}
                    <table class="checkbox-table">
                        <tbody>
                        <tr>
                            <td class="checkbox-header checkbox-header-left" style="width: 22%;">Réponses des {{ nbExpertCompletRepondant }} experts :</td>
                            <td class="checkbox-header checkbox-header-left" style="width: 32%;">Votre hypothèse ou option en est :</td>
                            <td style="width: 46%;"></td>
                        </tr>
                        {% for answer in question.reponses %}
                            <tr>
                                <td class="checkbox checkbox-left">
                                    <label><input type="checkbox" disabled="disabled" {% if question.displayAnswerChecked(answer) %}checked="checked"{% endif %}> {% if question.displayAnswerChecked(answer) %}{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}{% else %} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {% endif %}</label>
                                </td>
                                <td class="checkbox checkbox-left">
                                    <label><input type="checkbox" disabled="disabled" {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked="checked"{% endif %}></label>
                                </td>
                                <td>
                                    <label style="margin-left: -13rem;">{{ answer.reponse }}</label>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="tcs-result-items">
                        <div class="tcs-result-item tcs-result-item--restitution">
                            <span class="bold">Réponses des {{ nbExpertCompletRepondant }} experts</span>
                        </div>
                        <div class="tcs-result-item tcs-result-item--restitution tcs-votre-hypothese">
                            <span class="bold">Votre hypothèse ou option en est </span>
                        </div>
                    </div>
                    {% for answer in question.reponses %}
                        <div class="tcs-result-items">
                            <div class="tcs-result-item reponse tcs-result-item--restitution">
                                <input type="radio" disabled name="reponsesExperts-{{  answer.id }}" id="reponsesExperts{{ answer.id }}" value="{{ answer.id }}"
                                {% if question.displayAnswerChecked(answer) %}checked{% endif %}
                                >
                                {% if question.displayAnswerChecked(answer) %}
                                    <label class="marge-left" for="reponsesExperts{{ answer.id }}">{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}</label>
                                {% endif %}
                            </div>
                            <div class="tcs-result-item reponse votre-reponse tcs-result-item--restitution">
                                <input type="radio" {% if participationAnswerTCS.reponseTCS.id != answer.id %}disabled{% endif %} name="reponsesParticipation-{{ participationAnswerTCS.id }}" id="answer-participation-{{ answer.id }}" value="{{ answer.id }}"
                                    {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked{% endif %}
                                >
                                <div class="votre-reponse-text">
                                    <label for="answer-participation-{{ answer.id }}">{{ answer.reponse }}</label>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}

                <p style="{% if forPdf is not defined %}margin-left: 5rem;{% else %}margin-bottom: 4px;{% endif %}"><b>Votre score de concordance : <span class="eduprat"> {{ participationAnswerTCS.percentScore }}%</span></b></p>
                {% if forPdf is defined %}
                    <span class="eduprat" style="font-size:14px;"><b>JUSTIFICATIONS :</b></span>
                    <div style="margin-left: 2rem;">
                        <p style="margin-bottom: 5px;"><u><b>Votre justification :</b></u></p>
                        <span>{% if participationAnswerTCS.justification %}{{ participationAnswerTCS.justification }} {% else %} Pas de justification apportée{% endif %}</span>

                        <div class="tcs-answers-experts tcs-answers-experts-axes" style="margin-top:1rem">
                            <p style="font-weight:bold;text-decoration:underline;margin-bottom:5px">Justification des experts :</p>
                            <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                {% for expertsAnswer in question.expertsAnswersGroupByReponse %}
                                    <div class="tcs-answers-experts-question">
                                        <li><span class="bold">Votre hypothèse ou option en est {{ expertsAnswer[0].reponseTCS.reponse|lower }}</span></li>
                                        <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                            {% for expertAnswer in expertsAnswer %}
                                                <div class="tcs-answers-experts-item">
                                                    <span class="justification"><b>Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :</b></span> {% if expertAnswer.justification %} {{ expertAnswer.justification }} {% else %} Pas de justification apportée {% endif %}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <br>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}
    {% if loop.index < questionGroups|length %}
        <hr class="mtl mbl" style="min-width:95%">
    {% endif %}
{% endfor %}
