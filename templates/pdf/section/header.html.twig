<div id="header">
    <div id="programme">
        {% if participation is defined %}
            <div class="bold blue">
                <img width="20px" src="{{ asset('/img/User.png') }}" alt="">
                <div id="participant-name">{{ participation.participant.fullName }}</div>
            </div>
        {% endif %}
        <p>
            <span class="text-uppercase">{{ "restitution.periode" | trans }}</span>
            <br>
            <span class="bold">{{ formation.openingDate|date('d/m/Y') }}{%- if formation.openingDate != formation.closingDate %} - {{ formation.closingDate|date('d/m/Y') }}{%- endif -%}</span>
        </p>
        <div>
            <span class="text-uppercase">{{ "restitution.reference" | trans }}</span> <span class="bold">{{ formation.programme.reference }}</span>
        </div>
        {% if formation.sessionNumber %}
            <div>
                <span class="text-uppercase">{{ "restitution.session" | trans }}</span> <span class="bold">{{ formation.sessionNumber }}</span>
            </div>
        {% endif %}
        <div>Classe de {{ formation.participantCount }} participants</div>
    </div>
    <div id="coordinateur-formateur">
        {% if formation.formateurs %}
            {% set formateurs = formation.formateurs %}
            <div id="formateur">
                <div class="text-uppercase">{{ "restitution.formateur" | trans }}</div>
                {% for formateur in formateurs %}
                    <div>
                        <div class="bold blue">{{ formateur.person.fullname }}</div>
                        {# {% if formateur.person.phone is not null %}
                            <div>
                                <a class="bold" href="tel:+33{{ formateur.person.phone | slice(1, -1) }}">{{ formateur.person.phone }}</a>
                            </div>
                        {% endif %} #}
                        {% if formateur.person.email is not null %}
                            <div>{{ formateur.person.email }}</div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        {% if formation.coordinators is not null %}
            {% set coordinators = formation.coordinators %}
            <div class="text-uppercase mtm">{% if coordinators|length > 1 and (oneCoordinator is not defined or oneCoordinator == false) %}{{ "admin.formation.coordinators.title" | trans }}{% else %}{{ "admin.formation.coordinator.title" | trans }}{% endif %}</div>
            {% for coordinator in coordinators %}
                {% if oneCoordinator is not defined or (oneCoordinator == false or (oneCoordinator == true and participation.coordinator == coordinator)) %}
                    <div>
                        {% set person = coordinator.person %}
                        <div class="bold blue">{{ person.fullname }}</div>
                        {% if person.phone is not null %}
                            <div class="bold"><a href="tel:+33{{ person.phone | slice(1, -1) }}">{{ person.phone }}</a></div>
                        {% endif %}
                        {% if person.email is not null %}
                            <div>{{ person.email }}</div>
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </div>
</div>