{% if showTitle is not defined or showTitle == true %}
    <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Analyse par question</p>
{% endif %}
{% if auditId == 2 and not formation.vignette %}
    {% include "pdf/section/legend.html.twig" %}
{% endif %}
{% set hasDescription = false %}
{% for t in topo %}
    {% if t.description is defined %}
        <div class="restitution-patient bold text-uppercase" id="cas-patient-{{ t.question.patient }}">
            {{ ('front.label.formType.' ~ formation.formTypePost ~ '.patient') | trans }} {{ t.question.patient }}
        </div>
        <div>{{ t.description.description }}</div>
        {% if t.description.images|length > 0 %}
            <div class="pdf-image-gallery">
                {% for image in t.description.images %}
                    <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                {% endfor %}
            </div>
        {% endif %}
        {% set hasDescription = true %}
    {% endif %}
    <div class="row mln mrn restitution-row nbi {% if hasDescription %}restitution-row-pad{% endif %}">
        <div class="restitution-question blue">
            <span class="text-uppercase">{{ "restitution.question" | trans }} {{ t.index }}</span> {% if t.question.categoryQuestion %}({{ t.question.categoryQuestion.name }}){% endif %}
        </div>
        <p>{{ t.question.label }}</p>
        {% if t.question.images|length > 0 %}
            <div class="pdf-image-gallery">
                {% for image in t.question.images %}
                    <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                {% endfor %}
            </div>
        {% endif %}
        <div>
            {% set typeLabel = ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans %}
            {% if t.answer_score_1 is defined or t.answer_score_2 is defined %}
                <p>
                    {% if formation.isFormPostVignette %}
                        {% if t.answer_score_1 is defined and t.answer_score_2 is not defined %}
                            {{ typeLabel }} pré: {{ t.answer_score_1 }}%
                            {% if auditId == 2  %}| {% endif %}
                        {% endif %}
                    {% else %}
                        {% if t.answer_score_1 is defined %}
                            {{ typeLabel }} pré: {{ t.answer_score_1 }}%
                            {% if auditId == 2  %}| {% endif %}
                        {% endif %}
                    {% endif %}
                    {% if t.answer_score_2 is defined %}
                        {{ typeLabel }} post: {{ t.answer_score_2 }}%
                    {% endif %}
                </p>
            {% else %}
                <p>
                    {% if t.answer_1 is defined %}
                        {% set value1 = t.answer_1 == "0" ? "0%" : (t.answer_1 == "1" ? "100%" : "Non applicable") %}
                    {% else %}
                        {% set value1 = "Non applicable" %}
                    {% endif %}
                    {% if t.answer_2 is defined %}
                        {% set value2 = t.answer_2 == "0" ? "0%" : (t.answer_2 == "1" ? "100%" : "Non applicable") %}
                    {% else %}
                        {% set value2 = "Non applicable" %}
                    {% endif %}
                    {{ typeLabel }} pré: {{ value1 }}
                    {% if auditId == 2 %}| {{ typeLabel }} post: {{ value2 }}{% endif %}
                </p>
            {% endif %}
            {% if auditId == 2 and not formation.isFormPostVignette %}
                <div style="{% if (t.answered and t.notApplicable is not defined) or not participation.formation.isFormPostDefault %}display: inline-block; vertical-align: top; padding-right: 10px;{% endif %}"
                     class="">
                    {% if t.answered %}
                        {% if t.notApplicable is defined and t.notApplicable %}
                            <div>Non applicable</div>
                        {% else %}
                            <img width="35px" src="{{ absolute_url(asset('img/audit_' ~ t.state ~ '.png')) }}">
                        {% endif %}
                    {% else %}
                        {% if participation.formation.isFormPostDefault %}
                            Non répondue
                        {% else %}
                            <img width="35px" src="{{ absolute_url(asset('img/audit_00.png')) }}">
                        {% endif %}
                    {% endif %}
                </div>
            {% endif %}
            <div style="display: inline-block; max-width: 90%" class="">
                {% if formation.isFormPostDefault %}
                    <div class="bold">{{ "restitution.comment" | trans }}</div>
                    <div class="interpretation">
                        {{ t.interpretationgenerique | raw }}
                    </div>
                {% else %}
                    <div class="interpretation">
                        <div class="answers mbl">
                            {% if t.question.type == "choice" %}

                                <table class="checkbox-table">
                                    <tbody>
                                    <tr>
                                        {% if not formation.isFormPostVignette or (formation.isFormPostVignette and auditId == 1) %}
                                            <td class="checkbox-header">{{ ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans }}</br>pré</td>
                                        {% endif %}
                                        {% if auditId == 2 %}
                                            <td class="checkbox-header">{{ ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans }}</br>post</td>
                                        {% endif %}
                                        <td class="checkbox-header">Réponse(s)</br>attendue(s)</td>
                                        <td></td>
                                    </tr>
                                    {% for answer in t.question.choices %}
                                        <tr>
                                            {% if t.answer_audit_1.answer is defined  and not formation.isFormPostVignette or (formation.isFormVignette and auditId == 1) %}
                                                {% set isAnswer1 = answer.label in t.answer_audit_1.answer %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer1 %}bold{% endif %}"><input
                                                                type="checkbox" disabled="disabled"
                                                                {% if isAnswer1 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% if auditId == 2 and t.answer_audit_2.answer is defined %}
                                                {% set isAnswer2 = answer.label in t.answer_audit_2.answer %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer2 %}bold{% endif %}"><input
                                                                type="checkbox" disabled="disabled"
                                                                {% if isAnswer2 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% set isAnswer = answer.answer %}
                                            <td class="checkbox">
                                                <label class="{% if isAnswer %}bold{% endif %}"><input
                                                            type="checkbox" disabled="disabled"
                                                            {% if isAnswer %}checked="checked"{% endif %}></label>
                                            </td>
                                            <td>
                                                <label class="{% if isAnswer %}bold{% endif %}">{{ answer.label }}</label>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                        <tr>
                                            {% if t.answer_audit_1.answer is defined  and not formation.isFormPostVignette or (formation.isFormVignette and auditId == 1) %}
                                                {% set isAnswer1 = "ne_saispas" in t.answer_audit_1.answer %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer1 %}bold{% endif %}"><input
                                                                type="checkbox" disabled="disabled"
                                                                {% if isAnswer1 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% if auditId == 2 and t.answer_audit_2.answer is defined %}
                                                {% set isAnswer2 = "ne_saispas" in t.answer_audit_2.answer %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer2 %}bold{% endif %}"><input
                                                                type="checkbox" disabled="disabled"
                                                                {% if isAnswer2 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            <td class="checkbox">
                                                <label class=""><input
                                                            type="checkbox" disabled="disabled"
                                                            ></label>
                                            </td>
                                            <td>
                                                <label class=>Je ne sais pas</label>
                                            </td>
                                        </tr>

                                    </tbody>
                                </table>

                            {% elseif t.question.type == "radio" %}
                                <table class="checkbox-table">
                                    <tbody>
                                    <tr>
                                        <td class="checkbox-header">{{ ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans }}</br>pré</td>
                                        {% if auditId == 2 and not formation.isFormPostVignette %}
                                            <td class="checkbox-header">{{ ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans }}</br>post</td>
                                        {% endif %}
                                        <td class="checkbox-header">Réponse(s)</br>attendue(s)</td>
                                        <td></td>
                                    </tr>
                                    {% for answer in ["1","0"] %}
                                        <tr>
                                            {% if t.answer_score_1 is defined %}
                                                {% set isAnswer1 = answer == t.answer_1  %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer1 %}bold{% endif %}"><input type="checkbox" {% if isAnswer1 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% if t.answer_2 is defined %}
                                                {% set isAnswer2 = answer == t.answer_2  %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer2 %}bold{% endif %}"><input type="checkbox" {% if isAnswer2 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% set isAnswer = answer == t.question.answer  %}
                                            <td class="checkbox">
                                                <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" {% if isAnswer %}checked="checked"{% endif %}></label>
                                            </td>
                                            <td>
                                                <label class="{% if isAnswer %}bold{% endif %}">{{ answer == "1" ? "Oui" : "Non" }}</label>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                        <tr>
                                            {% if t.answer_score_1 is defined %}
                                                {% set isAnswer1 = "ne_saispas" == t.answer_1  %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer1 %}bold{% endif %}"><input type="checkbox" {% if isAnswer1 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% if t.answer_2 is defined %}
                                                {% set isAnswer2 = "ne_saispas" == t.answer_2  %}
                                                <td class="checkbox">
                                                    <label class="{% if isAnswer2 %}bold{% endif %}"><input type="checkbox" {% if isAnswer2 %}checked="checked"{% endif %}></label>
                                                </td>
                                            {% endif %}
                                            {% set isAnswer = "ne_saispas" == t.question.answer  %}
                                            <td class="checkbox">
                                                <label class=""><input type="checkbox"></label>
                                            </td>
                                            <td>
                                                <label class="">{{ "Je ne sais pas" }}</label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                            {% else %}
                                {{ t.question.answer | raw }}
                            {% endif %}
                        </div>
                        {{ t.question.comments | raw }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% if loop.index < topo|length %}
        <hr class="mts"/>
    {% endif %}
{% endfor %}