{% set preSufix = formation.hasFormPost ? "pré" : "" %}
{% if showTitle is not defined or showTitle == true %}
    <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Axes d'amelioration</p>
{% endif %}
{% if auditId == 2 and not formation.vignette %}
    {% include "pdf/section/legend.html.twig" %}
{% endif %}
{% set hasDescription = false %}
{% set questionnaireTcs =  participation.formation.questionnaireTCS %}
{% set nbExpertCompletRepondant = questionnaireTcs.nbExpertCompletRepondant %}
{% set questionGroups = questionnaireTcs.groupeQuestionsTCS %}
<div id="axes-amelioration">
    {% set formType = auditId == 1 ? formation.formTypePre : formation.formTypePost %}
    <div> Les axes d'amélioration correspondent aux réponses pour lesquelles vous n'êtes en concordance avec aucun des membres du panel d'expert.</div>
    <div class="section-synthese">
        <h5 class="synthese-category" {% if forPdf is defined %} style="margin-top:0px" {% endif %}>
            indicateur à améliorer :
        </h5>
        <div class="row mln mrn restitution-row nbi synthese-row">
            {% for questionGroup in questionGroups %}
                {% for question in questionGroup.questionsTCS %}
                    {% set participationAnswerTCS = participation.getAnswerTo(question) %}
                    {% if participationAnswerTCS.score == 0 %}
                        <p class="bold {% if forPdf is defined %}eduprat{% endif %}"><u>TCS n°{{ loop.index }} :</u> {{ questionGroup.description|striptags|raw }} </p>
                        <div class="mbl">
                            {% if forPdf is defined %}
                                <p class="eduprat bold" style="font-size: 14px;">QUESTION {{ loop.index }}</p>
                                <div class="tcs-rendu-question" style="font-size: 14px;">Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}</div>
                            {% else %}
                                <div class="eduprat tcs-rendu-question"><u><b>QUESTION {{ loop.index }} :</b></u> Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}</div>
                            {% endif %}
                            <div class="tcs-results tcs-results-analyse">
                                {% if forPdf is defined %}
                                    <table class="checkbox-table">
                                        <tbody>
                                        <tr>
                                            <td class="checkbox-header checkbox-header-left" style="width: 22%;">Réponses des {{ nbExpertCompletRepondant }} experts :</td>
                                            <td class="checkbox-header checkbox-header-left" style="width: 32%;">Votre hypothèse ou option en est :</td>
                                            <td style="width: 46%;"></td>
                                        </tr>
                                        {% for answer in question.reponses %}
                                            <tr>
                                                <td class="checkbox checkbox-left">
                                                    <label><input type="checkbox" disabled="disabled" {% if question.displayAnswerChecked(answer) %}checked="checked"{% endif %}> {% if question.displayAnswerChecked(answer) %}{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}{% else %} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {% endif %}</label>
                                                </td>
                                                <td class="checkbox checkbox-left">
                                                    <label><input type="checkbox" disabled="disabled" {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked="checked"{% endif %}></label>
                                                </td>
                                                <td>
                                                    <label style="margin-left: -12rem;">{{ answer.reponse }}</label>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                {% else %}
{#                                    <div class="tcs-result-items tcs-result-items-axes {% if forPdf is defined %}flex colonne12{% endif %}">#}
{#                                        <div class="tcs-result-item tcs-result-item-experts {% if forPdf is defined %}flex colonne2{% endif %}">#}
{#                                            <span class="bold">Réponses des {{ nbExpertCompletRepondant }} experts</span>#}
{#                                        </div>#}
{#                                        <div class="tcs-result-item {% if forPdf is defined %}flex colonne2{% endif %}">#}
{#                                            <span class="bold">Votre réponse</span>#}
{#                                        </div>#}
{#                                    </div>#}

                                    <div class="tcs-result-items">
                                        <div class="tcs-result-item tcs-result-item--restitution">
                                            <span class="bold">Réponses des {{ nbExpertCompletRepondant }} experts</span>
                                        </div>
                                        <div class="tcs-result-item tcs-result-item--restitution tcs-votre-hypothese">
                                            <span class="bold">Votre hypothèse ou option en est </span>
                                        </div>
                                    </div>
                                    {% for answer in question.reponses %}
                                        <div class="tcs-result-items">
                                            <div class="tcs-result-item reponse tcs-result-item--restitution">
                                                <input type="radio" disabled name="reponsesExperts-{{  answer.id }}" id="reponsesExperts{{ answer.id }}" value="{{ answer.id }}"
                                                       {% if question.displayAnswerChecked(answer) %}checked{% endif %}
                                                >
                                                {% if question.displayAnswerChecked(answer) %}
                                                    <label class="marge-left" for="reponsesExperts{{ answer.id }}">{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}</label>
                                                {% endif %}
                                            </div>
                                            <div class="tcs-result-item reponse votre-reponse tcs-result-item--restitution">
                                                <input type="radio" {% if participationAnswerTCS.reponseTCS.id != answer.id %}disabled{% endif %} name="reponsesParticipation-{{ participationAnswerTCS.id }}" id="answer-participation-{{ answer.id }}" value="{{ answer.id }}"
                                                        {% if participationAnswerTCS.reponseTCS.id == answer.id %}checked{% endif %}
                                                >
                                                <div class="votre-reponse-text">
                                                    <label for="answer-participation-{{ answer.id }}">{{ answer.reponse }}</label>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% endif %}
                                <p style="{% if forPdf is not defined %}margin-left: 5rem;{% else %}margin-bottom: 4px;{% endif %}"><u><b>Votre score de corrélation :</b></u> {{ participationAnswerTCS.percentScore }}%</p>
                                {% if forPdf is defined %}
                                    <span style="font-size: 14px;" class="eduprat"><b>JUSTIFICATIONS :</b></span><br>
                                {% endif %}
                                <div class="tcs-answers-experts tcs-answers-experts-axes">
                                    <p class="justification-panel justification-panel-axes">
                                    {% if forPdf is defined %}
                                        <span class="bold"><u>Justification des experts :</u></span><br>
                                    {% else %}
                                        <span class="underline bold">Justification des experts :</span><br>
                                    {% endif %}
                                    </p>
                                    <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                        {% for expertsAnswer in question.expertsAnswersGroupByReponse %}
                                            <div class="tcs-answers-experts-question" {% if forPdf is defined %}style="margin-bottom: 1rem;"{% endif %}>
                                                <p class="expert-ayant-repondu" {% if forPdf is defined %}style="margin-bottom: 0px;"{% endif %}>
                                                    <span class="bold">Votre hypothèse ou option en est {{ expertsAnswer[0].reponseTCS.reponse|lower }}</span>
                                                </p>
                                                <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                                    {% for expertAnswer in expertsAnswer %}
                                                        <div class="tcs-answers-experts-item">
                                                            {% if forPdf is not defined %}
                                                                <i style="font-size:30px" class="fa fa-user-circle fa-2x" aria-hidden="true"></i><br class="hide-desktop">
                                                                <span class="justification" style="position:absolute; margin-left:40px">Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :&nbsp;</span> <span style="margin-left:80px">{% if expertAnswer.justification %} {{ expertAnswer.justification }} {% else %} Pas de justification apportée {% endif %}</span>
                                                            {% else %}
                                                                <span class="justification">Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :&nbsp;</span> <span>{% if expertAnswer.justification %} {{ expertAnswer.justification }} {% else %} Pas de justification apportée {% endif %}</span>
                                                            {% endif %}
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                {% if loop.index < questionGroups|length %}
                    <hr class="mtl mbl" style="min-width:95%">
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
