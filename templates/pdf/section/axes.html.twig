{% set auditId = auditId is defined ? auditId : 1 %}
<div id="section-synthese" class="section">
    {% if showTitle is not defined or showTitle == true %}
        <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;{{ "restitution.synthese" | trans }}</p>
    {% endif %}
    <br>
    <div id="axes-amelioration">
        {% set formType = auditId == 1 ? formation.formTypePre : formation.formTypePost %}
        <div> Les axes d’amélioration correspondent aux questions auxquelles vous avez répondu faux {{ ("front.label.formType." ~ formType ~ ".a") | trans }} {% if formation.hasFormPost %}{{ auditId == 1 ? "pré" : "post" }}{% endif %}</div>
        {% for category, topo in axis %}
            <div class="section-synthese">
                <h5 class="synthese-category">
                    {% if formation.isFormAudit %}
                        {{ category }}
                    {% else %}
                        indicateur à améliorer : «&nbsp;{{ category }}&nbsp;»
                    {% endif %}
                </h5>
                {% for t in topo %}
                    <div class="row mln mrn restitution-row nbi synthese-row">
                        <div class="restitution-question blue text-uppercase">
                            {% if hasDescription is defined and hasDescription %}{{ ("front.label.formType." ~ formType ~ ".patient") | trans }} {{ t.question.patient }} - {% endif %}{{ "restitution.question" | trans }} {{ t.index }} {% if formation.isFormPresentielle %}({{ t.question.categoryQuestion.name }}){% endif %}
                        </div>
                        {% if formation.isFormAudit %}
                            <p>{{ t.question.label }}</p>
                        {% else %}
                            {{ t.question.label }}
                        {% endif %}
                        {% if formation.isFormPreVignette %}
                            <div class="answers mbl">
                                {% if t.question.type == "choice" %}
                                    {% for answer in t.question.choices %}
                                        {% set isAnswer = answer.answer %}
                                        <div class="checkbox">
                                            <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" style="margin:0px" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ answer.label }}</label>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    {{ t.question.answer | raw }}
                                {% endif %}
                            </div>
                        {% endif %}
                        <div>
                            {% if t.interpretationgenerique is not null and t.interpretationgenerique != "" %}
                                <div class="bold">{{ "restitution.comment" | trans }}</div>
                                <div class="interpretation">{{ t.interpretationgenerique | raw }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endfor %}
    </div>
</div>
