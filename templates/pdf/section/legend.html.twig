<div class="legend">
    <p class="text-uppercase blue"><b>{{ "restitution.legend.title" | trans }}</b></p>
    <ul>
        {% for i in ["11", "10", "01", "00"] %}
            <li>
                <div style="display: inline-block; vertical-align: top; margin-right: 10px; margin-top: 3px;"><img width="20px" src="{{ absolute_url(asset('img/audit_' ~ i ~ '.png')) }}" alt=""></div>
                <div style="display: inline-block; max-width: 90%;"><strong>{{ ("restitution.legend.text_" ~ i) | trans() }}</strong><br>
                    {% if formation.isFormPredefined %}
                        {{ ("restitution.predefined.result.text_" ~ i) | trans({"%type%": formation.isFormAudit ? "audits" : "questionnaires" }) }}
                    {% else %}
                        {{ ("restitution.result.text_" ~ i) | trans({"%type%": formation.isFormAudit ? "audits" : "questionnaires" }) }}
                    {% endif %}
                </div>
            </li>
        {% endfor %}
    </ul>
    {% if formation.isFormPredefined %}
        <div>
            {{ "restitution.predefined.result.sublegend"|trans }}
        </div>
    {% endif %}
</div>