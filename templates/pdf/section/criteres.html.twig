<div class="section" style="padding-top: 2em">
    {% if showTitle is not defined or showTitle == true %}
        <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Nombre de bonnes réponses par question</p>
    {% endif %}
    {% if auditId == 2 and not formation.isFormVignette %}
        <br>
        {% include "pdf/section/legend.html.twig" %}
    {% endif %}
    {% set formType = auditId == 1 ? formation.formTypePre :  formation.formTypePost %}
    <div id="questions">
        {% set hasDescription = false %}
        {% for question in questions %}
            {% if question.audit is defined or qIndex is not defined%}
                {% set qIndex = 0 %}
            {% endif %}
            {% if formation.isFormVignette and question.audit is defined %}
                {% if not loop.first %}
                    <div class="page-break"></div>
                {% endif %}
            {% endif %}
            {% if question.description is defined %}
                {% set qIndex = 0 %}
                <div class="restitution-patient blue bold text-uppercase">
                    {{ ("front.label.formType." ~ formType ~ ".patient") | trans }} {{ question.patient }}
                </div>
                <div>{{ question.description.description }}</div>
                {% if question.description.images|length > 0 %}
                    <div class="pdf-image-gallery">
                        {% for image in question.description.images %}
                            <img class="pdf-image" src="{{ absolute_url(image.relativeUrl) }}" alt="">
                        {% endfor %}
                    </div>
                {% endif %}
                {% set hasDescription = true %}
            {% endif %}
            {% set qIndex = qIndex + 1 %}
            <div class="restitution-row nbi {% if hasDescription %}restitution-row-pad{% endif %}">
                <div class="restitution-question blue"><b><span class="text-uppercase">{{ "restitution.question" | trans }}</span> {{ qIndex }} {% if question.entity.categoryQuestion %}({{ question.entity.categoryQuestion.name }}){% endif %} :</b></div>
                {% if auditId == 2 and not formation.isFormVignette %}
                    <div style="margin-top:10px; margin-bottom: 10px;">
                        <div style="display: inline-block; margin-right: 10px; "><img width="30px" src="{{ absolute_url(asset('img/audit_' ~ question.state ~ '.png')) }}" alt=""></div>
                        <div style="display: inline-block; max-width: 90%; vertical-align: middle;">{{ question.entity.label }}</div>
                    </div>
                {% else %}
                    <p>{{ question.entity.label }}</p>
                {% endif %}
                {% if question.entity.images|length > 0 %}
                    <div class="pdf-image-gallery">
                        {% for image in question.entity.images %}
                            <img class="pdf-image" src="{{ absolute_url(image.relativeUrl) }}" alt="">
                        {% endfor %}
                    </div>
                {% endif %}
                {% if formation.isFormPreDefault and question.entity.interpretation is defined %}
                    <div class="{% if not isPdf %}interpretation{% endif %}" style="margin-bottom: -1em;">{{ question.entity.interpretation | raw }}</div>
                {% elseif (financeSousMode is defined and financeSousMode.isActalians) or formation.isFormPredefined or formation.isFormPresentielle or formation.isFormVignette %}
                    {% if auditId is not defined or auditId != 2 or formation.isFormVignette %}
                        <div class="answers mbl">
                            {% if question.entity.type == "choice" %}
                                {% for answer in question.entity.choices %}
                                    {% set isAnswer = answer.answer %}
                                    <div class="checkbox">
                                        <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ answer.label }}</label>
                                    </div>
                                {% endfor %}
                            {% elseif question.entity.type == "radio" %}
                                {% for answer in ["1", "0"] %}
                                    {% set isAnswer = question.entity.answer == answer %}
                                    <div class="checkbox">
                                        <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ answer == "1" ? "Oui" : "Non" }}</label>
                                    </div>
                                {% endfor %}
                            {% else %}
                                {{ question.entity.answer | raw }}
                            {% endif %}
                        </div>
                    {% endif %}
                    {% if question.entity.comments %}
                        <div class="answers mbl bold">
                            {% if formation.isFormAudit %}
                                <div>Postulat</div>
                                <div class="answers-postulat mll">{{ question.entity.comments | raw }}</div>
                            {% else %}
                                {{ question.entity.comments | raw }}
                            {% endif %}
                        </div>
                    {% endif %}
                {% endif %}
                {% if not formation.isVfc %}
                    {% if question.count > 0 %}
                        {% if auditId == 2 and not formation.isFormVignette %}
                            <p>
                                {% set typeLabel = ("front.label." ~ formation.displayType ~ ".singular") | trans %}
                                {{ typeLabel }} {% if formation.hasFormPost %}{{(formation.isFormPredefined ? "pré" : "1")}}{% endif %}: {{ question.goodPercent_1 }}% | {{ typeLabel }} {{(formation.isFormPredefined ? "post" : "1")}}: {{ question.goodPercent_2 }}%
                            </p>
                        {% endif %}
                        <div class="column small-1 text-center"><img width="25px" src="{{ absolute_url(asset('img/audit_11.png')) }}"></div>
                        <div class="column small-10 pln prn">
                            <div class="progress">
                            <span class="progress-meter progress-meter-success" style="width: {{ question.goodPercent }}%">
                                <p class="progress-meter-text">{{ question.goodPercent }}%</p>
                            </span>
                                <span class="progress-meter progress-meter-error" style="width: {{ question.wrongPercent - 0.4 }}%">
                                <p class="progress-meter-text">{{ question.wrongPercent }}%</p>
                            </span>
                            </div>
                            {% if formation.isFormPreDefault %}
                                <div class="float-left progress-text">{{ question.good }} Bonne{% if question.good > 1 %}s{% endif %} réponse{% if question.good > 1 %}s{% endif %}</div>
                                <div class="float-right progress-text">{{ question.wrong }} Mauvaise{% if question.wrong > 1 %}s{% endif %} réponse{% if question.wrong > 1 %}s{% endif %} {% if question.notApplicable > 0 %}({{ question.notApplicable }} réponse{% if question.notApplicable > 1 %}s{% endif %} non applicable{% if question.notApplicable > 1 %}s{% endif %}){% endif %}</div>
                            {% else %}
                                <div class="float-left progress-text">{{ question.goodPercent }}% Juste</div>
                                <div class="float-right progress-text">{{ question.wrongPercent }}% Faux {% if question.notApplicable > 0 %}({{ question.notApplicable }} réponse{% if question.notApplicable > 1 %}s{% endif %} non applicable{% if question.notApplicable > 1 %}s{% endif %}){% endif %}</div>
                            {% endif %}
                        </div>
                        <div class="column small-1 text-center"><img width="25px" src="{{ absolute_url(asset('img/audit_00.png')) }}"></div>
                    {% else %}
                        <p>Aucune réponse de la part des participants {% if question.notApplicable > 0 %} - {{ question.notApplicable }} réponse{% if question.notApplicable > 1 %}s{% endif %} non applicable{% if question.notApplicable > 1 %}s{% endif %}{% endif %} {% if question.neSaisPas > 0 %} - {{ question.neSaisPas }} réponse{% if question.neSaisPas > 1 %}s{% endif %} ne sais pas{% endif %}</p>
                    {% endif %}
                {% endif %}
            </div>
            {% if question.count > 0 and question.neSaisPas > 0 %}
                <p>{{ question.neSaisPas }} réponse{% if question.neSaisPas > 1 %}s{% endif %} ne sais pas</p>
            {% endif %}
            {% if not loop.last %}
                <hr/>
            {% endif %}
        {% endfor %}
    </div>
</div>