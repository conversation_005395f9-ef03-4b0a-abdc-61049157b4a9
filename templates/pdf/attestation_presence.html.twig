<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>

        body {
            padding: 0;
            font-size: 0.70em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h4 {
            font-size: 18px;
            font-weight: bold;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 0cm 1cm;
        }

        #programme {
            padding: 1em;
            margin-top: 8em;
            text-align: right;
            float: right;
            min-width: 330px;
            font-family: stimupratlight;
        }

        #coordinateur-formateur {
            font-size: 1em;
        }

        .newFormateur {
            position: absolute;
            left: 9cm;
            top: 3cm;
            max-width: 15cm;
            font-size: 1em;
        }

        #contact {
            position: absolute;
            left: 4cm;
            top: 0cm;
            max-width: 15cm;
            font-size: 1em;
        }

        .title {
            text-align: center;
            font-size: 1.9em;
            /*margin-top: 0.25cm;*/
        }
        .littletitle {
            text-align: center;
            font-size: 1.25em;
        }
        .centered {
            text-align: center;
            margin-top: 0.5cm;
            /*margin-bottom: 1cm;*/
        }
        .infos {
            margin-top: 0.25cm;
            font-weight: bold;
        }
        .restitution-row {
            page-break-inside: avoid;
            padding-top: 1.8em;
        }

        .restitution-row::after {
            content: '';
            clear: both;
            display: block;
        }

        .interpretation {
            letter-spacing: -1px;
            font-family: stimupratbold, sans-serif;
            margin-bottom: 2em;
        }

        .interpretation p {
            margin: 0;
        }
        .comments > ul {
            list-style-position: inside;
        }
        #legend {
            height: 6cm;
            padding-top: 1cm;
        }
        #legend ul {
            margin-left: 0;
        }

        #legend li {
            margin-bottom: 0.5em;
        }

        #legend li > img {
            margin-right: 1em;
        }

        .interpretation, .interpretation span, .interpretation p {
            font-size: 1em !important;
            margin-bottom: 1em;
        }

        #detail-title {
            font-size: 1.2em;
        }

        #spiderweb {
            margin: 2em 0 0.2em 0;
        }

        .highcharts-root, .highcharts-container {
            overflow: visible !important;
        }
        .section {
            padding-top: 1em;
            page-break-inside: avoid;
        }

        .section-title {
            margin-bottom: 0;
            font-size: 1.75em;
            text-transform: uppercase;
            padding-top: 0.125em;
            font-family: stimupratbold, sans-serif;
            font-weight: 600;
        }

        .progress {
            font-size: 0;
            height: 20px;
            margin-bottom: 3px;
            background-color: transparent;
        }

        .progress-meter {
            display: inline-block;
        }

        .progress-text {
            font-size: 10px;
        }

        .progress-meter-success {
            background: linear-gradient(to right, #8b5e92, #6086b9);
            background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .progress-meter-error {
            background-color: #d71903;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }

        .restitution-question + p {
            margin-bottom: 0.5rem;
        }

        #barchart .bar {
            fill: #6c5d9f;
        }
        #barchart .axis path, #barchart .axis line {
            fill: none; stroke: #000; shape-rendering: crispEdges;
        }

        .page-break + .section {
            padding-top: 1cm;
        }

        .footer-fixed {
            position: absolute;
            top: 28.2cm;
            text-align: center;
            width: 100%;
            left:0;
            line-height: 1.3em;
            font-size: 0.8em;
        }


        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
        .bold {
            font-family: inherit;
        }

        .restitution-question.blue {
            font-size: 16px;
        }

        .restitution-question+p {
            font-size: 14px;
        }

        .answers label {
            font-size: 12px;
        }

        .answers-postulat {
            margin-top: 5px;
        }

        .answers-postulat p {
            margin-bottom: 0;
        }

        .restitution-patient {
            font-size: 16px;
            margin-top: 16px;
        }

        .restitution-patient-audit {
            font-size: 20px;
            margin-top: 16px;
        }

        .restitution-row-pad {
            padding-left: 16px;
        }

        .mtxl {
            margin-top:6em;
        }


        .mtl {
            margin-top:4em;
        }

        .mtm {
            margin-top:3em;
        }

        .mts {
            margin-top:2em;
        }

        .mll {
            margin-left:4em;
        }

        .margin-le {
            margin-left:20em;
        }

        
        .flex{
            display:flex;
            width: 100%;
        }

        .when, .where {
            font-weight: bold;
            font-style: italic;
            min-width: 20em;
        }

        .center {
            text-align: center;
        }

        {# .mbs {
            margin-bottom:2em;
        } #}
        .attestation-head {
            background-color: #0098DB;
            color: white;
            padding: 0.3em 3em;
        }

        .title-attestation {
            font-size: 18px;
            font-weight: 600;
        }

        .subtitle-attestation {
            font-size: 14px;
            line-height: 0px;
            font-weight: 400;
            margin-top:-10px;
            font-style:italic;
        }

        .underligned {
            border-bottom: 1px solid #0098DB;
            width: 100%;
            line-height: 18px;
        }

        .underligned-black {
            border-bottom: 1px solid black;
            width: 100%;
            line-height: 18px;
        }

        .boxLigned {
            border: 1px solid #0098DB;
            padding: 0.5em;
            line-height: 18px;
            margin-top:-1.3em;
            font-size: 14px;
        }

        .undertext {
            width: 100%;
            color: #0098DB;
            text-align:right;
            font-size:8px;
            line-height: 0px;
            margin-top:-8px;
            font-style: italic;
        }

        .values {
            margin-left:2em;
            text-transform: uppercase;
            font-size: 14px;
        }

        .values-date {
            margin-left:5em;
        }

        .organisme-number {
            text-align: right;
            border-right: 1px solid #0098DB;
            padding: 0.4em 0.6em;
            margin-top: -5px;
        }

        .organisme-number-first {
            border-left: 1px solid #0098DB;
            margin-left: 12em;
        }
      
        .block {
            padding: 0.5em 1em;
            font-size: 9px
        }

        .block-first {
            height: 15.8em;
            background-color: #FCD5A4;
        }

        .bdb-block {
            border-bottom: 1px solid black;
        }

        .block-second {
            background-color: #FFEAAD;
            height: 15.3em;
        }

        .no-padding {
            padding-left: 0px;
            padding-right: 0px;
        }

        .no-padding-right {
            padding-right: 0px;
        }

        .empty-block {
            height: 16px;
            background-color: white;
        }

        .undertext-block {
            margin-top:8px;
        }

        .empty-block-bottom {
            margin-left: auto;
            width: 70%;
        }

        .year {
            margin-left: -4.1em;
        }

        .nbjours {
            margin-left: -7.7em;
        }
        
        .nbheures {
            margin-left: -8.1em;
        }

        .reglement {
            margin-top:1em;    
            color: white;
            padding: 0.5em 4em;
            background-color: #939494;
            font-weight: 600;
        }

        .reglement-white-box {
            background-color: white;
        }

        .rd-txt {
            color:red;
            font-size:8px;
        }

        .block-signature {
            width:100%;
            height: 170px;
            border: 1px solid grey;
            font-size: 8px;
            padding: 4px;
        }

       .footer {
            
       }

    </style>
</head>
<body>
    <div class="fluid960 page">
        {% for participation in participations %}
            <div class="attestation-head">
                <p class="title-attestation">ATTESTATION DE PRÉSENCE ET DE RÈGLEMENT</p>
                <p class="subtitle-attestation">(Document à compléter par l'organisme de formation)</p>
            </div>
            <div class="pageWidth row ptn">
                <div class="row" style="margin-top:0.5em">
                    <div class="columns small-6">
                        <p class="underligned">Je soussigné:<span class="values">PLAS PHILIPPE</span></p>
                        <p class="undertext">(Nom du responsable de l'organisme de formation)</p>
                    </div>
                    <div class="columns small-6">
                        <p class="underligned">Fonction:  <span class="values">PDG</span></p>
                        <p class="undertext">(fonction exacte)</p>
                    </div>
                </div>
                <div class="row">
                    <div class="columns small-12" style="margin-top: -0.5em;">
                        <p class="underligned">de l'organisme de formation <span class="values">EDUPRAT FORMATIONS</span></p>
                        <p class="undertext">(Dénomination de l'organisme de formation)</p>
                    </div>
                </div>
                <div class="row">
                    <div class="columns small-12" style="margin-top: -0.5em;">
                        <p class="underligned" style="padding: 2px;">
                            déclaré en tant qu'organisme formateur sous le n°
                            <span class="organisme-number organisme-number-first">7</span>
                            <span class="organisme-number">2</span>
                            <span class="organisme-number">3</span>
                            <span class="organisme-number">3</span>
                            <span class="organisme-number">0</span>
                            <span class="organisme-number">9</span>
                            <span class="organisme-number">2</span>
                            <span class="organisme-number">7</span>
                            <span class="organisme-number">6</span>
                            <span class="organisme-number">3</span>
                            <span class="organisme-number">1</span>
                            <span class="organisme-number">3</span>
                        </p>
                        <p class="undertext">(11 chiffres)</p>
                    </div>
                </div>
                <div class="row">
                    <div class="columns small-12" style="margin-top: -0.5em;">
                        <p>certifie par la présente que le stagiaire</p>
                        <p class="underligned" style="margin-top:-1.2em;">Nom et prénom du stagiaire: <span class="values">{{ participation.participant.fullname }}</span></p>
                    </div>
                </div>
                <div class="row">
                    <div class="columns small-12" style="margin-top:-0.5em">
                        <p>a bien assisté à la totalité de la formation intitulée:</p>
                        <p class="boxLigned">{{ formation.programme.title }}</p>
                        <p class="underligned" style="margin-top:-0.5em;">Date de la formation :<span class="values-date">du {{ formation.openingDate | date('d/m/Y') }} au {{ formation.closingDate | date('d/m/Y') }} avec une réunion présentielle {% if formation.endDate | date('d/m/Y') %}du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}{% else %}le {{ formation.startDate | date('d/m/Y') }}{% endif %}</span></p>
                        <p class="undertext">(Indiquer la date de début et de fin de la formation)</p>
                    </div>
                </div>
                <div class="row">
                    {% set durationPresentielle =  formation.programme.durationPresentielle %}
                    {% set  nbreJrEntiers = durationPresentielle == 21 ? 3 : durationPresentielle == 14 ? 2  : durationPresentielle == 7 ? 1 : 0 %}

                    <div class="columns small-2 no-padding-right">
                        <img width="125px" src="{{ absolute_url(asset('img/left-attestation-presence.png')) }}">
                    </div>
                    <div class="columns small-10 no-padding">
                        <div class="block block-first">
                            <div class="row">
                                <div class="columns small-4">
                                    Nbre de jours entiers:
                                </div>
                                <div class="columns small-4">
                                    + Nbre de demi-journées:
                                </div>
                                <div class="columns small-4">
                                    Nbre total d'heures de formation:
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4">
                                    <div class="empty-block">{{ nbreJrEntiers }}</div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block">{{ durationPresentielle == 3 ? 1 : 0 }}</div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block">{{ durationPresentielle }}</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4">
                                    <p class="undertext undertext-block">(6 heures minimum)</p>
                                </div>
                                <div class="columns small-4">
                                    <p class="undertext undertext-block">(3 heures minimum)</p>
                                </div>
                                <div class="columns small-4">
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-12" style="margin-top:-0.5em">
                                    <p>Si formation se déroulant sur 2 années civiles, indiquez impérativement la durée distincte de chacune des années</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4" style="margin-left:-20px; margin-top:-1em">
                                    <div class="empty-block empty-block-bottom" ><p class="year">Année:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-left: 10px; margin-top:-1em">
                                    <div class="empty-block empty-block-bottom"><p class="nbjours">Nbre de jours:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-top:-1em">
                                    <div class="empty-block empty-block-bottom"><p class="nbheures">Nbre d'heures:</p></div>
                                </div>
                            </div>

                            <div class="row" style="margin-top:10px">
                                <div class="columns small-4" style="margin-left:-20px">
                                    <div class="empty-block empty-block-bottom" ><p class="year">Année:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-left: 10px">
                                    <div class="empty-block empty-block-bottom"><p class="nbjours">Nbre de jours:</p></div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block empty-block-bottom"><p class="nbheures">Nbre d'heures:</p></div>
                                </div>
                            </div>
                        </div>
                        <div style="height: 0.5em;"></div>
                        <div class="block block-second">
                            <div class="row">
                                <div class="columns small-4">
                                    Nbre de total d'heures de formations:
                                </div>
                                <div class="columns small-4">
                                    + Nbre d'étapes validées:
                                </div>
                                <div class="columns small-4">
                                    Date d'évaluation finale:
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4">
                                    <div class="empty-block">
                                        {% if formation.hasElearningUnity() %}
                                            {{ formation.programme.durationNotPresentielle }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block">
                                        {% if formation.hasElearningUnity() %}
                                            4,00
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block">
                                        {% if formation.hasElearningUnity() %}
                                            {{ formation.closingDate | date('d/m/Y') }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4">
                                    <p class="undertext undertext-block">(6 heures minimum)</p>
                                </div>
                                <div class="columns small-4">
                                    <p class="undertext undertext-block">(3 heures minimum)</p>
                                </div>
                                <div class="columns small-4">
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-12" style="margin-top:-0.5em">
                                    <p>Si formation se déroulant sur 2 années civiles, indiquez impérativement la durée distincte de chacune des années</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="columns small-4" style="margin-left:-20px; margin-top:-1em">
                                    <div class="empty-block empty-block-bottom" ><p class="year">Année:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-left: 10px; margin-top:-1em">
                                    <div class="empty-block empty-block-bottom"><p class="nbjours">Nbre de jours:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-top:-1em">
                                    <div class="empty-block empty-block-bottom"><p class="nbheures">Nbre d'heures:</p></div>
                                </div>
                            </div>
                            <div class="row" style="margin-top:10px">
                                <div class="columns small-4" style="margin-left:-20px">
                                    <div class="empty-block empty-block-bottom" ><p class="year">Année:</p></div>
                                </div>
                                <div class="columns small-4" style="margin-left: 10px">
                                    <div class="empty-block empty-block-bottom"><p class="nbjours">Nbre de jours:</p></div>
                                </div>
                                <div class="columns small-4">
                                    <div class="empty-block empty-block-bottom"><p class="nbheures">Nbre d'heures:</p></div>
                                </div>
                            </div>
                            {% if formation.hasElearningUnity() %}
                                {% set total = formation.programme.durationNotPresentielle + formation.programme.durationPresentielle %}
                                <div style="text-align:center; margin-top:0.5em; font-size:10px">SOIT TOTAL FORMATION : [{{ total }}H
                                    (
                                        {% if formation.programme.durationPresentielle %}{{formation.programme.durationPresentielle}}{% else %}0{% endif %}H PRESENTIELLES +
                                        {% if formation.programme.durationNotPresentielle %}{{formation.programme.durationNotPresentielle}}{% else %}0{% endif %}H NON PRESENTIELLES
                                    )]
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-left: 0px;">
                    <div class="columns small-12 reglement">
                        J'atteste également que le stagiaire stipulé ci-dessus a bien réglé la totalité de sa participation à la formation précitée,
                        soit un montant total de <span class="reglement-white-box">...........<span style="color: black; font-weight: 100;">{{ participation.price }}</span>............</span> € HT et <span class="reglement-white-box">...........<span style="color: black; font-weight: 100;">{{ participation.price }}</span>............</span> € TTC correspondant uniquement
                        au coût pédagogique de la formation.
                    </div>
                </div>
                <div class="row" style="margin-top:1em">
                    <div class="columns small-12 rd-txt">
                        Pour les professions de santé : l’organisme de formation atteste que la durée en jours de la formation stipulée sur ce présent document
                        (et pour laquelle une demande de prise en charge est constituée auprès du FIF PL) correspond à une durée en jours non facturée à l’ANDPC.
                    </div>
                </div>

                <div class="row" style="margin-top:1.5em">
                    <div class="columns small-4">
                        <p class="underligned-black">Fait à: <span class="values">MERIGNAC</span></p>
                        <p class="underligned-black">le: {{ formation.closingDate | date('d/m/Y') }}</p>
                        <p class="rd-txt" style="margin-top:5em">
                            Cette attestation de présence n’exclut pas l’obligation
                            pour l’organisme de formation de tenir à la disposition
                            du FIF PL les feuilles d’émargement, ainsi que les fiches
                            d’évaluation de chaque stagiaire. </p>
                    </div>
                    <div class="columns small-4">
                        <div class="block-signature" style="text-align: center;">
                            <p>
                                Cachet obligatoire de l'organisme de formation
                                <img width="200" src="{{ include('pdf/includes/data-image-tampon.html.twig') }}" alt="tampon edpurat">
                            </p>
                        </div>
                    </div>
                    <div class="columns small-4">
                        <div class="block-signature">
                            <p>
                                Nom, prénom et signature du responsable de
                                l’organisme de formation ou de toute autre
                                personne rattachée à l’organisme de formation
                                ayant pouvoir ou délégation de signature
                                <br>
                                <span style="font-size:14px">PLAS PHILIPPE</span>
                                <img width="140" src="{{ include('pdf/includes/data-image-signature.html.twig') }}" alt="signature edpurat">
                            </p>
                        </div>
                    </div>
                </div>
        {# border-top: 5px solid;
                    border-image:   linear-gradient(to right, #D39830 25%, #8D9F2F 25%, #8D9F2F 50%,#008AD2 50%, #008AD2 75%, #EBCD31 75%) 5; #}
                <p style="margin-top:2em; font-size:8px">Conformément à la loi “informatique et liberté” du 06 janvier 1978 (n°78-17) article 34, vous bénéficiez d’un droit d’accès et de rectification aux données portées dans nos fichiers.</p>
                <div class="row footer" style="margin-top:-1em">
                    <div style="display:flex">
                        <div style="width:100%; min-height:5px; background-color:#D39830"></div>
                        {# <div style="width:25%; min-height:5px; background-color:#8D9F2F"></div>
                        <div style="width:25%; min-height:5px; background-color:#008AD2"></div>
                        <div style="width:25%; min-height:5px; background-color:#EBCD31"></div> #}
                    </div>
                    <p style="margin-top:0.5em">104 rue de Miromesnil 75384 Paris Cedex 08 - Tél. 01 55 80 50 00 - Fax 01 55 80 50 29</p>
                    <p style="margin-top:-2em; font-size:8px">Agréé par arrêté ministériel du 17 mars 1993 publié au J.O le 25 mars 1993 - Siret : 398 119 965 00041</p>
                </div>
            </div>
            {% if loop.index != loop.length %} 
                <div class="page-break"></div>
            {% endif %}
        {% endfor %}
    </div>
</body>
</html>