{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            font-size: 0.9em;
        }

        table thead th {
            text-align: center;
            background-color: #cccccc;
            padding: 0.25em 1em;
            border: 1px solid #000;
            font-weight: bold;
        }

        table {
            font-size: 1em;
            margin-top: 0;
            margin-bottom: 0.8em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 20px;
        }

        .pageWidth {
            position: relative;
        }

        .blue {
            color: #087dd0;
            font-size: 0.8em;
            margin-bottom: 1em;
        }

        table tbody td {
            border: 1px solid black;
        }

        #footer-info {
            font-style: italic;
            line-height: 1em;
            font-size: 1em;
            /*position: absolute;
            bottom: 0;
            left: 0;*/
            padding-left: .9375rem;
            padding-right: .9375rem;
        }

        #footer-me {
            padding-top: 2rem;
        }
    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="column small-6">
                    <p class="mbs">{{ "emargement.dpc_name" | trans }} {{ "emargement.dpc_name_value" | trans }}</p>
                </div>
                <div class="column small-6">
                    <p class="mbs">{{ "emargement.id" | trans }} {{ "emargement.id_value" | trans }}</p>
                </div>
            </div>
            <div class="row">
                <div class="column small-6">
                    <p>{{ "emargement.nb_prog" | trans }} {{ formation.programme.reference }}</p>
                </div>
                <div class="column small-6">
                    <p>{{ "emargement.nb_session" | trans }} {{ formation.sessionNumber }}</p>
                </div>
            </div>
            <div class="row">
                <div class="column small-12">
                    {{ "emargement.place" | trans }}
                    <div>{{ formation.address }}</div>
                    {% if formation.address2 is not null %}
                        <div class="">{{ formation.address2 }}</div>
                    {% endif %}
                    <div>{{ formation.zipCode }} {{ formation.city }}</div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="column small-12">
                    <p>{{ "emargement.num_unity" | trans }}
                        {% if unityPosition is not null and unityPosition > 0 %}
                            {{ unityPosition }}
                        {% else %}
                            {% for unity in formation.programme.unities %}
                                {% if unity.isOnsite %}
                                    {{ loop.index }}
                                    {% set break = true %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    </p>
                </div>
            </div>
            <br>
            <div class="row mbs">
                <div class="column small-12 prn pln">
                    {{ "emargement.formationTitle" | trans }} {{ formation.programme.title }}
                </div>
            </div>
            <div class="row">
                <div class="column small-6">
                    <p>{{ "emargement.date" | trans }} {{ date }}</p>
                </div>
                <div class="column small-6">
                    <input id="box1" type="checkbox" />
                    <label for="box1">{{ "emargement.morning" | trans }}</label>
                    <input id="box2" type="checkbox" />
                    <label for="box2">{{ "emargement.afternoon" | trans }}</label>
                    <input id="box3" type="checkbox" />
                    <label for="box3">{{ "emargement.noon" | trans }}</label>
                </div>
            </div>
            <div class="row">
                <div class="column small-6">
                    <p>{{ "emargement.start" | trans }}</p>
                </div>
                <div class="column small-6">
                    <p>{{ "emargement.end" | trans }}</p>
                </div>
            </div>
            <div class="row">
                <p class="emargement-warning">ATTENTION : Une feuille d’émargement par ½ journée (si 1 jour = 2 feuilles d’émargement) ou par soirée</p>
            </div>
            <div class="row">
                <div class="column small-12">
                    <table>
                        <caption>Emargement des intervenants</caption>
                        <thead>
                        <tr>
                            <th style="width: 20%">{{ "emargement.table.lastname" | trans }}</th>
                            <th style="width: 20%">{{ "emargement.table.firstname" | trans }}</th>
                            <th style="width: 22%">{{ "emargement.table.id" | trans }}</th>
                            <th style="width: 19%">{{ "emargement.table.job" | trans }}</th>
                            <th style="width: 19%">{{ "emargement.table.signature" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for formateur in formateurs %}
                            <tr class="nbi">
                                <td>{{ formateur.person.lastname }}</td>
                                <td>{{ formateur.person.firstname }}</td>
                                <td>
                                    {% if formateur.person.rpps is not null and formateur.person.rpps != "" %}
                                        {{ formateur.person.rpps }}
                                    {% elseif formateur.person.adeli is not null and formateur.person.adeli != "" %}
                                        {{ formateur.person.adeli }}
                                    {% endif %}
                                </td>
                                <td>{{ formateur.person.job }}</td>
                                <td></td>
                            </tr>
                        {% endfor %}
                        {% for i in 1..5 %}
                            <tr class="nbi">
                                <td>&#8203;</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="page-break"></div>
            <div class="row">
                <div class="column small-12">
                    <table>
                        <caption>Emargement des participants</caption>
                        <thead>
                        <tr>
                            <th style="width: 16%">{{ "emargement.table.lastname" | trans }}</th>
                            <th style="width: 16%">{{ "emargement.table.firstname" | trans }}</th>
                            <th style="width: 16%">{{ "emargement.table.id" | trans }}</th>
                            <th style="width: 16%">{{ "emargement.table.job" | trans }}</th>
                            <th style="width: 16%">{{ "traceability.financeur" | trans }}</th>
                            <th style="width: 16%">{{ "emargement.table.signature" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                            {% for participation in participations %}
                                <tr class="nbi">
                                    <td>{{ participation.participant.lastname }}</td>
                                    <td>{{ participation.participant.firstname }}</td>
                                    <td>
                                        {% if participation.participant.rpps is not null and participation.participant.rpps != "" %}
                                            {{ participation.participant.rpps }}
                                        {% elseif participation.participant.adeli is not null and participation.participant.adeli != "" %}
                                            {{ participation.participant.adeli }}
                                        {% endif %}
                                    </td>
                                    <td>{{ participation.participant.category }}</td>
                                    <td>{% if participation.financeSousMode.priseEnCharge == "ANDPC" %}ANDPC{% else %}{{ participation.financeSousMode.name }}{% endif %}</td>
                                    <td></td>
                                </tr>
                            {% endfor %}
                            {% for i in 1..5 %}
                                <tr class="nbi">
                                    <td>&#8203;</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="nbi row">
                <div class="column small-12">
                    <p id="footer-info">{{ "emargement.footer.info" | trans }}</p>
                    <p id="footer-me">{{ "emargement.footer.me" | trans }}</p>
                    <p class="mbs">{{ "emargement.footer.done" | trans }}</p>
                    <p>{{ "emargement.footer.signature" | trans }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function ready() {
        var body = document.body, html = document.documentElement;
        var page = document.getElementsByClassName('pageWidth')[0];
        var height = page.clientHeight;
        //page.innerHTML = height;
        // J'ai pas trouvé mieux :) Risque d'explosion à la moindre modification
        if (height <= 2277) {
            page.style.height = 815;
        } else if (height <= 3100) {
            page.style.height = 1636;
        }  else if (height <= 3643) {
            page.style.height = 2450;
        }
    }
</script>
</body>
</html>

{% macro dots(number) %}
    {%- for i in 1..number -%}.{%- endfor -%}
{% endmacro %}