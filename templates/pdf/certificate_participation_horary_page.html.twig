{% set formation = participation.formation %}
{% set programme = participation.formation.programme %}
{% set former = participation.formation.formateursPersons.first %}
<div class="fluid960 page pageCentered">
    <div class="content">
        <div>
            <hr class="mbl">
            <h1 class="text-center">Attestation de participation horaire</h1>
            <hr class="mtl" style="	border-width: 1px;">
        </div>
        <br>
        <div class="row">
            <div class="column small-12">
                <h3 class="text-center">Formation {% if not participation.financeSousMode.isHorsDPC() and not participation.financeSousMode.isFif() %} DPC {% endif %}- {{ programme.title }} du {{ formation.searchStartDate | date('d/m/Y') }}{% if formation.searchStartDate|date('d/m/Y') != formation.searchEndDate|date('d/m/Y') %} au {{ formation.searchEndDate | date('d/m/Y') }}{% endif %}</h3>
            </div>
        </div>
        <br><br>
        <div class="row">
            <div class="column small-12">
                <p>Je, so<PERSON><PERSON><PERSON>, agissant en ma qualité de Directeur de l’organisme Eduprat
                    Formations, atteste que {{ participation.participant.lastname|upper }} {{ participation.participant.firstname }} a suivi :
                </p>
                <ul>
                    <li>
                        {% set totalDuration = participation.formation.programme.durationPresentielle + participation.formation.programme.durationNotPresentielle %}
                        {{totalDuration}} heure{%  if totalDuration >= 2 %}s{% endif %} de formation
                        {% if not participation.financeSousMode.isHorsDPC() and not participation.financeSousMode.isFif() %} DPC{% endif %}, dont
                        {% if participation.formation.programme.durationPresentielle > 0 %}
                            {{ participation.formation.programme.durationPresentielle }}
                            {% if participation.formation.programme.durationPresentielle >= 2 %}{{ 'admin.formation.heuresPresentielles'|trans }}{% else %}{{ 'admin.formation.heurePresentielle'|trans }}{% endif %}
                        {% endif %}
                        {% if participation.formation.programme.durationNotPresentielle > 0 and participation.nextModule is null %}
                            {% if participation.formation.programme.durationPresentielle %} et {% endif %}
                            {{ participation.formation.programme.durationNotPresentielle }}
                            {% if participation.formation.programme.durationNotPresentielle >= 2 %}{{ 'admin.formation.heuresNonPresentielles'|trans }}{% else %}{{ 'admin.formation.heureNonPresentielle'|trans }}{% endif %}
                        {% endif %}
                    </li>
                    <li>
                        De la formation intitulée {{ programme.title }}
                    </li>
                    {% if not participation.financeSousMode.isHorsDPC() and not participation.financeSousMode.isFif() %}
                        <li>
                            Référence DPC {{ formation.programme.reference }}, session n°{{ formation.sessionNumber }},
                        </li>
                    {% endif %}
                    <li>
                        {% if formation.programme.isClasseVirtuelle %}dont l’étape présentielle (en classe virtuelle) a eu lieu{% else %}ayant eu lieu {% if not formation.programme.isElearning %}à {{ formation.city }}{% endif %}{% endif %}{% if formation.searchStartDate|date('d/m/Y') == formation.searchEndDate|date('d/m/Y') %} le {{ formation.searchStartDate | date('d/m/Y') }}{% else %} du {{ formation.searchStartDate | date('d/m/Y') }} au {{ formation.searchEndDate | date('d/m/Y') }}{% endif %}.
                    </li>
                </ul>
                <br>
                <br>
                <br>
                <p>À Mérignac, le {{ participation.formation.closingDate|date('d/m/Y') }}</p>
                <div class="mtl column small-12 text-right">
                    <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                </div>
            </div>
        </div>
    </div>
</div>
