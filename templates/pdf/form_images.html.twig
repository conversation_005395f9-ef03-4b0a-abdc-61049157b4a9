{% for input in form %}
    {% if input.vars.attr['data-question'] is defined %}
        <div class="form-group">
            {{ form_label(input) }}
            {% if input.vars.attr['data-description'] is defined %}
                {{ form_errors(input) }}
                {{ form_widget(input) }}
            {% endif %}
            {% if input.vars.attr['data-images'] is defined %}
                <div class="pdf-image-gallery">
                    {% for image in input.vars.attr['data-images']|split(',') %}
                        <img class="pdf-image" src="{{ image }}" alt="">
                    {% endfor %}
                </div>
            {% endif %}
            {% if input.vars.attr['data-description'] is not defined %}
                {{ form_errors(input) }}
                {{ form_widget(input) }}
            {% endif %}
        </div>
        {% if not loop.first and input.children|length > 0 %}
            <hr class="thinMarge">
        {% endif %}
    {% endif %}
{% endfor %}