<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }

        #programme {
            padding: 1em;
            background: #ddd;
            float: right;
            min-width: 330px;
            font-family: Arial, sans-serif;
        }

        #coordinateur-formateur {
            font-size: 1em;
        }

        .newFormateur {
            position: absolute;
            left: 9cm;
            top: 3cm;
            max-width: 15cm;
            font-size: 1em;
        }

        #contact {
            position: absolute;
            left: 4cm;
            top: 0cm;
            max-width: 15cm;
            font-size: 1em;
        }

        .title {
            text-align: center;
            font-size: 1.9em;
            /*margin-top: 0.25cm;*/
        }
        .littletitle {
            text-align: center;
            font-size: 1.25em;
        }
        .centered {
            text-align: center;
            margin-top: 0.5cm;
            /*margin-bottom: 1cm;*/
        }
        .infos {
            margin-top: 0.25cm;
            font-weight: bold;
        }
        .restitution-row {
            page-break-inside: avoid;
            padding-top: 1.8em;
        }

        .restitution-row::after {
            content: '';
            clear: both;
            display: block;
        }

        .interpretation {
            letter-spacing: -1px;
            font-family: stimupratbold, sans-serif;
            margin-bottom: 2em;
        }

        .interpretation p {
            margin: 0;
        }
        .comments > ul {
            list-style-position: inside;
        }
        #legend {
            height: 6cm;
            padding-top: 1cm;
        }
        #legend ul {
            margin-left: 0;
        }

        #legend li {
            margin-bottom: 0.5em;
        }

        #legend li > img {
            margin-right: 1em;
        }

        .interpretation, .interpretation span, .interpretation p {
            font-size: 1em !important;
            margin-bottom: 1em;
        }

        #detail-title {
            font-size: 1.2em;
        }

        #spiderweb {
            margin: 2em 0 0.2em 0;
        }

        .highcharts-root, .highcharts-container {
            overflow: visible !important;
        }
        .section {
            padding-top: 1em;
            page-break-inside: avoid;
        }

        .section-title {
            margin-bottom: 0;
            font-size: 1.75em;
            text-transform: uppercase;
            padding-top: 0.125em;
            font-family: stimupratbold, sans-serif;
            font-weight: 600;
        }

        .progress {
            font-size: 0;
            height: 20px;
            margin-bottom: 3px;
            background-color: transparent;
        }

        .progress-meter {
            display: inline-block;
        }

        .progress-text {
            font-size: 10px;
        }

        .progress-meter-success {
            background: linear-gradient(to right, #8b5e92, #6086b9);
            background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .progress-meter-error {
            background-color: #d71903;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }

        .restitution-question + p {
            margin-bottom: 0.5rem;
        }

        #barchart .bar {
            fill: #6c5d9f;
        }
        #barchart .axis path, #barchart .axis line {
            fill: none; stroke: #000; shape-rendering: crispEdges;
        }

        .page-break + .section {
            padding-top: 1cm;
        }

        .footer-fixed {
            position: absolute;
            top: 28.2cm;
            text-align: center;
            width: 100%;
            left:0;
            line-height: 1.3em;
            font-size: 0.8em;
        }


        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
        .bold {
            font-family: Arial, sans-serif;
        }

        .restitution-question.blue {
            font-size: 16px;
        }

        .restitution-question+p {
            font-size: 14px;
        }

        .answers label {
            font-size: 12px;
        }

        .answers-postulat {
            margin-top: 5px;
        }

        .answers-postulat p {
            margin-bottom: 0;
        }

        .restitution-patient {
            font-size: 16px;
            margin-top: 16px;
        }

        .restitution-patient-audit {
            font-size: 20px;
            margin-top: 16px;
        }

        .restitution-row-pad {
            padding-left: 16px;
        }

        .interpretation {
            display: block;
        }
        .no-flex {
            display: block;
        }
    </style>
</head>
<body>
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        <hr style="border-width: 2px;" class="mtn mbs">
        <h1 class="text-center mbn mtn">
            {% if formation.isVignette() %}{{ "front.scoring"|trans }}{% else %}{% if auditId == 1 %}Pré-restitution{% else %}Restitution{% endif %}{% endif %} -
            {% if auditId == 1 %}{{ ("front.label.formType." ~ formation.formTypePre ~ ".singular") | trans }} {% else %} {{ ("front.label.formType." ~ formation.formTypePost ~ ".singular") | trans }} {% endif %}{% if auditId == 1 %}{% if formation.isFormPredefined%}pré{% else %}1{% endif %}{% endif %}
        </h1>
        <h2 class="text-center mbn">
            {% if auditId == 1 %}
                {% if formation.formTypePre == "survey" %}
                    {{ formation.questionnaire.label }}
                {% else %}
                    {{ formation.audit.label }}
                {% endif %}
            {% else %}
                {% if formation.formTypePre == "survey" %}
                    {{ formation.questionnaire.label }}
                {% else %}
                    {% if formation.isFormVignetteAudit %}
                        {{ formation.audit2.label }}
                    {% else %}
                        {{ formation.audit.label }}
                    {% endif %}
                {% endif %}
            {% endif %}
        </h2>
        <hr class="mts">
        {% include "pdf/section/header.html.twig" %}
        <br>
        {% set summaryIndex = 0 %}
        {% if formation.isFormVignette and not formation.isVfc %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score global groupe</p>
                <br>
                {% if auditId == 1 %}
                    <p style="font-size: 18px">Le groupe a obtenu <span class="bold">{{ globalScores[1]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePre ~ ".plural") | trans }} pré formation.</p>
                {% else %}
                    <p style="font-size: 18px">Le groupe a obtenu <span class="bold">{{ globalScores[2]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePost ~ ".plural") | trans }} post formation.</p>
                    <p style="font-size: 16px">En pré session, le groupe avait obtenu <span class="bold">{{ globalScores[1]|number_format(2, '.', ' ') }}%</span>.</p>
                {% endif %}
            </div>
        {% endif %}
        {% if not formation.isVfc %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score moyen par thème</p>
                {% if hasOneResponse %}
                    <div id="spiderweb"></div>
                {% else %}
                    <p>Aucune réponse de la part des participants</p>
                {% endif %}
            </div>
        {% endif %}
        {% if formation.isFormAudit and formation.audit.isDefaultType %}
            {% if auditId == 1 %}
                {# <div class="page-break"></div>
                <div class="section">
                    {% set summaryIndex = summaryIndex + 1 %}
                    <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Répartition du nombre de bonnes réponses</p>
                    {% if hasOneResponse %}
                        <div id="barchart"></div>
                        <div style="font-size: 16px;" class="text-center bold">Nombre de participants ayant bien répondu en %</div>
                        <hr class="mts">
                    {% else %}
                        <p>Aucune réponse de la part des participants</p>
                    {% endif %}
                </div> #}
            {% else %}
                {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/indicators.html.twig" with {show1: false, show2: false} %}
            {% endif %}
        {% else %}
            {% if (formation.isFormPredefined or formation.isFormPresentielle) and auditId == 2 %}
                {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/indicators.html.twig" with {show1: false, show2: false} %}
            {% else %}
                <div class="page-break"></div>
            {% endif %}
        {% endif %}
        {% set summaryIndex = summaryIndex + 1 %}
        {% include "pdf/section/criteres.html.twig" %}
        {% include "pdf/section/contact.html.twig" with { auditId: 1, displayFormers: false, title: formation.isFormVignette ? "contact" : false }%}
    </div>
</div>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
<script src="{{ asset('js/radarChart.js') }}"></script>
<script>

    var radarData = JSON.parse('{{ radarDataJson | escape('js') }}');

    var margin = {top: 80, right: 100, bottom: 60, left: 100};
    var width = 519;
    var height = 320;
    var maxValue = 100;

    var gradients = [["#74256b", "#3a619f"]{% if auditId == 2 %}, ["#005171", "#009098"]{% endif %}];

    radarData = radarData.sort(function(a, b) {
    	return a.group >= b.group;
    }).reduce(function(acc, data) {
        {% if auditId == 1 %}
        acc[0].push({group: data.group, axis: data.label, value: data.moy_1});
        {% else %}
	    acc[1].push({axis: data.label, value: data.score_1});
	    acc[0].push({axis: data.label, value: data.score_2});
        {% endif %}
        return acc;
    }, [[]{% if auditId == 2 %},[]{% endif %}]);

    {% if auditId == 2 %}
    var color = d3.scale.ordinal().range(["#1b9ba2", "#7253d2"]);
    {% else %}
    var color = d3.scale.ordinal().range(["#7253d2"]);
    {% endif %}

    var auditLabel = "{{ ('front.label.' ~ formation.displayType ~ '.singular') | trans }}";
    var auditId1 = "{% if formation.isFormPredefined %}pré{% else %}1{% endif %}";
    var auditId2 = "{% if formation.isFormPredefined %}post{% else %}2{% endif %}";

    var radarChartOptions = {
	    w: width,
	    h: height,
	    margin: margin,
	    maxValue: maxValue,
	    levels: 4,
	    roundStrokes: true,
	    labelFactor: 1.1,
	    lineBackground: true,
	    dotRadius: 0,
	    wrapWidth: 150,
	    color: color,
	    gradients: gradients,
        legends: ["Score du groupe " + auditLabel + " " + auditId1 {% if auditId == 2 %}, "Score du groupe " + auditLabel + " " + auditId2{% endif %}],
	    rotationFactor: radarData[0].length === 6 ? 0.5 : 0,
    };

    //Call function to draw the Radar chart
    RadarChart("#spiderweb", radarData, radarChartOptions);

    {% if formation.isFormAudit and formation.audit.isDefaultType %}
    {# BAR CHART #}
    var barData = JSON.parse('{{ barData | escape('js') }}');
    var margin = {top: 50, right: 20, bottom: 70, left: 30},
            width = 719 - margin.left - margin.right,
            height = 300 - margin.top - margin.bottom;

    // set the ranges
    var x = d3.scale.linear().range([0, width]);

    var y = d3.scale.linear().range([height, 0]);

    // define the axis
    var xAxis = d3.svg.axis()
            .scale(x)
            .orient("bottom")
            .ticks(20)

    var maxX = d3.max(barData, function(d) { return d.nb_good; });
    var maxY = d3.max(barData, function(d) { return d.nb_participant; });

    var yAxis = d3.svg.axis()
            .scale(y)
            .orient("left")
            .ticks(maxY);


    // add the SVG element
    var svg = d3.select("#barchart").append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .append("g")
            .attr("transform", "translate(" + margin.left + "," + margin.top + ")");

//    barData.forEach(function(d) {
//        d.nb_good = d.nb_good;
//        d.nb_participant = +d.nb_participant;
//    });

    // scale the range of the barData
    x.domain([0, 100]);
    y.domain([0, maxY]);

    // add axis
    svg.append("g")
            .attr("class", "x axis")
            .attr("transform", "translate(0," + height + ")")
            .call(xAxis)
            .append("text")
            .style("text-anchor", "end")
            .attr("x", width)
            .attr("dx", "0")
            .attr("dy", "3em")
            .text("% de bonnes réponses");

    svg.append("g")
            .attr("class", "y axis")
            .call(yAxis)
            .append("text")
            .attr("y", 5)
            .attr("dy", "-2em")
            .attr("dx", "15em")
            .style("text-anchor", "end")
            .text("Nb participants ayant bien répondu");

    // Add bar chart
    svg.selectAll("bar")
            .data(barData)
            .enter().append("rect")
            .attr("class", "bar")
            .attr("x", function(d) {
                return x(d.nb_good) - (Math.min(Math.floor(width / maxX) - 5, 10) / 2);
            })
            .attr("width", function(d) {
                return Math.min(Math.floor(width / maxX) - 5, 10);
            })
            .attr("y", function(d) { return y(d.nb_participant); })
            .attr("height", function(d) { return height - y(d.nb_participant); });
    {% endif %}
</script>
<script>
//    function ready() {
//        var page = document.getElementsByClassName('pageWidth')[0];
//        var height = document.body.scrollHeight;
//        var cm = 29.7 - 2.6;
//        var px = 37.74;
//        var pgh = 1041;
//        var nbpage = Math.ceil((height / pgh));
//        page.style.height = (cm * px * nbpage);
//    }
</script>
</body>
</html>
