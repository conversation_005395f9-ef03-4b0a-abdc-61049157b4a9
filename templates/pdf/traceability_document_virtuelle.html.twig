<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #5B9BD5;
            color: white;
        }

        table {
            font-size: 0.85em;
            border:2px solid black;
            border-top:none;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }

        ul {
            list-style-position: inside;
        }        

        .wrap {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .text-box1 {
            height: 570px;
            border: 1px solid black;
        }

        .text-box1.text-box1-ppt {
            height: 400px;
            border: 1px solid black;
        }

        .text-box1.text-box1-zoom {
            height: 280px;
            border: 1px solid black;
        }

        .text-box2 {
            height: 160px;
            border: 1px solid black;
        }

        .text-box2.text-box2-zoom {
            height: 280px;
        }

        .text-box-header {
            background-color: #d9e2f3;
            height: 20px;
            line-height: 20px;
        }

        .text-box-text1 {
            padding: 1em;
        }

        .text-box-text2 {
            padding: 2em .9375rem 1em .9375rem;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

        .mtl {
            margin-top:2em;
        }

        .info-txt {
            color:#0071B4;
            font-weight: 600;
        }

        .link-style {
            background-color:#FFC000;
            border:2px solid black;
            padding:4px;
            font-weight:bold
        }

        .table-title {
            font-weight:bold;
            font-size:16px;
            text-align:center;
            border:2px solid black;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="column large-12">
                    <div><span class="bold">{{ "traceability.organization_name" | trans }}</span> : {{ "traceability.organization_name_value" | trans }}</div>
                    <div><span class="bold">{{ "traceability.id" | trans }}</span> : {{ "traceability.id_value" | trans }}</div>
                </div>
            </div>

            <div class="row mtl mbm" style="background-color:#d9e2f3;">
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.nb_action_prog" | trans }}</span> : {{ formation.programme.reference }}</div>
                </div>
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.nb_session" | trans }}</span> : {{ formation.sessionNumber }}</div>
                </div>
                {% if isZoom %}
                    <div class="column small-3">
                        <div><span class="bold">{{ "traceability.nb_unity" | trans }}</span> : 2{% if formation.isVfc() or formation.isFourUnity or formation.isTCS() %} et 3{% endif %}</div>
                    </div>
                {% endif %}
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.vol_hour" | trans }}</span> : {{ formation.programme.durationPresentielle }}h</div>
                </div>
            </div>

            <div class="row mtl mbm info-txt">
                <p>→ ATTENTION : Un tableau est à compléter par vacation d'une 1/2 journée (1 jour = 2 tableaux) ou par soirée<br>
                → Cette attestation vous permet à la fois de déclarer les intervenants et les participants de la demi-journée concernée<br>
                → Les noms doivent être classés par ordre alphabatique et les prénoms et numéros d'identification complétés<br>
                → La liste des logs de connexion pour chaque  1/2 journée comportera non seulement ceux des professionnels de santé éligibles au financement de l'Agence mais éventuellement aussi ceux de
                    professionnels financés par un autre opérateur afin de valider le seuil minimal de 5 participants à la session.</p>
            </div>

            <div class="row mtl">
                <div class="column large-12 link-style">
                    Adresse url / logiciel utilisé : {{ formation.zoomLink }}
                </div>
            </div>

            {% if formation.formateurs | length %}
                <div class="row mtl mbm">
                    <div class="column large-12">
                        <div class="row" style="font-size:12px">
                            <div class="column small-5">
                                    <b> Date de la vacation : {{ formation.startDate | date('d/m/Y') }}</b>
                            </div>
                            <div class="column small-3">
                                    <b> Début : {% if formation.zoomStartTime %} {{ formation.zoomStartTime | date('H\\hi') }} {% endif %}</b>
                            </div>
                            <div class="column small-3">
                                <b> Fin : {% if formation.zoomEndTime %} {{ formation.zoomEndTime | date('H\\hi') }} {% endif %}</b>
                            </div>
                        </div>
                        <div class="table-title">INTERVENANTS</div>
                        <table>
                            <thead>
                            <tr>
                                <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                                <th>{{ "admin.participant.firstname" | trans }}</th>
                                <th>{{ "traceability.rpps_adeli" | trans }}</th>
                                {% if formation.programme.isClasseVirtuelle() %}
                                    <th>{{ "traceability.first_hour_connection" | trans }}</th>
                                    <th>{{ "traceability.last_hour_connection" | trans }}</th>
                                {% else %}
                                    <th>{{ "traceability.first_connection" | trans }}</th>
                                    <th>{{ "traceability.last_connection" | trans }}</th>
                                {% endif %}
                                <th>{{ "traceability.total_time" | trans }} (en min)</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for former in formation.formateurs %}
                                <tr>
                                    <td class="wrap">{{ former.person.lastname | upper }}</td>
                                    <td class="wrap">{{ former.person.firstname }}</td>
                                    <td class="center">{% if former.person.rpps %} {{ former.person.rpps }}{% endif %}</td>
                                        {% if former.totalTimeZoom %}
                                            {% if former.startedAtZoom and not isEmpty %}
                                                <td class="center">{{ former.startedAtZoom|date('d/m/Y') }}&nbsp{{ former.startedAtZoom|date('H\\hi') }}</td>
                                            {% else %}
                                                <td></td>
                                            {% endif %}
                                            {% if former.finishedAtZoom and not isEmpty %}
                                                <td class="center">{{ former.finishedAtZoom|date('d/m/Y') }}&nbsp{{ former.finishedAtZoom|date('H\\hi') }}</td>
                                            {% else %}
                                                <td></td>
                                            {% endif %}
                                            <td class="center">{% if not isEmpty %}{{ former.totalTimeZoomMin }}{% endif %}</td>
                                        {% else %}
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        {% endif %}
                                </tr>
                            {% endfor %}
                                {# <tr>
                                    <td colspan="7">{{ "traceability.average_participation_time" | trans }}</td>
                                    <td class="center">{{ averageParticipationTime }}</td>
                                </tr> #}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}

            {% if participations | length %}
                <div class="row mtl mbm">
                    <div class="column large-12">
                        <div class="row" style="font-size:12px">
                            {% if not formation.formateurs | length %}
                                <div class="column small-5">
                                        <b> Date de la vacation : {{ formation.startDate | date('d/m/Y') }}</b>
                                </div>
                                <div class="column small-3">
                                    <b> Début : {% if formation.zoomStartTime %} {{ formation.zoomStartTime | date('H\\hi') }} {% endif %}</b>
                                </div>
                                <div class="column small-3">
                                    <b> Fin : {% if formation.zoomEndTime %} {{ formation.zoomEndTime | date('H\\hi') }} {% endif %}</b>
                                </div>
                            {% endif %}
                        </div>
                        <div class="table-title">PARTICIPANTS</div>
                        <table>
                            <thead>
                            <tr>
                                <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                                <th>{{ "admin.participant.firstname" | trans }}</th>
                                <th>{{ "traceability.rpps_adeli" | trans }}</th>
                                <th>{{"traceability.financeur" | trans }}</th>
                                {% if not isZoom %}
                                    <th>{{ "traceability.activity_done" | trans }}</th>
                                    <th>{{ "traceability.ip_adress" | trans }}</th>
                                {% endif %}
                                {% if formation.programme.isClasseVirtuelle() %}
                                    <th>{{ "traceability.first_hour_connection" | trans }}</th>
                                    <th>{{ "traceability.last_hour_connection" | trans }}</th>
                                {% else %}
                                    <th>{{ "traceability.first_connection" | trans }}</th>
                                    <th>{{ "traceability.last_connection" | trans }}</th>
                                {% endif %}
                                <th>{{ "traceability.total_time_min" | trans }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for participation in participations %}
                                <tr>
                                    <td class="wrap">{{ participation.participant.lastname | upper }}</td>
                                    <td class="wrap">{{ participation.participant.firstname }}</td>
                                    {% if participation.participant.rpps == '' %}
                                        <td class="center">{{ participation.participant.adeli }}</td>
                                    {% else %}
                                        <td class="center">{{ participation.participant.rpps }}</td>
                                    {% endif %}
                                    <td class="center">{% if participation.financeSousMode.priseEnCharge == "ANDPC" %}ANDPC{% else %}{{ participation.financeSousMode.name }}{% endif %}</td>
                                    {% if not isZoom %}
                                        <td class="center">{{ "traceability.yes" | trans | upper }}</td>
                                        <td class="center">{% if not isEmpty %}{{ participation.ip }}{% endif %}</td>
                                    {% endif %}
                                    {% if isZoom %}
                                        {% if participation.totalTimeZoom %}
                                            {% if participation.startedAtZoom and not isEmpty %}
                                                <td class="center">{{ participation.startedAtZoom|date('d/m/Y') }}&nbsp{{ participation.startedAtZoom|date('H\\hi') }}</td>
                                            {% else %}
                                                <td></td>
                                            {% endif %}
                                            {% if participation.finishedAtZoom and not isEmpty %}
                                                <td class="center">{{ participation.finishedAtZoom|date('d/m/Y') }}&nbsp{{ participation.finishedAtZoom|date('H\\hi') }}</td>
                                            {% else %}
                                                <td></td>
                                            {% endif %}
                                            <td class="center">{% if not isEmpty %}{{ participation.totalTimeZoomMin }}{% endif %}</td>
                                        {% else %}
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        {% endif %}
                                    {% else %}
                                        {% if participation.startedAtLogs is null or isEmpty %}
                                            <td class="center"></td>
                                        {% else %}
                                            <td class="center">{{ participation.startedAtLogs|date('d/m/Y') }} {{ participation.startedAtLogs|date('H\\hi') }}</td>
                                        {% endif %}
                                        {% if participation.finishedAtLogs is null or isEmpty %}
                                            <td class="center"></td>
                                        {% else %}
                                            <td class="center">{{ participation.finishedAtLogs|date('d/m/Y') }} {{ participation.finishedAtLogs|date('H\\hi') }}</td>
                                        {% endif %}
                                        {% if participation.totalTimeText and not isEmpty %}
                                            <td class="center">{{ participation.totalTimeText }}</td>
                                        {% else %}
                                            <td></td>
                                        {% endif %}
                                    {% endif %}
                                </tr>
                            {% endfor %}
                                {# <tr>
                                    <td colspan="7">{{ "traceability.average_participation_time" | trans }}</td>
                                    <td class="center">{{ averageParticipationTime }}</td>
                                </tr> #}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endif %}

            <div class="row mbm mtm">              
            </div>

            <div class="nbi">
                <div class="row mbm">
                    <div class="column large-12">
                        {% if formation.programme.isClasseVirtuelle() %}
                            {{ "traceability.text_1_classe_virtuelle" | trans }}<br>
                            {{ "traceability.text_2_classe_virtuelle" | trans }}
                        {% else %}
                            {{ "traceability.text_1" | trans({"%ref%": formation.programme.reference, "%session%": formation.sessionNumber}) }}<br>
                            {{ "traceability.text_2_unity" | trans }}
                        {% endif %}
                    </div>
                </div>
                <div class="row">
                    <div class="column small-6">
                        {{ "traceability.address" | trans({"%date%" : formation.closingDate | date('d.m.Y') }) }}
                    </div>
                    <div class="column small-6">
                        {{ "traceability.signature" | trans }} : <br>
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat" />
                    </div>
                </div>
                <div class="row" style="margin-top:2em;">
                    <b><u>{{ "traceability.text_5_article_title" | trans }}</u></b> "{{ "traceability.text_5_article_detail" | trans }}"
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
