{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
{#    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">#}
    <link href='https://fonts.googleapis.com/css?family=Roboto+Condensed:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>

    <style>
        html {
            font-size: 100%; /* 16px */
        }

        body {
            width: 21cm;
            font-size: 12px; /* 12px / 1.6 = 7.5px */
            font-family: "Roboto Condensed", sans-serif;
        }

        label {
            font-weight: bold;
            color: #109199;
            text-transform: uppercase;
            font-size: 0.75em; /* 1.2em / 1.6 = 0.75em -> 14.4px / 1.6 = 9px */
        }

        p {
            margin-bottom: 0.9375rem; /* 1.5rem / 1.6 = 0.9375rem -> 24px / 1.6 = 15px */
            line-height: 1.2;
            font-size: 11.75px;
        }

        .mainContent p {
            margin-bottom: 0.625rem; /* 1rem / 1.6 = 0.625rem -> 16px / 1.6 = 10px */
        }

        .mainContent p.commentSInscrireBloc {
            margin-bottom: 0;
        }

        .art-group p:last-child {
            margin-bottom: 1.25em; /* 2em / 1.6 = 1.25em -> 32px / 1.6 = 20px */
        }

        h1 {
            font-size: 20px;
            margin-bottom: 0;
            text-transform: uppercase;
            color: white;
            font-family: Roboto;
            font-weight: bold;
            z-index: 2;
        }

        h2 {
            font-family: Roboto, sans-serif;
            font-size: 16px; /* 16px / 1.6 = 10px */
            font-weight: bold;
            width: 100%;
            {% if formation.programme.category and formation.programme.category.color %}
                color: {{ formation.programme.category.color }};
                border-color: {{ formation.programme.category.color }};
            {% else %}
                color: #e89f75;
                border-color: #e89f75;
            {% endif %}
            border-bottom: 1px solid;
            padding-left: 5px;
            {# margin-top: 5px; #}
            margin-bottom: 5px;
        }

        h3 {
            font-size: 12px;
            font-weight: bold;
            color: #737e8a;
            text-transform: uppercase;
            margin-bottom: 0px;
            margin-top: 5px;
            font-family: "Roboto Condensed", sans-serif;
        }

        ul {
            list-style-position: inside;
        }

        .title-block {
            margin-top: 100px;
            background: #109199;
            padding: 50px 20px 150px;
            text-align: center;
            font-size: 16.25px; /* 26px / 1.6 = 16.25px */
            font-weight: bold;
            color: white;
        }

        .underline {
            text-decoration: underline;
        }

        #conditions {
            font-size: 7.5px; /* 12px / 1.6 = 7.5px */
        }

        .flex {
            display: -webkit-box;
            display: flex;
        }

        .flex-colone {
            flex-direction: column;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
        }

        .colonne12 {
            display: -webkit-inline-box;
            display: inline-flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
            width: 8cm;
            height: -webkit-fit-content;
            height: fit-content;
        }

        .colonne1 {
            {% if formation.programme.category and formation.programme.category.color %}
                background-color: {{ formation.programme.category.color }};
            {% else %}
                background-color: #e89f75;
            {% endif %}
            width: 0.75cm;
            -webkit-box-align: end;
            align-items: end;
        }

        .colonne1 div {
            -webkit-transform-origin: left top;
            transform-origin: left top;
            -webkit-transform: rotate(-90deg) translateX(-0.75cm);
            transform: rotate(-90deg) translateX(-0.75cm);
            display: -webkit-box;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: center;
            justify-content: center;
            margin-top: 10px;
        }

        .colonne1 p {
            color: white;
            width: 14.8cm;
            text-transform: uppercase;
            margin-top: 6px;
            margin-left: 10px;
            font-size: 16px;
        }

        .colonne2 {
            width: 7.25cm;
            position: relative;
        }

        .absoluteElements {
            position: absolute;
            display: block;
            top: 100%;
            left: 0;
        }

        .colonne3 {
            position: absolute;
            top: 0;
            left: 8cm;
            width: 13cm;
            overflow: hidden;
            float: right;
        }

        .underline {
            text-decoration: underline;
        }
        .strong {
            font-weight: bold;
        }

        .referenceBloc {
            background-color: #ec681b;
            color: white;
            text-align: center;
            padding-top: 10px;
            padding-bottom: 10px;
            font-size: 14px;
        }
        .typeBloc {
            background-color: #737e8a;
            color: white;
            text-align: center;
            padding-top: 10px;
            padding-bottom: 10px;
            font-weight: bold;
        }
        .infosSessionBloc {
            padding: 5px 10px;
            font-size: 8.75px; /* 14px / 1.6 = 8.75px */
            position: relative;
        }
        .infosSessionBloc::before {
            {% if formation.programme.category and formation.programme.category.color %}
                background-color: {{ hexToRGBA(formation.programme.category.color, 0.2) }};
            {% else %}
                background-color: {{ hexToRGBA("#e89f75", 0.2) }};
            {% endif %}
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            content: " ";
            width: 100%;
            height: 100%;
        }

        .renseignementBloc,
        .lesaviezvousBloc {
            padding: 5px 10px;
            background-color: #f0f1f4;
        }

        .renseignementBloc p {
            font-size: 12px;
        }

        .lesaviezvousBloc {
            position: relative;
            margin-top: 20px; /* 32px / 1.6 = 20px */
            padding-top: 30px; /* 48px / 1.6 = 30px */
        }

        .asterisqueBloc {
            padding: 5px 10px 0 10px;
            font-size: 8px; /* 8px / 1.6 = 5px */
            font-weight: 160;
            font-family: Roboto;
        }

        .header {
            {% if formation.programme.picture %}
                background-image: url("{{ asset('uploads/programme/picture/'~formation.programme.picture) }}");
            {% endif %}
            background-size: cover;
            padding-left: 1.25rem; /* 32px / 1.6 = 20px */
            padding-bottom: 1.25rem; /* 32px / 1.6 = 20px */
            min-height: 12.9375rem; /* 331.2px / 1.6 = 207px */
            position: relative;
            display: -webkit-box;
            display: flex;
            -webkit-box-align: end;
            align-items: end;
            -webkit-box-pack: justify;
            justify-content: space-between;
            z-index: 0;
        }

        .header::before {
            {% if formation.programme.category and formation.programme.category.color %}
                background-color: {{ hexToRGBA(formation.programme.category.color, 0.5) }};
            {% else %}
                background-color: {{ hexToRGBA("#e89f75", 0.5) }};
            {% endif %}
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            content: " ";
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        .flex75 {
            position: relative;
            padding-right: 1rem; /* 16px / 1.6 = 10px */
            z-index: 2;
        }

        .flex48 {
            width: 5cm;
            position: relative;
            z-index: 2;
        }

        .listPresence {
            font-family: Roboto;
            font-size: 6.25px; /* 10px / 1.6 = 6.25px */
            font-weight: bold;
        }

        .listPresence > div {
            border-top-left-radius: 5px 10px;
            border-bottom-left-radius: 5px 10px;
            padding: 5px 0 5px 2.5rem;
            margin-top: 1rem;
            color: white;
        }
        .presentielle {
            background-color: #109199;
        }
        .classe-virtuelle {
            background-color: #ec681b;
        }
        .elearning {
            background-color: #737e8a;
        }
        .presentielle.notUse {
            background-color: rgba(16, 145, 153, 0.5);
            color: rgba(255, 255, 255, 0.5);
        }
        .classe-virtuelle.notUse {
            background-color: rgba(236, 104, 27, 0.5);
            color: rgba(255, 255, 255, 0.5);
        }
        .elearning.notUse {
            background-color: rgba(115, 126, 138, 0.5);
            color: rgba(255, 255, 255, 0.5);
        }
        .inscription {
            color : #cd1719;
        }
        .date {
            font-family: Roboto;
            font-weight: bold;
            font-size: 16px;
        }
        .renseignementBloc .title,
        .infosSessionBloc .title {
            text-transform: uppercase;
            font-weight: bold;
            font-family: Roboto;
        }
        .img-coordinateur {
            display: inline;
            margin: 5px auto;
            border-radius: 50%;
            width: 55px;
            height: 55px;
            object-fit: cover;
        }
        #infoBulle {
            position: absolute;
            top: -20px;
            left: 127px;
            width: 40px;
            height: 40px;
            {% if formation.programme.category and formation.programme.category.color %}
                background-color: {{ formation.programme.category.color }};
            {% else %}
                background-color: #e89f75;
            {% endif  %}
            border-radius: 50%;
            color: white;
            font-size: 30px;
            padding-top: -2px;
            font-weight: bold;
        }
        .mainContent {
            padding: 0.3125rem 1.25rem 0;
        }
        .mainContent p, .mainContent ul, .mainContent li {
            font-weight: 500;
            line-height: 12px;
        }
        .mainContent ul {
            margin-left: 2px;
            margin-bottom: 8px;
        }
        .mainContent a {
            color: black;
            text-decoration: underline;
        }
        .commentSInscrireBloc {
            font-size: 10px;
            margin-bottom: 0;
        }
        .infosSessionBloc p {
            line-height: 1;
            font-weight: 480;
        }
        .infosSessionBloc .title {
            font-family: Roboto;
            font-weight: bold;
        }
        .img-formater {
            display: inline;
            margin: 10px 10px 20px;
            border-radius: 50%;
            background-color: white;
            width: 80px;
            height: 80px;
        }
        .text-formater {
            max-width:150px;
            padding-top: 20px;
            padding-left: 10px;
            text-align: left;
        }
        .expert {
            font-size: 14px;
        }
    </style>
</head>
<body>

<div class="flex colonne12">
    <div class="flex colonne1">
        <div>
            {% if formation.programme.category %}
                <p>{{ formation.programme.category.name }}</p>
            {% endif %}
        </div>
    </div>
    <div class="flex flex-colone colonne2">
        <div class="flex" style="margin: 0.9375rem 0rem 0.9375rem 1.875rem; height: 77px;">
            <img src="{{ asset('img/eduprat-new-logo-web.png') }}" width="100" height="77" />
            {% if formation.programme.hasPrisesEnChargeDpc() %}
                <img src="{{ asset('img/Logo-ODPC-svg.png') }}" width="120" height="77" alt="logo ODPC" style="margin-left: 0.5rem;">
            {% endif %}
        </div>
        <div class="referenceBloc">
            <span class="underline">Référence de l'action :</span><br>
            <span class="strong">{{ formation.programme.reference }}{% if not formation.programme.isELearning %}, Session {{ formation.sessionNumber }}{% endif %}</span>
        </div>
        <div class="typeBloc">
            {{ formation.programme.getFormatByDurations()  }} ({{ ('admin.formation.form_type.'~formation.programme.getFormType())|trans }}) {{ formation.programme.getDurationTotal()
            }}h{% if not formation.programme.isElearning() %} | {{ formation.programme.getPresence() }}{% endif %}
        </div>
        <div class="infosSessionBloc">
            {% if not formation.programme.isELearning %}
                <p>
                    {% for date in formation.datesToString %}
                        <span class="date">{{ date.date|title }} {{ formation.startDate|date('Y') }}</span><br>
                        {{ date.hours|lower }}<br>
                    {% endfor %}
                </p>
                <p class="inscription">
                    <span class="title"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> INSCRIPTION</span><br>
                    Inscription au plus tard le <span class="strong">
                        {{ ('global.jour.' ~ (formation.dateLimiteInscription|date('l')))|trans }}
                        {{ formation.dateLimiteInscription|date('d') }}
                        {{ ('global.mois.' ~ (formation.dateLimiteInscription|date('F')))|trans }}
                        {{ formation.dateLimiteInscription|date('Y') }}
                    </span>.
                </p>
            {% endif %}
            <p>
                <span class="title">LIEU*</span><br>
                {% if formation.programme.isElearning() %}
                    Plateforme e-learning Eduprat
                {% elseif formation.programme.isClasseVirtuelle() %}
                    Zoom : <br>
                    Un lien d’accès à la classe virtuelle sera transmis par mail aux participants en amont de la formation.
                {% else %}
                    {{ formation.address }}<br>
                    {% if formation.address2 != '' %}
                        {{ formation.address2 }}<br>
                    {% endif %}
                    {{ formation.zipCode }} {{ formation.city }}
                {% endif %}
            </p>
            <p>
                {% if formation.formateurs|length %}
                    <span class="title">EXPERT(S)</span><br>
                    {% for formateur in formation.formateurs %}
                        <span class="strong expert">{{ formateur.person.civility }} {{ formateur.person.invertedFullName }}</span> - {{ formateur.person.job }}.<br>
                    {% endfor %}
                {% endif %}
            </p>
            <p>
                {% if formation.programme.specialities %}
                    <span class="title">
                        {% set plural = indemnisations[formation.id][0]|length > 1 %}
                        Public{% if plural %}s{% endif %}
                        {% if indemnisations[formation.id]["thereIsAPrice"] %}
                            {% if formation.isDpc() %}
                                et indemnisation{% if plural %}s{% endif %} DPC
                            {% else %}
                                ({% for financeMode in formation.financeModes %}{{ financeMode.name }}{% if loop.index != formation.financeModes|length %},{% endif %}{% endfor %})
                            {% endif %}
                        {% endif %}
                    </span><br>
                    {% if indemnisations[formation.id] is defined %}
                        {% for indemnisation in indemnisations[formation.id] %}
                            {% for speciality, value in indemnisation %}
                                {{ speciality }} {% if value and formation.isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endif %}
                {% endif %}
            </p>
            <p>
                <span class="title">PRISE(S) EN CHARGE POSSIBLE(S)**</span><br>
                {% for priseEnCharge in formation.programme.prisesEnCharge %}
                    {{ priseEnCharge.name }}{% if not loop.last %}, {% endif %}
                {% endfor %}
            </p>
        </div>
        <div class="absoluteElements">
            <div class="renseignementBloc">
                <span class="title underline">RENSEIGNEMENTS ET INSCRIPTIONS</span><br>
                <span class="title">CONTACTEZ VOTRE COORDINATEUR RÉGIONAL</span>
                {% if coordinateur.coordinatorBinome %}
                    <div style="display:flex; justify-content: center;gap: 10px; ">
                        <p class="text-center">
                            <img class="img-coordinateur" src="{% if coordinateur.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinateur.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" alt="" style="display: block;"/>
                            <span class="strong">{{ coordinateur.lastname }} {{ coordinateur.firstName }}</span><br>
                            <span class="strong">{{ coordinateur.phone }}</span><br>
                            {{ coordinateur.email }}
                        </p>
                        <p class="text-center">
                            <img class="img-coordinateur" src="{% if coordinateur.coordinatorBinome.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinateur.coordinatorBinome.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" alt="" style="display: block;"/>
                            <span class="strong">{{ coordinateur.coordinatorBinome.lastname }} {{ coordinateur.coordinatorBinome.firstName }}</span><br>
                            <span class="strong">{{ coordinateur.coordinatorBinome.phone }}</span><br>
                            {{ coordinateur.coordinatorBinome.email }}
                        </p>
                    </div>
                    <p class="text-center"> Responsables : {% for departement in coordinateur.mergedBinomeDepartments %}{{ departement }}{% if not loop.last %}, {% endif %}{% endfor %}</p>
                {% else %}
                    <p class="text-center">
                        <img class="img-coordinateur" src="{% if coordinateur.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinateur.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" alt="" style="display: block;"/>
                        <span class="strong">{{ coordinateur.lastname }} {{ coordinateur.firstName }}</span><br>
                        Responsable : {% for departement in coordinateur.departments %}{{ departement }}{% if not loop.last %}, {% endif %}{% endfor %}<br>
                        <span class="strong">{{ coordinateur.phone }}</span><br>
                        {{ coordinateur.email }}
                    </p>
                {% endif %}
            </div>
            <div class="asterisqueBloc" style="position: relative; z-index: 100;">
                * Si vous avez des questions sur l'accessibilité à la formation en cas de handicap, n'hésitez pas à nous contacter à l'adresse <EMAIL> ou 05&nbsp;56&nbsp;51&nbsp;65&nbsp;14.<br>
                ** Pour toute question à propos du coût de la formation, contacter :<br>
                <EMAIL> ou 05&nbsp;56&nbsp;51&nbsp;65&nbsp;14.
            </div>

            {% if logoPartenaire %}
                <img src="{{ asset(logoPartenaire.relativeUrl) }}" alt="{{ logoPartenaire.logoName }}" style="max-height: 110px;margin-top: 10px; position: absolute; z-index: 1; text-align: center;left: 50%;transform: translateX(-50%)">
            {% endif %}

            <div style="text-align: center; {% if logoPartenaire %}margin-top: 130px; {% else %}margin-top: 10px; {% endif %}position:relative; z-index: 50">
                <img src="{{ asset('img/comment-s-inscrire-qrcode.jpg') }}" width="150" alt="Comment s'inscrite QR CODE"><br>
                <p style="color: #ec702d; !important;margin-left: 3px;font-weight: 600;font-size: 20px;">Comment s'inscrire à <br>une formation DPC ?</p>
            </div>

            {#{% if formation.programme.additionalInfos %}
                <div class="lesaviezvousBloc text-center">
                    <span id="infoBulle">i</span>
                    {{ formation.programme.additionalInfos|raw }}
                    <div>
                        {% if formation.programme.firstAdditionalInfosPicture is not null %}
                            <a href="{{ formation.programme.firstAdditionalInfosPictureLink }}">
                                <img src="{{ asset('uploads/programme/picture/'~formation.programme.firstAdditionalInfosPicture) }}" alt="" width="100">
                            </a>
                        {% endif %}
                        {% if formation.programme.secondAdditionalInfosPicture is not null %}
                            <a href="{{ formation.programme.secondAdditionalInfosPictureLink }}">
                                <img src="{{ asset('uploads/programme/picture/'~formation.programme.secondAdditionalInfosPicture) }}" alt="" width="100">
                            </a>
                        {% endif %}
                        {% if formation.programme.isELearning() and formation.formateurs|length and formation.formateurs[0] %}
                            {% set formateurPerson = formation.formateurs[0].person %}
                            <div class="flex">
                                <img src="{% if formateurPerson.avatar is not null %}{{ asset('uploads/person/avatar/'~ formateurPerson.avatar) }}{% else %}{{ asset('img/eduprat-new-logo-web.png') }}{% endif %}" alt="" class="img-formater">
                                <p class="text-formater">
                                    <strong>{{ formateurPerson.civility }} {{ formateurPerson.fullname() }}</strong><br>
                                    {{ formateurPerson.job }} {% if formateurPerson.city %}à {{ formateurPerson.city }}{% endif %}
                                </p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}#}
        </div>
    </div>
</div>
<div class="flex flex-colone colonne3">
        <div class="header">
            <div class="flex75">
                <h1>{{ formation.programme.title }}</h1>
            </div>
{#            <div class="flex48 listPresence">#}
{#                <div class="presentielle {% if not formation.programme.isSurSite() %}notUse{% endif %}">Formation présentielle</div>#}
{#                <div class="classe-virtuelle {% if not formation.programme.isClasseVirtuelle() %}notUse{% endif %}">Formation classe virtuelle</div>#}
{#                <div class="elearning {% if not formation.programme.isElearning() %}notUse{% endif %}">Formation e-learning</div>#}
{#            </div>#}
        </div>
        <div class="mainContent">
            <h2>Pré-requis</h2>
            <p>{{ formation.programme.prerequis }}</p>
            <h2>Résumé - problématique</h2>
            <p>{{ formation.programme.resume }}</p>
            <h2>Objectifs pédagogiques</h2>
            <h3>Connaissances attendues</h3>
            <ul>
                {% for connaissance in formation.programme.connaissances %}
                    <li>{{ connaissance.name }}</li>
                {% endfor %}
            </ul>
            <h3>Compétences attendues</h3>
            <ul>
                {% for competence in formation.programme.competences %}
                    <li>{{ competence.name }}</li>
                {% endfor %}
            </ul>
            <h2>Déroulé pédagogique</h2>
            {% if formation.isTcs() %}
                 <img src="{{ asset('img/plaquette-etapes-invitation-tcs.jpg') }}" alt="" height="97" width="291" style="margin-left: 80px;">
            {% elseif formation.isVfc() %}
                <img src="{{ asset('img/plaquette-etapes-invitation-vfc.jpg') }}" alt="" height="97" width="291" style="margin-left: 80px;">
            {% else %}
                <img src="{{ asset('img/plaquette-etapes-invitation.jpg') }}" alt="">
            {% endif %}
            {% if formation.programme.additionalInfos %}
                <h2>Informations supplémentaires</h2>
                {{ formation.programme.additionalInfos|raw }}
            {% endif %}
        </div>
    </div>
</body>
</html>
