{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Roboto+Condensed:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>

    <style>
        body {
            padding: 0 0.5cm;
            font-size: 0.9em;
            font-family: "Roboto Condensed", sans-serif;
        }

        table thead th {
            text-align: center;
            background-color: #cccccc;
            padding: 0.25em 1em;
            border: 1px solid #000;
            font-weight: bold;
        }

        table {
            font-size: 0.9em;
            margin-top: 0;
            margin-bottom: 0.8em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 20px;
        }

        .pageWidth {
            position: relative;
        }

        .blue {
            color: #087dd0;
            font-size: 0.8em;
        }


        #footer-info {
            font-style: italic;
            line-height: 1em;
            font-size: 0.8em;
            /*position: absolute;
            bottom: 0;
            left: 0;*/
            padding-left: .9375rem;
            padding-right: .9375rem;
        }

        #footer-me {
            padding-top: 2rem;
        }

        .blue {
            color: #009398;
        }

        .orange {
            color: #ec702d;
        }

        .orange-dark {
            color: #C25714;
            font-weight: bold;
        }

        .grey {
            color: #737E8A;
        }

        .bg-grey {
            background-color: #f5f5f5;
        }

        .mts {
            margin-top: 5px;
        }

        .mt {
            margin-top: 10px;
        }

        .mtxl {
            margin-top: 30px;
        }

        .programme-title {
            font-weight: bold;
            font-size: 13px;
        }

        .hr {
            border:1px solid #ececec;
        }

        .mounth {
            color: white;
            font-size: 10px;
            background-color: #009398;
            padding: 5px 20px;
        }

        .column-plaquette {
            padding: 0rem;
        }

        .pls {
            padding-left: 0.9375rem;
        }

        .prs {
            padding-right: 0.9375rem;
        }

        table tbody td, table tbody th {
            padding: 0rem 0.625rem 0.625rem;
        }

        table tbody tr {
            border: 0px;
            border-bottom: 2px solid #ececec;
        }

        table tbody {
            border: 0px;
        }

        table tbody tr:nth-child(even) {
            background-color: white !important;
        }

        .sessionDates {
            font-size:12px;
        }

        .address {
            font-size:10px;
        }

        .commentSInscrireBloc {
            font-size: 10px;
            margin-bottom: 0;
        }
        .underline {
            text-decoration: underline;
        }
        .strong {
            font-weight: bold;
        }
    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div style="font-size:10px;">
            {% set mounth = null %}
            {# SUR SITE #}
            {% if sessions["Sur site"] is defined %}
                <div style="background-image: url({{  asset('/img/fd_eduprat_flou.jpg')}});background-repeat: no-repeat;background-position: top left;">
                    <br><br><br>
                    <table>
                        <tbody style="background-color: transparent;">
                            <tr style="border-bottom:0px;">
                                <td style="width:60%; padding-right:0; padding-left:0;">
                                    <div class="mtl" style="background-color:#009398; color:white; font-weight:bold; padding:0.5em 0 0.5em 2em; margin-top: 1em;">
                                        <h3 style="font-size:20px; font-weight:bold; margin-top:0.5em">PROGRAMME RÉGIONAL - PRÉSENTIEL</h3>
                                        <p>
                                            {% if coordinator.departments %} 
                                                {% for departement in coordinator.departments %}
                                                    {{ departement|upper }} {% if loop.index != coordinator.departments|length %}-{% endif %}
                                                {% endfor %}
                                            {% else %}
                                                &nbsp;
                                            {% endif %}
                                        </p>
                                    </div>
                                </td>
                                <td style="width:22%; text-align:center; padding-right:0; padding-left:0; padding-bottom:3em; border:0.5px solid #e7e7e7; background-color: white;">
                                    <img src="{% if coordinator.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinator.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" style="height:55px; border-radius: 30px; margin-top:-25px; background-color:white;"></img><br>
                                    <div class="mt" style="font-size: 9px;font-weight: 100;">
                                        VOTRE COORDINATEUR RÉGIONAL<br>
                                        <b>{{ coordinator.invertedFullName }}</b><br>
                                        <b>{{ coordinator.phone }}</b><br>
                                        {{ coordinator.email }}
                                    </div>
                                </td>
                                <td style="width:18%;padding-left: 2.5em;">
                                    <img src="{{ asset('img/eduprat-new-logo-web.png') }}" style="height:70px;"></img>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <table>
                    {% for session in sessions["Sur site"] %}
                        {# Entete #}
                        {% if session.startMounth() is defined and (session.startMounth() != mounth and session.programme.presence == "Sur site") %}
                            </table>
                            <table>
                            <div class="mounth mtxl"> {{ session.startMounth }} {% if ('now' | date('Y')) != (session.startDate | date('Y')) %}{{ session.startDate | date('Y') }}{% endif %} </div>
                            <div class="hr"></div>
                            {% set mounth = session.startMounth %}
                        {% endif %}
                        <tr>
                            <td style="width:42%; padding-top: 0.3125rem; padding-bottom: 0.3125rem;">
                                <div class="orange sessionDates">
                                    {% set dates = session.datesToString %}
                                    {% for sessionDate in dates %}
                                        <b>{{ sessionDate["date"] }}</b> | {{ sessionDate["hours"] }}
                                        {% if loop.index != dates|length %}
                                            <br>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="blue mts address">{{ session.address }} {{ session.address2 }} {{ session.zipCode }} {{ session.city }}</div>
                                <div class="former mts">
                                    {% for formateur in session.formateurs %} {% if formateur.person.civility == "M" %}Monsieur{% elseif formateur.person.civility == "Mme" %}Madame{% else %}{{ formateur.person.civility }}{% endif %} {{ formateur.person.invertedFullName }}{% if formateur.person.job %}, <font style="font-weight:100">{{ formateur.person.job }}</font>{% endif %}.{% if loop.index != session.formateurs|length %}<br>{% endif %}{% endfor %}
                                </div>
                            </td>
                            {% if session.programme.picture is not null %}<td style="width: 10%; background-image:url('{{ asset('uploads/programme/picture/'~ session.programme.picture)}}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td> {% else %} <td style="width: 10%; background-image:url('{{ asset('img/logo eduprat.png') }}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td>{% endif %}
                            <td class="bg-grey" style="width:48%">
                                <div class="programme-title mt"> {{ session.programme.title | upper  }}</div>
                                <div class="reference mts"><span class="grey"><b>Référence d’action :</b></span><span class="orange"> {{ session.programme.reference }}, Session {{ session.sessionNumber }}</span></div>
                                {% if indemnisations[session.id] is defined and session.programme.specialities %} 
                                    <div class="indemnisations mts"><span class="grey">
                                        {% set plural = indemnisations[session.id][0]|length > 1 %}
                                        <b>
                                            Public{% if plural %}s{% endif %} 
                                            {% if indemnisations[session.id]["thereIsAPrice"] %}
                                                {% if session.isDpc() %}
                                                    et indemnisation{% if plural %}s{% endif %} DPC
                                                {% else %}
                                                    ({% for financeMode in session.financeModes %}{{ financeMode.name }}{% if loop.index != session.financeModes|length %},{% endif %}{% endfor %})
                                                {% endif %}
                                            {% endif %}  :
                                        </b>
                                        {% for indemnisation in indemnisations[session.id] %}{% for speciality, value in indemnisation %} {{ speciality  }} {% if value and session.isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}{% endfor %}{% endfor %}
                                    </span></div>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </table>
                <table class="mtxl">
                    <tr style="border-bottom: none;">
                        <td style="width:0%;padding-left: 0.5em;">
                            <div style="background-color: #ec702d;color: white;font-weight: bold;font-size: 24px;text-align: center;width: 30px;border-radius: 35px;position: absolute; margin-top: -15px; padding: 1px;">i</div>
                        </td>
                        <td style="width:68%;">
                            <div style="background-color:#ededed;padding:2em;text-align:center; font-size:8px;">
                                Toutes les classes virtuelles Eduprat Formations se déroulent sur l’outil <b>Zoom</b></br>
                                Le jour de la formation vous recevrez les accès à votre salle ainsi qu’un tutoriel de connexion
                            </div>
                        </td>
                        <td style="width:16%;"><img src="{{ asset('img/qualiopi.png') }}" style="height:75px;"></img></td>
                        <td style="width:16%;"><img src="{{ asset('img/logo_andpc.jpg') }}" style="height:75px;"></img></td>
                    </tr>
                </table>
            {% endif %}

            {# CLASSE VIRTUELLE  #}
            {% if sessions["Classe virtuelle"] is defined %}
                {% if sessions["Sur site"] is defined %}
                    <div class="page-break"></div>
                {% endif %}
                <div style="background-image: url({{  asset('/img/fd_eduprat_flou.jpg')}});background-repeat: no-repeat;background-position: top left;">
                    <br><br><br>
                    <table>
                        <tbody style="background-color: transparent;">
                            <tr style="border-bottom:0px;">
                                <td style="width:60%; padding-right:0; padding-left:0;">
                                    <div class="mtl" style="background-color:#ec702d; color:white; font-weight:bold; padding:0.5em 0 0.5em 2em; margin-top: 1em;">
                                        <h3 style="font-size:20px; font-weight:bold; margin-top:0.5em">PROGRAMME CLASSES VIRTUELLES</h3>
                                        <p>
                                            {% if professions %}
                                                {% for profession in professions %}
                                                    {{ profession }} {% if loop.index != professions|length %} - {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                            {% if specialites %}
                                                {% if professions %} - {% endif %}
                                                {% for specialite in specialites %}
                                                    {{ specialite }} {% if loop.index != specialites|length %} - {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </p>
                                    </div>
                                </td>
                                <td style="width:22%; text-align:center; padding-right:0; padding-left:0; padding-bottom:3em; border:0.5px solid #e7e7e7; background-color: white;">
                                    <img src="{% if coordinator.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinator.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" style="height:55px; border-radius: 30px; margin-top:-25px; background-color:white;"></img><br>
                                    <div class="mt" style="font-size: 9px;font-weight: 100;">
                                        VOTRE COORDINATEUR RÉGIONAL<br>
                                        <b>{{ coordinator.invertedFullName }}</b><br>
                                        <b>{{ coordinator.phone }}</b><br>
                                        {{ coordinator.email }}
                                    </div>
                                </td>
                                <td style="width:18%;padding-left: 2.5em;">
                                    <img src="{{ asset('img/eduprat-new-logo-web.png') }}" style="height:70px;"></img>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mtxl"></div>
                <table>
                    {% for session in sessions["Classe virtuelle"] %}
                        <tr>
                            <td style="width:42%;">
                                <div class="orange sessionDates">
                                    {% set dates = session.datesToString %}
                                    {% for sessionDate in dates %}
                                        <b>{{ sessionDate["date"] }}</b> | {{ sessionDate["hours"] }}
                                        {% if loop.index != dates|length %}
                                            <br>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="blue mts address">{{ session.address }} {{ session.address2 }} {{ session.zipCode }} {{ session.city }}</div>
                                <div class="former mts">
                                    {% for formateur in session.formateurs %} {% if formateur.person.civility == "M" %}Monsieur{% elseif formateur.person.civility == "Mme" %}Madame{% else %}{{ formateur.person.civility }}{% endif %} {{ formateur.person.invertedFullName }}{% if formateur.person.job %}, <font style="font-weight:100">{{ formateur.person.job }}</font>{% endif %}.{% if loop.index != session.formateurs|length %}<br>{% endif %}{% endfor %}
                                </div>
                            </td>
                            {% if session.programme.picture is not null %}<td style="width: 10%; background-image:url('{{ asset('uploads/programme/picture/'~ session.programme.picture)}}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td> {% else %} <td style="width: 10%; background-image:url('{{ asset('img/logo eduprat.png') }}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td>{% endif %}
                            <td class="bg-grey" style="width:48%">
                                <div class="programme-title mt"> {{ session.programme.title | upper  }}</div>
                                <div class="reference mts"><span class="grey"><b>Référence d’action :</b></span><span class="orange"> {{ session.programme.reference }}, Session {{ session.sessionNumber }}</span></div>
                                {% if indemnisations[session.id] is defined and session.programme.specialities %} 
                                    <div class="indemnisations mts"><span class="grey">
                                        {% set plural = indemnisations[session.id][0]|length > 1 %}
                                        <b>
                                            Public{% if plural %}s{% endif %} 
                                            {% if indemnisations[session.id]["thereIsAPrice"] %}
                                                {% if session.isDpc() %}
                                                    et indemnisation{% if plural %}s{% endif %} DPC
                                                {% else %}
                                                    ({% for financeMode in session.financeModes %}{{ financeMode.name }}{% if loop.index != session.financeModes|length %},{% endif %}{% endfor %})
                                                {% endif %}
                                            {% endif %}  :
                                        </b>
                                        {% for indemnisation in indemnisations[session.id] %}{% for speciality, value in indemnisation %} {{ speciality }} {% if value and session.isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}{% endfor %}{% endfor %}
                                    </span></div>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </table>
                <table class="mtxl">
                    <tr style="border-bottom: none;padding-left: 0.5em;">
                        <td style="width:0%;">
                            <div style="background-color: #ec702d;color: white;font-weight: bold;font-size: 24px;text-align: center;width: 30px;border-radius: 35px;position: absolute; margin-top: -15px; padding: 1px;">i</div>
                        </td>
                        <td style="width:68%;">
                            <div style="background-color:#ededed;padding:2em;text-align:center; font-size:8px;">
                                Toutes les classes virtuelles Eduprat Formations se déroulent sur l’outil <b>Zoom</b></br>
                                Le jour de la formation vous recevrez les accès à votre salle ainsi qu’un tutoriel de connexion
                            </div>
                        </td>
                        <td style="width:16%;"><img src="{{ asset('img/qualiopi.png') }}" style="height:75px;"></img></td>
                        <td style="width:16%;"><img src="{{ asset('img/logo_andpc.jpg') }}" style="height:75px;"></img></td>
                    </tr>
                </table>
            {% endif %}

            {# E-LEARNING  #}
            {% if sessions["E-Learning"] is defined %}
                {% if sessions["Sur site"] is defined or sessions["Classe virtuelle"] is defined %}
                    <div class="page-break"></div>
                {% endif %}
                <div style="background-image: url({{  asset('/img/fd_eduprat_flou.jpg')}});background-repeat: no-repeat;background-position: top left;">
                    <br><br><br>
                    <table>
                        <tbody style="background-color: transparent;">
                            <tr style="border-bottom:0px;">
                                <td style="width:60%; padding-right:0; padding-left:0;">
                                    <div class="mtl" style="background-color:#2B6684; color:white; font-weight:bold; padding:0.5em 0 0.5em 2em; margin-top: 1em;">
                                        <h3 style="font-size:20px; font-weight:bold; margin-top:0.5em">PROGRAMME E-LEARNING</h3>
                                        <p>
                                            {% if professions %}
                                                {% for profession in professions %}
                                                    {{ profession }} {% if loop.index != professions|length %} - {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                            {% if specialites %}
                                                {% if professions %} - {% endif %}
                                                {% for specialite in specialites %}
                                                    {{ specialite }} {% if loop.index != specialites|length %} - {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </p>
                                    </div>
                                </td>
                                <td style="width:22%; text-align:center; padding-right:0; padding-left:0; padding-bottom:3em; border:0.5px solid #e7e7e7; background-color: white;">
                                    <img src="{% if coordinator.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinator.avatar) }}{% else %}{{ asset('img/logo eduprat.png') }}{% endif %}" style="height:55px; border-radius: 30px; margin-top:-25px; background-color:white;"></img><br>
                                    <div class="mt" style="font-size: 9px;font-weight: 100;">
                                        VOTRE COORDINATEUR RÉGIONAL<br>
                                        <b>{{ coordinator.invertedFullName }}</b><br>
                                        <b>{{ coordinator.phone }}</b><br>
                                        {{ coordinator.email }}
                                    </div>
                                </td>
                                <td style="width:18%;padding-left: 2.5em;">
                                    <img src="{{ asset('img/eduprat-new-logo-web.png') }}" style="height:70px;"></img>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mtxl"></div>
                <table>
                    {% for session in sessions["E-Learning"] %}
                        <tr>
                            {% if session.programme.picture is not null %}<td style="width: 10%; background-image:url('{{  asset('uploads/programme/picture/'~ session.programme.picture)}}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td> {% else %} <td style="width: 10%; background-image:url('{{ asset('img/logo eduprat.png') }}');background-size: cover;background-position: center;background-repeat: no-repeat;"></td>{% endif %}
                            <td style="width:62%;">
                                <div class="programme-title mt"> {{ session.programme.title | upper  }}</div>
                                <div class="reference mts"><span class="grey"><b>Référence d’action :</b></span><span class="orange"> {{ session.programme.reference }}</span></div>
                                {% if indemnisations[session.id] is defined and session.programme.specialities %} 
                                    <div class="indemnisations mts"><span class="grey">
                                        {% set plural = indemnisations[session.id][0]|length > 1 %}
                                        <b>
                                            Public{% if plural %}s{% endif %} 
                                            {% if indemnisations[session.id]["thereIsAPrice"] %}
                                                {% if session.isDpc() %}
                                                    et indemnisation{% if plural %}s{% endif %} DPC
                                                {% else %}
                                                    ({% for financeMode in session.financeModes %}{{ financeMode.name }}{% if loop.index != session.financeModes|length %},{% endif %}{% endfor %})
                                                {% endif %}
                                            {% endif %}  :
                                        </b>
                                        {% for indemnisation in indemnisations[session.id] %}{% for speciality, value in indemnisation %} {{ speciality }} {% if value %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}{% endfor %}{% endfor %}
                                    </span></div>
                                {% endif %}
                            </td>
                            <td class="bg-grey" style="width:28%; font-size:10px;">
                                <div class="orange-dark">
                                    FORMATION DE {{ session.programme.durationPresentielle + session.programme.durationNotPresentielle  }}H
                                </div>
                                {% if session.formateurs|length %}
                                    <div class="former mts">
                                        Expert{% if session.formateurs|length > 1 %}s{% endif %} : <br>
                                        {% for formateur in session.formateurs %} <b>{% if formateur.person.civility == "M" %}Monsieur{% elseif formateur.person.civility == "Mme" %}Madame{% else %}{{ formateur.person.civility }}{% endif %} {{ formateur.person.invertedFullName }}</b>{% if formateur.person.job %}, <font style="font-weight:100">{{ formateur.person.job }}</font>{% endif %}.{% if loop.index != session.formateurs|length %}<br>{% endif %}{% endfor %}
                                    </div>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </table>
                <table class="mtxl">
                    <tr style="border-bottom: none;padding-left: 0.5em;">
                        <td style="width:64%;"></td>
                        <td style="width:16%;"><img src="{{ asset('img/qualiopi.png') }}" style="height:75px;"></img></td>
                        <td style="width:16%;"><img src="{{ asset('img/logo_andpc.jpg') }}" style="height:75px;"></img></td>
                    </tr>
                </table>
            {% endif %}

            <div class="page-break"></div>

            <div style="background-image: url({{  asset('/img/fd_eduprat_flou.jpg')}});background-repeat: no-repeat;background-position: top left;">
                <br><br><br>
                <table>
                    <tbody style="background-color: transparent;">
                    <tr style="border-bottom:0px;">
                        <td style="width:60%; padding-right:0; padding-left:0;">
                            <div class="mtl" style="background-color:#009398; color:white; font-weight:bold; padding:0.5em 0 0.5em 2em; margin-top: 1em;">
                                <h3 style="font-size:20px; font-weight:bold; margin-top:0.5em">Comment s'inscrire ?</h3>
                            </div>
                        </td>
                        <td style="width:22%; text-align:center; padding-right:0; padding-left:0; padding-bottom:3em;"></td>
                        <td style="width:18%;padding-left: 2.5em;">
                            <img src="{{ asset('img/eduprat-new-logo-web.png') }}" style="height:70px;"></img>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <p class="commentSInscrireBloc">
                Rendez-vous sur <a href="https://www.agencedpc.fr">www.agencedpc.fr</a>, rubrique <strong>Professionnels de santé</strong> (en haut de l'écran).<br>
                Votre identification se fait sur la partie droite de l'écran :<br>
                <span class="underline"><strong>1- Vous n'avez pas encore de compte DPC</strong></span>
            </p>
            <ul class="commentSInscrireBloc">
                <li>Cliquez sur <strong> CRÉER UN COMPTE</strong> et saisissez vos informations personnelles puis laissez-vous guider.</li>
                <li>Pensez à vous munir d'un RIB au format PDF pour renseigner vos informations financières et ainsi bénéficier de la prise en charge de vos frais pédagogiques par l'ANDPC et d'une indemnisation</li>
            </ul>
            <p class="commentSInscrireBloc">
                <span class="underline"><strong>2- Vous avez déjà un compte DPC</strong></span>
            </p>
            <ul class="commentSInscrireBloc">
                <li>Saisissez votre identifiant et mot de passe puis cliquez sur  <strong> SE CONNECTER</strong>.</li>
                <li>Cliquez sur le menu <strong>Actions DPC</strong> puis sur <strong>Recherche une action / S'inscrire</strong>.
                    À gauche de l'écran, entrez la référence de l'action à 11 chiffres dans la case prévue à cet effet (ex: <strong>57202325059</strong>), puis cliquez sur <strong>Rechercher</strong>.
                    Après vérification cliquez sur le titre de la formation (en bleu), cliquez sur <strong>DÉTAIL</strong>, puis <strong>LISTE SESSIONS</strong> en haut de l'écran. Sélectionnez le numéro de session de votre choix, puis cliquer sur <strong> S'inscrire</strong> et valider.
                </li>
            </ul>
        </div>
    </div>
</div>
</body>
</html>