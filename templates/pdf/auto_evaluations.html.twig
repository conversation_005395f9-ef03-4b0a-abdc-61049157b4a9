<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/evaluation.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>
        body {
            padding: 0;
            width: 21cm;
            font-size: 14px;
        }

        #logo img {
            width: 120px;
        }

        #form-container {
            padding: 20px 45px;
        }

        h1 {
            font-size: 26px;
            margin-top: 0.5em;
            margin-bottom: 0;
            letter-spacing: inherit;
        }

        h2 {
            font-size: 20px;
        }

        h3 {
            font-size: 14px;
            margin-top: 1em;
            padding-top: 10px;
            font-weight: bold;
        }

        h4 {
            font-size: 14px;
            margin-left: 0.75em;
        }

        li {
            list-style-type: none;
            margin-left: 0.75em;
            line-height: 1.2em;
        }

        li::before {
            content: "●";
            color: #459494;
            margin-right: 4px;
            font-size: 8px;
        }

        #spiderweb-title {
            font-family: stimupratbold, sans-serif;
            font-size: 26px;
        }

        .group-padding {
            padding-top: 0.25cm;
        }

        #evaluation-info {
            margin: 0 0 1cm 0;
            font-size: 14px;
        }

        #evaluation-info .bold {
            font-size: 16px;
        }

        .text-group-small {
            font-size: 14px;
        }
        .section {
            padding-top: 1em;
            page-break-inside: avoid;
        }

        .section-title {
            margin-bottom: 1em;
            font-size: 1.3em;
            width: 100%;
            text-transform: uppercase;
            padding-top: 0.125em;
            font-family: stimupratbold, sans-serif;
            font-weight: 600;
        }
        .left-table {
            text-align: center;
            padding: 1.5em;
        }
        .b-blue {
            background-color: #109199;
            font-weight: bold;
            color: white;
        }
        .b-blue-header {
            padding: 10px;
            background-color: #109199;
            font-weight: bold;
            color: white;
            border-left: 1px solid black;
            border-right: 1px solid black;
            border-left: 1px solid black;
            border-top: 1px solid black;
            margin-top: -17px;
        }
        table,tr, .td-bordered {
            border: 1px solid black;
            font-size: 12px;
        }
        .mts {
            margin-top: 1em
        }
        .pt-reset {
            padding-top: 0em;
        }
        .mbxs {
            margin-bottom: 0.5em;
        }
        .anwser-count{
            font-size: 12px;
        }
        .clear-grey {
            color:#a6a6a6;
        }
        .grey {
            color:#737373;
        }
        .bold {
            font-family: inherit;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="bg-form reporting-container" id="form-container">
            <header>
                <div class="pull-left">
                    {# {% if participation is defined and participation is not null %}
                        <div>{{ "evaluation.header.periode" | trans }}</div>
                        <p class="bold">{{ participation.formation.startDate|date('d/m/Y') }}
                            - {{ participation.formation.endDate|date('d/m/Y') }}</p>
                    {% endif %} #}
                    <div>{{ "evaluation.header.periode" | trans }}</div>
                    <p class="bold" style="font-size:12px">{{ formation.openingDate|date('d/m/Y') }} - {{ formation.closingDate|date('d/m/Y') }}</p>
                    <p>N° DPC: <span class="blue">{{ formation.programme.reference }}</span><br>
                       N° de session: <span class="blue">{{ formation.sessionNumber }}</span>
                    </p>
                </div>
                <div class="pull-right">
                    <div id="logo">
                        <a href="{{ url('eduprat_audit_index') }}"><img src="{{ asset('img/eduprat-new-logo-web.png') }}" alt=""></a>
                    </div>
                </div>
            </header>
            <div class="clearfix"></div>
            <div>
                <hr class="mbm">
                    <h1 class="text-center">{% block box_title %}{{ "admin.formation.autoEval.title" | trans }}{% endblock box_title %}</h1>
                    <h2 class="text-center">{% block box_subtitle %}{{ formation.programme.title }}{% endblock box_subtitle %}</h2>
                <hr>
            </div>
            <div>
            <p class="text-right mbxs anwser-count">{{ datas.answeredPoeple }} personnes ont répondu à ce questionnaire</p>
            <div class="section pt-reset">
                <p class="section-title"><span class="blue">1.</span> {{ "admin.formation.autoEval.step1" | trans }}</p>
            </div>
            <p>Il s'agit du positionnement de l'ensemble des participants en amont de la formation, sur une échelle de 1 à 5.<br>
                1 étant une connaissance/compétence non acquise et 5 parfaitement maitrisée.</p>
            <div>
                {% block box_questions %}
                    {% if datas.connaissance.connaissances is defined %}
                        <h3>{{ "admin.formation.autoEval.expectedKnowledge" | trans }}
                            <span class="float-right mrs" style="width:31%"><span class="grey">Note moyenne :</span> {% if datas.connaissance.moyenneGlobale > 0 %}{{datas.connaissance.moyenneGlobale|round(2)}} / 5 {% else %} NA {% endif %} <span class="text-group-small"></span></span>
                        </h3>
                        <div class="row">
                            <div class="columns small-12">
                                {% for question,moyenne in datas.connaissance.connaissances  %}
                                    <li>{{question}} <span class="float-right mrs clear-grey">{% if moyenne > 0 %} {{moyenne|round(2)}} / 5 {% else %} NA {% endif %}</span></li>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    {% if datas.competence.competences is defined %}
                        <h3>{{ "admin.formation.autoEval.expectedSkills" | trans }}
                            <span class="float-right mrs" style="width:31%"><span class="grey">Note moyenne :</span> {% if datas.competence.moyenneGlobale > 0 %}{{datas.competence.moyenneGlobale|round(2)}} / 5 {% else %} NA {% endif %} <span class="text-group-small"></span></span>
                        </h3>
                        <div class="row">
                            <div class="columns small-12">
                                {% for question,moyenne in datas.competence.competences  %}
                                    <li>{{question}} <span class="float-right mrs clear-grey">{% if moyenne > 0 %} {{moyenne|round(2)}} / 5 {% else %} NA {% endif %}</span></li>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                {% endblock %}
            </div>


            {% if datas.attentes|length > 0%}
                <div class="page-break"></div>
                <div class="section mts">
                    <p class="section-title"><span class="blue">2.</span> {{ "admin.formation.autoEval.step2" | trans }}</p>
                </div>
                <table class="tableAutoEval mts">
                    {% for attente in datas.attentes %}
                        <tr>
                            <td class="left-table b-blue td-bordered" style="width: 20%;">Attente {{ loop.index }}</td>
                            <td class="td-bordered">{{ attente }}</td>
                        <tr>
                    {% endfor %}
                <table><br>
            {% endif %}

            {% if datas.casCliniques|length > 0%}
                <div class="page-break"></div>
                <div class="section mts">
                    <p class="section-title"><span class="blue">3.</span> {{ "admin.formation.autoEval.step3" | trans }}</p>
                </div> 
                {% for casClinique in datas.casCliniques %}
                    <h3>CAS CLINIQUE {{ loop.index }} / {{ datas.casCliniques|length }} SOUMIS PAR UN PARTICIPANT<span class="float-right mrs"></span></span>
                        </h3>
                    <table class="tableAutoEval">
                        <div class="b-blue-header">{{ "admin.formation.autoEval.casClinique" | trans }}</div>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Antécédents :</td>
                            <td class="td-bordered" >{{ casClinique.antecedents }}</td>
                        <tr>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Traitement en cours :</td>
                            <td class="td-bordered" >{{ casClinique.traitementsEnCours }}</td>
                        <tr>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Examen clinique :</td>
                            <td class="td-bordered" >{{ casClinique.examen }}</td>
                        <tr>
                    </table>
                    <table class="tableAutoEval">
                        <div class="b-blue-header">{{ "admin.formation.autoEval.iconographie" | trans }}</div>
                        {% for image in casClinique.images %}
                            <tr>
                                <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Image {{ loop.index }}</td>
                                <td class="td-bordered" style="text-align:center"><img src="{{ image.relativeUrl }}" width="250" alt=""></td>
                            <tr>
                        {% endfor %}
                    </table>
                    <table class="tableAutoEval">
                        <div class="b-blue-header">{{ "admin.formation.autoEval.problematique" | trans }}</div>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Traitement administré :</td>
                            <td class="td-bordered" >{{ casClinique.traitementAdministre }}</td>
                        <tr>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Evolution :</td>
                            <td class="td-bordered" >{{ casClinique.evolution }}</td>
                        <tr>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Problématique :</td>
                            <td class="td-bordered" >{{ casClinique.problematique }}</td>
                        <tr>
                        <tr>
                            <td class="td-bordered bold" style="width:32%; padding:2em 2em 2em 0.5em;">Questions :</td>
                            <td class="td-bordered" >{{ casClinique.questions }}</td>
                        <tr>
                    <table><br>
                    {% if loop.index != loop.length %} 
                        <div class="page-break"></div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        </div>
    </div>
</div>
</body>
</html>