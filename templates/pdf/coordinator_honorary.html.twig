<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff; 
        }

        table {
            font-size: 0.85em;
            width: 80%;
            margin: auto;
            page-break-inside: avoid;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }    

        .wrapleft {
            width: 80%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .wrapright {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
            text-align: right;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

        .details {
            margin: 5em 0 0 0;
        }
        .h1-title {
            font-weight: bold;
            margin-bottom: 30px;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row mtm mbm">
                <h1 class="text-center h1-title">Détail note d'honoraire {{ year }}</h1>
                <div class="column large-12">
                    <div><span class="bold">{{ "compensation.coordinator" | trans }}</span> : {{ coordinator.person.lastname }} {{ coordinator.person.firstname }}</div>
                </div>
                <div class="column large-12">
                    <div><span class="bold">{{ "compensation.sessionRef" | trans }}</span> : {{ formation.programme.reference }}</div>
                </div>
                <div class="column large-12">
                    {% if openingDate|date('d/m/Y') == closingDate|date('d/m/Y') %}
                        <div>
                            <span class="bold">{{ "compensation.date" | trans }}</span> : {{ openingDate | date("d/m/Y") }}
                        </div>
                    {% else %}
                        <div>
                            <span class="bold">{{ "compensation.date" | trans }}</span> : {{ openingDate | date("d/m/Y") }} - {{ closingDate | date("d/m/Y")  }}
                        </div>
                    {% endif %} 
                </div>
                {% if formation.city %}
                    <div class="column large-12">
                        <div><span class="bold">{{ "compensation.location" | trans }}</span> : {{ formation.city }}</div>
                    </div>
                {% endif %}
                <div class="column large-12">
                    <div><span class="bold">{{ "compensation.orateur" | trans }}</span> :

                    {% set formateurs = formation.formateurs %}
                    {% if formateurs|length %}
                        {% for formateur in formateurs %}
                            {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                        {% endfor %}
                    {% else %}
                        /
                    {% endif %}
                    </div>
                </div>
            </div>
            <div class="row mtm mbm">
                <div class="mtm column large-12 details">
                    <h4>{{ "budgetCRTotal.totalFormation" | trans }}</h4>
                    <table>
                        {% if not newHonorary %}
                            <thead>
                            <tr>
                                <th>{{ "budgetCRTotal.refFormation" | trans }}</th>
                                <th>{{ "budgetCRTotal.NameFormation" | trans }}</th>
                                <th>{{ "budgetCRTotal.budgetCR" | trans }}</th>
                            </tr>
                            </thead>
                        {% endif %}
                        <tbody>
                        {% if newHonorary %}
                            {% set honorary = n1 ? coordinator.honoraryN1 : coordinator.honorary %}
                            {% if honorary %}
                                <tr>
                                    <td class="wrapleft">{{ "budgetCRTotal.budgetCR" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">{{ honorary|number_format(2, '.', ' ') }} €</td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td class="wrapleft">{{ "budgetCRTotal.ca" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">{{ coordinator.caTotal(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <td class="wrapleft subcost">- {{ "budgetCRTotal.formationCost" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ coordinator.formationCost|number_format(2, '.', ' ') }} €</td>
                                </tr>

                                <tr>
                                    <td class="wrapleft bold">{{ "budgetCRTotal.commissionTheorique" | trans }}</td>
                                    <td class="wrapcenterspan bold"  colspan="2">{{ coordinator.commissionTheorique(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <td class="wrapleft subcost">- Avances formation :</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ coordinator.avancesCost|number_format(2, '.', ' ') }} €</td>
                                </tr>

                            {% endif %}
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.commissionsTtc" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ totalHonorary.total|number_format(2, '.', ' ') }} €</td>
                            </tr>
                        {% else %}
                            {% for key,formation in totalHonorary %}
                                {% if key != 'total' and key != 'totalTtc' and key != 'formateursSupplement' and key != 'newHonorary' %}
                                    <tr>
                                        <td class="wrapleft">{{ key }} </td>
                                        <td class="wrapcenter">{{ formation.title }} </td>
                                        <td class="wrapright">{{ formation.total }} €</td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                            <tr>
                                <td class="wrapleft">- {{ "budgetCRTotal.restaurationHonorary" | trans }}</td>
                                <td class="wrapcenterspan"  colspan="2">- {{ restaurationHonorary|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            {% if totalHonorary.formateursSupplement %}
                                <tr>
                                    <td class="wrapleft">- {{ "budgetCRTotal.formateursSupplement" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ totalHonorary.formateursSupplement|number_format(2, '.', ' ') }} €</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.total" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ totalHonorary.total|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.totalTtc" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ totalHonorary.totalTtc|number_format(2, '.', ' ') }} €</td>
                            </tr>
                        {% endif %}
                        </tbody>
                    </table>

                    {% if not newHonorary %}
                        <p class="info-italic">{{ "budgetCRTotal.info3"|trans }}</p>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
