{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1em;
        }

        h1 {
            font-size: 36px;
        }

        table thead th {
            text-align: center;
            background-color: #00949a;
            color: white;
            padding: 0.25em 1em;
            border: 2px solid white;
            font-weight: bold;
            font-size: 16px;
        }

        table thead tr.light-blue th {
            background-color: #8ad4c7;
            color: #083738;
            font-weight: 300;
        }

        table {
            font-size: 0.9em;
            margin-top: 0;
            margin-bottom: 0.8em;
        }

        table tbody td {
            border: 2px solid white;
        }

        table tbody tr:nth-child(even), table tbody tr:nth-child(odd) {
            background-color: #e3e3e3;
        }

        #signature {
            border: 2px solid #888;
            width: 100%;
            height: 150px;
            margin-top: 10px;
        }

        span.dots {
            color: white;
            font-size: 14px;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <hr class="mtn">
            <h1 class="text-center mbn mtn">Feuille d'émargement actalians</h1>
            <hr class="mtl" style="	border-width: 1px;">
            <div class="row">
                <div class="column small-12">

                    {% set programme = formation.programme %}
                    {% set former = formation.formateursPersons.first %}

                    <div>Intitulé de la formation : {{ programme.title }}</div>
                    <div>Date(s) de la formation : Du {{ formation.openingDate|date('d/m/Y') }} au {{ formation.closingDate|date('d/m/Y') }}, avec une réunion présentielle {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}le {{ formation.startDate | date('d/m/Y') }}{% else %}du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}{% endif %}</div>
                    {% set durationPresentielle = formation.programme.durationPresentielle is not null ? formation.programme.durationPresentielle : 0 %}
                    {% set durationNotPresentielle = formation.programme.durationNotPresentielle is not null ? formation.programme.durationNotPresentielle : 0 %}
                    {% set durationEtape3 = formation.programme.durationNotPresentielleActalians is not null and formation.programme.durationNotPresentielleActalians > 0 ? formation.programme.durationNotPresentielleActalians : 0 %}
                    <div>Durée de la formation en heures : {{ durationPresentielle + durationNotPresentielle + durationEtape3 }} heures, dont {{ durationPresentielle }} heures présentielles et {{ durationNotPresentielle + durationEtape3 }} heures non présentielles</div>
                    <div>Lieu de la formation : {{ formation.address }} {{ formation.address2 }} {{ formation.zipCode }} {{ formation.city }}</div>
                    <div>Nom et prénom du formateur : {{ former.lastname }} {{ former.firstname }}</div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="column small-6 float-right">
                    <div>Signature formateur :</div>
                    <div>Par ma signature, j’atteste avoir dispensé</div>
                    <div>la formation ci-dessus nommée.</div>
                    <div id="signature"></div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="column small-12">
                    <table>
                        <thead>
                        <tr>
                            <th ROWSPAN=2 style="width: 25%">NOM/PRÉNOM DES STAGIAIRES</th>
                            <th ROWSPAN=2 style="width: 25%">ENTREPRISE/<br>ÉTABLISSEMENT</th>
                            <th COLSPAN=2 style="width: 50%; height: 50px">SIGNATURES</th>
                        </tr>
                        <tr class="light-blue">
                            <th style="width: 25%">
                                Matin <br>
                                De <span class="dots">............</span> heures <br>
                                A <span class="dots">..............</span> heures
                            </th>
                            <th style="width: 25%">
                                Après-midi <br>
                                De <span class="dots">............</span> heures <br>
                                A <span class="dots">..............</span> heures
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participation in participations %}
                            <tr class="nbi">
                                <td>{{ participation.participant.lastname }} {{ participation.participant.firstname }}</td>
                                <td>{{ participation.participant.address }} {% if participation.participant.zipCode or participation.participant.city %} - {% endif %}{{ participation.participant.zipCode }} {{ participation.participant.city }}</td>
                                <td></td>
                                <td></td>
                            </tr>
                        {% endfor %}
                        {% for i in 1..5 %}
                            <tr class="nbi">
                                <td>&#8203;</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

{% macro dots(number) %}
    {%- for i in 1..number -%}.{%- endfor -%}
{% endmacro %}