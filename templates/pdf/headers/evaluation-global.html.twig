<!doctype html>
<html lang="fr">
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link rel="stylesheet" href="{{ asset('admin/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('admin/dist/css/AdminLTE.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/front.css') }}">
    <link rel="stylesheet" href="{{ asset('css/evaluation.css') }}">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link rel="stylesheet" href="{{ asset('css/form.css') }}">
    <style>
        #form-container {
            margin: 0;
            min-height: inherit;
            height: 3.6cm;
            padding: 1em 2em;
        }
        #form-container.bg-form {
            background-size: cover;
        }
        #logo img {
            width: 130px;
        }
        #text {
            font-size: 14px;
        }
    </style>
</head>
<body style="margin:0; padding:0;">
<div id="form-container" class="bg-form" style="width: 21cm; overflow: hidden">
    <div class="pull-left" id="text">
        {% if formation is defined and formation is not null %}
            <div>{{ "evaluation.header.periode" | trans }}</div>
            <p class="bold" style="white-space: nowrap">{{ formation.openingDate|date('d/m/Y') }} - {{ formation.closingDate|date('d/m/Y') }}</p>
            <div>N° DPC: <span class="bold blue">{{ formation.programme.reference }}</span></div>
            <div>Session: <span class="bold blue">{{ formation.sessionNumber }}</span></div>
        {% endif %}
    </div>
    <div class="pull-right">
        <div id="logo">
            <a href="{{ url('eduprat_audit_index') }}"><img src="{{ asset('img/eduprat-new-logo-web.png') }}" alt=""></a>
        </div>
    </div>
</div>
</body>
</html>