<!doctype html>
<html lang="fr">
<head>
    <style>
        .participant {
            float: right;
            line-height: 20px;
            padding-top: 2em;
            padding-bottom: 16px;
            height: 52px;
            text-align: right;
            display: none;
        }
        .red {
            color: red;
        }
        .participant[data-page='1'] {
            display: block;
        }
    </style>
    <script>
		function subst() {
			var vars={};
			var x=document.location.search.substring(1).split('&');
			for (var i in x) {var z=x[i].split('=',2);vars[z[0]] = unescape(z[1]);}
			var x=['page'];
			for (var i in x) {
				var y = document.getElementsByClassName(x[i]);
				for (var j=0; j<y.length; ++j) {
					console.log(vars);
					y[j].setAttribute('data-page', vars[x[i]]);
				}
			}
		}
    </script>
</head>
<body style="margin:0; padding:0;" onload="subst()">
    <img style="margin-top: 1em; margin-bottom: 1em" width="100" src="{{ absolute_url(asset('img/logo.png')) }}" alt="eduprat logo">
    {% if participation is not null %}
        <div class="participant page">
            {{ participation.participant.civility }} {{ participation.participant.fullname }}
            <br>
            <div class="red"><b>Temps d’analyse des 5 dossiers patients : 2 heures</b></div>
        </div>
    {% endif %}
</body>
</html>