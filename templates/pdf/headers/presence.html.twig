<!doctype html>
<html lang="fr">
<head>
    <style>
        .participant {
            float: right;
            line-height: 20px;
            padding-top: 2em;
            padding-bottom: 16px;
            height: 52px;
            text-align: right;
            display: none;
        }
        .red {
            color: red;
        }
        .participant[data-page='1'] {
            display: block;
        }
    </style>
    <script>
		function subst() {
			var vars={};
			var x=document.location.search.substring(1).split('&');
			for (var i in x) {var z=x[i].split('=',2);vars[z[0]] = unescape(z[1]);}
			var x=['page'];
			for (var i in x) {
				var y = document.getElementsByClassName(x[i]);
				for (var j=0; j<y.length; ++j) {
					console.log(vars);
					y[j].setAttribute('data-page', vars[x[i]]);
				}
			}
		}
    </script>
</head>
<body style="margin:0; padding:0; display:flex;max-width: 75rem; margin-left: auto; margin-right: auto;" onload="subst()">
    <div style="width:100%;">
        <img style="margin-top: 3em; margin-left:2em; margin-bottom: 1em; float:left" width="125" src="{{ absolute_url(asset('img/logo_fifpl.png')) }}" alt="eduprat logo">
    </div>
    <div style="width:100%; text-align:right">
        <div style="width:100px; height:100px"></div><br>
        <p  style="margin-right:2em; font-size:12px; color:#0098DB">Ce document est disponible sur le site <b><a style="color:#0098DB; text-decoration: none;" href="www.fifpl.fr">www.fifpl.fr</a></b></p>
    </div>
    <br>
</body>
</html>