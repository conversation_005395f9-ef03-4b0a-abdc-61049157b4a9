{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1em;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h4 {
            font-size: 16px;
        }

        p {
            margin-bottom: 0;
            line-height: 1.4;
        }
        label {
            /*font-size: 1em;*/
            display: inline;
            line-height: 1.5;
        }
        label.control-label {
            font-weight: bold;
            font-family: Arial, sans-serif;
        }
        .form-group {
            page-break-inside: avoid;
            margin: 2em 0;
        }
        textarea {
            font-size: 1em;
        }
        textarea[rows] {
            height: 5.5em;
        }
        input[type=radio] {
            margin-top: 0.25em;
            margin-bottom: 0.5em;
        }
        .radio input[type=radio]:after {
            display: inline-block;
            content: "";
            width: 18px;
            height: 18px;
            margin: 0 0.25em 0 0;
            background: #dbdfe1;
        }
        .radio input[type=radio]:checked:after {
            background: #333333;
            border: 4px solid #dbdfe1;
        }
        .radio label {
            font-family: Arial, sans-serif;
        }
        .form-group {
            margin: 1em 0;
        }

        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .checkbox label, label+span.required {
            color: #107D85;
        }

        label>[type=checkbox] {
            margin-right: .25rem;
        }

        textarea {
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            height: 150px;
            font-size: 18px;
        }
    </style>
</head>
<body>
{% for participation in participations %}
{% set participant = participation.participant %}
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <hr style="border-width: 3px;" class="mtn mbs">
            <h1 class="text-center mbn mtn">{{ participation.formation.programme.title }}</h1>
            <h1 class="text-center mbn mtn">{{ ("front.label." ~ participation.formation.displayType ~ ".singular") | trans }} {% if surveyId == 1 %}pré{% else %}post{% endif %}-formation</h1>
            <hr />
            <div id="form">
            <h4>{{ "admin.participation.survey.stagiaire" | trans }} : <strong>{{ participation.participant.fullname }}</strong></h4>
            <hr />
            <div><strong>{{ 'admin.participation.survey.answers'|trans }}</strong></div>
            <div class="answers">
            {% for question in participation.formation.questionnaire.questions %}
                <div class="answer nbi">
                <strong>{{ question.label }}</strong>
                {% if question.images|length > 0 %}
                    <div class="pdf-image-gallery">
                        {% for image in question.images %}
                            <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                        {% endfor %}
                    </div>
                {% endif %}
                <br />
                {% if participation.getSurveyAnswerByQuestion(question, surveyId) is not null %}
                    {% set surveyAnswer = participation.getSurveyAnswerByQuestion(question, surveyId) %}
                    {% if question.type == "choice" %}
                        {% for answer in question.choices %}
                            {% set isAnswer = answer.label in surveyAnswer %}
                            <div class="checkbox">
                                <label class=""><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ answer.label }}</label>
                            </div>
                        {% endfor %}
                    {% elseif question.type == "radio" %}
                        <div class="checkbox">
                            <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == 1 %}checked="checked"{% endif %}>Oui</label>
                        </div>
                        <div class="checkbox">
                            <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == 0 %}checked="checked"{% endif %}>Non</label>
                        </div>
                    {% else %}
                        {{ surveyAnswer }}
                    {% endif %}
                {% else %}
                    {{ "audit.choices.no_answer" | trans }}
                {% endif %}
                </div>
            {% endfor %}
            </div>
            <div class="page-break"></div>
        </div>
    </div>
</div>
{% endfor %}
</body>
</html>
