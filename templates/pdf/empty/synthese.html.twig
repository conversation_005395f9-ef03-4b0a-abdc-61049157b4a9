{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/front.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1em;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        p {
            margin-bottom: 0;
            line-height: 1.4;
        }
        label {
            /*font-size: 1em;*/
            display: inline;
            line-height: 1.5;
        }
        label.control-label {
            font-weight: bold;
            font-family: Arial, sans-serif;
            margin: 10px 0;
        }
        .form-group {
            page-break-inside: avoid;
            margin: 2em 0;
        }
        textarea {
            font-size: 1em;
        }
        textarea[rows] {
            height: 5.5em;
        }
        input[type=radio] {
            margin-top: 0.25em;
            margin-bottom: 0.5em;
        }
        .radio input[type=radio]:after {
            display: inline-block;
            content: "";
            width: 18px;
            height: 18px;
            margin: 0 0.25em 0 0;
            background: #dbdfe1;
        }
        .radio input[type=radio]:checked:after {
            background: #333333;
            border: 4px solid #dbdfe1;
        }
        .radio label {
            font-family: Arial, sans-serif;
        }
        .form-group {
            margin: 1em 0;
        }

        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .checkbox label, label+span.required {
            color: #107D85;
        }

        textarea {
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            height: 150px;
            font-size: 18px;
        }

        .text-big, .etutorat-rows-title, .control-label {
            font-size: 1em !important;
            margin: 10px 0;
        }

        .etutorat-notes {
            display: none !important;
        }

        .etutorat-row {
            flex-direction: column;
            align-items: flex-start;
            padding: 0 0 10px 5px;
        }

        .etutorat-row-label {
            margin: 15px 0 10px 0;
        }

        .etutorat-row-answers .radio-inline, .etutorat-row-answers .radio-inline+.radio-inline {
            margin-left: 0;
            padding-left: 0;
            margin-right: 32px;
            font-size: inherit;
        }

        .etutorat-row-answers .radio-inline input[type=radio] {
            display: inline-block;
            width: inherit;
            margin-right: 5px;
            margin-top: 0;
        }

        form[name=etutorat] {
            margin-top: 32px;
        }

        .etutorat-row-answers-synthese {
            display: flex !important;
            flex-direction: row !important;;
        }

    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <hr style="border-width: 3px;" class="mtn mbs">
            <h1 class="text-center mbn mtn">{{ participation.formation.programme.title }}</h1>
            <h2 class="text-center mbn mtn">{{ "front.course.steps.synthese"|trans }}</h2>

            <div class="responsive">
                {{ block("module_body", "audit/module/synthese.html.twig") }}
            </div>
        </div>
    </div>
</div>

</body>
</html>
