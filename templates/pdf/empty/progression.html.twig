{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/front.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1em;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        p {
            margin-bottom: 0;
            line-height: 1.4;
        }

        label {
            display: inline;
            line-height: 1.5;
        }

        textarea {
            font-size: 1em;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            border: 1px solid black !important;
            border-radius: 0!important;
            margin: 10px 0 30px 0;
        }

    </style>
</head>
<body onload="ready()">
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <hr style="border-width: 3px;" class="mtn mbs">
            <h1 class="text-center mbn mtn">{{ participation.formation.programme.title }}</h1>
            <h2 class="text-center mbn mtn">{{ "front.course.steps.progression"|trans }}</h2>

            {% for subForm in form.ficheProgressions %}
                <div class="action-row nbi">
                    <div>
                        <div class="mtm">
                            <strong class="text-bold">Cas clinique {{ subForm.vars.data.patient.patient }} :</strong>
                        </div>
                        <br>
                        {% if participation.id %}
                            <a target="_blank" href="{{ url("eduprat_front_formation_module", { id: participation.id, module: "restitution", _fragment: ("cas-patient-" ~ subForm.vars.data.patient.patient) }) }}">Revoir le cas patient {{ subForm.vars.data.patient.patient }}</a>
                        {% endif %}
                    </div>
                    <div data-th="">
                        <label>Points clés retenus / Points d'améliorations actés</label>
                        {{ form_widget(subForm.pointsCles, { attr: { placeholder: null }}) }}
                    </div>
                    <div>
                        <label for="">Points pouvant encore être améliorés après la formation</label>
                        {{ form_widget(subForm.ameliorations, { attr: { placeholder: null }}) }}
                    </div>
                    <div>
                        <label for="">Moyens pour y parvenir (ex : nouvelles formations, discussion avec des pairs, lecture d'articles ...)</label>
                        {{ form_widget(subForm.moyens, { attr: { placeholder: null }}) }}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

</body>
</html>
