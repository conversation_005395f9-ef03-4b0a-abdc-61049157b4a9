<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>

        body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h4 {
            font-size: 18px;
            font-weight: bold;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }

        #programme {
            padding: 1em;
            margin-top: 8em;
            text-align: right;
            float: right;
            min-width: 330px;
            font-family: stimupratlight;
        }

        #coordinateur-formateur {
            font-size: 1em;
        }

        .newFormateur {
            position: absolute;
            left: 9cm;
            top: 3cm;
            max-width: 15cm;
            font-size: 1em;
        }

        #contact {
            position: absolute;
            left: 4cm;
            top: 0cm;
            max-width: 15cm;
            font-size: 1em;
        }

        .title {
            text-align: center;
            font-size: 1.9em;
            /*margin-top: 0.25cm;*/
        }
        .littletitle {
            text-align: center;
            font-size: 1.25em;
        }
        .centered {
            text-align: center;
            margin-top: 0.5cm;
            /*margin-bottom: 1cm;*/
        }
        .infos {
            margin-top: 0.25cm;
            font-weight: bold;
        }
        .restitution-row {
            page-break-inside: avoid;
            padding-top: 1.8em;
        }

        .restitution-row::after {
            content: '';
            clear: both;
            display: block;
        }

        .interpretation {
            letter-spacing: -1px;
            font-family: stimupratbold, sans-serif;
            margin-bottom: 2em;
        }

        .interpretation p {
            margin: 0;
        }
        .comments > ul {
            list-style-position: inside;
        }
        #legend {
            height: 6cm;
            padding-top: 1cm;
        }
        #legend ul {
            margin-left: 0;
        }

        #legend li {
            margin-bottom: 0.5em;
        }

        #legend li > img {
            margin-right: 1em;
        }

        .interpretation, .interpretation span, .interpretation p {
            font-size: 1em !important;
            margin-bottom: 1em;
        }

        #detail-title {
            font-size: 1.2em;
        }

        #spiderweb {
            margin: 2em 0 0.2em 0;
        }

        .highcharts-root, .highcharts-container {
            overflow: visible !important;
        }
        .section {
            padding-top: 1em;
            page-break-inside: avoid;
        }

        .section-title {
            margin-bottom: 0;
            font-size: 1.75em;
            text-transform: uppercase;
            padding-top: 0.125em;
            font-family: stimupratbold, sans-serif;
            font-weight: 600;
        }

        .progress {
            font-size: 0;
            height: 20px;
            margin-bottom: 3px;
            background-color: transparent;
        }

        .progress-meter {
            display: inline-block;
        }

        .progress-text {
            font-size: 10px;
        }

        .progress-meter-success {
            background: linear-gradient(to right, #8b5e92, #6086b9);
            background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .progress-meter-error {
            background-color: #d71903;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }

        .restitution-question + p {
            margin-bottom: 0.5rem;
        }

        #barchart .bar {
            fill: #6c5d9f;
        }
        #barchart .axis path, #barchart .axis line {
            fill: none; stroke: #000; shape-rendering: crispEdges;
        }

        .page-break + .section {
            padding-top: 1cm;
        }

        .footer-fixed {
            position: absolute;
            top: 28.2cm;
            text-align: center;
            width: 100%;
            left:0;
            line-height: 1.3em;
            font-size: 0.8em;
        }


        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
        .bold {
            font-family: inherit;
        }

        .restitution-question.blue {
            font-size: 16px;
        }

        .restitution-question+p {
            font-size: 14px;
        }

        .answers label {
            font-size: 12px;
        }

        .answers-postulat {
            margin-top: 5px;
        }

        .answers-postulat p {
            margin-bottom: 0;
        }

        .restitution-patient {
            font-size: 16px;
            margin-top: 16px;
        }

        .restitution-patient-audit {
            font-size: 20px;
            margin-top: 16px;
        }

        .restitution-row-pad {
            padding-left: 16px;
        }

        .mtxl {
            margin-top:6em;
        }


        .mtl {
            margin-top:4em;
        }

        .mtm {
            margin-top:3em;
        }

        .mts {
            margin-top:2em;
        }

        .mll {
            margin-left:4em;
        }

        .margin-le {
            margin-left:20em;
        }

        
        .flex{
            display:flex;
            width: 100%;
        }

        .when, .where {
            font-weight: bold;
            font-style: italic;
            min-width: 20em;
        }

        .center {
            text-align: center;
        }

        {# .mbs {
            margin-bottom:2em;
        } #}

    </style>
</head>
<body>
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        {% for participant in participants %}
            <hr style="border-width: 2px;" class="mbs">
            <h1 class="text-center mbn">
                Attestation sur l'honneur
            </h1>
            <hr class="mts">
            {% include "pdf/section/header_attestation.html.twig" with {'participant': participant} %}
            <br>

            <h4 class="mtm">Objet : Attestation de participation sur l'honneur </h4>
            <p class="mts">Madame, Monsieur,</p>
            <div class="content mts">
                Je soussigné(e) {{ participant.civility }} {{ participant.getFullname }}, {{ participant.category }} demeurant au {% if participant.address %}{{ participant.address }}{% else %}<span style="display:inline-block;width: 10cm"></span>{% endif %} {% if participant.zipCode %}{{ participant.zipCode }}{% else %}<span style="display:inline-block;width: 1cm"></span>{% endif %} {% if participant.city %}{{ participant.city }}{% else %}<span style="display:inline-block;width: 3cm"></span>{% endif %},
                atteste sur l'honneur avoir participé à l’intégralité du programme de DPC {% if formation.programme.isFormatMixte %}mixte{% elseif formation.programme.isFormatElearning %}non présentiel{% endif %} n°{{ formation.programme.reference }} session {{ formation.sessionNumber }} du
                {{ formation.openingDate | date('d.m.Y') }} au {{ formation.closingDate | date('d.m.Y') }} dispensé par l’organisme Eduprat Formations et
                notamment aux {% if formation.programme.durationNotPresentielleTotal %} {{ formation.programme.durationNotPresentielleTotal }} {% else %} 0 {% endif %} heures en non connecté.
            </div>
            <p class="mts">
                J'ai pris connaissance des sanctions pénales encourues par l'auteur d'une fausse attestation. 
                Fait pour servir et valoir ce que de droit. 
            </p>
            <div class="mtm">
                <b><i>Fait à</i><i class="margin-le">, le</i></b>
            </div>
            <div class="mtm">
                Signature + cachet professionnel
            </div>
            <div class="mtxl">
                <b>Article 441-1 du code pénal:</b> <i>"Constitue un faux toute altération frauduleuse de la vérité, de
                 nature à causer un préjudice et accomplie par quelque moyen que ce soit, dans un écrit ou tout autre
                  support d'expression de la pensée qui a pour objet ou qui peut avoir pour effet d'établir la preuve
                   d'un droit ou d'un fait ayant des conséquences juridiques.</i>
                   <br>
                   <i>Le faux et l'usage de faux sont punis de trois ans d'emprisonnement et de 45 000 euros d'amende."</i>
            </div>
            {% if loop.index != participants|length %}
                <div class="page-break"></div>
            {% endif %}
        {% endfor %}
</div>
</body>
</html>