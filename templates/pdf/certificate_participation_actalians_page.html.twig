{% set formation = participation.formation %}
{% set programme = participation.formation.programme %}
{% set former = participation.formation.formateursPersons.first %}
<div class="fluid960 page pageCentered">
    <div class="content">
        <div>
            <hr class="mbl">
            <h1 class="text-center">Attestation individuelle de formation</h1>
            <hr class="mtl" style="	border-width: 1px;">
        </div>
        <div class="row">
            <div class="column small-12">
                <p>L’organisme de formation EDUPRAT Formations, déclaré auprès de la préfecture de la région Aquitaine sous le numéro
                    72330927633, atteste que {{ participant.lastname }} {{ participant.firstname }} a suivi l’action de formation suivante :</p>
                <div><span class="bold">Intitulé de la formation :</span> {{ programme.title }}</div>
                <div><span class="bold">Animée par</span> {{ former.lastname }} {{ former.firstname }}</div>
                <p><span class="bold">Nature de l’action :</span> Actions d’adaptation et de développement des compétences</p>
                <ul>
                    <li>action d’adaptation et de développement des compétences des salariés</li>
                    <li>action de promotion professionnelle</li>
                    <li>action d’acquisition, d’entretien ou de perfectionnement des connaissances</li>
                    <li>action de lutte contre l’illettrisme et l’apprentissage de la langue française</li>
                    <li>action relative à la radioprotection des personnes (L.1333-11 du code de la santé publique)</li>
                    <li>action de bilan de compétences</li>
                    <li>action de validation des acquis de l’expérience (VAE)...</li>
                </ul>

                <p><span class="bold">Objectifs :</span> {{ formation.programme.objectives | raw }}</p>
                <div><span class="bold">Date(s) :</span> {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}le {{ formation.startDate | date('d/m/Y') }}{% else %}du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}{% endif %}</div>
                <div><span class="bold">Lieu de la formation :</span> {{ formation.address }} {{ formation.address2 }} {{ formation.zipCode }} {{ formation.city }}</div>
                {% set durationPresentielle = formation.programme.durationPresentielle ? formation.programme.durationPresentielle : 0 %}
                {% set durationNotPresentielle = formation.programme.durationNotPresentielle ? formation.programme.durationNotPresentielle : 0 %}
               {% set durationEtape3 = formation.programme.durationNotPresentielleActalians is not null and formation.programme.durationNotPresentielleActalians > 0 ? formation.programme.durationNotPresentielleActalians : 0 %}
                <div><span class="bold">Durée :</span> {{ durationPresentielle + durationNotPresentielle + durationEtape3 }}  heures, dont {{ durationPresentielle }} heures présentielles et {{ durationNotPresentielle + durationEtape3 }} heures non présentielles</div>

                <br><br><br>

                <div>Fait à Mérignac, le {{ formation.closingDate | date('d/m/Y') }}</div>
                <div>Au nom de l'entreprise</div>

                <div class="mtl column small-12 text-right">
                    <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                </div>
            </div>
        </div>




    </div>
</div>
