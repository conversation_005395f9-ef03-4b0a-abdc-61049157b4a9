{% extends 'pdf/base-report.html.twig' %}

{% block box_questions %}
    {% for question in reporting.participantsAccueil %}
        {% if reporting.participantSummaryAccueil[question.identifier] is defined %}
            {% if question.title is defined %}
                <div class="nbi {% if not loop.first %}group-padding{% endif %}">
                <h3>Accompagnement des participants</h3>
                <div class="row">
                <div class="columns small-9">
                <h4>
                    <span class="blue">{{ "evaluation.questions"|trans }}</span> {{ ("evaluation.global." ~ question.title ~ ".label")|trans }}
                </h4>
            {% endif %}
            <li>{{ question.label|trans }}</li>
            {% if question.close is defined and question.close == true %}
                </div>
                <div class="columns small-3">
                    <div class="row">
                        <div class="evaluation-number blue columns small-6">
                            <span class="evaluation-number-number">{{ reporting.participantSummaryAccueil[question.identifier].total }}</span>
                            <span class="evaluation-number-label">Nombre de réponses</span>
                        </div>
                        <div class="evaluation-number dark columns small-6">
                            <span class="evaluation-number-number">{{ reporting.participantSummaryAccueil[question.identifier].avg }}</span>
                            <span class="evaluation-number-label">Note moyenne par question sur 5</span>
                        </div>
                    </div>
                </div>
                </div>
                </div>
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for question in reporting.participantsConformite %}
        {% if reporting.participantSummaryConformite[question.identifier] is defined %}
            {% if question.title is defined %}
                <div class="nbi {% if not loop.first %}group-padding{% endif %}">
                <h3>Conformité et adaptation des locaux - Participants</h3>
                <div class="row">
                <div class="columns small-9">
                <h4>
                    <span class="blue">{{ "evaluation.questions"|trans }}</span> {{ ("evaluation.global." ~ question.title ~ ".label")|trans }}
                </h4>
            {% endif %}
            <li>{{ question.label|trans }}</li>
            {% if question.close is defined and question.close == true %}
                </div>
                <div class="columns small-3">
                    <div class="row">
                        <div class="evaluation-number blue columns small-6">
                            <span class="evaluation-number-number">{{ reporting.participantSummaryConformite[question.identifier].total }}</span>
                            <span class="evaluation-number-label">Nombre de réponses</span>
                        </div>
                        <div class="evaluation-number dark columns small-6">
                            <span class="evaluation-number-number">{{ reporting.participantSummaryConformite[question.identifier].avg }}</span>
                            <span class="evaluation-number-label">Note moyenne par question sur 5</span>
                        </div>
                    </div>
                </div>
                </div>
                </div>
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for question in reporting.formersAccueil %}
        {% if reporting.formerSummaryAccueil[question.identifier] is defined %}
            {% if question.title is defined %}
                <div class="nbi {% if not loop.first %}group-padding{% endif %}">
                <h3>Accompagnement des formateurs</h3>
                <div class="row">
                <div class="columns small-9">
                <h4>
                    <span class="blue">{{ "evaluation.questions"|trans }}</span> {{ ("evaluation.global." ~ question.title ~ ".label")|trans }}
                </h4>
            {% endif %}
            <li>{{ question.label|trans }}</li>
            {% if question.close is defined and question.close == true %}
                </div>
                <div class="columns small-3">
                    <div class="row">
                        <div class="evaluation-number blue columns small-6">
                            <span class="evaluation-number-number">{{ reporting.formerSummaryAccueil[question.identifier].total }}</span>
                            <span class="evaluation-number-label">Nombre de réponses</span>
                        </div>
                        <div class="evaluation-number dark columns small-6">
                            <span class="evaluation-number-number">{{ reporting.formerSummaryAccueil[question.identifier].avg }}</span>
                            <span class="evaluation-number-label">Note moyenne par question sur 5</span>
                        </div>
                    </div>
                </div>
                </div>
                </div>
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for question in reporting.formersConformite %}
        {% if reporting.formerSummaryConformite[question.identifier] is defined %}
            {% if question.title is defined %}
                <div class="nbi {% if not loop.first %}group-padding{% endif %}">
                <h3>Conformité et adaptation des locaux - Formateurs</h3>
                <div class="row">
                <div class="columns small-9">
                <h4>
                    <span class="blue">{{ "evaluation.questions"|trans }}</span> {{ ("evaluation.global." ~ question.title ~ ".label")|trans }}
                </h4>
            {% endif %}
            <li>{{ question.label|trans }}</li>
            {% if question.close is defined and question.close == true %}
                </div>
                <div class="columns small-3">
                    <div class="row">
                        <div class="evaluation-number blue columns small-6">
                            <span class="evaluation-number-number">{{ reporting.formerSummaryConformite[question.identifier].total }}</span>
                            <span class="evaluation-number-label">Nombre de réponses</span>
                        </div>
                        <div class="evaluation-number dark columns small-6">
                            <span class="evaluation-number-number">{{ reporting.formerSummaryConformite[question.identifier].avg }}</span>
                            <span class="evaluation-number-label">Note moyenne par question sur 5</span>
                        </div>
                    </div>
                </div>
                </div>
                </div>
            {% endif %}
        {% endif %}
    {% endfor %}
{% endblock %}