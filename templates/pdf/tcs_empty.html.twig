<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/pdf/restitution_audit_groupe_individuelle.css')) }}" rel="stylesheet">
    <style>
    body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }
        .eduprat {
            color: #109199;
        }
        .flex {
            display: -webkit-box;
            display: flex;
        }
        .flex-colone {
            flex-direction: column;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
        }
        .colonne2 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 4cm;
            position: relative;
        }
        .colonne4 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 8cm;
            position: relative;
        }
        .colonne12 {
            display: -webkit-inline-box;
            display: inline-flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
            width: 20cm;
            height: -webkit-fit-content;
            height: fit-content;
        }
    </style>
</head>
<body>
{% set forPdf = true %}
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        <hr style="border-width: 5px;" class="mtn mbs">
        <h1 class="text-center mbn mtn">{{ formation.programme.title }}</h1>
        <h2 class="text-center mbn mtn">Test de concordance de script (TCS)</h2>
        <hr class="mts">
        <br>
        {% set questionGroups = questionnaireTcs.groupeQuestionsTCS %}
        {% set nbExpertCompletRepondant = questionnaireTcs.nbExpertCompletRepondant %}
        {% for questionGroup in questionGroups %}
            <div class="bold">
                <p style="margin-top:1rem">TCS n°{{ loop.index }}</p>
                <p style="background-color:#898a8d; color:white; padding:0.5rem"> {{ questionGroup.description|striptags|raw }} </p>
            </div>

            {% for question in questionGroup.questionsTCS %}
                <div class="mll mbl">
                    <span class="eduprat bold" style="font-size:14px;">QUESTION {{ loop.index }}</span>
                    <div class="tcs-rendu-question" style="margin-top:4px; font-size: 14px;"> Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }}</div>

                    <div class="tcs-results tcs-results-analyse">

                        <table class="checkbox-table">
                            <tbody>
                            <tr>
                                {# <td class="checkbox-header checkbox-header-left" style="width: 22%;">Réponses des {{ nbExpertCompletRepondant }} experts :</td> #}
                                <td class="checkbox-header checkbox-header-left" style="width: 35%;"></td>
                                <td style="width: 65%;"></td>
                            </tr>
                            {% for answer in question.reponses %}
                                <tr>
                                    {# <td class="checkbox checkbox-left">
                                        <label><input type="checkbox" disabled="disabled" {% if question.displayAnswerChecked(answer) %}checked="checked"{% endif %}> {% if question.displayAnswerChecked(answer) %}{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}{% else %} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {% endif %}</label>
                                    </td> #}
                                    <td class="checkbox checkbox-left">
                                        <label><input type="checkbox" disabled="disabled"></label>
                                    </td>
                                    <td>
                                        <label style="margin-left: -13rem;">Votre hypothèse ou option en est {{ answer.reponse }}</label>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endfor %}
            {% if loop.index < questionGroups|length %}
                <hr class="mtl mbl" style="min-width:95%">
            {% endif %}
        {% endfor %}
    </div>
</div>
</body>
</html>
