<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.9em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.9em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="col-lg-12">
                    {{ "attendance.header" | trans }}
                </div>
            </div>
            <div class="row mtm mbm">
                <div class="column small-offset-1 small-10">
                    <table>
                        <thead>
                        <tr>
                            <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                            <th>{{ "admin.participant.firstname" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participation in participations %}
                            <tr>
                                <td>{{ participation.participant.lastname | upper }}</td>
                                <td>{{ participation.participant.firstname }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="nbi">
            <div class="row mbm">
                <div class="col-lg-12">
                    {{ "attendance.info_1" | trans({"%ref%": formation.programme.reference, "%session%": formation.sessionNumber}) }}<br>
                    {{ "attendance.info_2" | trans }}
                </div>
            </div>
            <div class="row mbs mtl">
                <div class="col-lg-12">
                    {{ "attendance.addres" | trans({"%date%" : formation.closingDate | date('d.m.Y') }) }}
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    {{ "attendance.signature" | trans }}
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
