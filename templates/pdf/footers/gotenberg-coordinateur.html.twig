<!doctype html>
<html lang="fr">
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
{#    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">#}
{#    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">#}
    <style>
        html {
            font-size: 100%;
        }
        body {
            width: 21cm;
            background-color: #f2f2f2 !important;
            -webkit-print-color-adjust: exact;
            margin: 0;
            padding: 0;
            font-size: 12px;
            font-family: "Roboto", sans-serif;
            z-index: 0;
        }
        .footer {
            width: 21cm;
            font-size: 12px;
            height: 125px;
            color: white;
            background-color: #005672;
            -webkit-print-color-adjust: exact;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
            z-index: 2;
        }
        #footer-row-1 {
        }
        #footer-row-2 {
            font-size: 8px;
        }
        .imgParent {
            clear: both;
            display: inline-block;
            margin-top: -50px !important;
            margin-left: 235px !important;
        }
        .img-coordinateur {
            display: inline;
            margin: 5px auto;
            border-radius: 50%;
            background-color: white;
            -webkit-print-color-adjust: exact;
            object-fit: cover;
        }
        .titreFooter {
            text-decoration: underline;
            font-weight: bold;
            font-size: 16px;
            display: block;
        }
        .content {
            display: inline-block;
            min-width: 300px;
            font-size: 12px;
            max-width: 435px;
            padding: 35px 0 0;
            margin: 0 0 0 10px;
            vertical-align: top;
        }
        .prefooter {
            width: 21cm;
            background-color: #f2f2f2;
            -webkit-print-color-adjust: exact;
            height: 60px;
            bottom: 125px;
            display: block;
            position: absolute;
            z-index: 1;
        }
        .gauche {
            width: 16cm;
            background-color: #005672;
            -webkit-print-color-adjust: exact;
            padding-left: 5px;
        }
        .droiteParent {
            overflow: hidden;
            width: 480px;
        }
        .droite {
            -webkit-transform: rotate(-14deg);
            transform: rotate(-14deg);
            height: 200px;
            background-color: white;
            -webkit-print-color-adjust: exact;
            margin-top: -5em;
            margin-right: -10em;
            width: 100%;
            margin-left: 5em;
            padding: 3em;
        }
        .droiteContent {
            -webkit-transform: rotate(14deg);
            transform: rotate(14deg);
            padding-top: 45px;
            padding-left: 16px;
            display: flex;
        }
        #header, #footer, body { padding: 0 !important; margin: 0 !important; }
        .flex-binome {
            display: flex;
        }

        .flex-binome .img-coordinateur {
            margin:0px 10px;
        }

    </style>
</head>
<body style="padding-top: 50px !important;">
<div class="prefooter"></div>
<footer class="footer">
    <div class="gauche">
        <div class="imgParent {% if coordinateur.coordinatorBinome %}flex-binome {% endif %}" style="position:absolute;">
            {% if coordinateur.avatar is not null %}
                <img class="img-coordinateur" src="data:image/jpg;base64, {{ asset('uploads/person/avatar/'~ coordinateur.avatar)|base64_encode }}" alt="" style="display: block;" width="90" height="90" />
            {% else %}
                <img class="img-coordinateur" src="data:image/png;base64, {{ asset('img/eduprat-new-logo-web.png')|base64_encode }} }}" alt="" style="display: block;" width="90" height="90" />
            {% endif %}
            {% if coordinateur.coordinatorBinome %}
                 {% if coordinateur.coordinatorBinome.avatar is not null %}
                    <img class="img-coordinateur" src="data:image/jpg;base64, {{ asset('uploads/person/avatar/'~ coordinateur.coordinatorBinome.avatar)|base64_encode }}" alt="" style="display: block;" width="90" height="90" />
                {% else %}
                    <img class="img-coordinateur" src="data:image/png;base64, {{ asset('img/eduprat-new-logo-web.png')|base64_encode }} }}" alt="" style="display: block;" width="90" height="90" />
                {% endif %}
            {% endif %}
        </div>
        <p class="content">
            <span class="titreFooter">Informations et inscriptions :</span>
            {% if coordinateur.coordinatorBinome %}
                Responsables : {% for departement in coordinateur.mergedBinomeDepartments %}{{ departement }}{% if not loop.last %}, {% endif %}{% endfor %}<br>
                {{ coordinateur.firstName }} {{ coordinateur.lastname }}  - {{ coordinateur.phone }} - {{ coordinateur.email }}<br>
                {{ coordinateur.coordinatorBinome.firstName }} {{ coordinateur.coordinatorBinome.lastname }}  - {{ coordinateur.coordinatorBinome.phone }} - {{ coordinateur.coordinatorBinome.email }}<br>
            {% else %}
                {{ coordinateur.firstName }} {{ coordinateur.lastname }}  -
                Responsable {% for departement in coordinateur.departments %}{{ departement }}{% if not loop.last %}, {% endif %}{% endfor %}<br>
                {{ coordinateur.phone }} - {{ coordinateur.email }}<br>
            {% endif %}
        </p>
    </div>
    {%  if hasDPC %}
        <div class="droiteParent">
            <div class="droite">
                <div class="droiteContent">
                    <div style="margin-left: -32px;">
                        <img style="position: absolute; margin-top: -10px;" src="data:image/jpg;base64, {{ asset('img/comment-s-inscrire-qrcode.jpg')|base64_encode }}" width="75" alt="Comment s'inscrite QR CODE"><br>
                        <p style="position: absolute;color: #373737;margin-left: 3px;margin-top: 50px;font-weight: 600;font-size: 10px;">Comment s'inscrire à <br>une formation DPC ?</p>
                    </div>
                    <img style="margin-left:135px;margin-top: -5px;" src="data:image/png;base64, {{ asset('img/Logo-ODPC-svg.png')|base64_encode }}" width="120" alt="logo ODPC">
                </div>
            </div>
        </div>
    {%  endif %}
</footer>
</body>
</html>
