<!doctype html>
<html lang="fr">
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link rel="stylesheet" href="{{ absolute_url(asset('admin/bootstrap/css/bootstrap.min.css')) }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ absolute_url(asset('admin/dist/css/AdminLTE.min.css')) }}">
    <link rel="stylesheet" href="{{ absolute_url(asset('css/front.css')) }}">
    <link rel="stylesheet" href="{{ absolute_url(asset('css/evaluation.css')) }}">
    <link rel="stylesheet" href="{{ absolute_url(asset('font/stylesheet.css')) }}">
    <style>
        footer {
            font-size: 12px;
            border-top-width: 1px;
        }
        footer .bold {
            font-family: 'stimupratbold', sans-serif;
            font-size: 12px;
            font-weight: bold;
        }
        footer .blue {
            font-size: 10px;
        }
        footer img {
            margin-right: 10px;
            height: 30px;
        }

        #footer-row-1 {
            letter-spacing: -1px;
        }

        .inline {
            display: inline;
        }

        .footer-caret {
            font-family: stimupratlight, sans-serif;
            margin-right: 2px;
        }

        #footer-pagination {
            text-align: right;
            font-size: 12px;
            margin-bottom: 8px;
            padding-bottom: 3px;
            border-bottom: 1px solid #ccc;
        }

        .blue {
            color: #107D85;
        }

        #footer-page {
            line-height: 26px;
            font-size: 18px;
            font-weight: bold;
        }

        .footer-italic {
            font-size: 0.8em;
            font-style: italic;
            margin-top: 0;
            margin-bottom: 0;
        }

    </style>
{#    <script>#}
{#	    var offset = {% if offset is not null %}{{ offset }}{% else %}0{% endif %};#}
{#	    function subst() {#}
{#		    var vars={};#}
{#		    var x=document.location.search.substring(1).split('&');#}
{#		    for (var i in x) {var z=x[i].split('=',2);vars[z[0]] = unescape(z[1]);}#}
{#		    var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];#}
{#		    for (var i in x) {#}
{#			    var y = document.getElementsByClassName(x[i]);#}
{#			    for (var j=0; j<y.length; ++j) {#}
{#				    var value = vars[x[i]];#}
{#				    if (x[i] == "page" || x[i] == "topage") {#}
{#					    value = parseInt(value) + parseInt(offset);#}
{#				    }#}
{#				    if (x[i] == "page" || x[i] == "topage") {#}
{#				    	// if (!(parseInt(vars["page"]) === 1 && parseInt(vars["topage"]) === 1)) {#}
{#						    y[j].textContent = value;#}
{#					    // }#}
{#                    } else {#}
{#					    y[j].textContent = value;#}
{#                    }#}
{#			    }#}
{#		    }#}
{#	    }#}
{#    </script>#}
</head>
{% if height is not defined %}
    {% set height = "20mm" %}
{% endif %}
<body style="width: 21cm; height: {{ height }}; margin: 0 0.5cm; padding-right: 1cm; padding-left: 15px;" onload="subst()">
{% block prefooter %}{% endblock %}
<div id="footer-pagination">
</div>
<footer style="height: {{ height }}; overflow: hidden">
    <div class="pull-right">
        <span class="page pageNumber" id="footer-page"></span>
    </div>
    <div class="pull-left">
        <img src="{{ absolute_url(asset('img/eduprat_element_logo.png')) }}" alt="Logo Eduprat">
    </div>
    <div>
        {% block rows %}
            <div id="footer-row-1"><div class="bold inline">SAS EDUPRAT FORMATIONS</div> <span class="footer-caret">-</span> 4, Avenue Neil Armstrong <span class="footer-caret">-</span> Bâtiment Mermoz <span class="footer-caret">-</span> 33700 MERIGNAC <span class="footer-caret">-</span> <span class="bold">05 56 51 65 14</span> <span class="footer-caret">-</span> <EMAIL></div>
            <div id="footer-row-2" class="blue">SIRET : 79943599500029 - Déclaration d’activité enregistrée sous le numéro 72 33 09276 33 auprès du préfet de la région Aquitaine - Enregistrement ANDPC 5720</div>
        {% endblock %}
    </div>
</footer>
</body>
</html>
