<!doctype html>
<html lang="fr">
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet">
    <style>
        html {
            font-size: 62.5%;
        }
        body {
            width: 21cm;
            background-color: #f2f2f2;
            margin: 0;
            font-size: 12px;
            font-family: "Roboto", sans-serif;
        }
        .footer {
            width: 21cm;
            font-size: 12px;
            height: 200px;
            color: white;
            background-color: #005672;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
        }
        #footer-row-1 {
        }
        #footer-row-2 {
            font-size: 8px;
        }
        .imgParent {
            clear: both;
            display: inline-block;
            margin-top: -40px;
        }
        .img-coordinateur {
            display: inline;
            margin: 5px auto;
            border-radius: 50%;
            background-color: white;
        }
        .titreFooter {
            text-decoration: underline;
            font-weight: bold;
            font-size: 20px;
            display: block;
        }
        .content {
            margin-left: 20px;
            display: inline-block;
            min-width: 300px;
            font-size: 14px;
            max-width: 435px;
        }
        .prefooter {
            width: 100%;
            background-color: #f2f2f2;
            height: 50px;
            margin-bottom: -25px;
        }
        .gauche {
            width: 16cm;
            background-color: #005672;
            padding-left: 2rem;
        }
        .droiteParent {
            overflow: hidden;
            width: 240px;
        }
        .droite {
            -webkit-transform: rotate(-14deg);
            transform: rotate(-14deg);
            height: 300px;
            background-color: white;
            margin-top: -4em;
            margin-right: -10em;
            width: 100%;
            margin-left: 5em;
            padding: 3em;
        }
        .droiteContent {
            -webkit-transform: rotate(14deg);
            transform: rotate(14deg);
            padding-top: 4rem;
            padding-left: 1rem;
        }
    </style>
</head>
<body>
<div class="prefooter"></div>
<footer class="footer">
    <div class="gauche">
        <div class="imgParent">
            <img class="img-coordinateur" src="{% if coordinateur.avatar is not null %}{{ asset('uploads/person/avatar/'~ coordinateur.avatar) }}{% else %}{{ asset('img/eduprat-new-logo-web.png') }}{% endif %}" alt="" style="display: block;" width="90" height="90" />
        </div>
        <p class="content">
            <span class="titreFooter">Informations et inscriptions :</span>
            {{ coordinateur.firstName }} {{ coordinateur.lastname }}  -
            Responsable {% for departement in coordinateur.departments %}{{ departement }}{% if not loop.last %}, {% endif %}{% endfor %}<br>
            {{ coordinateur.phone }} - {{ coordinateur.email }}<br>
        </p>
        <div id="footer-row-1">Retrouvez toutes nos formations sur <span class="bold">www.eduprat.fr</span></div>
        <div id="footer-row-2">© Tous droits réservés Eduprat Formations - {{ "now"|date('Y') }}</div>
    </div>
    {%  if hasDPC %}
        <div class="droiteParent">
            <div class="droite">
                <div class="droiteContent">
                    <img src="{{ asset('img/Logo-ODPC-svg.png') }}" width="100" alt="logo ODPC">
                </div>
            </div>
        </div>
    {%  endif %}
</footer>
</body>
</html>
