<!doctype html>
<html lang="fr">
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <script>

        var offset = {% if offset is not null %}{{ offset }}{% else %}0{% endif %};
        function subst() {
            var vars={};
            var x=document.location.search.substring(1).split('&');
            for (var i in x) {var z=x[i].split('=',2);vars[z[0]] = unescape(z[1]);}
            var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
            for (var i in x) {
                var y = document.getElementsByClassName(x[i]);
                for (var j=0; j<y.length; ++j) {
                	var value = vars[x[i]];
                	if (x[i] == "page" || x[i] == "topage") {
                		value = parseInt(value) + parseInt(offset);
                    }
                	y[j].textContent = value;
				}
            }
        }
    </script>
</head>
<body style="margin:0; padding:0{% if padding is defined %} {{ padding }}em{% endif %}; margin-bottom: 1em; width: 960px;" onload="subst()">
<div style="text-align: center;">
    <div>SAS EDUPRAT FORMATIONS - 4, Avenue Neil Armstrong - Bâtiment Mermoz - 33700 MERIGNAC SIRET : 79943599500029</div>
    <div>Déclaration d’activité enregistrée sous le numéro 72 33 09276 33 auprès du préfet de la région Aquitaine</div>
    <div>Enregistrement ANDPC 5720</div>
</div>
<div style="text-align: right"><b>Page <span class="page"></span>{% if offset is not defined or offset is null %} sur <span class="topage">{% endif %}</span></b></div>
</body>
</html>