<script>

	var radarData = JSON.parse('{{ radarDataJson | escape("js") }}');
	var margin = {top: 80, right: 100, bottom: 80, left: 100};
	var width = 519;
	var height = 380;
	var maxValue = 100;
	var colors = ["#7253d2", "#1b9ba2", "#ff9a07"];
	var gradients = [["#c6b600", "#cc3f0b"], ["#005171", "#009098"], ["#74256b", "#3a619f"]];

	radarData = radarData.sort(function(a, b) {
		return a.group >= b.group;
	}).reduce(function (acc, data) {
		acc[2].push({axis: data.label, value: data.score_1});
		acc[1].push({axis: data.label, value: data.score_2});
		acc[0].push({axis: data.label, value: data.moy_2});
		return acc;
	}, [[], [], []]);

	var color = d3.scale.ordinal().range(colors);
	var auditLabel = "{{ ('front.label.formType.' ~ formation.formTypePost ~ '.singular') | trans }}";
	var auditId1 = "pré";
	var auditId2 = "post";

	var radarChartOptions = {
		w: width,
		h: height,
		margin: margin,
		maxValue: maxValue,
		levels: 4,
		roundStrokes: true,
		labelFactor: 1.1,
		lineBackground: true,
		dotRadius: 0,
		wrapWidth: 150,
		color: color,
		gradients: gradients,
		legends: ["Votre score " + auditLabel + " " + auditId1, "Votre score " + auditLabel + " " + auditId2, "Score du groupe " + auditLabel + " " + auditId2],
		rotationFactor: radarData[0].length === 6 ? 0.5 : 0,
		actalians: {{ participation.financeSousMode.isActalians ? 1 : 0 }}
	};

	RadarChart("#spiderweb", radarData, radarChartOptions);

</script>



