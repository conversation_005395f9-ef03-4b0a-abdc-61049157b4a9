<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ asset('admin/dist/css/v4-shims.min.css') }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/pdf/restitution_audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/pdf/restitution_audit_groupe_individuelle.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h5 {
            font-size: 18px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }

    </style>
</head>
<body>
{% set formation = participation.formation %}
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        <hr style="border-width: 2px;" class="mtn mbs">
        <h1 class="text-center mbn mtn">
            {% if formation.isVignette() %}{{ "restitution.scoring"|trans }}{% else %}{{ "restitution.restitution"|trans }}{% endif %} - {% if formation.isFormVignetteAudit %}{{ ("front.label.formType." ~ formation.formTypePost ~ ".singular") | trans }} post{% else %}{{ ("front.label." ~ formation.displayType ~ ".restitution") | trans }}{% endif %}
        </h1>
        <h2 class="text-center mbn">
            {% if formation.formTypePost == "survey" %}
                {{ formation.questionnaire.label }}
            {% else %}
                {% if formation.isFormVignetteAudit %}
                    {{ formation.audit2.label }}
                {% else %}
                    {{ formation.audit.label }}
                {% endif %}
            {% endif %}
        </h2>
        <hr class="mts">
            <div class="relative text-center">
                <h3><img width="18px" src="{{ asset('img/arrow.png') }}" alt="">{{ "audit.notice.step.4" | trans | raw }}</h3>
            </div>
        <br>
        {% include "pdf/section/header.html.twig" %}
        <div class="clearfix"></div>
        <hr class="mts">
        {% set summaryIndex = 0 %}
        {% if formation.isFormPredefined or formation.isFormPostVignette %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score global individuel</p>
                <br>
                <p style="font-size: 18px">Vous avez <span class="bold">{{ globalScores[2]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePost ~ ".plural") | trans }} post formation.</p>
                {% if not formation.isFormVignette %}<p style="font-size: 16px">En pré session, vous aviez <span class="bold">{{ globalScores[1]|number_format(2, '.', ' ') }}%</span>.</p>{% endif %}
            </div>
        {% endif %}
        <div class="page-break"></div>

        <div class="section">
            {% set summaryIndex = summaryIndex + 1 %}
            <p class="section-title" style="margin-bottom: 0;"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;{% if formation.isFormVignetteAudit %}Score moyen par thème{% else %}Analyse globale des {{ ("front.label." ~ formation.displayType ~ ".plural") | trans }}{% endif %}</p>
            {% if radarData|length %}
                <div id="spiderweb"></div>
            {% else %}
                <p class="text-center">Aucune réponse enregistrée</p>
            {% endif %}
        </div>
        <div class="page-break"></div>
        {% set summaryIndex = summaryIndex + 1 %}
        {% set show1 = formation.isFormVignetteAudit ? false : true %}
        {% include "pdf/section/indicators.html.twig" with { 'showAvg1': false , 'show1' : show1} %}
        {% if formation.isFormVignetteAudit %}
            {% if formation.isFormPostVignette %}
                <div class="page-break"></div>
                <div class="section">
                    {% set summaryIndex = summaryIndex + 1 %}
                    {% include "pdf/section/analysePost.html.twig" %}
                </div>
            {% endif %}
        {% else %}
            <div class="page-break"></div>
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/analysePost.html.twig" %}
            </div>
        {% endif %}
        {% if formation.isFormVignetteAudit and formation.isFormPostDefault %}
            <div class="page-break"></div>
            <div class="section">
            {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/criteresPost.html.twig" with { auditId: 2 } %}
            </div>
        {% endif %}
        <div class="page-break"></div>
        {% if formation.isFormAudit and formation.isDefaultType or formation.isFormVignette %}
            {% set summaryIndex = summaryIndex + 1 %}
            {% include "pdf/section/axesPost.html.twig" with { auditId: 2 }%}
        {% endif %}
        {% if ( not formation.isFormVignetteAudit and not formation.isFormPostDefault) or (formation.isFormVignetteAudit and formation.isFormPostVignette) %}
            {% include "pdf/section/contact.html.twig" with { auditId: 2 } %}
        {% endif %}
    </div>
</div>
<script type="text/javascript" src="{{ absolute_url(asset('admin/plugins/jQuery/jquery-2.2.3.min.js')) }}"></script>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
<script src="{{ absolute_url(asset('/js/radarChart.js')) }}"></script>
<script src="{{ absolute_url(asset('/js/radial-progress-chart.js')) }}"></script>
{% include "pdf/script/postrestitution.html.twig" %}
</body>
</html>