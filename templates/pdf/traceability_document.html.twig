<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.85em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }

        ul {
            list-style-position: inside;
        }        

        .wrap {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .text-box1 {
            height: 570px;
            border: 1px solid black;
        }

        .text-box1.text-box1-ppt {
            height: 400px;
            border: 1px solid black;
        }

        .text-box1.text-box1-zoom {
            height: 280px;
            border: 1px solid black;
        }

        .text-box2 {
            height: 160px;
            border: 1px solid black;
        }

        .text-box2.text-box2-zoom {
            height: 280px;
        }

        .text-box-header {
            background-color: #d9e2f3;
            height: 20px;
            line-height: 20px;
        }

        .text-box-text1 {
            padding: 1em;
        }

        .text-box-text2 {
            padding: 2em .9375rem 1em .9375rem;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="column large-12">
                    <div><span class="bold">{{ "traceability.organization_name" | trans }}</span> : {{ "traceability.organization_name_value" | trans }}</div>
                    <div><span class="bold">{{ "traceability.id" | trans }}</span> : {{ "traceability.id_value" | trans }}</div>
                </div>
            </div>

            <div class="row mtm mbm">
                <div class="column small-4">
                    <div><span class="bold">{{ "traceability.nb_action_prog" | trans }}</span> : {{ formation.programme.reference }}</div>
                </div>
                <div class="column small-4">
                    <div><span class="bold">{{ "traceability.nb_session" | trans }}</span> : {{ formation.sessionNumber }}</div>
                </div>
                <div class="column small-4">
                    <div><span class="bold">{{ "traceability.vol_hour" | trans }}</span> : {% if isZoom %}{{ formation.programme.durationTotal }}h{% else %}{{ formation.programme.durationNotPresentielle }}h{% endif %}</div>
                </div>
            </div>

            <div class="row mtm mbm text-box1 {% if formation.isPowerpoint %}text-box1-ppt{% elseif isZoom %}text-box1-zoom{% endif %}">
                <div class="text-box-header">
                    <div class="column small-6 text-box-header">
                        <div><span class="bold">{{ "traceability.desc_activities_not_connected" | trans }}</span> :</div>
                    </div>
                    <div class="column small-6 text-box-header">
                        <div><span class="bold">{{ "traceability.vol_hour" | trans }}</span> : {{ formation.programme.durationNotPresentielle ? formation.programme.durationNotPresentielle : 0 }}h</div>
                    </div>
                </div>
                <div class="text-box-text1">
                    {% if isZoom %}
                        <span class="bold">{{ "traceability.zoom.desc_activities_not_connected_value1" | trans }}</span><br>
                        {{ "traceability.zoom.desc_activities_not_connected_value3" | trans }}<br>
                        <br>
                        <span class="bold">{{ "traceability.zoom.desc_activities_not_connected_value4" | trans }}</span><br>
                        {{ "traceability.zoom.desc_activities_not_connected_value6" | trans }}<br>
                        <br>
                        <span class="bold">{{ "traceability.zoom.desc_activities_not_connected_value7" | trans }}</span><br>
                        {{ "traceability.zoom.desc_activities_not_connected_value9" | trans }}<br>
                    {% elseif formation.isAudit and (formation.isPredefined or formation.isVignette) %}
                            <span class="bold">{{ "traceability.predefined.desc_activities_not_connected_value1" | trans }}</span><br>
                            {{ "traceability.predefined.desc_activities_not_connected_value2" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value3" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value4" | trans({"%type%": ('front.label.' ~ formation.displayType ~ '.plural') | trans}) }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value5" | trans }}<br>
                            <div class="column small-12">
                                <ul>
                                    <li>{{ "traceability.predefined.desc_activities_not_connected_value6" | trans }}</li>
                                </ul>
                            </div>
                            <span class="bold">{{ "traceability.predefined.desc_activities_not_connected_value7" | trans }}</span><br>
                            {{ "traceability.predefined.desc_activities_not_connected_value8" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value3" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value9" | trans({"%type%": ('front.label.' ~ formation.displayType ~ '.plural') | trans}) }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value5" | trans }}<br>
                            <div class="column small-12">
                                <ul>
                                    <li>{{ "traceability.predefined.desc_activities_not_connected_value11" | trans }}</li>
                                </ul>
                            </div>
                            <span class="bold">{{ "traceability.predefined.desc_activities_not_connected_value12" | trans }}</span><br>
                            {{ "traceability.predefined.desc_activities_not_connected_value13" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value14" | trans }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value15" | trans({"%type%": ('front.label.' ~ formation.displayType ~ '.plural') | trans}) }}<br>
                            {{ "traceability.predefined.desc_activities_not_connected_value16" | trans }}<br>
                    {% elseif formation.isPowerpoint %}
                        <span class="bold">{{ "traceability.powerpoint.desc_activities_not_connected_value1" | trans }}</span><br>
                        {{ "traceability.powerpoint.desc_activities_not_connected_value2" | trans }}<br><br>
                        <span class="bold">{{ "traceability.powerpoint.desc_activities_not_connected_value3" | trans }}</span><br>
                        {{ "traceability.powerpoint.desc_activities_not_connected_value4" | trans }}<br><br>
                        <span class="bold">{{ "traceability.powerpoint.desc_activities_not_connected_value5" | trans }}</span><br>
                        {{ "traceability.powerpoint.desc_activities_not_connected_value6" | trans }}<br><br>
                    {% elseif formation.isElearning %}
                        <span class="bold">{{ "traceability.elearning.desc_activities_not_connected_value1" | trans }}</span><br>
                        {{ "traceability.elearning.desc_activities_not_connected_value2" | trans }}<br><br>
                        <span class="bold">{{ "traceability.elearning.desc_activities_not_connected_value3" | trans }}</span><br>
                        {{ "traceability.elearning.desc_activities_not_connected_value4" | trans }}<br><br>
                        <span class="bold">{{ "traceability.elearning.desc_activities_not_connected_value5" | trans }}</span><br>
                        {{ "traceability.elearning.desc_activities_not_connected_value6" | trans }}<br><br>
                    {% else %}
                        <p>
                            <span class="bold">{{ "traceability.desc_activities_not_connected_value1" | trans }}</span><br>
                            <span class="bold">{{ "traceability.desc_activities_not_connected_value2a" | trans }}</span><br>
                            {{ "traceability.desc_activities_not_connected_value2" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value3" | trans }}<br>
                        </p>
                        <div class="row mtm mbm">
                            <div class="column small-6">
                                <ul>
                                    <li>{{ "traceability.desc_activities_not_connected_value4.1" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value4.2" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value4.3" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value4.4" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value4.5" | trans }}</li>
                                </ul>
                            </div>
                            <div class="column small-6">
                                <ul>
                                    <li>{{ "traceability.desc_activities_not_connected_value5.1" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value5.2" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value5.3" | trans }}</li>
                                    <li>{{ "traceability.desc_activities_not_connected_value5.4" | trans }}</li>
                                </ul>
                            </div>
                        </div>
                        <p>
                            <span class="bold">{{ "traceability.desc_activities_not_connected_value6" | trans }}</span><br>
                            {{ "traceability.desc_activities_not_connected_value7" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value8" | trans }}<br>
                        </p>
                        <p>
                            <span class="bold">{{ "traceability.desc_activities_not_connected_value9" | trans }}</span><br>
                            {{ "traceability.desc_activities_not_connected_value10" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value11" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value12" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value13" | trans }}<br>
                            {{ "traceability.desc_activities_not_connected_value14" | trans }}<br>
                        </p>
                    {% endif %}
                </div>
            </div>

            {% if not formation.isPowerpoint and not isZoom %}
                <div class="page"></div>
            {% endif %}

            <div class="row mtm mbm text-box2 {% if isZoom %}text-box2-zoom{% endif %}">
                <div class="text-box-header">
                    <div class="column small-6">
                        <div><span class="bold">{{ "traceability.desc_activities_connected" | trans }}</span> :</div>
                    </div>
                    <div class="column small-6">
                        <div>
                            <span class="bold">{{ "traceability.vol_hour" | trans }}</span> : {% if isZoom %}{{ formation.programme.durationPresentielle ? formation.programme.durationPresentielle : 0 }}{% else %}{{ "traceability.vol_hour_value2" | trans }}{% endif %}
                        </div>
                    </div>
                </div>
                {% if not formation.programme.isClasseVirtuelle %}
                    <div class="column small-12">
                        {% if isZoom %}<br>{% endif %}
                        {% if formation.programme.isClasseVirtuelle %}
                            <span class="bold"> Date de classe virtuelle</span> : {{ formation.startDate|date('d/m/Y') }}<br>
                            <span class="bold"> Heure de début</span> : {{ formation.startDate|date('H') }}h{{ formation.startDate|date('i') }}<br>
                            {% if formation.startDate|date('d/m/Y') != formation.endDate|date('d/m/Y') %}
                                <span class="bold"> Date de fin de classe virtuelle</span> : {{ formation.endDate|date('d/m/Y') }}<br>
                            {% endif %}
                            <span class="bold"> Heure de fin</span> : {{ formation.endDate|date('H') }}h{{ formation.endDate|date('i') }}<br>
                        {% endif %}
                        <span class="bold">{{ "traceability.adressUrl" | trans }}</span> : {% if isZoom and formation.zoomLink is defined %}{{ formation.zoomLink }}{% else %}{{ "traceability.url" | trans }}{% endif %}
                    </div>
                    {% if isZoom %}
                        <div class="text-box-text2">
                            <span class="bold">{{ "traceability.zoom.desc_activities_connected_value1" | trans }}</span><br>
                            {{ "traceability.zoom.desc_activities_connected_value3" | trans }}<br>
                            {{ "traceability.zoom.desc_activities_connected_value4" | trans }}<br>
                            {{ "traceability.zoom.desc_activities_connected_value5" | trans }}
                        </div>
                    {% elseif formation.isAudit and (formation.isPredefined or formation.isVignette) %}
                        <div class="text-box-text2">{{ "traceability.desc_activities_connected_value_predefined" | trans({"%type%": ('front.label.' ~ formation.displayType ~ '.plural') | trans}) }}</div>
                    {% elseif formation.isElearning %}
                        <div class="text-box-text2">{{ "traceability.desc_activities_connected_value_elearning" | trans }}</div>
                    {% elseif formation.isPowerpoint %}
                        <div class="text-box-text2">{{ "traceability.desc_activities_connected_value_powerpoint" | trans }}</div>
                    {% else %}
                        <div class="text-box-text2">{{ "traceability.desc_activities_connected_value" | trans }}</div>
                    {% endif %}
                {% endif %}
            </div>

            {% if formation.isPowerpoint %}
                <div class="page"></div>
            {% endif %}

            <div class="row mtm mbm">
            </div>

            <div class="row mtm mbm">
                <div class="column large-12">
                    <table>
                        <thead>
                        <tr>
                            <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                            <th>{{ "admin.participant.firstname" | trans }}</th>
                            <th>{{ "traceability.rpps_adeli" | trans }}</th>
                            <th>{{ "traceability.activity_done" | trans }}</th>
                            {% if not isZoom %}
                                <th>{{ "traceability.ip_adress" | trans }}</th>
                            {% endif %}
                            <th>{{ "traceability.first_connection" | trans }}</th>
                            <th>{{ "traceability.last_connection" | trans }}</th>
                            <th>{{ "traceability.total_time" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participation in participations %}
                            <tr>
                                <td class="wrap">{{ participation.participant.lastname | upper }}</td>
                                <td class="wrap">{{ participation.participant.firstname }}</td>
                                {% if participation.participant.rpps == '' %}
                                    <td class="center">{{ participation.participant.adeli }}</td>
                                {% else %}
                                    <td class="center">{{ participation.participant.rpps }}</td>
                                {% endif %}
                                <td class="center">{{ "traceability.yes" | trans | upper }}</td>
                                {% if not isZoom %}
                                    <td class="center">{% if not isEmpty %}{{ participation.ip }}{% endif %}</td>
                                {% endif %}
                                {% if isZoom %}
                                    {% if participation.totalTimeZoom %}
                                        {% if participation.startedAtZoom and not isEmpty %}
                                            <td class="center">{{ participation.startedAtZoom|date('d/m/Y') }}&nbsp{{ participation.startedAtZoom|date('H\\hi') }}</td>
                                        {% else %}
                                            <td></td>
                                        {% endif %}
                                        {% if participation.finishedAtZoom and not isEmpty %}
                                            <td class="center">{{ participation.finishedAtZoom|date('d/m/Y') }}&nbsp{{ participation.finishedAtZoom|date('H\\hi') }}</td>
                                        {% else %}
                                            <td></td>
                                        {% endif %}
                                        <td class="center">{% if not isEmpty %}{{ participation.totalTimeZoom|date('H\\hi', false) }}{% endif %}</td>
                                    {% else %}
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    {% endif %}
                                {% else %}
                                    {% if participation.startedAtLogs is null or isEmpty %}
                                        <td class="center"></td>
                                    {% else %}
                                        <td class="center">{{ participation.startedAtLogs|date('d/m/Y') }} {{ participation.startedAtLogs|date('H\\hi') }}</td>
                                    {% endif %}
                                    {% if participation.finishedAtLogs is null or isEmpty %}
                                        <td class="center"></td>
                                    {% else %}
                                        <td class="center">{{ participation.finishedAtLogs|date('d/m/Y') }} {{ participation.finishedAtLogs|date('H\\hi') }}</td>
                                    {% endif %}
                                    {% if participation.totalTimeText and not isEmpty %}
                                        <td class="center">{{ participation.totalTimeText }}</td>
                                    {% else %}
                                        <td></td>
                                    {% endif %}
                                {% endif %}
                            </tr>
                        {% endfor %}
                            {# <tr>
                                <td colspan="7">{{ "traceability.average_participation_time" | trans }}</td>
                                <td class="center">{{ averageParticipationTime }}</td>
                            </tr> #}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row mbm mtm">              
            </div>

            <div class="nbi">
                <div class="row mbm">
                    <div class="column large-12">
                        {{ "traceability.text_1" | trans({"%ref%": formation.programme.reference, "%session%": formation.sessionNumber}) }}<br>
                        {{ "traceability.text_2" | trans }}
                    </div>
                </div>
                <div class="row">
                    <div class="column small-6">
                        {{ "traceability.address" | trans({"%date%" : formation.closingDate | date('d.m.Y') }) }}
                    </div>
                    <div class="column small-6">
                        {{ "traceability.signature" | trans }} : <br>
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
