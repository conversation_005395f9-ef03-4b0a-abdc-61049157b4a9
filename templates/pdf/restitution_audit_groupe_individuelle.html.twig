<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/pdf/restitution_audit_groupe_individuelle.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }
        .eduprat {
            color: #109199;
        }
        .flex {
            display: -webkit-box;
            display: flex;
        }
        .flex-colone {
            flex-direction: column;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
        }
        .colonne2 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 4cm;
            position: relative;
        }
        .colonne4 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 8cm;
            position: relative;
        }
        .colonne12 {
            display: -webkit-inline-box;
            display: inline-flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
            width: 20cm;
            height: -webkit-fit-content;
            height: fit-content;
        }
    </style>
</head>
<body>
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        <hr style="border-width: 2px;" class="mtn mbs">
        <h1 class="text-center mbn mtn">
            {% if formation.isTcs %}
                RESTITUTION TEST DE CONOCRDANCE DE SCRIPT (TCS)
            {% else %}
                {% if formation.isVignette() %}{{ "front.scoring"|trans }}{% else %}Pré-restitution{% endif %} -
                {{ ("front.label.formType." ~ formation.formTypePre ~ ".singular") | trans }} pré
            {% endif %}
        </h1>
        <h2 class="text-center mbn">
            {% if formation.formTypePre == "survey" %}
                {{ formation.questionnaire.label }}
            {% else %}
                {{ formation.audit.label }}
            {% endif %}
        </h2>
        <hr class="mts">
        {% include "pdf/section/header.html.twig" %}
        <br>
        {% set summaryIndex = 0 %}
        {% if (formation.isFormPredefined or formation.isFormPreVignette or formation.isTcs) and not formation.isVfc %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score global individuel</p>
                <br>
                {% if formation.isTcs %}
                    <p style="font-size: 18px">Votre score global de concordance est de <span class="bold">{{ tcsMoyenne }}%</span> par rapport au panel d'expert</p>
                {% else %}
                    <p style="font-size: 18px">Vous avez <span class="bold">{{ globalScores[1]|number_format(2, '.', ' ') }}%</span> de bonnes réponses sur l’ensemble des {{ ("front.label.formType." ~ formation.formTypePre ~ ".plural") | trans }} pré formation.</p>
                {% endif %}
            </div>
        {% endif %}
        <div class="page-break"></div>
        {% if not formation.isVfc %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;{% if formation.isTCS %}Score de concordance global{% else %}Score moyen par thème{% endif %}</p>
                <div id="spiderweb"></div>
            </div>
            <div class="page-break"></div>
            {% set summaryIndex = summaryIndex + 1 %}
            {% include "pdf/section/indicators.html.twig" with {'show2': false, 'showAvg2': false} %}
            <div class="page-break"></div>
        {% endif %}
        {% if formation.isFormPreVignette %}
            <div class="section">
                {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/analyse.html.twig" with { auditId: 1 } %}
            </div>
        {% endif %}
        {% if formation.isTcs %}
            <div class="section">
               {% set summaryIndex = summaryIndex + 1 %}
               {% include "pdf/section/analyseTcs.html.twig" with { auditId: 1, forPdf: true} %}
            </div>
        {% endif %}
        {% if formation.isFormPreDefault %}
            {% set summaryIndex = summaryIndex + 1 %}
            {% include "pdf/section/criteres.html.twig" %}
        {% endif %}
        {% if (formation.isFormPreDefault or formation.isFormPreVignette) and not formation.isVfc %}
            <div class="page-break"></div>
            {% set summaryIndex = summaryIndex + 1 %}
            {% include "pdf/section/axes.html.twig" with { auditId: 1 } %}
        {% endif %}
        {% if formation.isTcs %}
                <div class="page-break"></div>
                {% set summaryIndex = summaryIndex + 1 %}
                {% include "pdf/section/axesTcs.html.twig" with { auditId: 1, forPdf: true} %}
        {% endif %}
        {% if formation.isFormPreVignette or formation.isTcs %}
            {% include "pdf/section/contact.html.twig" with { auditId: 1, title: "contact" }%}
        {% endif %}
    </div>
</div>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
<script src="{{ asset('js/radarChart.js') }}"></script>
{% include "pdf/script/prerestitution.html.twig" %}
</body>
</html>
