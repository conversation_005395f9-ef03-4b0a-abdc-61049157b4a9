{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1.25em;
        }

        h3 {
            font-size: 1.125em;
        }

        p {
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }
        table {
            font-size: 1em;
            margin-top: 0.75em;
        }
        table tbody tr:nth-child(even) {
            background-color: inherit;
        }
        table tbody {
            background-color: inherit;
            border: none;
            border-top: 1px solid #f1f1f1;
        }
        table tbody tr {
            border-bottom: 1px solid #f1f1f1;
        }
        .desc-patient {
            padding-left: 0.625rem;
        }
        .radio input[type="radio"] {
            margin: 0px 4px 4px 0px;
        }
        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .checkbox label, label+span.required {
            color: #107D85;
        }

        label>[type=checkbox] {
            margin-right: .25rem;
        }
    </style>
</head>
<body>
{% set audit = formation.isVignette and auditId == 2 ? formation.audit2 : formation.audit %}
{% for participation in participations %}
    {% set participant = participation.participant %}
    {% if audit.nbPatients %}
        {% for patient in 1..audit.nbPatients  %}
    <div class="fluid960 page pageCentered">
        <div class="content">
            <div class="pageWidth">
            {% if patient == 1 %}
                <hr style="border-width: 2px;" class="mtn mbs">
                <h1 class="text-center mbn mtn">{{ participation.formation.programme.title }}</h1>
                <h1 class="text-center mbn mtn">{{ ("front.label." ~ participation.formation.displayType ~ ".singular") | trans }} {% if auditId == 1 %}pré{% else %}post{% endif %}-formation</h1>
                <hr />
            {% endif %}
                <h4>{{ "admin.participation.audit.stagiaire" | trans }} : <strong>{{ participation.participant.fullname }}</strong></h4>
                <hr />
                <h5>{{ "audit.patient" | trans }} n°<strong>{{ patient }} / {{ audit.nbPatients }}</strong></h5>
                <hr />
                <div><strong>{{ "admin.participation.audit.description" | trans }} :</strong></div>
                <div class="desc-patient">
                    {% if audit.isDefaultType %}
                        {{ participation.getDescriptionByPatient(auditId, patient) }}
                    {% else %}
                        {% set description = audit.getDescriptionByPatient(patient) %}
                        {{ description.description }}
                        {% if description.images|length > 0 %}
                            <div class="pdf-image-gallery">
                                {% for image in description.images %}
                                    <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                                {% endfor %}
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
                <br />
                <div><strong>{{ 'admin.participation.audit.answers'|trans }}</strong></div>
                <table>
                    <tbody>
                    {% set questions = audit.isDefaultType ? audit.questions : audit.surveyQuestions %}
                    {% for question in questions %}
                        {% if audit.isDefaultType or question.patient == patient %}
                            {% if not audit.isDefaultType %}
                                <div class="answer nbi">
                                    <strong>{{ question.label }}</strong>
                                    {% if question.images|length > 0 %}
                                        <div class="pdf-image-gallery">
                                            {% for image in question.images %}
                                                <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <br />
                                    {% set surveyAnswer = participation.getAuditAnswerByQuestionForSurvey(question, auditId, patient) %}
                                    {% if surveyAnswer is not null %}
                                        {% if question.type == "choice" %}
                                            {% for answer in question.choices %}
                                                {% set isAnswer = answer.label in surveyAnswer %}
                                                <div class="checkbox">
                                                    <label class=""><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ answer.label }}</label>
                                                </div>
                                            {% endfor %}
                                            {% set isAnswer = "ne_saispas" in surveyAnswer %}
                                            <div class="checkbox">
                                                <label class=""><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>Je ne sais pas</label>
                                            </div>
                                        {% elseif question.type == "radio" %}
                                            <div class="checkbox">
                                                <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == 1 %}checked="checked"{% endif %}>Oui</label>
                                            </div>
                                            <div class="checkbox">
                                                <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == 0 %}checked="checked"{% endif %}>Non</label>
                                            </div>
                                            <div class="checkbox">
                                                <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == 2 %}checked="checked"{% endif %}>Non applicable</label>
                                            </div>
                                            <div class="checkbox">
                                                <label class=""><input type="checkbox" disabled="disabled" {% if surveyAnswer == "ne_saispas" %}checked="checked"{% endif %}>Je ne sais pas</label>
                                            </div>
                                        {% else %}
                                            {{ surveyAnswer }}
                                        {% endif %}
                                    {% else %}
                                        {{ "audit.choices.no_answer" | trans }}
                                    {% endif %}
                                </div>
                            {% else %}
                                <tr>
                                    <td width="80%" valign="top">
                                        <div>{{ question.label }}</div>
                                        {% if question.images|length > 0 %}
                                            <div class="pdf-image-gallery">
                                                {% for image in question.images %}
                                                    <img class="pdf-image" src="{{ image.relativeUrl }}" alt="">
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </td>

                                    <td width="20%" valign="top"><b>
                                            {% if participation.getAuditAnswerByQuestion(question, auditId, patient) is not null %}
                                                {{ ("audit.choices." ~ participation.getAuditAnswerByQuestion(question, auditId, patient)) | trans }}
                                            {% else %}
                                                {{ "audit.choices.no_answer" | trans }}
                                            {% endif %}
                                        </b></td>
                                </tr>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                    </tbody>
                </table>
                <div class="page-break"></div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endif %}
{% endfor %}
</body>
</html>
