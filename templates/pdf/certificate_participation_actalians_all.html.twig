<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <style>

        body {
            padding: 0 45px;
            width: 21cm;
            font-size: 0.9em;
        }

        h1 {
            font-size: 30px;
            margin-bottom: 0;
        }

        h2 {
            font-size: 22px;
            font-weight: 600;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.9em;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 10px;
        }

        #title {
            font-size: 1.2em;
        }

        .form-part {
            background-color: #eeece1;
            text-align: center;
            width:100%;
        }

        .bold {
            font-family: inherit;
        }

        ul {
            list-style-position: inside;
        }

    </style>
</head>
<body>
{% for participation in participations %}
    {% set participant = participation.participant %}
    {% set formation = participation.formation %}
    {% set programme = participation.formation.programme %}
    {% set former = participation.formation.formateursPersons.first %}
    <div class="fluid960 page pageCentered">
        <div class="content">
            <div>
                <hr class="mbl">
                <h1 class="text-center">Attestation de présence</h1>
                <hr class="mtl" style="	border-width: 1px;">
            </div>
            <div class="row">
                <div class="column small-12">
                    <p>Je soussigné M Philippe PLAS, agissant en ma qualité de Gérant de l’organisme Eduprat Formations, organisme déclaré
                        auprès de la Préfecture de la région Aquitaine sous le numéro 72330927633, certifie que :</p>
                    <p>{{ participant.civility }} {{ participant.lastname }} {{ participant.firstname }}, salarié(e) de l’entreprise ou de l’établissement {{ participation.participant.address }} {% if participation.participant.zipCode or participation.participant.city %} - {% endif %}{{ participation.participant.zipCode }} {{ participation.participant.city }} a régulièrement suivi l’action de formation suivante : {{ programme.title }}</p>
                    <div><span class="bold">Date de la formation :</span> {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}le {{ formation.startDate | date('d/m/Y') }}{% else %}du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}{% endif %}</div>
                    <div><span class="bold">Lieu de réalisation de la formation :</span> {{ formation.address }} {{ formation.address2 }} {{ formation.zipCode }} {{ formation.city }}</div>
                    {% set durationPresentielle = formation.programme.durationPresentielle is not null ? formation.programme.durationPresentielle : 0 %}
                    {% set durationNotPresentielle = formation.programme.durationNotPresentielle is not null ? formation.programme.durationNotPresentielle : 0 %}
                    {% set durationEtape3 = formation.programme.durationNotPresentielleActalians is not null and formation.programme.durationNotPresentielleActalians > 0 ? formation.programme.durationNotPresentielleActalians : 0 %}
                    <div><span class="bold">Durée :</span> {{ durationPresentielle + durationNotPresentielle + durationEtape3 }}  heures, dont {{ durationPresentielle }} heures présentielles et {{ durationNotPresentielle + durationEtape3 }} heures non présentielles</div>

                    <br><br><br>

                    <div>Fait à Mérignac, le {{ formation.closingDate | date('d/m/Y') }}</div>
                    <br><br><br>

                    <div class="mtl column small-12 text-right">
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
</body>
</html>
