{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href='https://fonts.googleapis.com/css?family=Roboto:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>
    <style>
        html {
            font-size: 62.5%;
        }
        body {
            /*padding: 0 45px;*/
            width: 21cm;
            /*margin: 0 auto;*/
            /*width: 1190px;*/
            font-size: 12px;
            font-family: "Roboto", sans-serif;
            background-color: #f2f2f2;
        }

        label {
            font-weight: bold;
            color: #109199;
            text-transform: uppercase;
            font-size: 1.2em;
        }

        p {
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }
        ul {
            list-style-position: inside;
        }

        .underline {
            text-decoration: underline;
        }

        .header {
            display: -webkit-box;
            display: flex;
            position: relative;
            width: 100%;
            overflow: hidden;
        }
        .lieuDateBloc {
            left: 0;
            top: 0;
            width: 10cm;
            color: white;
            position: relative;
            -webkit-box-flex: 1;
            -ms-flex: 1;
            flex: 1;
            text-transform: uppercase;
        }
        .lieuDateBlocLogo {
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            padding: 40px;
        }
        .lieuBloc {
            padding: 20px;
            width: 600px;
            background-color: #009398;
            text-align: center;
            color: white;
        }
        .lieuBlocFull {
            width: 100%;
        }
        .dateInLieuBloc {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .typeFormation {
            font-size: 28px;
            margin-bottom: 5px;
            font-weight: bold;
            line-height: 1.3;
        }
        .ligne1Address {
            font-size: 14px;
            margin-bottom: 0;
            font-weight: bold;
        }
        .ligne1Adresse2 {
            font-size: 12px;
            margin-bottom: 0;
            font-weight: 200;
        }
        .orange-border {
                border-left: 5px solid #ed6d2e;
                padding-left: 6px;
        }
         .nbFormation-1 .orange-border {
            font-size: 14px;
         }
        .session-ref-number {
            color:#ed6d2e;
            font-weight: bold;
        }
        .formateur {
            font-weight: bold;
        }
        .sessionIdAndPhotos {
            border-left: 32px white solid;
            -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg);
            margin-top: -50px;
            overflow: hidden;
            width: 11cm;
            background-color: rgb(233,233,233);
            margin-right: -43px;
            margin-bottom: -50px;
        }
        .nbFormation-2 .sessionIdAndPhotos {
            margin-right: -48px;
        }
        .sessionIdAndPhotosContent {
            -webkit-transform: rotate(12deg);
            transform: rotate(12deg);
            margin-left: -30px;
            margin-top: 50px;
        }
        .nbFormation-2 .sessionIdAndPhotosContent {
            margin-left: -35px;
        }
        .sessionBloc {
            background-color: #ed6d2e;
            color: white;
            font-size: 20px;
            padding: 8px 0 8px 40px;
            margin-right: 39px;
            font-weight: bold;
            text-align: center;
        }
        .sessionBloc.sessionBloc--nbexpert0 {
            margin-right: 30px;
        }
        .expertBloc {
            padding-left: 80px;
            padding-top: 0px;
            padding-bottom: 20px;
        }

        .pt-20 {
             padding-top: 20px;
        }

        .pt-30 {
             padding-top: 30px;
        }

        .pt-40 {
             padding-top: 40px;
        }

        .mtn-10 {
            margin-top: -10px;
        }

        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-3 {
            padding-top: 0px;
        }
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-2,
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-3,
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-4 {
            margin-bottom: calc({{ formations|length }} *40px);
        }
        .nbFormation-1 .sessionIdAndPhotos--small .expertTitle {
            margin-top: 15px;
        }
        .nbFormation-2 .expertBloc.expertBloc--nb-2 {
            padding-top: 15px;
        }
        .nbFormation-2 .expertBloc.expertBloc--nb-3 {
            padding-top: 0px;
        }
        .nbFormation-3 .expertBloc.expertBloc--nb-1 {
            padding-top: 70px;
        }
        .nbFormation-3 .expertBloc.expertBloc--nb-4 {
            padding-top: 10px;
        }
        .nbFormation-2 .expertBloc,
        .nbFormation-3 .expertBloc,
        .nbFormation-4 .expertBloc {
            padding-left: 100px;
        }
        .commentSInscrireTitle {
            text-decoration: underline;
            font-weight: bold;
            font-size: 20px;
            text-align: center;
        }
        .commentSInscrireBloc {
            text-align: left;
            font-size: 10px;
            margin-bottom: 0;
        }
        .infosSessionBloc p {
            line-height: 1;
            font-weight: 300;
        }
        .infosSessionBloc .title {
            font-family: Roboto;
            font-weight: bold;
        }
        .imagesSession {
            width: 100%;
            /*height: 6.15cm;*/
        }
        .imageSession {
            width: 100%;
            height: 6.15cm;
            background-size: cover;
            background-position: center;
            margin-left: -2.5rem;
        }
        .nbFormation-1 .imageSession {
            height: 4.9cm;
        }
        .nbFormation-2 .imageSession {
            height: 4.6cm;
        }
        .nbFormation-3 .imageSession,
        .nbFormation-4 .imageSession{
            height: 1.6cm;
        }
        .nbFormation-2 .imageSession + .imageSession {
            border-top: 0.3cm solid white;
        }
        .nbFormation-3 .imageSession + .imageSession {
            border-top: 0.2cm solid white;
        }
        .nbFormation-4 .imageSession + .imageSession {
            border-top: 0.1cm solid white;
        }
        .content {
            margin: 10px 40px 0;
        }
        .nbFormation-2 .content,
        .nbFormation-3 .content,
        .nbFormation-4 .content {
            {% if (samePicture and formationType == "classique") %}
                margin-left: 60px;
            {% else %}
                margin-left: 0px;
            {% endif %}
            
        }
        .nbFormation-3 .content {
            margin-top: 40px;
        }
        .nbFormation-4 .content {
            margin-top: 30px;
        }
        .formation {
            margin-bottom: 0.8cm;
        }
        .nbFormation-4 .formation {
            margin-bottom: 0.5cm;
        }
        .date {
            font-weight: bold;
            font-size: 22px;
        }
        .nbFormation-2 .date {
            margin-bottom: 0;
            font-size: 12px;
        }
        .nbFormation-3 .date {
            margin-bottom: 0;
           font-size: 12px;
        }
        .nbFormation-4 .date {
            margin-bottom: 0;
            font-size: 12px;
        }
        .title {
            margin-top: 8px;
            font-weight: bold;
            font-size: 24px;
        }
        .title-same {
            margin-bottom: 0px;
            font-weight: bold;
            font-size: 30px;
        }
        .nbFormation-1 .title {
            margin-bottom: 5px;
        }
        .nbFormation-2 .title {
            font-size: 22px;
            margin-bottom: 0;
        }
        .nbFormation-3 .title {
            font-size: 20px;
            margin-bottom: 0;
        }
        .nbFormation-4 .title {
            font-size: 20px;
            margin-bottom: 0;
        }
        .reference {
            font-size: 22px;
            color: #ed6d2e;
            margin-bottom: 0.5rem;
        }
        .nbFormation-2 .reference {
            margin-bottom: 0;
            font-size: 18px;
        }
        .nbFormation-3 .reference {
            margin-bottom: 0;
            font-size: 16px;
        }
        .nbFormation-4 .reference {
            margin-bottom: 0;
            font-size: 14px;
        }
        .indemnisationBloc {
            font-size: 14px;
        }
        .indemnisationMonoFormation {
            background-color: #E6E7E9;
            padding: 10 10 25 10;
            margin-bottom: 10px !important;
        }
        .nbFormation-2 .indemnisationBloc,
        .nbFormation-2 .address {
            font-size: 14px;
        }
        .nbFormation-3 .indemnisationBloc,
        .nbFormation-3 .address {
            font-size: 12px;
        }
        .nbFormation-4 .indemnisationBloc,
        .nbFormation-4 .address {
            font-size: 14px;
        }
        .expertTitle {
            font-size: 14px;
            margin-bottom: 0;
            margin-top: 20px;
        }
        .nbFormation-2 .expertTitle,
        .nbFormation-3 .expertTitle,
        .nbFormation-4 .expertTitle {
            margin-top: 10px;
        }
        .indemnisationBloc,
        .address {
            margin-bottom: 0;
        }
        .civility {
            font-weight: bold;
            font-size: 16px;
        }
        .job {
            font-size: 12px;
            margin-bottom: 0;
            font-weight: lighter;
            margin-right: 50px;
        }
        a {
            text-decoration: underline;
            color: black;
        }
        .flex {
            display: flex;
        }
        .ml15 {
            margin-left: 15px;
        }
    </style>
</head>
<body class="nbFormation-{{ formations|length }}">
{% set thereIsAnAdress = adresseCommune and (formations[0].address or formations[0].zipCode or formations[0].city) %}
{% if formationType == "thematique" %}
    <img src="{{ asset('img/flyer_thematique.png') }}" alt="logo eduprat">
{% elseif formationType == "ethique" %}
    <img src="{{ asset('img/flyer_ethique.png') }}" alt="logo eduprat">
{% elseif formationType == "pratique" %}
    <img src="{{ asset('img/flyer_pratique.png') }}" alt="logo eduprat">
{% endif %}
<div class="header">
    {% if formationType == "classique" %}
        <div class="lieuDateBloc">
            <div class="lieuDateBlocLogo">
                <img src="{{ asset('img/eduprat-new-logo-web.png') }}" width="140" alt="logo eduprat">
                {% if logoPartenaire %}
                    <img src="{{ asset(logoPartenaire.relativeUrl) }}" alt="{{ logoPartenaire.logoName }}" style="margin-top: 20px;">
                {% endif %}
            </div>
        </div>
    {% endif %}
    <div class="lieuBloc {% if formationType != "classique" %}lieuBlocFull{% endif %}">
        {% if samesDate %} 
            <p class="dateInLieuBloc">
                {{ samesDate.date }} | {{ samesDate.hours }}
            </p>
        {% endif %}
        <p class="typeFormation">{{ titre }}</p>
        {% if adresseCommune %}
            <span class="ligne1Address">{{ formations[0].address }} </span>{%  if formations[0].address2 %}<span class="ligne1Adresse2">{{ formations[0].address2 }} {% if formations[0].zipCode or formations[0].city %} - {% endif %}</span>{% endif %}
            <span class="ligne1Adresse2">{{ formations[0].zipCode }} {{ formations[0].city }}</span>
        {% endif %}
    </div>
    
</div>
{% if samePicture and formationType == "classique" %}
    <img style="width:100%;height:120px;object-fit: cover;" src="{{  asset('uploads/programme/picture/'~ samePicture)}}"/>
    {% if showFormatorsInHeader %} 
        <div style="position:relative;text-align: right;margin-right: 50px;margin-top: -70px;">
            {% for formateur in formations[0].formateurs %} 
                <img style="max-width:80px;object-fit: contain;margin:10px;" src="{{  asset('uploads/person/avatar/'~ formateur.person.avatar) }}"/>
            {% endfor %}
        </div>
    {% endif %}
{% endif %}

{# LISTING DES FORMATIONS #}
<div class="content" style="clear: both">
    {% if formations|length > 1 %}
        {% if sameProgramme %} 
            <div style="margin-bottom:30px;">
                <p class="title-same {% if formationType != "classique" %}ml15{% endif %}">{{ sameProgramme.title }}</p>
                {% if sameProgramme.specialities %}
                    <p class="indemnisationBloc {% if formationType != "classique" %}ml15{% endif %}">
                        <strong>
                            {% set plural = indemnisations[0][formations[0].id]["thereIsAPrice"] and indemnisations[0][formations[0].id][0]|length > 1 %}
                            Public{% if plural %}s{% endif %}
                            {% if indemnisations[0][formations[0].id]["thereIsAPrice"] %}
                                {% if sameProgramme.hasPrisesEnChargeDpc() %}
                                    et indemnisation{% if plural %}s{% endif %} DPC
                                {% else %}
                                    ({% for financeMode in formations[0].financeModes %}{{ financeMode.name }}{% if loop.index != formations[0].financeModes|length %},{% endif %}{% endfor %})
                                {% endif %}
                            {% endif %}  :
                        </strong>
                        {% if indemnisations[0][formations[0].id] is defined %}
                            {% for indemnisation in indemnisations[0][formations[0].id] %}
                                {% for speciality, value in indemnisation %}
                                    {{ speciality }} {% if value and formations[0].isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                                {% endfor %}
                            {% endfor %}
                        {% endif %}
                    </p>
                {% endif %}
            </div>
        {% endif %}
        {% set showFormationPicture = not samePicture or (samePicture and formationType != "classique") %}
        {% for key, formation in formations %}
            <div class="formation">
                <div class="{% if showFormationPicture %}flex{% endif %}">
                    {% if showFormationPicture %}
                        {% if showFormatorsPictures %}
                            <div style="position:relative;display: flex;justify-content: flex-end; padding: 0 15 15 0;">
                                {% if formation.programme.picture is not null %}
                                    <img style="max-width:220px;object-fit: contain;" src="{{  asset('uploads/programme/picture/'~ formation.programme.picture)}}"/>
                                {% else %}
                                    <img style="max-width:220px;object-fit: contain;" src="{{  asset('img/default_formation_picture.png')}}"/>
                                {% endif %}
                                <div style="display:flex;position:absolute;bottom: 0;right: 0;">
                                    {% for formateur in formation.formateurs %}
                                        <img style="max-width:65px;object-fit: contain;margin:5px;" src="{{  asset('uploads/person/avatar/'~ formateur.person.avatar) }}"/>
                                    {% endfor %}
                                </div>
                            </div>
                        {% else %}
                            {% if formation.programme.picture is not null %}
                                <img style="max-width:220px;object-fit: contain;" src="{{  asset('uploads/programme/picture/'~ formation.programme.picture)}}"/>
                            {% else %}
                                <img style="max-width:220px;object-fit: contain;" src="{{  asset('img/default_formation_picture.png')}}"/>
                            {% endif %}
                        {% endif %}

                    {% endif %}
                    <div class="{% if showFormationPicture %}ml15{% endif %}">
                        <div class="orange-border">
                            {% if not formation.programme.isELearning %}
                                <div class="date" >
                                    {% for date in formation.datesToString %}
                                        <span><span style="text-transform: uppercase;">{{ date.date|title }}</span> {{ formation.startDate|date('Y') }} | <span style="font-weight: 200;">{{ date.hours|lower }}</span></span><br>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <span class="session-ref-number">{{ formation.programme.reference }}, Session n°{{ formation.sessionNumber }}</span>
                        </div>
                        {% if not sameProgramme %}
                            <p class="title">
                                {{ formation.programme.title }}
                            </p>
                        {% endif %}
                        {% if not adresseCommune %}
                            <p class="address">
                                {{ formation.address }} {{ formation.address2 }} {{ formation.zipcode }} {{ formation.city }}
                            </p>
                        {% endif %}
                            <p class="formateurs">
                                {% for formateur in formation.formateurs %}
                                    <span class="formateur">{{ formateur.person.resumeCivility }} {{ formateur.person.fullname }}</span>{% if formateur.person.job%}, <span class="job">{{ formateur.person.job }}{% endif %}</span><br>
                                {% endfor %}
                            </p>
                        {% if formation.programme.specialities and not sameProgramme %}
                            <p class="indemnisationBloc">
                                <strong>
                                    {% set plural = indemnisations[0][formations[0].id]["thereIsAPrice"] and indemnisations[key][formation.id][0]|length > 1 %}
                                    Public{% if plural %}s{% endif %}
                                    {% if indemnisations[key][formation.id]["thereIsAPrice"] %}
                                        {% if formation.isDpc() %}
                                            et indemnisation{% if plural %}s{% endif %} DPC
                                        {% else %}
                                            ({% for financeMode in formation.financeModes %}{{ financeMode.name }}{% if loop.index != formation.financeModes|length %},{% endif %}{% endfor %})
                                        {% endif %}
                                    {% endif %}  :
                                </strong>
                                {% if indemnisations[key][formation.id] is defined %}
                                    {% for indemnisation in indemnisations[key][formation.id] %}
                                        {% for speciality, value in indemnisation %}
                                            {{ speciality }} {% if value and formation.isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                {% endif %}
                            </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        {% set formation = formations[0] %}
        {% set text = formation.programme.resume %}
        {% if text|length > 1350 %}
            {# On prend les 1350 premiers caractères #}
            {% set first_raw = text|slice(0, 1350) %}

            {# On coupe en mots #}
            {% set words = text|split(' ') %}

            {# Construction intelligente de la première partie sans couper de mot #}
            {% set first_part = '' %}
            {% set char_count = 0 %}
            {% for word in words %}
                {% set word_length = word|length + 1 %} {# +1 pour l'espace #}
                {% if char_count + word_length <= 1350 %}
                    {% set first_part = first_part ~ word ~ ' ' %}
                    {% set char_count = char_count + word_length %}
                {% else %}
                    
                {% endif %}
            {% endfor %}

            {# Supprimer l’espace final #}
            {% set first_part = first_part|trim %}

            {# Deuxième partie : tout le reste du texte #}
            {% set second_part = text|slice(first_part|length)|trim %}

            <div style="display:flex">
                <div style="padding:10px; width:50%">
                    <div class="orange-border">
                        <span class="session-ref-number">{{ formation.programme.reference }}, Session n°{{ formation.sessionNumber }}</span>
                    </div>
                    <p class="title">
                        {{ formation.programme.title }}
                    </p>
                    <p style="text-align : justify;">{{ first_part }}</p>
                </div>
                <div style="padding:10px; width:50%">
                     <p style="text-align : justify;">{{ second_part }}</p>
                     <p class="indemnisationBloc indemnisationMonoFormation">
                        <strong>
                            {% set plural = indemnisations[0][formations[0].id]["thereIsAPrice"] and indemnisations[0][formations[0].id][0]|length > 1 %}
                            Public{% if plural %}s{% endif %}
                            {% if indemnisations[0][formations[0].id]["thereIsAPrice"] %}
                                {% if sameProgramme.hasPrisesEnChargeDpc() %}
                                    et indemnisation{% if plural %}s{% endif %} DPC
                                {% else %}
                                    ({% for financeMode in formations[0].financeModes %}{{ financeMode.name }}{% if loop.index != formations[0].financeModes|length %},{% endif %}{% endfor %})
                                {% endif %}
                            {% endif %}  :
                        </strong>
                        {% if indemnisations[0][formations[0].id] is defined %}
                            {% for indemnisation in indemnisations[0][formations[0].id] %}
                                {% for speciality, value in indemnisation %}
                                    {{ speciality }} {% if value and formations[0].isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                                {% endfor %}
                            {% endfor %}
                        {% endif %}
                    </p>
                    <div class="formatorsBloc">
                        {% for formateur in formation.formateurs %}
                            <div style="display:flex">
                                <img style="max-width:110px;object-fit: contain;" src="{{  asset('uploads/person/avatar/'~ formateur.person.avatar) }}"/>
                                <div style="margin-left: 10px; align-self: end;">
                                    <span class="formateur">{{ formateur.person.resumeCivility }} {{ formateur.person.fullname }}</span>{% if formateur.person.job%}, <span class="job">{{ formateur.person.job }}{% endif %}</span><br>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% else %}
            <div style="padding:10px;">
                <div class="orange-border">
                    <span class="session-ref-number">{{ formation.programme.reference }}, Session n°{{ formation.sessionNumber }}</span>
                </div>
                <p class="title">
                    {{ formation.programme.title }}
                </p>
                <p style="text-align : justify;">{{ text }}</p>
                <p class="indemnisationBloc indemnisationMonoFormation">
                    <strong>
                        {% set plural = indemnisations[0][formations[0].id]["thereIsAPrice"] and indemnisations[0][formations[0].id][0]|length > 1 %}
                        Public{% if plural %}s{% endif %}
                        {% if indemnisations[0][formations[0].id]["thereIsAPrice"] %}
                            {% if sameProgramme.hasPrisesEnChargeDpc() %}
                                et indemnisation{% if plural %}s{% endif %} DPC
                            {% else %}
                                ({% for financeMode in formations[0].financeModes %}{{ financeMode.name }}{% if loop.index != formations[0].financeModes|length %},{% endif %}{% endfor %})
                            {% endif %}
                        {% endif %}  :
                    </strong>
                    {% if indemnisations[0][formations[0].id] is defined %}
                        {% for indemnisation in indemnisations[0][formations[0].id] %}
                            {% for speciality, value in indemnisation %}
                                {{ speciality }} {% if value and formations[0].isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endif %}
                </p>
                <div class="formatorsBloc">
                    {% for formateur in formation.formateurs %}
                        <div style="display:flex">
                            <img style="max-width:110px;object-fit: contain;" src="{{  asset('uploads/person/avatar/'~ formateur.person.avatar) }}"/>
                            <div style="margin-left: 10px; align-self: end;">
                                <span class="formateur">{{ formateur.person.resumeCivility }} {{ formateur.person.fullname }}</span>{% if formateur.person.job%}, <span class="job">{{ formateur.person.job }}{% endif %}</span><br>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

        {% endif %}
    {% endif %}
</div>
</body>
</html>