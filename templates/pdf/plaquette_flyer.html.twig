{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href='https://fonts.googleapis.com/css?family=Roboto:wght@0,300;0,400;0,700;1,300;1,400;1,700' rel='stylesheet' type='text/css'>
    <style>
        html {
            font-size: 62.5%;
        }
        body {
            /*padding: 0 45px;*/
            width: 21cm;
            /*margin: 0 auto;*/
            /*width: 1190px;*/
            font-size: 12px;
            font-family: "Roboto", sans-serif;
            background-color: #f2f2f2;
        }

        label {
            font-weight: bold;
            color: #109199;
            text-transform: uppercase;
            font-size: 1.2em;
        }

        p {
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }
        ul {
            list-style-position: inside;
        }

        .underline {
            text-decoration: underline;
        }

        .header {
            display: -webkit-box;
            display: flex;
            position: relative;
            width: 100%;
            background: #009398;
            overflow: hidden;
        }
        .nbFormation-2 .header {
            min-height: 500px;
        }
        .nbFormation-3 .header {
            min-height: 400px;
        }
        .nbFormation-4 .header {
            min-height: 440px;
        }
        .lieuDateBloc {
            left: 0;
            top: 0;
            width: 10cm;
            min-height: 300px;
            color: white;
            position: relative;
            -webkit-box-flex: 1;
            -ms-flex: 1;
            flex: 1;
            text-transform: uppercase;
        }
        .lieuDateBlocLogo {
            background-color: white;
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            padding: 40px;
        }
        .lieuBloc {
            padding: 50px 0 40px 20px;
        }
        .dateInLieuBloc {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .typeFormation {
            font-size: 35px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .ligne1Address {
            font-size: 16px;
            margin-bottom: 0;
            font-weight: bold;
        }
        .sessionIdAndPhotos {
            border-left: 32px white solid;
            -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg);
            margin-top: -50px;
            overflow: hidden;
            width: 11cm;
            background-color: rgb(233,233,233);
            margin-right: -43px;
            margin-bottom: -50px;
        }
        .nbFormation-2 .sessionIdAndPhotos {
            margin-right: -48px;
        }
        .sessionIdAndPhotosContent {
            -webkit-transform: rotate(12deg);
            transform: rotate(12deg);
            margin-left: -30px;
            margin-top: 50px;
        }
        .nbFormation-2 .sessionIdAndPhotosContent {
            margin-left: -35px;
        }
        .sessionBloc {
            background-color: #ed6d2e;
            color: white;
            font-size: 20px;
            padding: 8px 0 8px 40px;
            margin-right: 39px;
            font-weight: bold;
            text-align: center;
        }
        .sessionBloc.sessionBloc--nbexpert0 {
            margin-right: 30px;
        }
        .expertBloc {
            padding-left: 80px;
            padding-top: 0px;
            padding-bottom: 20px;
        }

        .pt-20 {
             padding-top: 20px;
        }

        .pt-30 {
             padding-top: 30px;
        }

        .pt-40 {
             padding-top: 40px;
        }

        .mtn-10 {
            margin-top: -10px;
        }

        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-3 {
            padding-top: 0px;
        }
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-2,
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-3,
        .nbFormation-1 .sessionIdAndPhotos--small .expertBloc.expertBloc--nb-4 {
            margin-bottom: calc({{ formations|length }} *40px);
        }
        .nbFormation-1 .sessionIdAndPhotos--small .expertTitle {
            margin-top: 15px;
        }
        .nbFormation-2 .expertBloc.expertBloc--nb-2 {
            padding-top: 15px;
        }
        .nbFormation-2 .expertBloc.expertBloc--nb-3 {
            padding-top: 0px;
        }
        .nbFormation-3 .expertBloc.expertBloc--nb-1 {
            padding-top: 70px;
        }
        .nbFormation-3 .expertBloc.expertBloc--nb-4 {
            padding-top: 10px;
        }
        .nbFormation-2 .expertBloc,
        .nbFormation-3 .expertBloc,
        .nbFormation-4 .expertBloc {
            padding-left: 100px;
        }
        .commentSInscrireTitle {
            text-decoration: underline;
            font-weight: bold;
            font-size: 20px;
            text-align: center;
        }
        .commentSInscrireBloc {
            text-align: left;
            font-size: 10px;
            margin-bottom: 0;
        }
        .infosSessionBloc p {
            line-height: 1;
            font-weight: 300;
        }
        .infosSessionBloc .title {
            font-family: Roboto;
            font-weight: bold;
        }
        .imagesSession {
            width: 100%;
            /*height: 6.15cm;*/
        }
        .imageSession {
            width: 100%;
            height: 6.15cm;
            background-size: cover;
            background-position: center;
            margin-left: -2.5rem;
        }
        .nbFormation-1 .imageSession {
            height: 4.9cm;
        }
        .nbFormation-2 .imageSession {
            height: 4.6cm;
        }
        .nbFormation-3 .imageSession,
        .nbFormation-4 .imageSession{
            height: 1.6cm;
        }
        .nbFormation-2 .imageSession + .imageSession {
            border-top: 0.3cm solid white;
        }
        .nbFormation-3 .imageSession + .imageSession {
            border-top: 0.2cm solid white;
        }
        .nbFormation-4 .imageSession + .imageSession {
            border-top: 0.1cm solid white;
        }
        .content {
            margin: 50px 40px 0;
        }
        .nbFormation-2 .content,
        .nbFormation-3 .content,
        .nbFormation-4 .content {
            margin-left: 60px;
        }
        .nbFormation-1 .content {
            text-align: center;
        }
        .nbFormation-3 .content {
            margin-top: 40px;
        }
        .nbFormation-4 .content {
            margin-top: 30px;
        }
        .formation {
            margin-bottom: 0.8cm;
        }
        .nbFormation-4 .formation {
            margin-bottom: 0.5cm;
        }
        .date {
            font-weight: bold;
            font-size: 22px;
            color: #005672;
            text-transform: uppercase;
        }
        .nbFormation-2 .date {
            margin-bottom: 0;
            font-size: 18px;
        }
        .nbFormation-3 .date {
            margin-bottom: 0;
            font-size: 16px;
        }
        .nbFormation-4 .date {
            margin-bottom: 0;
            font-size: 14px;
        }
        .title {
            font-weight: bold;
            font-size: 30px;
        }
        .nbFormation-2 .title {
            font-size: 28px;
            margin-bottom: 0;
        }
        .nbFormation-3 .title {
            font-size: 26px;
            margin-bottom: 0;
        }
        .nbFormation-4 .title {
            font-size: 20px;
            margin-bottom: 0;
        }
        .reference {
            font-size: 22px;
            color: #ed6d2e;
            margin-bottom: 0.5rem;
        }
        .nbFormation-2 .reference {
            margin-bottom: 0;
            font-size: 18px;
        }
        .nbFormation-3 .reference {
            margin-bottom: 0;
            font-size: 16px;
        }
        .nbFormation-4 .reference {
            margin-bottom: 0;
            font-size: 14px;
        }
        .indemnisationBloc {
            font-size: 16px;
        }
        .nbFormation-2 .indemnisationBloc,
        .nbFormation-2 .address {
            font-size: 18px;
        }
        .nbFormation-3 .indemnisationBloc,
        .nbFormation-3 .address {
            font-size: 16px;
        }
        .nbFormation-4 .indemnisationBloc,
        .nbFormation-4 .address {
            font-size: 14px;
        }
        .expertTitle {
            font-size: 14px;
            margin-bottom: 0;
            margin-top: 20px;
        }
        .nbFormation-2 .expertTitle,
        .nbFormation-3 .expertTitle,
        .nbFormation-4 .expertTitle {
            margin-top: 10px;
        }
        .indemnisationBloc,
        .address {
            margin-bottom: 0;
        }
        .civility {
            font-weight: bold;
            font-size: 16px;
        }
        .job {
            font-size: 12px;
            margin-bottom: 0;
            font-weight: lighter;
            margin-right: 50px;
        }
        a {
            text-decoration: underline;
            color: black;
        }
    </style>
</head>
<body class="nbFormation-{{ formations|length }}">
{% set thereIsAnAdress = adresseCommune and (formations[0].address or formations[0].zipCode or formations[0].city) %}
<div class="header">
    <div class="lieuDateBloc">
        <div class="lieuDateBlocLogo">
            <img src="{{ asset('img/eduprat-new-logo-web.png') }}" width="140" alt="logo eduprat">
        </div>
        <div class="lieuBloc">
            {% if formations|length == 1 or (not hasElearning and sameStartDate) %}
                {% if not formations[0].programme.isELearning %}
                    <p class="dateInLieuBloc">
                        {% for date in formations[0].datesToString %}
                            {{ date.date|title }} {{ formations[0].startDate|date('Y') }}<br>
                        {% endfor %}
                    </p>
                {% endif %}
            {% else %}
                <p class="dateInLieuBloc">
                    Formations{% if isFullDPC %} DPC{% endif %}
                </p>
            {% endif %}
            <p class="typeFormation">{{ titre }}</p>
            {% if adresseCommune %}
                <p class="ligne1Address">{{ formations[0].address }}</p>
                {%  if formations[0].address2 %}{{ formations[0].address2 }}<br>{% endif %}
                {{ formations[0].zipCode }} {{ formations[0].city }}
            {% endif %}
        </div>
    </div>
    <div class="sessionIdAndPhotos {% if formations|length == 1 and not formations[0].programme.isELearning or adresseCommune %}sessionIdAndPhotos--small{% endif %}">
        <div class="sessionIdAndPhotosContent">
            <div class="imagesSession">
                {% for formation in formations %}
                    {% if formation.programme.picture %}
                        <div class="imageSession" style="background-image: url({{  asset('uploads/programme/picture/'~ formation.programme.picture)}});"></div>
                    {% else %}
                        <div class="imageSession" style="background-image: url({{  asset('img/default-formation-pdf.png')}});"></div>
                    {% endif %}
                {% endfor %}
            </div>
            {% if formations|length == 1 %}
            <div class="sessionBloc sessionBloc--nbexpert{{ nbFormateurs }}">
                {{ formations[0].programme.reference }}{% if not formations[0].programme.isELearning %}, Session {{ formations[0].sessionNumber }}{% endif %}
            </div>
            {% endif %}
            <div class="expertBloc expertBloc--nb-{{ nbFormateurs }}
                {% if thereIsAnAdress %}
                    {% if nbFormateurs == 2  %}pt-30
                    {% elseif nbFormateurs == 1  %}pt-40
                    {% else %}mtn-10{% endif %}
                {% else %}
                    {% if nbFormateurs == 2  %}pt-20
                    {% elseif nbFormateurs == 1  %}pt-30
                    {% else %}mtn-10{% endif %}
                {% endif %}
            ">
                {% if nbFormateurs %}
                    <p class="expertTitle">Expert(s) :</p>
                    {% for expert in formateurs %}
                        <div class="civility">{{ expert.civility }} {{ expert.firstname }} {{ expert.lastname|upper }}</div>
                        <p class="job">{{ expert.job }}</p>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{# LISTING DES FORMATIONS #}
<div class="content" style="clear: both">
    {% for key, formation in formations %}
        <div class="formation">
            {% if not formation.programme.isELearning %}
                <p class="date">
                    <div style="display: inline-block; vertical-align: top;"><img style="width: {% if formations|length == 1 %}20{% elseif formations|length == 2 %}14{% elseif formations|length == 3 %}14{% else %}14{% endif %}px; margin-bottom: 4px; margin-right: 2px;" src="{{  asset('/img/icon_dates_flyer.png')}}"/></div>
                    <div class="date" style="display: inline-block; margin-top: -8px;">
                        {% for date in formation.datesToString %}
                            <span>{{ date.date|title }} {{ formation.startDate|date('Y') }} | {{ date.hours|lower }}</span><br>
                        {% endfor %}
                    </div>
                </p>
            {% endif %}
            <p class="title">
                {{ formation.programme.title }}
            </p>
            <p class="reference">
                Référence {% if formation.programme.hasPrisesEnChargeDpc() %}DPC{% endif %} : {{ formation.programme.reference }}{% if not formation.programme.isELearning %}, Session {{ formation.sessionNumber }}{% endif %}
            </p>
            {% if not adresseCommune %}
                <p class="address">
                    {{ formation.address }} {{ formation.address2 }} {{ formation.zipcode }} {{ formation.city }}
                </p>
            {% endif %}
            {% if formation.programme.specialities %}
                <p class="indemnisationBloc">
                    <strong>
                        {% set plural = indemnisations[key][formation.id][0]|length > 1 %}
                        Public{% if plural %}s{% endif %}
                        {% if indemnisations[key][formation.id]["thereIsAPrice"] %}
                            {% if formation.isDpc() %}
                                et indemnisation{% if plural %}s{% endif %} DPC
                            {% else %}
                                ({% for financeMode in formation.financeModes %}{{ financeMode.name }}{% if loop.index != formation.financeModes|length %},{% endif %}{% endfor %})
                            {% endif %}
                        {% endif %}  :
                    </strong>
                    {% if indemnisations[key][formation.id] is defined %}
                        {% for indemnisation in indemnisations[key][formation.id] %}
                            {% for speciality, value in indemnisation %}
                                {{ speciality }} {% if value and formation.isDpc() %}({{value}}€){% endif %}{% if loop.index != indemnisation|length %},{% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endif %}
                </p>
            {% endif %}
        </div>
    {% endfor %}
    {% if formations|length == 1 and formations[0].programme.hasPrisesEnChargeDpc() %}
        <div class="commentSInscrireBloc">
            <p class="commentSInscrireTitle">Comment s'inscrire ?</p>
            <p class="commentSInscrireBloc">
                Rendez-vous sur <a href="https://www.agencedpc.fr">www.agencedpc.fr</a>, rubrique <strong>Professionnels de santé</strong> (en haut de l'écran).<br>
                Votre identification se fait sur la partie droite de l'écran :<br>
                <span class="underline"><strong>1- Vous n'avez pas encore de compte DPC</strong></span>
            </p>
            <ul class="commentSInscrireBloc">
                <li>Cliquez sur <strong> CRÉER UN COMPTE</strong> et saisissez vos informations personnelles puis laissez-vous guider.</li>
                <li>Pensez à vous munir d'un RIB au format PDF pour renseigner vos informations financières et ainsi bénéficier de la prise en charge de vos frais pédagogiques par l'ANDPC et d'une indemnisation</li>
            </ul>
            <p class="commentSInscrireBloc">
                <span class="underline"><strong>2- Vous avez déjà un compte DPC</strong></span>
            </p>
            <ul class="commentSInscrireBloc">
                <li>Saisissez votre identifiant et mot de passe puis cliquez sur  <strong> SE CONNECTER</strong>.</li>
                <li>Cliquez sur le menu <strong>Actions DPC</strong> puis sur <strong>Recherche une action / S'inscrire</strong>.
                    À gauche de l'écran, entrez la référence de l'action à 11 chiffres dans la case prévue à cet effet (ex: <strong>{{ formations[0].programme.reference }}</strong>), puis cliquez sur <strong>Rechercher</strong>.
                        Après vérification cliquez sur le titre de la formation (en bleu), cliquez sur <strong>DÉTAIL</strong>, puis <strong>LISTE SESSIONS</strong> en haut de l'écran. Sélectionnez le numéro de session de votre choix, puis cliquer sur  <strong> S'inscrire</strong> et valider.
                </li>
            </ul>
        </div>
    {% endif %}
</div>
</body>
</html>