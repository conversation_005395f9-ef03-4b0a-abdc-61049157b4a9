<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/gotenberg.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.85em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }

        ul {
            list-style-position: inside;
        }        

        .wrap {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .text-box {
            margin: 1em 1em 1em 1em;
            border: 1px solid black;
        }

        .text-box1 {
            height: 570px;
            border: 1px solid black;
        }

        .text-box1.text-box1-ppt {
            height: 400px;
            border: 1px solid black;
        }

        .text-box1.text-box1-zoom {
            height: 280px;
            border: 1px solid black;
        }

        .text-box2 {
            height: 160px;
            border: 1px solid black;
        }

        .text-box2.text-box2-zoom {
            height: 280px;
        }

        .text-box-header {
            display:flex;
            background-color: #d9e2f3;
            height: 20px;
            line-height: 20px;
            padding: 0em 1em 0em 1em;
        }

        .text-box-text1 {
            padding: 1em;
        }

        .text-box-text2 {
            padding: 2em .9375rem 1em .9375rem;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="column large-12">
                    <div><span class="bold">{{ "traceability.organization_name" | trans }}</span> : {{ "traceability.organization_name_value" | trans }}</div>
                    <div><span class="bold">{{ "traceability.id" | trans }}</span> : {{ "traceability.id_value" | trans }}</div>
                </div>
            </div>

            <div class="row mtm mbm">
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.nb_action_prog" | trans }}</span> : {{ formation.programme.reference }}</div>
                </div>
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.nb_session" | trans }}</span> : {{ formation.sessionNumber }}</div>
                </div>
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.nb_unity" | trans }}</span> : {% if unityPosition == 3 and formation.isFourUnity %}4{% else %}{{ unityPosition }}{% endif %}</div>
                </div>
                <div class="column small-3">
                    <div><span class="bold">{{ "traceability.vol_hour_unity" | trans }}</span> : {{ unity.nbHoursOffline + unity.nbHoursConnected }}h</div>
                </div>
            </div>

            <div class="row mtm mbm text-box {% if formation.isPowerpoint %}text-box1-ppt{% endif %}">
                <div class="text-box-header">
                    <div class="row" style="width: 100%;">
                        <div class="column small-8">
                            <span class="bold">{{ "traceability.desc_activities_not_connected" | trans }} :</span>
                        </div>
                        <div class="column small-4">
                            <span class="bold">{{ "traceability.vol_hour_declared" | trans }} : {% if unity.nbHoursOffline %}{{ unity.nbHoursOffline }}h{% endif %}</span>
                        </div>
                    </div>
                </div>
                <div class="text-box-text1">
                    {{ unity.descriptifOffline|raw }}
                </div>
            </div>

            <div class="row mtm mbm text-box">
                <div class="text-box-header">
                    <div class="row" style="width: 100%;">
                        <div class="column small-8">
                            <span class="bold">{{ "traceability.desc_activities_connected" | trans }} :</span>
                        </div>
                        <div class="column small-4">
                            <span class="bold">{{ "traceability.vol_hour_declared" | trans }} : {% if unity.nbHoursConnected %}{{ unity.nbHoursConnected }}h{% endif %}</span>
                        </div>
                    </div>
                </div>
                <div class="text-box-text1">
                    {% set totalTime = unity.nbHoursConnected * 60 %}
                    {% if formation.isTcs() %}
                        Etude des Tests de Concordance de Script (TCS) - {{ totalTime }} min
                    {% else %}
                        {% if unityPosition == 3 %}
                            Saisie {{ ("admin.formation.typeTraceability."  ~ formation.formTypePost) | trans }} - {{ totalTime }} min
                        {% else %}
                            Saisie {{ ("admin.formation.typeTraceability."  ~ formation.formTypePre) | trans }}{% if unityPosition != 2 and not (formation.programme.isElearningTwoUnity and unityPosition == 1) %} - {{ totalTime }} min {% endif %} 
                        {% endif %}
                    {% endif %}
                    {% if formation.programme.isElearningOneUnity or (formation.programme.isElearningTwoUnity and unityPosition == 1) or (formation.programme.isElearningThreeUnity and unityPosition == 2) %}
                        <br>
                        Module e-learning ({{ formation.elearning.label }}) : <br>
                        <ul>
                            {% for l in formation.elearning.lessons %}
                                <li>{{ l.label }} : {{ l.declaredDuration }}min</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                    {{ unity.descriptifConnected|raw }}
                </div>
            </div>

            {% if formation.isPowerpoint %}
                <div class="page"></div>
            {% endif %}

            <div class="row mtm mbm">
            </div>

            <div class="row mtm mbm">
                <div class="column large-12">
                    <table>
                        <thead>
                        <tr>
                            <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                            <th>{{ "admin.participant.firstname" | trans }}</th>
                            <th>{{ "traceability.rpps_adeli" | trans }}</th>
                            <th>{{ "traceability.activity_done" | trans }}</th>
                            <th>{{ "traceability.first_connection" | trans }}</th>
                            <th>{{ "traceability.last_connection" | trans }}</th>
                            <th>{{ "traceability.total_time_min" | trans }}</th>
                            <th>{{ "traceability.avancement" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participation in participations %}
                            {% set avancement = participation.avancementByUnity(unityPosition) %}
                            {% set startedAtLogs = participation.startedAtLogsByStep(unityPosition) %}
                            {% set finishedAtLogs = participation.finishedAtLogsByStep(unityPosition) %}
                            <tr>
                                <td class="wrap">{{ participation.participant.lastname | upper }}</td>
                                <td class="wrap">{{ participation.participant.firstname }}</td>
                                {% if participation.participant.rpps == '' %}
                                    <td class="center">{{ participation.participant.adeli }}</td>
                                {% else %}
                                    <td class="center">{{ participation.participant.rpps }}</td>
                                {% endif %}
                                <td class="center">{% if unity.nbHoursOffline and unity.nbHoursOffline > 0 %} {{ "traceability.yes" | trans | upper }} {% else %}{{ "traceability.no" | trans | upper }}{% endif %}</td>
                                <td class="center">{% if startedAtLogs %}{{ startedAtLogs|date('d/m/Y') }} {{ startedAtLogs|date('H\\hi') }}{% endif %}</td>
                                <td class="center">{% if finishedAtLogs %}{{ finishedAtLogs|date('d/m/Y') }} {{ finishedAtLogs|date('H\\hi') }}{% endif %}</td>
                                {% if participation.totalTime %}
                                    <td class="center">{{ participation.totalTime }}</td>
                                {% else %}
                                    <td></td>
                                {% endif %}
                                <td class="center">{{ avancement }}%</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row mbm mtm">              
            </div>

            <div class="nbi">
                <div class="row mbm" style="font-size:10px">
                    <div class="column large-12">
                        {{ "traceability.text_1_unity" | trans }}<br>
                        {{ "traceability.text_2_unity" | trans }}<br>
                        {{ "traceability.text_3_unity" | trans }}<br><br>
                        <b style="color:#2EB0E5;">{{ "traceability.text_4_unity" | trans }}</b>
                    </div>
                </div>
                <div class="row" style="margin-top:2em">
                    <div class="column small-6">
                        {{ "traceability.address" | trans({"%date%" : closingDate | date('d.m.Y') }) }}
                    </div>
                    <div class="column small-6">
                        {{ "traceability.signature" | trans }} : <br>
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat" />
                    </div>
                </div>
                <div class="row" style="margin-top:2em; font-size:10px">
                    <b><u>{{ "traceability.text_5_article_title" | trans }}</u></b> "{{ "traceability.text_5_article_detail" | trans }}"
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
