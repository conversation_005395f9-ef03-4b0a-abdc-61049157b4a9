<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0.5em 1cm;
            font-size: 0.9em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.9em;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 10px;
        }

        #title {
            font-size: 1.2em;
        }

        .form-part {
            background-color: #eeece1;
            text-align: center;
            width:100%;
        }

    </style>
</head>

<body>
    {# <div style="width:100%;">
        <img style="margin-top: 1em; margin-bottom: 1em; float: left;" width="100" src="{{ absolute_url(asset('img/logo.png')) }}" alt="eduprat logo">
    </div>
    <div style="width:100%;">
        <img style="margin-top: 1em; margin-bottom: 1em; float: right" width="100" src="{{ absolute_url(asset('img/logo_ministere.jpg')) }}" alt="eduprat logo">
    </div> #}
    <div class="fluid960 page pageCentered">
        {% for participation in participations %}
            {% set participant = participation.participant %}
            <div class="pageWidth row" style="margin-bottom:em">
                <div class="header">
                    <div class="pageWidth row">
                        <div class="row">
                            <div class="column small-8 small-offset-2">
                                <div id="title" class="text-center mtl">
                                    <b>{{ "cert_realisation.titles.page" | trans }}</b>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:3em" class="content">
                <div class="pageWidth">
                    <div class="row mtl mbl">
                        <div class="column small-12">
                            <p>Je soussigné(e) <b>Mr PLAS Philippe</b>,<br>
                            représentant légal du dispensateur de formation <b>EDUPRAT FORMATIONS</b><br>
                            atteste que <b>{{ participant.firstname }} {{ participant.lastname }}</b><br>
                            salarié(e) de l’entreprise : <b>{{ participant.address }} {{ participant.address2 }} {{ participant.zipcode }} {{ participant.city }}</b></p>
                                                        
                            <p style="margin-top:1.5em">A suivi l'action de formation : <b>{{ formation.programme.title }}</b></p>
                            
                            <p style="margin-top:1.5em"><i>Nature de l'action de formation :</i></p>
                            <div>
                                <input type="checkbox"  checked>
                                <label>action de formation</label>
                            </div>
                            <div>
                                <input type="checkbox">
                                <label>bilan de compétences</label>
                            </div>
                            <div>
                                <input type="checkbox">
                                <label>action de VAE</label>
                            </div>
                            <div>
                                <input type="checkbox">
                                <label>action de formation par apprentissage</label>
                            </div>

                            <p style="margin-top:1.5em">qui s'est déroulée {% if formation.openingDate|date('d/m/Y') == formation.closingDate|date('d/m/Y') %}le <b>{{ formation.openingDate | date('d/m/Y') }}</b>{% else %}du <b>{{ formation.openingDate | date('d/m/Y') }}</b> au <b>{{ formation.closingDate | date('d/m/Y') }}</b>{% endif %} pour une durée totale de <b>{{ formation.programme.durationTotal }}</b> heures. <span style="font-size:14px">*</span></p>
                            

                            <p style="margin-top:1.5em">Sans préjudice des délais imposés par les règles fiscales, comptables ou commerciales, je m’engage
                                à conserver l’ensemble des pièces justificatives qui ont permis d’établir le présent certificat pendant
                                une durée de 3 ans à compter de la fin de l’année du dernier paiement. En cas de cofinancement des
                                fonds européens la durée de conservation est étendue conformément aux obligations
                                conventionnelles spécifiques.</p>


                            <div style="margin-top:1.5em" class="row mtl">
                                <div class="column small-6">
                                    <p class="mbn">Fait à : Mérignac<br>
                                    Le : {{ formation.closingDate|date('d/m/Y') }}</p>
                                </div>
                                <div class="column small-6">
                                    <div style="border:1px solid black;width:250px; text-align:center;padding:1em 3em">
                                        <span style="font-size:12px">Cachet et signature</span>
                                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                                    </div>
                                </div>
                            </div>

                            <div style="width:200px; border-top:1px solid black;margin-bottom:5px; margin-top:2.5em"></div>
                            <p style="font-size:10px"><span style="font-size:14px">*</span> Dans le cadre des formations à distance prendre en compte la réalisation des activités pédagogiques et le temps estimé pour les réaliser.</p>
                                
                        </div>
                    </div>
                </div>
            </div>
            {% if loop.index != loop.length %} 
                <div class="page-break"></div>
            {% endif %}
        {% endfor %}
    </div>
</body>
</html>
