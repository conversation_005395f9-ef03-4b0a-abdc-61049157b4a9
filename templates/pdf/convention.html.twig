{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 45px;
            width: 21cm;
            font-size: 14px;
        }

        label {
            font-weight: bold;
            color: #109199;
            text-transform: uppercase;
            font-size: 1.2em;
        }

        p {
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }

        .art-group p:last-child {
            margin-bottom: 2em;
        }

        h1 {
            font-size: 30px;
            margin-bottom: 0;
        }

        h2 {
            font-family: stimupratbold, sans-serif;
            font-size: 22px;
            font-weight: 600;
        }

        h3 {
            margin-top: 15px;
            margin-bottom: 5px;
            font-size: 16px;
            font-weight: bold;
            color: #109199;
        }

        h4 {
            margin-top: 15px;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
            color: #109199;
        }

        h3.arrow-title {
            font-size: 17px;
            margin-bottom: 15px;
        }

        ul {
            list-style-position: inside;
        }

        .h1-title {
            font-weight: bold;
            color: black;
            font-size: 34px;
        }

        .title-block {
            margin-top: 100px;
            background: #109199;
            padding: 50px 20px 150px;
            text-align: center;
            font-size: 26px;
            font-weight: bold;
            color: white;
        }

        .underline {
            text-decoration: underline;
        }

        #conditions {
            font-size: 12px;
        }

    </style>
</head>
<body>

{% set programme = formation.programme %}
{% set former = formation.formateursPersons.first %}

<div class="fluid960 page pageCentered">
    <div class="content">
        <div>
            <hr class="mbl">
            <h1 class="text-center h1-title">Convention de formation professionnelle</h1>
            <div class="title-block">
                {{ formation.programme.title }}
            </div>
            <div class="page-break"></div>
        </div>
        <div>
            <hr class="mbl">
            <h1 class="text-center">Convention de formation professionnelle</h1>
            <p class="text-center">(Articles L.6353-2 et R.6353-1 du Code du travail)</p>
            <hr class="mtl" style="	border-width: 1px;">
        </div>
        <div class="row">
            <div class="column small-12">
                <p>Entre les soussignés :</p>
                <div class="bold">
                    <div>La S.A.S EDUPRAT Formations</div>
                    <div>4, Avenue Neil Armstrong – Bât Mermoz – 2ème étage</div>
                    <div>N° SIRET : 799 435 995 00029</div>
                    <p>N° Déclaration Activité Formation : 72330927633 auprès du préfet de la région Aquitaine</p>
                </div>
                <div>Et</div>
                <div class="bold">
                    <div>{% if financeSousMode is not null %}{{ financeSousMode.name }}{% else %}&nbsp{% endif %}</div>
                    <div>{% if financeSousMode is not null %}{{ financeSousMode.address }}{% else %}&nbsp{% endif %}</div>
                    <div>{% if financeSousMode is not null %}{{ financeSousMode.address2 }}{% else %}&nbsp{% endif %}</div>
                    <div>{% if financeSousMode is not null %}{{ financeSousMode.zipCode }} {{ financeSousMode.city }}{% else %}&nbsp{% endif %}</div>
                </div>
                <br>

                <div class="underline bold">A compléter par l’établissement</div>
                <div>N° SIREN de l’établissement : {% if financeSousMode is not null %}{{ financeSousMode.siren }}{% else %}&nbsp{% endif %}</div>
                <p>Mail de contact (pour la facturation) : {% if financeSousMode is not null %}{{ financeSousMode.rpsMail }}{% else %}&nbsp{% endif %}</p>
                <div>Est conclue la convention suivante, en application du livre III de la Sixième partie du Code du travail portant organisation de la formation professionnelle continue :</div>
            </div>
        </div>


        <div class="row">
            <div class="column small-12">
                <h4>Article 1</h4>
                <div>
                    <p>L’organisme de formation SAS Eduprat Formations organise l’action de formation suivante :</p>
                    <p>1° Intitulé : {{ formation.programme.title }} {% if financeSousMode is not null and not financeSousMode.isHorsDPC() and not financeSousMode.isFif() %}(Référence DPC : n° {{ formation.programme.reference }}, session {{ formation.sessionNumber }}{% endif %}</p>
                    <p>2° Résumé - Problématique : {{ formation.programme.resume|nl2br }}</p>
                    <p>3° Objectif : <br> {{ formation.programme.objectives|raw }}</p>
                    <p>4° Modalités d’évaluation : Toutes les étapes sont obligatoires pour valider la formation. Les modalités d’évaluation reposent sur une comparaison des questionnaires pré et post formation présentielle.</p>
                    <p>5° Votre compte extranet participant vous permet de : </p>
                    <ul>
                        <li>{% if formation.programme.isFormatPresentiel %}Compléter vos questionnaires pré et post {% else %}Suivre votre parcours de formation non présentiel{% endif %}</li>
                        <li>Télécharger vos restitutions</li>
                        <li>Télécharger vos attestations de participation</li>
                        <li>Télécharger les supports de formation</li>
                    </ul>
                    <p style="font-style: italic">Le compte extranet sera accessible via internet.</p>
                    <p>6° Catégorie : 4 au sens de l’Article L.6313-1 du Code du travail : Action de Prévention</p>
                    <p>
                        {% set hasOneDay = formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
                        {% if hasOneDay %}
                            7° Date :
                            {# {% if financeSousMode is not null and not financeSousMode.isHorsDPC() and not financeSousMode.isFif() %} DPC N° {{ formation.programme.reference }} session {{ formation.sessionNumber }} : {% endif %} #}
                            Du {{ formation.openingDate|date('d/m/y') }} au {{ formation.closingDate|date('d/m/y') }}
                            {% if not formation.programme.isElearning %}
                                {% if formation.programme.isClasseVirtuelle %}avec une classe virtuelle{% else %}avec une réunion présentielle{% endif %}
                                {% set hoursStart = formation.startDate|date('H\\hi') == '00h00' ? null : formation.startDate|date('H\\hi') %}
                                {% set hoursEnd = formation.endDate|date('H\\hi') == '00h00' ? null : formation.endDate|date('H\\hi') %}
                                le {{ formation.startDate | date('d/m/Y') }} {% if hoursStart == hoursEnd %}à {{ hoursStart }}{% else %}de {{ hoursStart }} à {{ hoursEnd }}{% endif %}
                            {% endif %}
                        {% else %}
                            {% set reunionDays = formation.getReunionDays %}
                            7° Date : {% if reunionDays|length == 0 %}  Du {{ formation.openingDate|date('d/m/y') }} au {{ formation.closingDate|date('d/m/y') }} {% endif %} {% if formation.programme.isClasseVirtuelle %}avec une classe virtuelle{% endif %}
                            {% for day in reunionDays  %}
                                <br>
                                {% set hoursStart = day.startDate|date('H\\hi') == '00h00' ? null : day.startDate|date('H\\hi') %}
                                {% set hoursEnd = day.endDate|date('H\\hi') == '00h00' ? null : day.endDate|date('H\\hi') %}
                                {% if day.startDate|date('d/m/Y') == day.endDate|date('d/m/Y') %}
                                    - le {{ day.startDate | date('d/m/Y') }} {% if hoursStart == hoursEnd %}à {{ hoursStart }}{% else %}de {{ hoursStart }} à {{ hoursEnd }}{% endif %}
                                {% else %}
                                    - du {{ day.startDate | date('d/m/Y') }} {{ hoursStart }} au {{ day.endDate | date('d/m/Y') }} {{ hoursEnd }}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    </p>
                    <div>
                        {% set durationEtape3 = formation.programme.durationNotPresentielleActalians is not null and formation.programme.durationNotPresentielleActalians > 0 ? formation.programme.durationNotPresentielleActalians : 0 %}
                        8° Durée :
                        <ul>
                            {% if formation.programme.durationPresentielle > 0 %}
                                {% if formation.programme.isClasseVirtuelle %}
                                    <li>{{ formation.programme.durationPresentielle }} heure{% if formation.programme.durationPresentielle > 1 %}s{% endif %} de classe virtuelle</li>
                                {% else %}
                                    <li>{{ formation.programme.durationPresentielle }} {% if formation.programme.durationPresentielle > 1%}heures présentielles{% else %}heure présentielle{% endif %}</li>
                                {% endif %}
                            {% endif %}
                            {% if formation.programme.durationNotPresentielle > 0 %}
                                {% set plurialNotPresentielle = formation.programme.durationNotPresentielle > 1 %}
                                {% if formation.isVFC() %}
                                    <li>{{ formation.programme.durationNotPresentielle}} {% if plurialNotPresentielle %}heures non-présentielles{% else %}heure non-présentielle{% endif %} comprenant l'étude des vignettes cliniques.</li>
                                {% elseif formation.isTcs() %}
                                    <li>{{ formation.programme.durationNotPresentielle}} {% if plurialNotPresentielle %}heures non-présentielles{% else %}heure non-présentielle{% endif %} comprenant l'étude des tests de concordance de script.</li>
                                {% else %}
                                    <li>{{ formation.programme.durationNotPresentielle }} {% if plurialNotPresentielle %}heures non-présentielles{% else %}heure non-présentielle{% endif %}, comprenant :
                                        <ul>
                                            <li>L'étude {{ ('front.label.' ~ formation.displayType ~ '.des') | trans }} avant et après la formation</li>
                                            <li>Le bilan pédagogique : comprend l’étude de la restitution comparative personnalisée des {{ ('front.label.' ~ formation.displayType ~ '.plural') | trans }}, un retour des apprenants, le téléchargement du support et de l’attestation de participation.</li>
                                        </ul>
                                    </li>
                                {% endif %}
                            {% endif %}
                        </ul>
                    </div>
                    <div>9° Lieu : {{ formation.address }}, {{ formation.address2 }}, {{ formation.zipCode }} {{ formation.city }}</div>
                    <div>
                        10° Modalités de déroulement : la formation {% if financeSousMode is not null and not financeSousMode.isHorsDPC() and not financeSousMode.isFif() %}{{ formation.programme.reference }} session {{ formation.sessionNumber }} est {% endif %} assurée par :
                        <ul>
                            {% for formateur in formation.formateurs %}
                                <li>{{ formateur.person.civility }} {{ formateur.person.firstname }} {{ formateur.person.lastname }}{% if formateur.person.job is defined %}, {{ formateur.person.job }}{% endif %}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        {% set participations = formation.getParticipationsPerMode(financeSousMode) %}

        <div class="row">
            <div class="column small-12">
                <h4>Article 2</h4>
                <div>A l’inscription, le cocontractant s'engage à acquitter les frais suivants :</div>
                <div>Prix par participant par session de formation : {{ formation.cost is not null and participations.first == false ? formation.cost : participations.first.price is defined ? participations.first.price : "" }} €</div>
                <p>Nombre de participants : {{ formation.manualParticipantCount is not null and participations.count == 0 ? formation.manualParticipantCount : participations.count }}</p>
                {% if participations.count > 0 %}
                    <div>Identité:</div>
                    <div>
                        {% for participation in participations %}
                            {% set p = participation.participant %}
                            <div>{{ p.civility }} {{ p.firstname }} {{ p.lastname }}</div>
                            <div>{{ p.address }}</div>
                            <div>{{ p.zipCode }} {{ p.city }}</div>
                            {% if p.rpps is not null and p.rpps is not empty %}
                                <div>RPPS: {{ p.rpps }}</div>
                            {% endif %}
                            {% if p.adeli is not null and p.adeli is not empty %}
                                <div>ADELI: {{ p.adeli }}</div>
                            {% endif %}
                            <br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div>TOTAL GÉNÉRAL :
                    {% if participations.count == 0 %}
                        {{ formation.manualParticipantCount * formation.cost }} €
                    {% else %}
                        {{ formation.getCaTotalPerMode(financeSousMode) }} €
                    {% endif %}
                </div>
            </div>
        </div>

        {% if financeSousMode is null %}
            <div class="page-break"></div>
        {% endif %}

        <div class="row">
            <div class="column small-12">
                <h4>Article 3</h4>
                <div>En cas d'inexécution totale ou partielle de l'action de formation, la société EDUPRAT Formations
                    doit rembourser au cocontractant les sommes indûment perçues de ce fait.
                    Une nouvelle session de formation sera organisée dans les meilleurs délais.
                </div>
            </div>
        </div>

        <div class="row">
            <div class="column small-12">
                <h4>Article 4</h4>
                <div>En cas de désistement du cocontractant avant la date prévue de formation, la société EDUPRAT Formations ne facturera pas celle-ci.
                </div>
            </div>
        </div>

        <div class="row">
            <div class="column small-12">
                <h4>Article 5</h4>
                <div>Le demandeur décide lui-même du nombre de personnes qu'il inscrit à une session, et ne se voit facturer que ce nombre précis.
                </div>
            </div>
        </div>

        <br><br>

        <div class="row" style="page-break-inside: avoid;">
            <div class="column small-12">
                <p>Fait, en double exemplaires, à Mérignac, le {{ "now"|date('d/m/Y') }}</p>
                <div class="row mtl">
                    <div class="column small-6">
                        <p class="mbn"><b>Pour le co-contractant</b></p>
                        <p>Signature et cachet</p>
                    </div>
                    <div class="column small-6">
                        <div><b>Pour la société Eduprat Formations</b></div>
                        <div>Signature manuscrite et cachet</div>
                        <div class="text-center">
                            <img class="mtl" width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="page-break"></div>

        <div id="conditions">

            <hr style="border-width: 3px;" class="mtn">
                <h1 class="text-center">CONDITIONS GÉNÉRALES DE VENTE</h1>
            <hr/>

            <h3 class="arrow-title">Clause n°1 : Objet</h3>

            <div class="row">
                <div class="column small-12">
                    <div>Les conditions générales de vente décrites ci-après détaillent les droits et obligations de la
                        société Eduprat Formations et de son client dans le cadre de la vente de la prestation de service
                        suivante : formation continue. Toute prestation accomplie par la société Eduprat Formations implique
                        donc l’adhésion sans réserve de l’acheteur aux présentes conditions générales de vente.
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°2 : Prix et condition de paiement</h3>

            <div class="row">
                <div class="column small-12">
                    <div>Eduprat Formations n’est pas assujettie à la TVA, nos prix sont établis en euros hors taxe. La
                        facture est adressée au client après exécution de la prestation. En cas de non prise en charge par
                        l’organisme financeur, la totalité des frais de formation peut être facturée au client. La société
                        Eduprat Formations s’engage à facturer les prestations de service commandées aux prix indiqués lors
                        de l’enregistrement de la commande.
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°3 : Règlement</h3>

            <div class="row">
                <div class="column small-12">
                    <div>L’organisme financeur paye directement la société Eduprat Formations.</div>
                    <div>Le client effectue le règlement par chèque ou par virement bancaire.</div>
                    <div>Le client peut demander une prise en charge auprès de l’organisme financeur dont il dépend.</div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°4 : Convention de formation </h3>

            <div class="row">
                <div class="column small-12">
                    <div>Nos conventions de formation font office de devis. Une convention peut être adressée sur simple
                        demande (hors prise en charge ANDPC).
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°5 : Inscription / Rétractation </h3>

            <div class="row">
                <div class="column small-12">
                    <div>L’inscription à une formation peut être faite par le client par l’un des moyens suivants :</div>
                    <ul class="mbn">
                        <li>Une inscription en ligne auprès de l’ANDPC sur : https://www.agencedpc.fr</li>
                        <li>Pour tout autre mode de financement : nous contacter :
                            <ul>
                                <li>Tél : ***********.14</li>
                                <li>Email : <EMAIL></li>
                            </ul>
                        </li>
                    </ul>
                    <div>Un accusé de confirmation de l’inscription est adressé au client dès réception de la demande
                        d’inscription.
                    </div>
                    <div>Si le client souhaite se rétracter il doit contacter Eduprat Formations aux coordonnées
                        ci-dessus.
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°6 : Prérequis</h3>

            <div class="row">
                <div class="column small-12">
                    <div>Les formations de la société Eduprat Formations ne nécessitent pas de prérequis, en cas d’exception
                        il sera indiqué sur la fiche de description de la formation.
                    </div>
                </div>
            </div>

            <div class="page-break"></div>

            <h3 class="arrow-title">Clause n°7 : Déroulement de la formation</h3>

            <div class="row">
                <div class="column small-12">
                    <div>L’Organisme de formation est libre d’utiliser les méthodes et outils pédagogiques de son choix, qui
                        relèvent de sa seule compétence.
                        Les détails des formations sont précisés sur le site Internet et sur les documents de communication
                        de l’Organisme de formation.
                    </div>
                    <div class="underline">Déroulement d’une formation</div>
                    <div class="text-center">
                        {% if formation.isVFC() %}
                            <img style="max-width: 75%;margin: 15px;" src="{{ asset("img/VFC-svg.png") }}" alt="">
                        {% elseif formation.isTcs() %}
                            <img style="max-width: 75%;margin: 15px;" src="{{ asset("img/TCS-svg.png") }}" alt="">
                        {% else %}
                            <img style=" max-width:75%; margin: 15px;" src="{{ asset("img/plaquette-etapes-invitation.jpg") }}" alt="">
                        {% endif %}
                    </div>
                    <div>L’organisme de formation peut annuler ou reporter une formation, notamment lorsque le nombre de
                        participants est jugé inapproprié.
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°8 : Renseignements et réclamations</h3>

            <div class="row">
                <div class="column small-12">
                    <div>Toute réclamation du client relative aux CGV devra être formulée par écrit :</div>
                    <ul class="mbn">
                        <li>Par courrier postal : Société Eduprat Formations 4 avenue Neil Armstrong Bâtiment Mermoz 33700 Mérignac</li>
                        <li>Par mail : <EMAIL></li>
                    </ul>
                    <div>Une demande d’information pourra être adressée par téléphone : 05 56 51 65 14 ou par mail : <EMAIL></div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°9 : Force majeure</h3>

            <div class="row">
                <div class="column small-12">
                    <div>La société Eduprat Formations ne pourra être tenue responsable à l’égard du client en cas
                        d’inexécution de ses obligations résultant d’un évènement de force majeure. Sont considérés comme
                        cas de force majeure ou cas fortuit, outre ceux habituellement reconnus par la jurisprudence des
                        cours et tribunaux français et sans que cette liste soit restrictive : la maladie ou l’accident d’un
                        consultant ou d’un animateur de formation, les grèves ou conflits sociaux internes ou externes à la
                        société Eduprat Formations, les désastres naturels, les incendies, la non obtention de visas, des
                        autorisations de travail ou d’autres permis, les lois ou règlements mis en place ultérieurement,
                        l’interruption des télécommunications, l’interruption de l’approvisionnement en énergie,
                        interruption des communications ou des transports de tout type, ou toute autre circonstance
                        échappant au contrôle raisonnable de la société Eduprat Formations.
                    </div>
                </div>
            </div>

            <h3 class="arrow-title">Clause n°10 : Propriété intellectuelle</h3>

            <div class="row">
                <div class="column small-12">
                    <div>Les supports pédagogiques en version papier ou en version numérique remis lors de la formation ou
                        accessibles via l’extranet sont la propriété d’Eduprat Formations.
                    </div>
                    <div>Le client s’interdit d’utiliser le contenu des formations et engage sa responsabilité sur le
                        fondement des articles L. 122-4 et L.335-2 et suivant du code de la propriété intellectuelle en cas
                        de cession ou de communication des contenus non autorisée. Toute reproduction, représentation,
                        modification, publication, transmission, dénaturation, totale ou partielle des contenus de
                        formations sont strictement interdites, et ce quels que soient le procédé et le support utilisés.
                    </div>
                    <div>Le client s’engage à ne pas faire de concurrence à Eduprat Formations de façon directe ou indirecte
                        en utilisant, cédant ou communiquant ces documents.
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</body>
</html>
