{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('font/stylesheet.css') }}">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <style>
        body {
            padding: 0 1cm;
            width: 960px;
            font-size: 1em;
        }

        h1 {
            font-size: 24px;
        }

        h2 {
            font-size: 20px;
        }
        hr.thinMarge {
            margin: 0.25rem 0;
        }
        p {
            margin-bottom: 0;
            line-height: 1.4;
        }
        label {
            font-size: 1em;
            display: inline;
            line-height: 1.5;
        }
        label.control-label {
            font-weight: bold;
            font-family: Arial, sans-serif;
        }
        .form-group {
            page-break-inside: avoid;
            margin: 0;
        }
        textarea {
            font-size: 1em;
        }
        textarea[rows] {
            height: 5.5em;
        }
        input[type=radio] {
            margin-top: 0.25em;
            margin-bottom: 0.5em;
        }
        .radio input[type=radio]:after {
            display: inline-block;
            content: "";
            width: 18px;
            height: 18px;
            margin: 0 0.25em 0 0;
            background: #dbdfe1;
        }
        .radio input[type=radio]:checked:after {
            background: #333333;
            border: 4px solid #dbdfe1;
        }
        .radio label {
            font-family: Arial, sans-serif;
        }

        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 10px;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input[type=checkbox]:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        .checkbox label, label+span.required {
            color: #107D85;
        }

        .checkbox {
            font-size: 0.8em;
        }

    </style>
</head>
<body>
{% for key, param in params %}
    {% if key > 0 %}
    <div class="page-break"></div>
    {% endif %}
    {% set participation = param['participation'] %}
    {% set patient = param['patient'] %}
    {% set auditId = param['auditId'] %}
    {% set audit = param['audit'] %}
    {% set form = param['form'] %}
    {% set auditLabel = param['auditLabel'] %}
    <div class="fluid960 page">
        <div class="content">
            <div class="pageWidth">
                <b>{{ participation.participant.lastName }} {{ participation.participant.firstName }}</b><br>
                <b> {% if participation.participant.rpps %} RPPS : {{ participation.participant.rpps }} {% elseif participation.participant.adeli %} ADELI : {{ participation.participant.adeli }} {% endif %} <b>
                <hr style="border-width: 5px;" class="mtn mbs">
                {% if patient == 1 %}
                {% set formType = auditId == 1 ? participation.formation.formTypePre : participation.formation.formTypePost%}
                    <h1 class="text-center mbn mtn">{{ participation.formation.programme.title }}</h1>
                    <h2 class="text-center mbn mtn">{{ ("front.label.formType." ~ formType ~ ".singular") | trans }} {% if auditId == 1 %}pré{% else %}post{% endif %}-formation</h2>
                    <hr class="mts">
                {% endif %}
                <h4 class="blue">{{ "audit.patient" | trans }} n°{{ patient }} / {{ audit.nbPatients }}</h4>
                {% if patient == 1 %}
                    <div>
                        {% if audit.isDefaultType() %}
                            <div>{{ "audit.notice.info" | trans({"%patient%": audit.nbPatients}) }}</div>
                        {% endif %}
                        {% if audit.introduction > '' %}
                            {{ audit.introduction | raw }}
                        {% else %}
                            <div>{{ "audit.notice.more" | trans | raw }} {{ "audit.notice.list.1" | trans | raw }}</div>
                            <br>
                            <div class="blue">{{ "audit.notice.list.2" | trans | raw }}</div>
                            <div>{{ "audit.notice.list.3" | trans | raw }}</div>
                        {% endif %}
                    </div>
                {% endif %}
                <div id="form">
                    {{ form_start(form) }}
                    {% include "pdf/form_images.html.twig" %}
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                </div>
            </div>

            {% if patient == audit.nbPatients %}
                {% if (auditId == 1 and participation.validTimeAudit1) or (auditId == 2 and participation.validTimeAudit2) %}
                    <div>
                        <img src="{{ absolute_url(asset('img/yes.png')) }}"> {{ "audit.validTempsMiniNew" | trans({"%type%" : (("front.label.formType." ~ formType ~ ".singular") | trans), '%nbPatients%': audit.nbPatients}) }} {% if auditId == 1 %}pré{% else %}post{% endif %}-formation
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endfor %}

<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script>

	function autoheight(a) {
		if (!$(a).prop('scrollTop')) {
			do {
				var b = $(a).prop('scrollHeight');
				var h = $(a).height();
				$(a).height(h - 5);
			}
			while (b && (b != $(a).prop('scrollHeight')));
		};
		$(a).height($(a).prop('scrollHeight') + 20);
	}

    $(document).ready(function () {
//        var h = document.getElementById('table-left').clientHeight;
//        var marged = false;
//        $.each($('.participant-cell'), function(index, val) {
//            if ($(val).offset().top > 1310 && !marged) {
//                val.style["margin-top"] = "100px";
//                marged = true;
//                h += 106;
//            }
//        });
//        document.getElementById('table-right').style.height = h + 2;
//        $('#table-right').find('tbody').find('tr').last().css('height', (h - 36 - 45 - ($('#table-right').find('tbody').find('tr').length - 1) * 30) + "px");

		$.each($("textarea"), function(index, val) {
			autoheight($(val));
		});

    });
</script>
</body>
</html>
