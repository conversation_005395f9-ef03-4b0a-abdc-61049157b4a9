{% import _self as utils %}
<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ absolute_url(asset('font/stylesheet.css')) }}">
    <style>
        body {
            padding: 0 45px;
            width: 21cm;
            font-size: 0.9em;
        }

        .title {
            text-transform: uppercase;
            border: 1px double #109199;
            padding: 1em;
            width: 100%;
            text-align: center;
            font-weight: bold;
            margin-bottom: 2em;
        }

        .subtitle {
            text-transform: uppercase;
            border: 1px double #109199;
            padding: 0.25em;
            width: 100%;
            font-weight: bold;
            font-style: italic;
            margin-bottom: 1em;
        }

        .article {
            text-transform: uppercase;
            padding: 0.25em 0;
            font-weight: bold;
            width: 100%;
            margin-bottom: 1.3em;
            font-size: 1.1em;
        }

        .article-nb {
            margin-right: 0.5em;
            font-weight: bold;
            font-size: 1.25em;
        }

        label {
            font-weight: bold;
            color: #109199;
            /*font-size: 1rem;*/
        }

        @font-face {
            font-family: 'nimbus_sans_lregular';
            src: url('/font/NimbusSans-Regular.otf') format('opentype');
            font-weight: normal;
            font-style: normal;
        }
        p {
            font-family: "nimbus_sans_lregular";
            margin-bottom: 1.5rem;
            line-height: 1.4;
        }
        .annexe p {
            margin-bottom: 0;
        }

        .art {
            page-break-inside: avoid;
        }

        .art-group p:last-child {
            margin-bottom: 2em;
        }

        h1 {
            font-size: 30px;
            margin-bottom: 0;
        }

        h2 {
            font-family: stimupratbold, sans-serif;
            font-size: 22px;
            font-weight: 600;
        }

        h3.arrow-title {
            margin-top: 10px;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .bold {
            color: #0c0c0c;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div>
            <hr class="mbm">
            <h1 class="text-center">Convention de formateur dpc</h1>
            <h2 class="text-center">Contrat formateur {% if formation.programme.isClasseVirtuelle %}classe virtuelle{% endif %}</h2>
            <hr class="mtn">
        </div>

        <h3 class="arrow-title">Entre les soussignes</h3>
        <div class="row">
            <div class="column small-12">
                <p><span class="ttu mbl bold">Sas eduprat formations,</span> société par actions simplifiée, immatriculée au Registre du
                    Commerce et des Sociétés de Bordeaux sous le numéro 799.435.995, dont le siège social se
                    situe 4, Avenue Neil Armstrong - Bâtiment Mermoz - 33700 MERIGNAC, représentée par son
                    Président en exercice, Monsieur Philippe PLAS, dument habilité à l’effet des présentes</p>
                <p class="mtl">(Ci-après, séparément dénommée une « Partie » et collectivement les « Parties »)</p>
                <br>
                <br>
            </div>
        </div>
        <div class="row">
            <div class="column small-5">
                <p class="mbn">Ci-après dénommée « EDUPRAT »</p>
                <p class="ttu mbl bold">D'une part,</p>
            </div>
            <div class="column small-2">
                <p class="ttu mbl"><b>Et</b></p>
            </div>
            <div class="column small-5">
                <div><b>{% if former.person.civility %}{{ former.person.civility }}{% else %}Monsieur/Madame{% endif %} {{ former.person.firstname }} {{ former.person.lastname | upper }}</b></div>
                <p>Adresse :<br>
                    {{ former.person.address }}<br>
                    {% if former.person.address2 is not null %}
                        {{ former.person.address2 }}<br>
                    {% endif %}
                    {{ former.person.zipCode }}, {{  former.person.city }}<br>
                    Siret: {{ former.person.siret }}
                </p>
                <div class="mbn">Ci-après dénommé(e) le « Formateur »</div>
                <p class="ttu mbl"><b>D'autre part,</b></p>
            </div>
        </div>

        <div class="page-break"></div>

        <h3 class="arrow-title">IL EST TOUT D'ABORD EXPOSE CE QUI SUIT :</h3>

        <div class="row">
            <div class="column small-12">
                <p class="mbm">EDUPRAT est un organisme de développement professionnel continu consacré d’une part, à
                    l'analyse et l'amélioration des pratiques professionnelles du secteur médical et, d’autre part, à
                    l'approfondissement et l'actualisation des connaissances.</p>
                <p class="mbm">EDUPRAT fait appel à des praticiens médicaux de renom afin de dispenser les diverses
                    formations éligibles au Développement Professionnel Continu (D.P.C.) qu’elle propose aux
                    praticiens (médecins, pharmaciens, kinésithérapeutes, infirmiers, podologues, sages-femmes,
                    orthoptistes et orthophonistes).</p>
                <p class="mbm">Le Formateur est un membre du corps médical titulaire des diplômes, de l’expérience
                    professionnelle et de la notoriété nécessaires à l’organisation de formations auprès de ses
                    confrères et des professionnels de santé.</p>
                <p class="mbm">EDUPRAT a ainsi sollicité le Formateur afin que ce dernier lui fournisse ses services en matière
                    de formation des professionnels du secteur médical, proposition que le Formateur a acceptée eu
                    égard à l’expérience qu’il a acquise en la matière.</p>
                <p class="mbm">Les Parties se sont par conséquent rapprochées afin d’arrêter les termes de la présente
                    convention.</p>
            </div>
        </div>

        <h3 class="arrow-title">EN CONSEQUENCE DE QUOI IL A ETE CONVENU DE CE QUI SUIT :</h3>
        <div class="row">
            <div class="column small-12">
                <h4 class="article"><span class="blue">ARTICLE 1 :</span> OBJET DE LA CONVENTION</h4>
                <div class="art-group">
                    <p class="art"><span class="article-nb">1.1.</span>Le présent Contrat a pour objet de définir les conditions dans lesquelles le Formateur
                        conçoit et dispense, à la demande d’EDUPRAT, la formation éligible au titre du D.P.C. dont
                        les principaux éléments sont définis en <b>Annexe&nbsp;1</b> des présentes (ci-après la « Formation »).</p>

                    <p class="art"><span class="article-nb">1.2.</span>Les Parties déclarent expressément qu'elles sont et demeureront, pendant toute la durée
                        du présent Contrat, des partenaires professionnels indépendants. En conséquence, aucune
                        disposition du présent Contrat ne saurait ainsi être interprétée comme établissant entre les
                        Parties soit une société de fait, soit un quelconque lien de subordination de nature à caractériser
                        l’existence d’un contrat de travail soumis au code du même nom.</p>
                </div>

                <h4 class="article"><span class="blue">ARTICLE 2 :</span> OBLIGATIONS DU FORMATEUR</h4>
                <div class="art-group">
                    <p class="art"><span class="article-nb">2.1.</span>Le Formateur s’engage à consacrer à l’exécution de la Formation tout le soin et toute la
                        disponibilité nécessaire. Il organisera, en sa qualité de formateur indépendant, son activité à sa
                        seule discrétion, à l’exception toutefois des dates et lieux des Formations fixées d’un commun
                        accord entre les Parties en <b>Annexe&nbsp;1</b> des présentes.</p>
                    {% if formation.programme.isClasseVirtuelle %}
                        <p class="art"><span class="article-nb">2.2.</span>La mission confiée par EDUPRAT au
                            Formateur comprend la dispense de la Formation en classe virtuelle basée sur un programme de
                            formation fourni par EDUPRAT.</p>
                    {% else %}
                        <p class="art"><span class="article-nb">2.2.</span>La mission confiée par EDUPRAT au Formateur comprend ainsi en particulier :
                            <b>(a)</b> la
                            détermination du thème de la Formation ; <b>(b)</b> l’établissement du programme de la Formation ;
                            <b>(c)</b> la dispense de la Formation aux dates et lieux convenus ; <b>(d)</b> l’établissement et la remise à
                            EDUPRAT du support écrit de la Formation sous format « pdf » et/ou d’un livret pédagogique
                            destiné(s) aux participants dans le cadre d’une formation hors programme EDUPRAT.</p>

                        <p class="art"><span class="article-nb">2.3.</span>Dans le cadre d’une formation hors programme EDUPRAT, le Formateur garantit à
                            EDUPRAT qu’il est le seul et unique auteur de la Formation, de son support ainsi que du livret
                            pédagogique et, plus généralement, qu’aucune autre personne physique et/ou morale n’a
                            participé à leur conception, de quelque manière que ce soit. Le Formateur garantit en particulier
                            EDUPRAT contre toute réclamation revendication, action en contrefaçon et/ou en concurrence
                            déloyale ou en parasitisme qui pourrait être intentée à son encontre du fait de l’exploitation des
                            éléments précités.</p>
                    {% endif %}
                </div>

                <br>

                <h4 class="article"><span class="blue">ARTICLE 3 :</span> DISPOSITIONS FINANCIERES</h4>
                <div class="art-group">
                    {% if formation.programme.isClasseVirtuelle %}
                        <p class="art"><span class="article-nb">3.1.</span>En contrepartie des prestations de
                            formation rendues en exécution du présent Contrat et de la cession des droits d’image de la
                            captation vidéo, telle que définie à l’article 5 des présentes, EDUPRAT s’engage à verser au
                            Formateur l’honoraire forfaitaire fixé en <b>Annexe&nbsp;1</b> du présent Contrat.</p>
                    {% else %}
                        <p class="art"><span class="article-nb">3.1.</span>En contrepartie des prestations de formation rendues en exécution du présent Contrat,
                            EDUPRAT s’engage à verser au Formateur l’honoraire forfaitaire fixé en <b>Annexe&nbsp;1</b> du présent
                            Contrat.</p>
                    {% endif %}

                    <p class="art"><span class="article-nb">3.2.</span>Le Formateur facturera ses honoraires à EDUPRAT une fois la Formation accomplie.</p>

                    <p class="art"><span class="article-nb">3.3.</span>Les factures d’honoraires ainsi émises par le Formateur seront payables par virement
                        bancaire <b>TRENTE (30) jours fin de mois</b> après la date d’émission de sa facture. Toute somme non réglée
                        à échéance supportera des frais financiers à concurrence de trois fois le taux d’intérêt légal.</p>

                    <p class="art"><span class="article-nb">3.4.</span>Le Formateur sera seul responsable du paiement de ses impôts et charges sociales, le tout
                        de telle manière qu’EDUPRAT ne puisse pas être inquiétée à cet égard. Le Formateur fournira,
                        à première demande d’EDUPRAT, la justification de son immatriculation auprès de la recette
                        des impôts pour le règlement de la TVA et des organismes sociaux, ainsi que la justification du
                        paiement des contributions et impôts correspondants.</p>
                </div>

                <h4 class="article"><span class="blue">ARTICLE 4 :</span> DURÉE</h4>

                <div class="art-group">
                    <p class="art"><span class="article-nb">4.1.</span>Le présent Contrat, qui prend effet à compter de sa signature, est conclu pour une durée
                        déterminée qui expirera automatiquement et de plein droit à l’issue de la Formation détaillée en
                        <b>Annexe&nbsp;1.</b></p>

                    <p class="art"><span class="article-nb">4.2.</span>Il ne se renouvellera en aucun cas par tacite reconduction. En conséquence, les Parties
                        seront libres, à son expiration, de renégocier un nouveau contrat ayant pour objet une autre
                        formation.</p>

                    <p class="art"><span class="article-nb">4.3.</span>Le présent Contrat pourra enfin être résilié par anticipation, par l’une ou l’autre des
                        Parties, en cas d’inexécution de l’une quelconque des obligations y figurant.</p>
                </div>

                {% if formation.programme.isClasseVirtuelle %}

                <h4 class="article"><span class="blue">ARTICLE 5 :</span> PROPRIETE DES PROGRAMMES DE FORMATION</h4>

                <div class="art-group">
                    <p>A compter de la signature du Contrat, le Formateur cède à EDUPRAT l’ensemble des droits
                        d’exploitation de la captation vidéo réalisée dans le cadre de la classe virtuelle pour le
                        compte d’EDUPRAT conformément au présent Contrat.</p>
                    <p>Cette exploitation des programmes de formation pourra être librement réalisée par EDUPRAT, à
                        titre gratuit ou onéreux, par quelque moyen ou procédé, sur quelque support et/ou réseau, sans
                        limitation, directement ou indirectement, avec le concours de tout tiers, auprès de tout public
                        notamment auprès de tous professionnels de santé et pour toutes finalités notamment pour des
                        actions de formation de DPC menées par EDUPRAT auprès de professionnels de santé.</p>
                    <p>Le Formateur renonce expressément à ce que son nom soit mentionné par EDUPRAT sur tout support
                        reproduisant le ou les programmes de formation conçus au titre des présentes.</p>
                </div>
                {% endif %}

                <h4 class="article"><span class="blue">ARTICLE {% if formation.programme.isClasseVirtuelle %}6{% else%}5{% endif%} :</span> COMPORTEMENT LOYAL ET DE BONNE FOI</h4>

                <div class="art-group">
                    <p>Chaque Partie s'engage à se comporter vis-à-vis de l’autre Partie, comme un partenaire loyal et
                        de bonne foi, et notamment, à porter sans délai à la connaissance de l’autre Partie, tout différend
                        ou toute difficulté qu'il pourrait rencontrer dans le cadre de l'exécution du présent Contrat.</p>
                </div>

                <h4 class="article"><span class="blue">ARTICLE {% if formation.programme.isClasseVirtuelle %}7{% else%}6{% endif%} :</span> MODIFICATIONS DU CONTRAT</h4>

                <div class="art-group">
                    <p>Le présent Contrat ne pourra être modifié, en cours d'exécution, que d'un commun accord entre
                        les Parties, par voie d'avenant écrit, signé par chacune d'elles.</p>
                </div>

                <br>
                {% if not formation.programme.isClasseVirtuelle %}
                <br>
                <br>
                {% endif %}


                <div>Fait à Mérignac,</div>
                <p>En deux (2) exemplaires originaux,</p>
                <p>Le {{ "now"|date('d/m/Y') }}</p>
                <div class="row mtl">
                    <div class="column small-6">
                        <p class="mbn"><b>Pour le Formateur</b></p>
                        <p>Signature manuscrite</p>
                    </div>
                    <div class="column small-6">
                        <div><b>Pour la SAS Eduprat Formations</b></div>
                        <div>Philippe PLAS, Président</div>
                        <div>Signature manuscrite et cachet</div>
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat">
                    </div>
                </div>
            </div>
        </div>

        <div class="page-break"></div>
        <div class="annexe">
            <div>
                <hr class="mbm">
                <h1 class="text-center">Annexe&nbsp;1 : Formation</h1>
                <h2 class="text-center">Contrat formateur</h2>
                <hr>
            </div>

            <div class="row mbl">
                <div class="column small-3">
                    <label for="">Intitulé de la formation</label>
                </div>
                <div class="column small-1-offset small-8">
                    {{ former.formation.programme.title }}
                </div>
            </div>

            <div class="row mbl">
                <div class="column small-3">
                    <label for="">Date</label>
                </div>
                <div class="column small-1-offset small-8">
                    Du {{ formation.openingDate | date('d/m/Y') }} au {{ formation.closingDate | date('d/m/Y') }}<br>
                    {% if formation.isAudit or formation.isPresentielle or formation.isTcs %}
                        {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
                            Avec une session présentielle en réunion le {{ formation.startDate | date('d/m/Y') }}<br>
                        {% else %}
                            Avec une session présentielle en réunion du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}<br>
                        {% endif %}
                    {% endif %}
                    {% if formation.programme.isClasseVirtuelle %}
                        Formation type « classe virtuelle »<br>
                    {% endif %}
                </div>
            </div>


            <div class="row mbl">
                <div class="column small-3">
                    {% if formation.programme.isClasseVirtuelle %}
                        <label for="">Durée de la classe virtuelle</label>
                    {% else %}
                        <label for="">Durée de l'étape présentielle</label>
                    {% endif %}
                </div>
                <div class="column small-1-offset small-8">
                    {% if formation.programme.durationPresentielle is not null %}
                        {{ formation.programme.durationPresentielle }} heure{% if formation.programme.durationPresentielle > 1 %}s{% endif %}
                    {% else %}
                        0 heure
                    {% endif %}
                </div>
            </div>

            <div class="row mbl">
                <div class="column small-3">
                    <label for="">Lieu(x)</label>
                </div>
                <div class="column small-1-offset small-8">
                    <p>{{ formation.address }}</p>
                    <p>{{ formation.address2 }}</p>
                    <p>{{ formation.zipCode }}</p>
                    <p>{{ formation.city }}</p>
                </div>
            </div>

            <div class="row mbl">
                <div class="column small-3">
                    <label for="">Honoraires du formateur</label>
                </div>
                <div class="column small-1-offset small-8">
                    {{ former.honorary }} €uros HT
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
