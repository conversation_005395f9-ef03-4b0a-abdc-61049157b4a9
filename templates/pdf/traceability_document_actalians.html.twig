<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff;
        }

        table {
            font-size: 0.85em;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 40px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }

        ul {
            list-style-position: inside;
        }        

        .wrap {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .text-box1 {
            height: 302px;
            border: 1px solid black;
        }

        .text-box2 {
            height: 148px;
            border: 1px solid black;
        }

        .text-box-header {
            background-color: #d9e2f3;
            height: 20px;
            line-height: 20px;
            font-size: 9px;
        }

        .text-box-text1 {
            padding: 1em;
            font-size: 10px;
            line-height: 11px;
        }

        .text-box-text2 {
            padding: 1em;
            font-size: 10px;
            line-height: 10px;
        }

        .text-box-text1 p, .text-box-text2 p {
            line-height: 11px;
        }

        .bold {
            font-weight: bold;
        }

        .center {
            text-align: center;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row">
                <div class="column large-12">
                    <div><span class="bold">{{ "traceability.organization_name" | trans }}</span> : {{ "traceability.organization_name_value" | trans }}</div>
                </div>
            </div>

            <div class="row mtm mbm">
                <div class="column large-12">
                    <div><span class="bold">{{ "traceability.vol_hour" | trans }}</span> : 7h, dont 3 heures présentielles et 4 heures non présentielles</div>
                </div>
            </div>

            <div class="row mtm mbm text-box1">
                <div class="text-box-header">
                    <div class="column small-4 text-box-header prn">
                        <div><span class="bold">Description des activités non présentielles :</span></div>
                    </div>
                    <div class="column small-5 text-box-header pln prn" style="width: 46%">
                        <div><span class="bold">adresses url : https://extranet.eduprat.fr/ et https://360learning.fr/</span></div>
                    </div>
                    <div class="column small-3 text-box-header pln" style="width: 20%">
                        <div><span class="bold">volume horaire déclaré : 4h</span></div>
                    </div>
                </div>
                <div class="text-box-text1">
                    <p>
                        <span class="bold">Etape 1 : Formation e-learning (3h)</span><br>
                        Dans cette étape, le programme est découpé en modules ou e-leçons afin de répondre aux objectifs de manière structurée (introduction, rappels, contenu et résumé).
                        L’actualisation des connaissances s’effectue ainsi sous forme d’auto-apprentissage (didacticiel d’apprentissage numérique).
                    </p>
                    <p>
                        <span class="bold">Un questionnaire pré e-learning</span><br>
                        évalue les connaissances et les pratiques professionnelles de l'apprenant.
                    </p>
                    <p>
                        <span class="bold">Un questionnaire post e-learning</span><br>
                        reprend les questions "connaissances" précédentes afin d'évaluer l'acquisition des connaissances.
                    </p>
                    <p>
                        Le questionnaire de connaissance et de compétence balaye différents domaines selon les thématiques des programmes (épidémiologie, législation, physiopathologie,
                        connaissance du médicament, diagnostic, interrogatoire au comptoir, détection d'interactions médicamenteuses, conseils associés et éducation thérapeutique) ce qui permet de
                    </p>
                    <p>
                        <span class="bold">Etape 3 : Evaluation et suivi : Restitution des résultats (1h)</span><br>
                        Un deuxième questionnaire (identique au questionnaire pré e-learning) devra être réalisé durant les quinze jours à distance de la formation.<br>
                        Il permet de mesurer l’impact de la formation présentielle et d’évaluer le suivi des axes d’amélioration.<br>
                        Une fiche d’aide à la progression sera transmise à l’apprenant :<br>
                        Elle comporte les résultats des questionnaires, les acquis, les axes de progrès et l’identification des actions d’amélioration.<br>
                        Remise des documents et supports pédagogiques via la plateforme.<br>
                        Enquête de satisfaction :<br>
                        Complétée par l’apprenant en fin de formation dans une démarche de qualité interne.
                    </p>
                </div>
            </div>

            <div class="row mtm mbm text-box2">
                <div class="text-box-header">
                    <div class="column small-6">
                        <div><span class="bold">Description des activités présentielles :</span></div>
                    </div>
                    <div class="column small-3">
                        <div>
                            <span class="bold">volume horaire déclaré : 3h</span>
                        </div>
                    </div>
                </div>
                <div class="text-box-text2">
                    <p>
                        <span class="bold">Etape 2 : Formation présentielle (3h)</span><br>
                        La méthodologie proposée par Eduprat est basée sur une mise en pratique des connaissances acquises au cours du e-learning. Pour cela l'apprenant est mis en situation sur des
                        {{ ('front.label.' ~ formation.displayType ~ '.plural') | trans }} fictifs, afin d'aborder toutes les possibilités de l'accueil au comptoir (demande spontanée du patient ou délivrance d'ordonnance). Cette méthode dite active permet
                        aux participants d’être confrontés à la réalité de leurs pratiques et aux difficultés rencontrées et ainsi d'évoluer dans leur pratiques professionnelles.
                        L’expert formateur commentera ensuite ces cas concrets afin de permettre d’acquérir ou d’approfondir les compétences de l'apprenant (par méthode affirmative, démonstrative
                        et expositive). Il s’appuiera sur les récentes données scientifiques et les dernières recommandations HAS afin d'actualiser les pratiques et les connaissances des professionnels.
                        Au cours de cette étape une synthèse collective anonyme des données permet aux participants de se positionner par rapport au reste du groupe et d'établir ainsi son plan
                        d’amélioration centré sur l'activité officinale.
                    </p>
                </div>
            </div>

            <div class="row mtm mbm">
                <div class="column large-12">
                    <table>
                        <thead>
                        <tr>
                            <th>{{ "admin.participant.lastname" | trans | upper }}</th>
                            <th>{{ "admin.participant.firstname" | trans }}</th>
                            <th>Etablissement</th>
                            <th>A réalisé les activités<br>
                                présentielles<br>
                                OUI/NON</th>
                            <th>{{ "traceability.ip_adress" | trans }}</th>
                            <th>{{ "traceability.first_connection" | trans }}</th>
                            <th>{{ "traceability.last_connection" | trans }}</th>
                            <th>{{ "traceability.total_time" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participation in participations %}
                            <tr>
                                <td class="wrap">{{ participation.participant.lastname | upper }}</td>
                                <td class="wrap">{{ participation.participant.firstname }}</td>
                                <td class="center">
                                    {% for financeSousMode in participation.formation.financeSousModes %}
                                        {{ financeSousMode.name }} <br>
                                    {% endfor %} 
                                </td>
                                <td class="center">{{ "traceability.yes" | trans | upper }}</td>
                                <td class="center">{{ participation.ip }}</td>
                                {% if participation.startedAtLogs is null %}
                                    <td class="center"></td>
                                {% else %}
                                    <td class="center">{{ participation.startedAtLogs|date('d/m/Y') }} {{ participation.startedAtLogs|date('H\\hi') }}</td>
                                {% endif %}
                                {% if participation.finishedAtLogs is null %}
                                    <td class="center"></td>
                                {% else %}
                                    <td class="center">{{ participation.finishedAtLogs|date('d/m/Y') }} {{ participation.finishedAtLogs|date('H\\hi') }}</td>
                                {% endif %}
                                {% if participation.totalTimeText %}
                                    <td class="center">{{ participation.totalTimeText }}</td>
                                {% else %}
                                    <td></td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row mbm mtm">              
            </div>

            <div class="nbi">
                <div class="row mbm">
                    <div class="column large-12">
                        Je soussigné Monsieur Philippe PLAS agissant en ma qualité de Président de l'organisme EDUPRAT Formations atteste que les personnes dont les noms
                        figurent ci-dessus ont suivi des séquences non présentielles de la formation {{ formation.programme.title }} sur la période du {{ formation.openingDate | date('d/m/Y') }} au {{ formation.closingDate | date('d/m/Y') }}.
                        Je tiens à la disposition de ACTALIANS les documents attestant de ces participations.
                    </div>
                </div>
                <div class="row">
                    <div class="column small-4">
                        {{ "traceability.signature" | trans }} : <br><br>
                        <img width="200" src="{{ absolute_url(asset('/img/signature.png')) }}" alt="signature edpurat" />
                    </div>
                    <div class="column small-4">
                        à Mérignac
                    </div>
                    <div class="column small-4">
                        le {{ formation.closingDate | date('d/m/Y') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
