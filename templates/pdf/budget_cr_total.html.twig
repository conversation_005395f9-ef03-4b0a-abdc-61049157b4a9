<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('font/stylesheet.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/gotenberg.css')) }}" rel="stylesheet">
    <style>
        h1 {
            font-size: 28px;
        }

        body {
            padding: 0 1cm;
            font-size: 0.8em;
        }

        table thead, table tbody {
            border: none;
        }

        table thead th {
            text-align: center;
            padding: 0.25em 1em;
            border: none;
            font-weight: bold;
            background: #fff; 
        }

        table {
            font-size: 0.85em;
            width: 100%;
            margin: auto;
            page-break-inside: avoid;
        }

        .page {
            overflow: hidden;
            page-break-after: always;
        }

        .content {
            margin-top: 0px;
        }

        table tbody tr:nth-child(even) {
            background-color: #d9e2f3;
        }

        table tbody tr {
            border-top: 1px solid #8eaadb;
        }

        table tbody tr:last-child {
            border-bottom: 1px solid #8eaadb;
        }

        table tbody td:not(:last-child) {
            border-right: 1px solid #8eaadb;
        }

        tr, td div, th div{
            page-break-inside: avoid;
        }    


        .wrapleft {
            width: 30%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .wrapcenter {
            width: 50%;
            word-break: break-all;
            word-wrap: break-word;
        }

        .wrapcenterspan {
            word-break: break-all;
            word-wrap: break-word;
            text-align: right;
        }

        .wrapright {
            width: 20%;
            word-break: break-all;
            word-wrap: break-word;
            text-align: right;
        }

        .bold {
            font-weight: bold !important;
            font-family: sans-serif !important;
        }

        .center {
            text-align: center;
        }

        .details {
            margin: 1em 0 2em 0;
        }

        .avoid-page-break {
            page-break-inside: avoid;
        }

        .info-italic {
            font-size: 0.7em;
            font-style: italic;
            margin-top: 0;
            margin-bottom: 0;
        }

        .subcost {
            padding-left: 16px;
        }

        .subcost-2 {
            padding-left: 32px;
        }

        .rappel table {
            margin-top: 20px;
        }

    </style>
</head>
<body>
<div class="fluid960 page pageCentered">
    <div class="content">
        <div class="pageWidth">
            <div class="row mtm mbm">
                <hr style="border-width: 2px;" class="mtn mbs">
                <h1 class="text-center mbn mtn">
                    Honoraires coordinateur
                </h1>
                <hr class="mts" style="border-width: 1px">
                <div class="row mtm mbm">
                    <div class="column large-12">
                        <div><span class="bold">{{ "budgetCRTotal.coordinator" | trans }}</span> : {{ budgetCR['coordinator'].person.lastname }} {{ budgetCR['coordinator'].person.firstname }}</div>
                    </div>
                    <div class="column large-12">
                        <div><span class="bold">{{ "budgetCRTotal.title" | trans }}</span> : {{ formation.programme.title }}</div>
                    </div>
                    <div class="column large-12">
                        <div><span class="bold">{{ "budgetCRTotal.sessions" | trans }}</span> : 
                        {{ formation.programme.reference }}{% if formation.sessionNumber %} session n° {{ formation.sessionNumber }}{% endif %}
                        </div>
                    </div>
                    <div class="column large-12">
                        {% if openingDate|date('d/m/Y') == closingDate|date('d/m/Y') %}
                            <div>
                                <span class="bold">{{ "compensation.date" | trans }}</span> : {{ openingDate | date("d/m/Y") }}
                            </div>
                        {% else %}
                            <div>
                                <span class="bold">{{ "compensation.date" | trans }}</span> : {{ openingDate | date("d/m/Y") }} - {{ closingDate | date("d/m/Y")  }}
                            </div>
                        {% endif %} 
                    </div>
                    <div class="column large-12">
                        <div><span class="bold">{{ "budgetCRTotal.location" | trans }}</span> : {{ formation.city }}</div>
                    </div>
                    <div class="column large-12">
                        <div><span class="bold">{{ "budgetCRTotal.orateur" | trans }}</span> :

                        {% set formateurs = formation.formateurs %}
                        {% if formateurs|length %}
                            {% for formateur in formateurs %}
                                {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                            {% endfor %}
                        {% else %}
                            /
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mtm mbm">
            </div>
            
            <div class="row mtm mbm">
                <div class="mtm column large-12 details">
                <h5>{{ "budgetCRTotal.totalFormation" | trans }}</h5>
                    <table>
                        {% if not newHonorary %}
                        <thead>
                        <tr>
                            <th>{{ "budgetCRTotal.refFormation" | trans }}</th>
                            <th>{{ "budgetCRTotal.NameFormation" | trans }}</th>
                            <th>{{ "budgetCRTotal.budgetCR" | trans }}</th>
                        </tr>
                        </thead>
                        {% endif %}
                        <tbody>
                        {% if newHonorary %}
                            {% set honorary = n1 ? coordinatorObject.honoraryN1 : coordinatorObject.honorary %}
                            {% if honorary %}
                                <tr>
                                    <td class="wrapleft">{{ "budgetCRTotal.budgetCR" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">{{ honorary|number_format(2, '.', ' ') }} €</td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td class="wrapleft">{{ "budgetCRTotal.ca" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">{{ coordinatorObject.caTotal(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <td class="wrapleft subcost">- {{ "budgetCRTotal.formationCost" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.formationCost(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>

                                <tr>
                                    <td class="wrapleft bold">{{ "budgetCRTotal.commissionTheorique" | trans }}</td>
                                    <td class="wrapcenterspan bold"  colspan="2">{{ coordinatorObject.commissionTheorique(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <td class="wrapleft subcost">- Avances formation :</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.avancesCost(n1)|number_format(2, '.', ' ') }} €</td>
                                </tr>

                            {% endif %}
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.commissionsTtc" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ budgetCR['honorary'].total|number_format(2, '.', ' ') }} €</td>
                            </tr>
                        {% else %}
                            {% for key,formation in budgetCR['honorary'] %}
                                {% if key != 'total' and key != 'totalTtc' and key != 'formateursSupplement' and key != 'newHonorary' %}
                                    <tr>
                                        <td class="wrapleft">{{ key }} </td>
                                        <td class="wrapcenter">{{ formation.title }} </td>
                                        <td class="wrapright">{{ formation.total }} €</td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                            <tr>
                                <td class="wrapleft">- {{ "budgetCRTotal.restaurationHonorary" | trans }}</td>
                                <td class="wrapcenterspan"  colspan="2">- {{ restaurationHonorary|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            {% if budgetCR['honorary'].formateursSupplement %}
                                <tr>
                                    <td class="wrapleft">- {{ "budgetCRTotal.formateursSupplement" | trans }}</td>
                                    <td class="wrapcenterspan"  colspan="2">- {{ budgetCR['honorary'].formateursSupplement|number_format(2, '.', ' ') }} €</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.total" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ budgetCR['honorary'].total|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <td class="bold wrapleft">{{ "budgetCRTotal.totalTtc" | trans | upper }}</td>
                                <td class="bold wrapcenterspan" colspan="2">{{ budgetCR['honorary'].totalTtc|number_format(2, '.', ' ') }} €</td>
                            </tr>
                        {% endif %}
                        </tbody>
                    </table>

                    {% if not newHonorary %}
                        <p class="info-italic">{{ "budgetCRTotal.info3"|trans }}</p>
                    {% endif %}

                </div>
            </div>
            {% if newHonorary %}
                <div></div>
                <div class="row mbm">
                    <div class="column large-12 details rappel">
                        <table>
                            <tbody>
                            <tr>
                                <th class="text-center" colspan="2">{{ "budgetCRTotal.rappel" | trans }}</th>
                            </tr>
                            <tr>
                                <td class="bold text-center">{{ "budgetCRTotal.particiants" | trans }}</td>
                                <td class="bold text-center">{{ "budgetCRTotal.montants" | trans }}</td>
                            </tr>
                            {% for price, detail in caDetails %}
                                <tr>
                                    <td class="wrapleft">{{ detail.count }}</td>
                                    <td class="wrapcenterspan" colspan="2">
                                        {% if detail.price is null %}
                                            NC
                                        {% else %}
                                            {{ detail.price|number_format(2, '.', ' ') }} €
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                            <tr>
                                <td class="bold wrapleft">Total : {{ coordinatorObject.participantCount }}</td>
                                <td class="bold wrapcenterspan">{{ coordinatorObject.caTotal(n1)|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            </tbody>
                        </table>
                        <table>
                            <tbody>
                            <tr>
                                <th class="text-center" colspan="2">Détail coût formation</th>
                            </tr>
                            <tr>
                                <td class="wrapleft bold">{{ "budgetCRTotal.formationCost" | trans }}</td>
                                <td class="wrapcenterspan bold"  colspan="2">{{ coordinatorObject.formationCost(n1)|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            {% if formation.programme.isSurSite and showCosts %}
                                {% if coordinatorObject.surchargedCostFormers(n1) > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costFormers" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostFormers(n1)|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.surchargedCostKilometres > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costKilometres" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostKilometres|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.surchargedCostBadges > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costBadges" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostBadges|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.surchargedCostRetrocessions > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costRetrocessions" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostRetrocessions|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.surchargedCostMateriel > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costMateriel" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostMateriel|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.surchargedCostDiversFormation > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costDivers" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.surchargedCostDiversFormation|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                            {% endif %}
                            </tbody>
                        </table>
                        <table>
                            <tbody>
                            <tr>
                                <th class="text-center" colspan="2">Détail avances formation</th>
                            </tr>

                            <tr>
                                <td class="wrapleft bold">Avances formation :</td>
                                <td class="wrapcenterspan bold"  colspan="2">{{ coordinatorObject.avancesCost(n1)|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            {% if showCosts %}
                                {% if coordinatorObject.costRepas > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costRepas" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.costRepas|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}

                                {% if coordinatorObject.costSalle > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costSalle" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.costSalle|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.costTimbres > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costTimbres" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.costTimbres|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                {% if coordinatorObject.costDivers > 0 %}
                                    <tr>
                                        <td class="wrapleft">{{ "admin.formation.costDivers" | trans }}</td>
                                        <td class="wrapcenterspan"  colspan="2">- {{ coordinatorObject.costDivers|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                            {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% else %}
                <h4>{{ "budgetCRTotal.totalSession" | trans }}</h4>
                {% for formationRef,compensations in formations %}
                    <div class="row mbm">
                        <div class="column large-12 details">
                            <table>
                                <tbody>
                                <thead>
                                <tr>
                                    <th colspan="2">
                                        {{ budgetCR['honorary'][formationRef].title }} - {{ formationRef }}
                                    </th>
                                </tr>
                                </thead>
                                {% for category,compensation in compensations %}
                                    {% if category != 'total' %}
                                        {% for value in compensation %}
                                            {% for budget,data in value %}
                                                <tr>
                                                    <td class="wrapleft">{{ data.nbParticipant }} {{ category }} x {{ budget }} € ({{ "compensation.budgetOf" | trans }} {{ data.nbHour }}h)</td>
                                                    <td class="wrapright">{{ data.totalBudgetCR }} €</td>
                                                </tr>
                                            {% endfor %}
                                        {% endfor %}
                                    {% endif %}
                                {% endfor %}
                                <tr>
                                    <td class="bold wrapleft">{{ "compensation.totalInvoice" | trans }}</td>
                                    <td class="bold wrapright">{{ compensations.total|number_format(2, '.', ' ') }} €</td>
                                </tr>

                                {# {% if compensations.totalTtc is defined %}
                                    <tr>
                                        <td class="bold wrapleft">{{ "compensation.ttc" | trans }}</td>
                                        <td class="bold wrapright">{{ compensations.totalTtc }} €</td>
                                    </tr>
                                {% endif %} #}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</div>
</body>
</html>
