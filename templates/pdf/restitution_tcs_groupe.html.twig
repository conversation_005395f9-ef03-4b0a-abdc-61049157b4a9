<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="{{ absolute_url(asset('admin/dist/css/v4-shims.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/form.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/audit.css')) }}" rel="stylesheet">
    <style>

        body {
            padding: 0;
            font-size: 0.75em;
            width: 21cm;
            color: #333333;
        }

        h1 {
            font-size: 28px;
        }

        h2 {
            font-size: 22px;
        }

        h3 {
            font-size: 18px;
            margin-bottom: 0;
        }

        h3:before {
            content: "";
            width: 18px;
            height: 18px;
            background-size: 18px;
            left: calc(50% - 203px);
            top: 10px;
        }

        h5 {
            font-size: 18px;
        }

        hr {
            border-width: 1px;
        }

        .pageWidth {
            padding: 1cm 1cm 1cm 1cm;
        }

        #programme {
            padding: 1em;
            background: #ddd;
            float: right;
            min-width: 330px;
            font-family: Arial, sans-serif;
        }

        #coordinateur-formateur {
            font-size: 1em;
        }

        .newFormateur {
            position: absolute;
            left: 9cm;
            top: 3cm;
            max-width: 15cm;
            font-size: 1em;
        }

        #contact {
            position: absolute;
            left: 4cm;
            top: 0cm;
            max-width: 15cm;
            font-size: 1em;
        }

        .title {
            text-align: center;
            font-size: 1.9em;
            /*margin-top: 0.25cm;*/
        }
        .littletitle {
            text-align: center;
            font-size: 1.25em;
        }
        .centered {
            text-align: center;
            margin-top: 0.5cm;
            /*margin-bottom: 1cm;*/
        }
        .infos {
            margin-top: 0.25cm;
            font-weight: bold;
        }
        .restitution-row {
            page-break-inside: avoid;
            padding-top: 1.8em;
        }

        .restitution-row::after {
            content: '';
            clear: both;
            display: block;
        }

        .interpretation {
            letter-spacing: -1px;
            font-family: stimupratbold, sans-serif;
            margin-bottom: 2em;
        }

        .interpretation p {
            margin: 0;
        }
        .comments > ul {
            list-style-position: inside;
        }
        #legend {
            height: 6cm;
            padding-top: 1cm;
        }
        #legend ul {
            margin-left: 0;
        }

        #legend li {
            margin-bottom: 0.5em;
        }

        #legend li > img {
            margin-right: 1em;
        }

        .interpretation, .interpretation span, .interpretation p {
            font-size: 1em !important;
            margin-bottom: 1em;
        }

        #detail-title {
            font-size: 1.2em;
        }

        #spiderweb {
            margin: 2em 0 0.2em 0;
        }

        .highcharts-root, .highcharts-container {
            overflow: visible !important;
        }
        .section {
            padding-top: 1em;
            page-break-inside: avoid;
        }

        .section-title {
            margin-bottom: 0;
            font-size: 1.75em;
            text-transform: uppercase;
            padding-top: 0.125em;
            font-family: stimupratbold, sans-serif;
            font-weight: 600;
        }

        .progress {
            font-size: 0;
            height: 20px;
            margin-bottom: 3px;
            background-color: transparent;
        }

        .progress-meter {
            display: inline-block;
        }

        .progress-text {
            font-size: 10px;
        }

        .progress-meter-success {
            background: linear-gradient(to right, #8b5e92, #6086b9);
            background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .progress-meter-error {
            background-color: #d71903;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }

        .restitution-question + p {
            margin-bottom: 0.5rem;
        }

        #barchart .bar {
            fill: #6c5d9f;
        }
        #barchart .axis path, #barchart .axis line {
            fill: none; stroke: #000; shape-rendering: crispEdges;
        }

        .page-break + .section {
            padding-top: 1cm;
        }

        .footer-fixed {
            position: absolute;
            top: 28.2cm;
            text-align: center;
            width: 100%;
            left:0;
            line-height: 1.3em;
            font-size: 0.8em;
        }


        input[type=checkbox] {
            display: inline-block;
            vertical-align: text-top;
            margin-bottom: 0;
            width: 0;
            position: relative;
        }

        .checkbox label:before {
            content: "";
            display: inline-block;
            width: 15px;
            height: 15px;
            vertical-align: text-top;
            background: #d8dde4;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        input:checked:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .checkbox input:checked:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .checkbox input:after {
            left: -10px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid #000;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
        .bold {
            font-family: Arial, sans-serif;
        }

        .restitution-question.blue {
            font-size: 16px;
        }

        .restitution-question+p {
            font-size: 14px;
        }

        .answers label {
            font-size: 12px;
        }

        .answers-postulat {
            margin-top: 5px;
        }

        .answers-postulat p {
            margin-bottom: 0;
        }

        .restitution-patient {
            font-size: 16px;
            margin-top: 16px;
        }

        .restitution-patient-audit {
            font-size: 20px;
            margin-top: 16px;
        }

        .restitution-row-pad {
            padding-left: 16px;
        }
        .eduprat {
            color: #109199;
        }
        .flex {
            display: -webkit-box;
            display: flex;
        }
        .flex-colone {
            flex-direction: column;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
        }
        .colonne2 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 4cm;
            position: relative;
        }
        .colonne8 {
            /*flex: 35;*/
            /*-webkit-box-flex: 35;*/
            /*-webkit-flex: 35;*/
            width: 16cm;
            position: relative;
        }
        .colonne12 {
            display: -webkit-inline-box;
            display: inline-flex;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            flex-direction: row;
            width: 20cm;
            height: -webkit-fit-content;
            height: fit-content;
        }
        .qna-block {
            margin-left: 1rem;
        }

    </style>
</head>
<body>
<div class="fluid960 page">
    <div class="pageWidth row ptn">
        <hr style="border-width: 2px;" class="mtn mbs">
        <h1 class="text-center mbn mtn">
            RESTITUTION GROUPE<br>
            TEST DE CONOCRDANCE DE SCRIPT (TCS)
        </h1>
        <h2 class="text-center mbn">
            {{ formation.audit.label }}
        </h2>
        <hr class="mts">
        {% include "pdf/section/header.html.twig" %}
        <br>
        {% set summaryIndex = 0 %}
        <div class="section">
            {% set summaryIndex = summaryIndex + 1 %}
            <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score global groupe</p>
            <br>
            <p style="font-size: 18px">Le groupe a obtenu <span class="bold">{{ tcsGroupMoyenne }}%</span> de concordance avec le panel d'experts.</p>
        </div>
        <div class="section">
            {% set summaryIndex = summaryIndex + 1 %}
            <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Score de concordance global</p>
            {% if hasOneResponse %}
                <div id="spiderweb"></div>
            {% else %}
                <p>Aucune réponse de la part des participants</p>
            {% endif %}
        </div>
        <div class="page-break"></div>
        <div class="section">
            {% set summaryIndex = summaryIndex + 1 %}
            <p class="section-title"><span class="blue">{{ summaryIndex }}.</span>&nbsp;&nbsp;&nbsp;Comparaison experts participants</p>
            {% set questionGroups = questionnaire_tcs.groupeQuestionsTCS %}
            {% set nbExpertCompletRepondant = questionnaire_tcs.nbExpertCompletRepondant %}
            {% for questionGroup in questionGroups %}
                {% set questions = questionGroup.questionsTCS %}
                <div class="restitution-question blue"><b><span class="text-uppercase">TCS</span> n°{{loop.index}}</b></div>
                <table style="margin-top : 1rem">
                    <thead>
                        <tr style="background-color: #898A8D; color:white">
                            <th colspan="3">
                                <span>{{ questionGroup.description|striptags|raw }}</span>
                            </th>
                        </tr>
                    </thead>
                </table>

                <table>
                    <thead>
                        <tr style="background-color: #AEB1B5; color:white; padding-top: 1em;">
                            <th width="25%">Si vous pensez...</th>
                            <th width="25%">Et qu'alors vous trouvez...</th>
                            <th width="50%">Votre hypothèse ou option en est...</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for question in questions %}
                        <tr style="background-color: white">
                            <td style="border: 1px solid #AEB1B5">
                                <span style="font-size: 12px;">{{ question.libelleSiVousPensiez|raw }}</span>
                            </td>
                            <td style="border: 1px solid #AEB1B5">
                                <span style="font-size: 12px;">{{ question.libelleEtQuAlorsVousTrouvez|raw }}</span>
                            </td>
                            <td style="border: 1px solid #AEB1B5;">
                                {% for answer in question.reponses %}
                                    {# <input style="position: absolute; margin-top: 6px;" type="radio" disabled name="reponsesExperts" id="reponsesExperts{{ answer.id }}" value="{{ answer.id }}"> #}
                                    <label style="font-size: 12px; margin-left: 20px;" for="reponsesExperts{{ answer.id }}">{{ answer.reponse }}</label>
                                    {# <br> #}
                                {% endfor %}
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                {% for question in questions %}
                    <div class="restitution-question blue"><b><span class="text-uppercase">Question</span> {{loop.index}} :</b></div>
                    <span> Si vous pensez {{ question.libelleSiVousPensiez|raw }} et qu'alors vous trouvez {{ question.libelleEtQuAlorsVousTrouvez|raw }} vous pensez maintenant que :

                        <table class="checkbox-table" style="margin-top:0.5rem">
                            <tbody>
                            <tr>
                                <td class="checkbox-header checkbox-header-left" style="width: 22%; padding-left:0; font-weight:bold;">Réponses des {{ nbExpertCompletRepondant }} experts :</td>
                                <td class="checkbox-header checkbox-header-left" style="width: 22%; padding-left:0; font-weight:bold;">Réponses du groupe :</td>
                                <td style="width: 56%;"></td>
                            </tr>
                            {% for answer in question.reponses %}
                                <tr>
                                    <td class="checkbox checkbox-left">
                                        <label><input type="checkbox" disabled="disabled" {% if question.displayAnswerChecked(answer) %}checked="checked"{% endif %}> {% if question.displayAnswerChecked(answer) %}{{ question.countAnswer(answer) }}/{{ question.expertsAnswers|length }}{% else %} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {% endif %}</label>
                                    </td>
                                    <td class="checkbox checkbox-left">
                                        <label><input type="checkbox" disabled="disabled" {% if question.displayAnswerCheckedParticipant(answer) %}checked="checked"{% endif %}>{% if question.displayAnswerCheckedParticipant(answer) %} {{ question.countParticipantAnswer(answer) }}/{{ question.participationAnswersTCS|length }} {% else %}  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {% endif %}</label>
                                    </td>
                                    <td>
                                        <label style="margin-left: -4rem;">Votre hypothèse ou option en est {{ answer.reponse }}</label>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>

                        <div class="tcs-answers-experts tcs-answers-experts-axes">
                            <p class="justification-panel justification-panel-axes">
                                <div class="restitution-question blue"><b><span class="text-uppercase">Justifications </span> :</b></div>
                            </p>
                            <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                {% set expertsAnswers = question.expertsAnswersGroupByReponse %}
                                {% for expertsAnswer in expertsAnswers %}
                                    <div class="tcs-answers-experts-question">
                                        <li><span class="bold">{{ expertsAnswer[0].reponseTCS.reponse }}</span></li>
                                        <div class="tcs-answers-experts-items tcs-answers-experts-items-axes">
                                            {% for expertAnswer in expertsAnswer %}
                                                <div class="tcs-answers-experts-item">
                                                    <span class="justification"><b>Expert {{ expertAnswer.questionnaireTCSExpert.expert.id }} :</b>&nbsp;</span> {% if expertAnswer.justification %}{{ expertAnswer.justification }}{% else %}Pas de justification apportée{% endif %}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                        {% if loop.index != expertsAnswers|length %}
                                        <br>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    {% if loop.index != questions|length %}
                        <br>
                    {% endif %}
                {% endfor %}
                {% if loop.index != questionGroups|length %}
                    <div class="page-break"></div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
<script src="{{ asset('js/radarChart.js') }}"></script>
<script>

    var radarData = JSON.parse('{{ radarDataJson | escape('js') }}');

    var margin = {top: 80, right: 100, bottom: 60, left: 100};
    var width = 519;
    var height = 320;
    var maxValue = 100;

    var gradients = [["#005171", "#009098"]];

    radarData = radarData.sort(function(a, b) {
    	return a.group >= b.group;
    }).reduce(function(acc, data) {
            acc[0].push({axis: data.label, value: data.moy_1});
        return acc;
    }, [[]]);

    var color = d3.scale.ordinal().range(["#1b9ba2", "#7253d2"]);

    var scoreGroup = "{{ tcsGroupMoyenne ~ '%' }}";
    var scoreExperts = "{{ tcsExpertMoyenne ~ '%' }}";

    var radarChartOptions = {
	    w: width,
	    h: height,
	    margin: margin,
	    maxValue: maxValue,
	    levels: 4,
	    roundStrokes: true,
	    labelFactor: 1.1,
	    lineBackground: true,
	    dotRadius: 0,
	    wrapWidth: 150,
	    color: color,
	    gradients: gradients,
        legends: ["Score de concordance global de l'ensemble des participants : " + scoreGroup],
	    rotationFactor: radarData[0].length === 6 ? 0.5 : 0,
    };

    //Call function to draw the Radar chart
    RadarChart("#spiderweb", radarData, radarChartOptions);
</script>
</body>
</html>
