<html>
<head>
    <meta charset="UTF-8"/>
    <title></title>
    <meta name="viewport" content="width=device-width">
    <link href="{{ absolute_url(asset('css/foundation-components/reset_base.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation/foundation.min.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/layout/page-width.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/style.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/foundation-components/spaces.css')) }}" rel="stylesheet">
    <link href="{{ absolute_url(asset('css/commun_pdf_front.css')) }}" rel="stylesheet">
    <style>
        body {
            width: 960px;
            margin: 0 auto;
            padding: 0 45px;
        }

        h1 {
            font-size: 1.5em;
            text-transform: uppercase;
            font-weight: bold;
            margin-bottom: 0.75em;
        }

        table thead th {
            text-align: center;
            background-color: #cccccc;
            padding: 0.25em 1em;
            border: 1px solid #000;
            font-weight: normal;
        }

        table {
        }

        tbody td {
            border-right: 1px solid black;
            border-left: 1px solid black;
        }

        tbody tr:last-child {
            border-bottom: 1px solid black;
        }

        .pageWidth {
            position: relative;
        }

        table tbody tr:nth-child(even) {
            background-color: #fff;
        }

        td {
            page-break-inside: avoid;
        }

        .nbl td, .nbl th {
            border-left-width: 1px;
        }

        #table-right td {
            line-height: 0.5em;
        }

        #table-right tbody tr:first-child td {
            padding-top: 36px;
            vertical-align: top;
        }

        #table-right tr {
            height: 30px;
        }

        #price {
            background-color: #cccccc;
            font-size: 1.2em;
        }

        .participant {
            height: 20px;
        }

        .participant td {
            padding-top:0.1em;
        }

        #address {
            -webkit-transform: translateY(3em);
            -moz-transform: translateY(3em);
            -ms-transform: translateY(3em);
            -o-transform: translateY(3em);
            transform: translateY(3em);
        }

        #table-left tr > td:last-child, #table-left tr > th:last-child {
            border-right-width: 0;
        }

        .participant-cell {
            margin-right: 10px;
            font-size: initial;
            height: 18px;
        }

        .table-invoice th {
            height: 51px;
        }

    </style>
</head>
<body>
<div class="fluid960 pageCentered">
    <div class="pageWidth row">
        <div class="row">
            <div class="column small-7">
                <div class="mtl">
                    <img width="145px" src="{{ absolute_url(asset('img/eduprat-new-logo-web.png')) }}" alt="eduprat logo">
                </div>
            </div>
            <div class="column small-5">
                <div id="address" class="mtl">
                    <b>{{ financeSousMode.name }}</b><br>
                    <b>{{ financeSousMode.address }}</b><br>
                    {% if financeSousMode.address2 %}
                        <b>{{ financeSousMode.address2 }}</b><br>
                    {% endif %}
                    <b>{{ financeSousMode.zipCode }} {{ financeSousMode.city }}</b><br>
                    {% if financeSousMode.siren %}
                        <b>{{ "invoice.siren" | trans }} {{ financeSousMode.siren }}</b><br>
                    {% endif %}
                </div>
            </div>

        </div>
        <div class="row mtl mbs">
            <div class="column small-12">
                {% if financeSousMode.identifiant is not null  %}
                    <b>{{ (financeSousMode.isActalians ? "invoice.andpc_id" : "invoice.id")| trans }}</b> {{ financeSousMode.identifiant }}
                {% endif %}
            </div>
        </div>
        <div class="row">
            <div id="debug1"></div>
        </div>
        <div class="row">
            <div id="debug2"></div>
        </div>
        <div class="row mtm">
            <div class="column small-12">
                <table>
                    <thead>
                    <tr>
                        <th style="width: 20%">{{ "invoice.date" | trans }}</th>
                        <th style="width: 20%">{{ "invoice.echeance" | trans }}</th>
                        <th style="width: 40%"><b>{{ "invoice.number" | trans }}</b></th>
                        <th style="width: 20%"><b>{{ "invoice.presence" | trans }}</b></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr class="text-center">
                        <td>{{ closingDate | date('d/m/Y') }}</td>
                        <td>{{ "invoice.reception" | trans }}</td>
                        <td>{{ invoice.number }}</td>
                        <td>{{ ("invoice.presencesTypes." ~ formation.programme.presence) | trans }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row mtm">
            <div class="column small-12">
                <table class="table-invoice" id="table-left" style="width: {% if not formation.isSedd %}50{% else %}40{% endif %}%; float: left;">
                    <thead>
                    <tr>
                        <th style="width: 76%">{{ "invoice.designation" | trans }}</th>
                        {% if not formation.isSedd %}
                            <th style="width: 24%; border-right-width: 0;">{{ "invoice.ref" | trans }}</th>
                        {% endif %}
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td valign="top" style="min-height: 50px">
                            <b>{{ "invoice.formation" | trans }}</b><br>
                            <p>{{ formation.programme.title }}</p>
                        </td>
                        {% if not formation.isSedd %}
                            <td class="text-center">{{ formation.programme.reference }}</td>
                        {% endif %}
                    </tr>
                    {% if formation.sessionNumber is not null %}
                        <tr>
                            <td valign="top" style="min-height: 50px">
                                <b>{{ "invoice.session" | trans }}</b><br>
                                {{ "invoice.session_prefix" | trans }} {{ formation.sessionNumber }}
                            </td>
                            {% if not formation.isSedd %}
                                <td></td>
                            {% endif %}
                        </tr>
                    {% endif %}
                    <tr>
                        <td valign="top" style="min-height: 50px">
                            <b>{{ "invoice.session_date" | trans }}</b><br>
                            {# <p>{{ formation.startDate|date('d/m/Y') }}{%- if formation.startDate != formation.endDate %} - {{ formation.endDate|date('d/m/Y') }}{%- endif -%}</p> #}
                            <p>{{ openingDate|date('d/m/Y')}} - {{ closingDate|date('d/m/Y') }}</p>
                        </td>
                        {% if not formation.isSedd %}
                            <td></td>
                        {% endif %}
                    </tr>
                    {% if formation.isSedd %}
                    <tr>
                        <td valign="top" style="min-height: 50px">
                            {% set days = formation.daysCount %}
                            {% set hours = formation.programme.durationTotal %}
                            <b>{{ "invoice.duration" | trans }}</b><br>

                            <p>{{ days }} {{ "invoice.days" | trans }}{% if days > 1 %}s{% endif %} soit {{ hours }} {{ "invoice.hours" | trans }}{% if hours > 1 %}s{% endif %}</p>
                        </td>
                        {% if not formation.isSedd %}
                            <td></td>
                        {% endif %}
                    </tr>
                    {% endif %}
                    <tr>
                        <td valign="top" style="min-height: 50px">
                            <b>{{ "invoice.place" | trans }}</b><br>
                            <p>{{ formation.address }}<br>
                                {% if formation.address2 is not null %}
                                    {{ formation.address2 }}<br>
                                {% endif %}
                                {{ formation.zipcode }} {{ formation.city }}</p>
                        </td>
                        {% if not formation.isSedd %}
                            <td></td>
                        {% endif %}
                    </tr>
                    <tr>
                        <td valign="top" style="min-height: 200px">
                            <b>{{ "invoice.participants" | trans }}</b>
                        </td>
                        {% if not formation.isSedd %}
                            <td></td>
                        {% endif %}
                    </tr>
                    <tr class="participant">
                        <td style="page-break-inside: auto; font-size: 0;" height="">{% for participation in participations %}<span class="nbi participant-cell">{{ participation.participant.lastname | upper }}{% if not loop.last %}, {% endif %}</span>{% endfor %}</td>
                        {% if not formation.isSedd %}
                            <td></td>
                        {% endif %}
                    </tr>
                    </tbody>
                </table>
                <table id="table-right" class="nbl table-invoice"
                       style="width: {% if not formation.isSedd %}50{% else %}60{% endif %}%; float:right; clear: right;">
                    <thead>
                    <tr class="text-center">
                        <th style="width: 5%">{{ "invoice.coordinator" | trans }}</th>
                        <th style="width: 10%">{{ "invoice.nb_participant" | trans }}</th>
                        {% if formation.isSedd %}<th style="width: 10%">{{ "invoice.nb_days" | trans }}</th>{% endif %}
                        <th style="width: 10%">{{ "invoice.price_unit_ht" | trans }}</th>
                        <th style="width: 20%">{{ "invoice.price_ht" | trans }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% set lastCoordinator = null %}
                    {% for identifier, prices in formation.participantsPricesDistributionPerModeAndCoordinator(financeSousMode, n1) %}
                        {% for price, count in prices %}
                            <tr>
                                <td class="text-center">{% if lastCoordinator != identifier %}{{ identifier }}{% endif %}</td>
                                <td class="text-center">{{ count }}</td>
                                {% if formation.isSedd %}<td class="text-center">{{ formation.daysCount }}</td>{% endif %}
                                <td class="text-right">
                                    {% if price|number_format(3, ',', ' ') ends with "0" %}{{ price|number_format(2, ',', ' ') }}€{% else%}{{ price|number_format(3, ',', ' ') }}€{% endif %}
                                </td>
                                <td class="text-right">
                                    {% set total = (count * price|default(0) * (formation.isSedd ? formation.daysCount : 1)) %}
                                    {% if total|number_format(3, ',', ' ') ends with "0" %}{{ total|number_format(2, ',', ' ') }}€{% else%}{{ total|number_format(3, ',', ' ') }}€{% endif %}
                                </td>
                            </tr>
                            {% set lastCoordinator = identifier %}
                        {% endfor %}
                    {% endfor %}
                    <tr>
                        <td></td>
                        <td></td>
                        {% if formation.isSedd %}<td></td>{% endif %}
                        <td></td>
                        <td></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="nbi">
            <div class="row mtm">
                <div class="column small-12">
                    <div class="column small-7 text-center">
                        {{ "invoice.footer_1" | trans }}
                    </div>
                    <div class="column small-5 prn">
                        <div id="price">
                            <div class="column small-7 text-right">
                                {{ "invoice.total_ht" | trans }}<br>
                                <b>{{ "invoice.total_payment" | trans }}</b>
                            </div>
                            <div class="column small-5 text-right">
                                {% set total = formation.caTotalPerMode(financeSousMode, n1) %}
                                {% if total|number_format(3, ',', ' ') ends with "0" %}{{ total|number_format(2, ',', ' ') }}€{% else%}{{ total|number_format(3, ',', ' ') }}€{% endif %}<br>
                                <b>{% if total|number_format(3, ',', ' ') ends with "0" %}{{ total|number_format(2, ',', ' ') }}€{% else%}{{ total|number_format(3, ',', ' ') }}€{% endif %}</b>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<br><p>Banque : BNP PARIBAS     BIC : BNPAFRPPXXX     IBAN : FR76 3000 4005 8800 0104 7076 164</p>
<script type="text/javascript" src="{{ asset('admin/plugins/jQuery/jquery-2.2.3.min.js') }}"></script>
<script>
    $(document).ready(function () {
        var h = document.getElementById('table-left').clientHeight;
        var marged = false;
        $.each($('.participant-cell'), function(index, val) {
            if ($(val).offset().top > 1130 && !marged) {
                val.style["margin-top"] = "100px";
                marged = true;
                h += 106;
            }
        });
        document.getElementById('table-right').style.height = h + 10;
        $('#table-right').find('tbody').find('tr').last().css('height', (h - 36 - 45 - ($('#table-right').find('tbody').find('tr').length - 1) * 30) + "px");
    });
</script>
</body>
</html>
