{% extends 'crm/base.html.twig' %}

{% block title %}Suivi des inscriptions{% endblock %}

{% block body %}

{% include "crm/comptabilite/form.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Suivi des inscriptions (dernières 72h)</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Type</th>
          <th>Le</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Gestion</th>
          {% if is_granted("ROLE_SUPERVISOR") %}
            <th>Coordinateur</th>
          {% endif %}
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for participationHistory in report.inscriptions %}
          <tr>
            <td>{{ participationHistory.participation.participant.lastname }}</td>
            <td>{{ participationHistory.participation.participant.firstname }}</td>
            <td>{{ participationHistory.action }}</td>
            <td>{{ participationHistory.createdAt|date("d/m/Y H:i:s") }}</td>
            <td>{{ participationHistory.participation.formation.programme.reference }}</td>
            <td>{{ participationHistory.participation.formation.sessionNumber }}</td>
            <td>{% if participationHistory.participation.formation.coordinators.count > 1 %}Multi CR{% else %}Individuelle{% endif %}</td>
            {% if is_granted("ROLE_SUPERVISOR") %}
              <td>{% if participationHistory.participation.associatedCoordinator %}{{ participationHistory.participation.associatedCoordinator.person.invertedFullname }}{% endif %}</td>
            {% endif %}
            <td>
              <a href="{{ url('admin_formation_show', {'id': participationHistory.participation.formation.id, '_fragment': ("row-participation-" ~ participationHistory.participation.id) }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
{% endblock %}
