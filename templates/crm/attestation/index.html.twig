{% extends 'crm/base.html.twig' %}

{% block title %}Questionnaires manquants{% endblock %}

{% block body %}

{% include "crm/comptabilite/form.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Liste des questionnaires manquants</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Questionnaire manquant</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Type de session</th>
          {% if is_granted("ROLE_SUPERVISOR") %}
            <th>Coordinateur</th>
          {% endif %}
          <th>Echéance</th>
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for result in report.participations.result %}
          <tr {% if result.echeance|date("Ymd") <= "now"|date("Ymd") %}class="participation-echeance-expired" {% endif %}>
            <td>{{ result.participation.participant.lastname }}</td>
            <td>{{ result.participation.participant.firstname }}</td>
            <td>
              {% if result.formType == "elearning" %}
                Plateforme Elearning
              {% else %}
                {% if result.participation.formation.isAudit %}
                  {% if result.participation.formation.isPredefined %}
                    Cas clinique
                  {% else %}
                    Audit
                  {% endif %}
                {% elseif result.participation.formation.isPresentielle %}
                  Questionnaire
                {% endif %}
                {% if result.formType == "form1" %}
                  1
                {% elseif result.formType == "form2" %}
                  2
                {% endif %}
              {% endif %}
            </td>
            <td>{{ result.participation.formation.programme.reference }}</td>
            <td>{{ result.participation.formation.sessionNumber }}</td>
            <td>
              {% if formation.isElearning %}
                {{ ("admin.formation.type.formation_elearning") | trans }}
              {% else %}
                {{ ("admin.formation.type." ~ formation.displayType) | trans }}</td>
              {% endif %}
            </td>
            {% if is_granted("ROLE_SUPERVISOR") %}
              <td>{% if result.participation.associatedCoordinator %}{{ result.participation.associatedCoordinator.person.invertedFullname }}{% endif %}</td>
            {% endif %}
            <td>{{ result.echeance|date("d/m/Y") }}</td>
            <td data-name="{{ result.participation.participant.fullname }}">
              <a href="{{ url('admin_formation_show', {'id': result.participation.formation.id, '_fragment': ("row-participation-" ~ result.participation.id) }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
              {% if result.participation.participant.email %}
                {% if result.formType == "elearning" %}
                  <a class="btn btn-eduprat btn-icon btn-email" title='{{ "admin.participation.email.relanceElearning" | trans }}' href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ELEARNING'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% elseif result.formType == "form1" %}
                  <a class="btn btn-eduprat btn-icon btn-email" title="{{ "admin.participation.email.relancePreSession" | trans }}" href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_PRE_FORMATION'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% elseif result.formType == "form2" %}
                  <a class="btn btn-eduprat btn-icon btn-email" title="Envoyer le mail de relance de disponibilité parcours post-session + attestation sur l'honneur" href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\Email\\EmailFormationJP1Relance::ALIAS'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% endif %}
              {% endif %}
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-sm-12">
        {% if npages > 1 %}
          {% include 'crm/common/pagination.html.twig' with {urlName : app.request.get('_route'), extraParams : search.getParams() } %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
<script>
  $('.btn-email').on('click', function(e) {
      let name = $(this).closest("td").data("name");
      var valid = confirm('Est-vous sûr de vouloir ' + this.title.toLowerCase() + ' à ' + name + " ?");
      if (!valid) {
          e.preventDefault();
      }
  });
</script>
{% endblock %}