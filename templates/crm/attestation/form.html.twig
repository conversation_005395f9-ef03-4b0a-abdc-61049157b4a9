{% if form is defined and form.children|length > 1 %}
  <div class="box box-solid box-primary">
    <div class="box-header">
      <h3 class="box-title">Filtrer par coordinateur</h3>
    </div>
    <div class="box-body eduprat-search">
      {{ form_start(form, {'attr': {'id': 'eduprat_comptabilite_search', 'class': 'form-inline'}}) }}
      <div class="row">
        <div class="col-sm-12">
          <div class="form-inline">
            {% if form.querySearchStartDate is defined %}
              <div class="form-group mrm">
                {{ form_label(form.querySearchStartDate) }}<br>
                {{ form_widget(form.querySearchStartDate) }}
                <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
              </div>
              <div class="form-group mrm">
                {{ form_label(form.querySearchEndDate) }}<br>
                {{ form_widget(form.querySearchEndDate) }}
                <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
              </div>
            {% endif %}
            {% if form.coordinator is defined %}
              <div class="form-group mrm">
                {{ form_label(form.coordinator) }}<br>
                {{ form_widget(form.coordinator) }}
              </div>
            {% endif %}
            {% if form.webmaster is defined %}
              <div class="form-group mrm">
                {{ form_label(form.webmaster) }}<br>
                {{ form_widget(form.webmaster) }}
              </div>
            {% endif %}
            <div class="form-group mrm">
              {{ form_label(form.courseEnded) }}<br>
              {{ form_widget(form.courseEnded) }}
            </div>
            <div style="margin-left: 2em;" class="form-group mrm">
              <b>Présence</b><br>
              {{ form_widget(form.classe_virtuelle) }}<br>
              {{ form_widget(form.e_learning) }}<br>
              {{ form_widget(form.sur_site) }}<br>
             </div>
            <div class="text-right">
              <button class="btn btn-eduprat" type="submit">
                <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
              </button>
              <button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{{ url('participation_generate_missing_attestations') }}">
                <span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }}
              </button>
            </div>
            <div class="text-right">
                <div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
                  <span>{{ "admin.formation.generation"|trans }}</span>
                </div>
                {% if hasGenerated %}
                  {% if hasFinished %}
                    <div id="generate-participant-export-download" class="text-right">
                     <a href="{{ url('csv_missing_attestations_file_get') }}" target="_blank">{{ "admin.participant.export"|trans }}</a>
                    </div>
                  {% endif %}
                {% endif %}
                {% if hasError %}
                    <div id="generate-participant-export-error" class="text-right">
                        <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                    </div>
                {% endif %}
            </div>
          </div>
        </div>
      </div>
      {{ form_end(form)}}
    </div>
  </div>
{% endif %}
