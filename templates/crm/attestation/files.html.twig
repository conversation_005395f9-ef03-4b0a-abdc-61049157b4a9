{% extends 'crm/base.html.twig' %}

{% block title %}Attestations manquantes{% endblock %}

{% block body %}

{% for formAttestation in forms_attestation_honneur %}
    {{ form_errors(formAttestation) }}
{% endfor %}

{% include "crm/attestation/form.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Attestations manquantes</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Présence</th>
          <th>Gestion</th>
          <th>Coordinateur</th>
          <th>Attestation</th>
          <th>Echéance</th>
          <th>Parcours terminé</th>
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for participation in report.files.result %}
          {% set formation = participation.formation %}
          {% set shouldDisplayAttestationN1 = formation.shouldDisplayAttestationN1() %}
          {% set missing = not participation.attestationHonneur and formation.isMissingAttestation %}
          {% set missingN1 = not participation.attestationHonneurN1 and formation.isMissingAttestationN1 %}
          {% set echeance = formation.getAttestationEcheanceN %}
          {% set echeanceN1 = missingN1 ? formation.getAttestationEcheanceN1 : false %}
          {% set year = formation.openingDate|date('Y') %}
          {% set yearN1 = formation.closingDate|date('Y') %}
          {% set echeanceOutDated = (missing and echeance and date(echeance) < date()) or (missingN1 and echeanceN1 and date(echeanceN1) < date()) %}
          <tr {% if echeanceOutDated %}class="participation-echeance-expired"{% endif %}>
            <td>{{participation.participant.lastname}}</td>
            <td>{{participation.participant.firstname}}</td>
            <td>{{formation.programme.reference}}</td>
            <td>{{formation.sessionNumber}}</td>
            <td>{{formation.programme.presence}}</td>
            <td>{% if formation.coordinators.count > 1 %}Multi CR{% else %}Individuelle{% endif %}</td>
            <td>
                {% if participation.coordinator %}
                    {{ participation.coordinator.person.fullname }}
                {% endif %}
            </td>
            <td>
              
              {{ year }}
              {% if missing %}
                <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NO" /><br>
              {% else %}
                <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="YES" /><br>
              {% endif %}
              {% if formation.isPluriannuelle and shouldDisplayAttestationN1 and formation.isMissingAttestationN1 %}
                {{ yearN1 }}
                {% if missingN1 %} 
                  <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NO" /><br>
                {% else %}
                  <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="YES" /><br>
                {% endif %}
              {% endif %}
            </td>
            <td>
            {{ echeance|date("d-m-Y") }}
            {% if echeanceN1 %}
              <br>
              {{ echeanceN1|date("d-m-Y") }}
            {% endif %}
            </td>
            <td>
                {% set course = courseManager.getCourseDetail(participation) %}
                {% set preCompleted = courseManager.isCoursePreCompleted(participation) %}
                <div style="display:flex">
                  <div style="width:40%">
                    Pré {% if preCompleted %} <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" /> {% else %} <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" /> {% endif %}
                  </div>
                  <div style="width:40%">
                    &nbsp;Post
                    {% if courseManager.hasModule(course, "synthese") %}
                        {% if participation.isStepCompleted("synthese") %}
                            <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" />
                        {% else %}
                            <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" />
                        {% endif %}
                    {% else %}
                        {% if participation.isStepCompleted("end") %}
                            <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" />
                        {% else %}
                            <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" />
                        {% endif %}
                    {% endif %}
                  </div>
                </div>
            </td>
            <td class="text-center">
              <a class="btn btn-eduprat btn-icon" href="{{ url('admin_formation_show', {'id': participation.formation.id }) }}" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
              {% if participation.participant.email %}
                  <a href="#" title="Liste des emails" class="btn btn-eduprat btn-icon btn-email emailListLink"><i class="fa fa-envelope"></i></a>
              {% endif %}
              <a href="#" data-participationid="{{ participation.id }}" class="attestationListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
            </td>
          </tr>
          {% if participation.participant.email %}
            <tr class="emailWrap" id="email-{{ participation.id }}">
              <td colspan="10">
                <div class="emailList">
                  {% if participation.formation.isPluriAnnuelle %}
                    <a onclick="return confirm('Etes-vous sûr(e) de vouloir envoyer le mail de relance de l\'attestation mi-parcours ?')" title="{{ "admin.participation.email.relanceAttestationMidCourse" | trans }}" href="{{ url('admin_participation_email', {id: participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ATTESTATION_MI_PARCOURS'), referer: app.request.uri }) }}">{{ "admin.participation.email.relanceAttestationMidCourse" | trans }}</a><br>
                  {% endif %}
                  <a onclick="return confirm('Etes-vous sûr(e) de vouloir envoyer le mail de relance de l\'attestation ?')" title="{{ "admin.participation.email.relanceAttestation" | trans }}" href="{{ url('admin_participation_email', {id: participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ATTESTATION'), referer: app.request.uri }) }}">{{ "admin.participation.email.relanceAttestation" | trans }}</a>
                </div>
              </td>
            </tr>
          {% endif %}
          <tr class="sessionsWrap" id="attestation-{{ participation.id }}">
                  <td colspan="10">
                      <div class="attestationList">
                          <div class="collaps-list collaps-list-attestation collaps-list-attestation-crm">
                            {# génération attestation #}
                            {% if missing %}
                                <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }}
                                {% if missingN1 %}{{ year }}{% endif %}</a><br>
                            {% endif %}
                            {# génération attestation N+1 #}
                            {% if missingN1 %}
                              <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null", n1: true }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} {{ participation.formation.closingDate|date('Y') }}</a><br>
                            {% endif %}
                            {# upload attestation #}
                            {% if missing %}
                              <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneur %}true{%else%}false{%endif%}" id="attestationHonneurFile_{{ participation.id }}">
                                  <i class="fa fa-download  btn-attestation" ></i>
                                  <div style="display: initial;margin-left: 2px;">{{ 'admin.participation.attestation_honneur.import'|trans }}
                                  {% if formation.isPluriannuelle and shouldDisplayAttestationN1 %} {{ year }}{% endif %}</div>
                              </div>
                            {% endif %}
                            {# upload attestation N+1 #}
                            {% if missingN1 %}
                              <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneurN1 %}true{%else%}false{%endif%}" id="attestationHonneurFileN1_{{ participation.id }}">
                                  <i class="fa fa-download  btn-attestation" ></i>
                                  <div style="display: initial;margin-left: 2px;">{{ 'admin.participation.attestation_honneur.import'|trans }} {{ yearN1 }}</div>
                              </div>
                            {% endif %}
                            {# visualisation attestation #}
                            {% if participation.attestationHonneur %}
                              <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id }) }}">{{ 'admin.participation.attestation_honneur.view'|trans }} {% if formation.isPluriannuelle and shouldDisplayAttestationN1 %} de {{ year }}{% endif %}</a><br>
                            {% endif %}
                            {# visualisation attestation N+1 #}
                            {% if participation.attestationHonneurN1 %}
                              <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id, n1: true }) }}">{{ 'admin.participation.attestation_honneur.view'|trans }} de {{ yearN1 }}</a><br>
                            {% endif %}

                            {# formulaire upload attestation N et N+1 #}
                            <div id="attestationHonneurFile" class="downloadFile">
                              {% if missing %}
                                {{ form_start(forms_attestation_honneur[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                {{ form_widget(forms_attestation_honneur[participation.id].attestationHonneurFile) }}
                                {{ form_end(forms_attestation_honneur[participation.id]) }}
                              {% endif %}

                              {% if missingN1 %}
                                  {{ form_start(forms_attestation_honneurN1[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                  {{ form_widget(forms_attestation_honneurN1[participation.id].attestationHonneurFileN1) }}
                                  {{ form_end(forms_attestation_honneurN1[participation.id]) }}
                              {% endif %}
                            </div>
                          </div>
                      </div>
                  </td>
              </tr>
        {% endfor %}

        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-sm-12">
        {% if npages > 1 %}
          {% include 'crm/common/pagination.html.twig' with {urlName : app.request.get('_route'), extraParams : search.getParams() } %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}


{% block javascripts %}

    <script type="text/javascript">
        $(document).ready(function() {
            $('[name=csvAll]').click(function (e) {
                var $this = $(this);
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    type: "POST",
                    data: $('#eduprat_comptabilite_search').serialize(),
                    success: function(data, textStatus, xhr) {
                console.log(data);
                        if (data.status == "ok") {
                            $('#generate-participant-export-loading').removeClass('hidden');
                            $('#generate-participant-export-download').addClass('hidden');
                            $('#generate-participant-export-error').addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

            $('.btn-download-file').click(function () {
                $('form[name="'+$(this).attr('id')+'"]').find('input').click();
            });

            $('.btn-download-file-attestation').click(function () {
                if($(this).data("attestation")) {
                    if(confirm("En choisissant un fichier, vous écraserez l'attestation actuelle")) {
                        $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                    }
                } else {
                    $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                }
            });

            $('.delete-attestation').click(function () {
                if(confirm("Supprimer l'attestation ?")) {
                    return true;
                } else {
                    return false;
                }
            });

            $('form.formDownloadFile').on('change', 'input[type="file"]', function () {
                $(this).closest("form").submit();
            });

        });
    </script>

{% endblock %}