{% extends 'crm/base.html.twig' %}

{% block title %}Professionnels de santé{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    {% if form is defined %}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <h3 class="box-title">Rechercher un professionnel de santé</h3>
            </div>
            <div class="box-body eduprat-search">

                {% if not form.vars.valid %}
                    {% for error in form.vars.errors.form.getErrors(true) %}
                        <div class="callout callout-danger">{{ (error.cause.propertyPath|replace({'data.': 'admin.participant.'}))|trans }} : {{ error.message }}</div>
                    {% endfor %}
                {% endif %}

                {{ form_start(form, {'attr': {'id': 'eduprat-participant-search', 'class': 'form-inline'}}) }}
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="form-group mrm">
                                {{ form_label(form.lastname) }}<br>
                                {{ form_widget(form.lastname) }}
                            </div>
                            <div class="form-group mrm">
                                 {{ form_label(form.firstname) }}<br>
                                 {{ form_widget(form.firstname) }}
                             </div>
                            <div class="form-group mrm">
                                {{ form_label(form.email) }}<br>
                                {{ form_widget(form.email) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.zipCode) }}<br>
                                {{ form_widget(form.zipCode, {'attr': {maxlength: '5', pattern: "[0-9]*"}}) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.region) }}<br>
                                {{ form_widget(form.region) }}
                            </div>
                            {% if not sib %}
                            <div style="display:none";>
                                {% endif %}
                                <br>
                                <div class="form-group mrm department-grp" style="width: 60%">
                                    {{ form_row(form.departement) }}
                                </div>
                                {% if not sib %}
                            </div>
                            {% endif %}
                            <br>
                            <div class="form-group mrm" {% if sib %} style="display:none" {% endif %}>
                                {{ form_label(form.adeli) }}<br>
                                {{ form_widget(form.adeli, {'attr': {maxlength: '9', pattern: "[0-9]*"}}) }}
                            </div>
                            <div class="form-group mrm" {% if sib %} style="display:none" {% endif %}>
                                {{ form_label(form.rpps) }}<br>
                                {{ form_widget(form.rpps, {'attr': {maxlength: '11', pattern: "[0-9]*"}}) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.category) }}<br>
                                {{ form_widget(form.category) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.speciality) }}<br>
                                {{ form_widget(form.speciality) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.ugaName) }}<br>
                                {{ form_widget(form.ugaName) }}
                            </div>
                            <div class="form-group mrm" {% if sib %} style="display:none" {% endif %}>
                                {{ form_label(form.isCreated) }}<br>
                                {{ form_widget(form.isCreated) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.partenariat) }}<br>
                                {{ form_widget(form.partenariat) }}
                            </div>

                            <br>
                            {% if not sib %}
                                <div class="form-group mrm">
                                    {{ form_label(form.programmeTitle) }}<br>
                                    {{ form_widget(form.programmeTitle) }}
                                </div>
                                <div class="form-group mrm mr-participant">
                                    {{ form_label(form.reference) }}<br>
                                    {{ form_widget(form.reference) }}
                                </div>
                                <div class="form-group mrm mr-participant">
                                    {{ form_label(form.presence) }}<br>
                                    {{ form_widget(form.presence) }}
                                </div>
                            {% endif %}
                            <div class="form-group mrm">
                                {{ form_label(form.exerciseMode) }}<br>
                                {{ form_widget(form.exerciseMode) }}
                            </div>
                            {% if form.coordinator is defined %}
                                <div class="form-group mrm">
                                    {{ form_label(form.coordinator) }}<br>
                                    {{ form_widget(form.coordinator) }}
                                </div>
                            {% endif %}
                            <div class="form-group mrm">
                                {{ form_label(form.isProspect) }}<br>
                                {{ form_widget(form.isProspect) }}
                            </div>
                            <div class="form-group mrm" style="vertical-align: top !important;">
                                {{ form_label(form.status) }}<br>
                                {{ form_widget(form.status) }}
                            </div>
                            <br>
                            <div class="form-group mrm" style="vertical-align: sub">
                                <label class="control-label" for="eduprat_participant_search_gdprAgreement">Consentement :</label>
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.gdprAgreement) }}<br>
                                {{ form_widget(form.gdprAgreement) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.gdprAgreementPost) }}<br>
                                {{ form_widget(form.gdprAgreementPost) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.gdprAgreementCall) }}<br>
                                {{ form_widget(form.gdprAgreementCall) }}
                            </div>
                            {% if not sib %}
                                <div class="text-right">
                                    {% if form|length > 2 %} <br>{% endif %}
                                    <button class="btn btn-eduprat" type="submit">
                                        <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                                    </button>
                                    {% if not app.user.isCoordinatorLbi %}
                                        <button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{{ url('participant_generate_csv_export') }}">
                                            <span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }}
                                        </button>
                                    {% endif %}
                                </div>
                                <div class="text-right">
                                    <div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
                                        <span>{{ "admin.formation.generation"|trans }}</span>
                                    </div>
                                    {% if hasGenerated %}
                                        {% if hasFinished %}
                                            <div id="generate-participant-export-download" class="text-right">
                                                <a href="{{ url('csv_participant_file_get') }}" target="_blank">{{ "admin.participant.export"|trans }}</a>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                    {% if hasError %}
                                        <div id="generate-participant-export-error" class="text-right">
                                            <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% if not sib %}
                <div style="display:none";>
                    {% endif %}
                    <br>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-inline">
                                {% if sib %}
                                    <div class="form-group mrm participant-title">
                                        {{ form_label(form.programmeTitle) }}<br>
                                        {{ form_widget(form.programmeTitle) }}
                                    </div>
                                {% endif %}
                                <div class="form-group mrm">
                                    {{ form_label(form.withTitre) }}<br>
                                    {{ form_widget(form.withTitre) }}
                                </div>
                                {% if sib %}
                                    <div class="form-group mrm mr-participant">
                                        {{ form_label(form.reference) }}<br>
                                        {{ form_widget(form.reference) }}
                                    </div>
                                {% endif %}
                                <div class="form-group mrm">
                                    {{ form_label(form.withReference) }}<br>
                                    {{ form_widget(form.withReference) }}
                                </div>
                                <div class="form-group mrm mr-participant">
                                    {{ form_label(form.nbSession) }}<br>
                                    {{ form_widget(form.nbSession) }}
                                </div>
                                <div class="form-group mrm">
                                    {{ form_label(form.withNbSession) }}<br>
                                    {{ form_widget(form.withNbSession) }}
                                </div>
                                <div class="form-group mrm mr-participant">
                                    {{ form_label(form.type) }}<br>
                                    {{ form_widget(form.type) }}
                                </div>
                                <div class="form-group mrm">
                                    {{ form_label(form.withType) }}<br>
                                    {{ form_widget(form.withType) }}
                                </div><br>
                                <div style="display:none";>
                                    {{ form_widget(form.formType) }}
                                </div>
                                <div class="text-right">
                                    {% if form|length > 2 %} <br>{% endif %}
                                    <button class="btn btn-eduprat" type="submit">
                                        <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                                    </button>
                                    {% if not app.user.isCoordinatorLbi %}
                                        <button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{{ url('participant_generate_csv_export_sendinblue') }}">
                                            <span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }} pour Sendinblue
                                        </button>
                                    {% endif %}
                                </div>
                                {% if sib %}
                                    <div class="text-right">
                                        <div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
                                            <span>{{ "admin.formation.generation"|trans }}</span>
                                        </div>
                                        {% if hasGenerated %}
                                            {% if hasFinished %}
                                                <div id="generate-participant-export-download" class="text-right">
                                                    <a href="{{ url('csv_participant_file_get') }}" target="_blank">{{ "admin.participant.export"|trans }}</a>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                        {% if hasError %}
                                            <div id="generate-participant-export-error" class="text-right">
                                                <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% if not sib %}
                </div>
                {% endif %}
                {{ form_widget(form._token) }}
                {{ form_end(form, {'render_rest': false}) }}
            </div>
        </div>
    {% endif %}
    <div class="box box-primary">
        <div class="box-header">
            <h3 class="box-title">{{ "participant.titles.index"|trans }}</h3>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-stacktable" data-display-header="false" data-header-index="2">
                        <thead>
                        <tr>
                            {% if is_granted('ROLE_WEBMASTER') %}
                                <th class="hidden-xs hidden-sm">{{ "admin.participant.fusion"|trans }}</th>
                            {% endif %}
                            <th>{{ "admin.participant.firstname"|trans }}</th>
                            <th>{{ "admin.participant.lastname"|trans }}</th>
                            <th>{{ "admin.participant.email"|trans }}</th>
                            <th>{{ "admin.participant.speciality"|trans }}</th>
                            <th>{{ "admin.participant.identifiant"|trans }}</th>
                            <th>{{ "admin.participant.zipCode"|trans }}</th>
                            <th>{{ "admin.participant.created"|trans }}</th>
                            <th>{{ "admin.participant.statut"|trans }}</th>
                            <th>{{ "admin.participant.uga"|trans }}</th>
                            <th>{{ "admin.global.actions"|trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participant in participants %}
                            <tr {% if participant.isProspect %}class="table-row-participant-prospect"{% endif %}>
                                {% if is_granted('ROLE_WEBMASTER') %}
                                <td class="text-center hidden-xs hidden-sm">
                                    <input data-id="{{ participant.id }}" data-prospect="{{ participant.isProspect }}" class="participant-fusion" type="checkbox" name="participant-fusion">
                                </td>
                                {% endif %}
                                <td>{{ participant.firstname }}</td>
                                <td>{{ participant.lastname }}</td>
                                <td>{{ participant.email }}</td>
                                <td>{{ participant.speciality }}</td>
                                <td>{{ participant.identifier }}</td>
                                <td>{{ participant.zipcode }}</td>
                                <td class="text-center">
                                    {% if not participant.isProspect %}
                                        {% if participant.user and participant.user.hasCreatedPassword %}
                                            <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                        {% else %}
                                            <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td id="stateCell-{{participant.id}}">
                                    <select style="color:white" data-participantid="{{ participant.id }}" data-url="{{ url('participant_updateStatus', {'id': participant.id }) }}" class="statusPs form-control"  id="statusPs-{{ participant.id }}" value="{{participant.status}}" style="margin-top: 8px;">
                                        <option class="statusPsOption" {% if participant.status == "Actif" %} selected="selected" {% endif %} value="Actif">Actif</option>
                                        <option class="statusPsOption" {% if participant.status == "Actif Remplaçant" %} selected="selected" {% endif %} value="Actif Remplaçant">Actif Remplaçant</option>
                                        <option class="statusPsOption" {% if participant.status == "Retraité actif" %} selected="selected" {% endif %} value="Retraité actif">Retraité actif</option>
                                        <option class="statusPsOption" {% if participant.status == "Retraité inactif" %} selected="selected" {% endif %} value="Retraité inactif">Retraité inactif</option>
                                        <option class="statusPsOption" {% if participant.status == "Décédé" %} selected="selected" {% endif %} value="Décédé">Décédé</option>
                                        <option class="statusPsOption" {% if participant.status == "Autre inactif" %} selected="selected" {% endif %} value="Autre inactif">Autre inactif</option>                                            
                                    </select>
                                </td>
                                <td>{{ participant.uga }}</td>
                                <td>
                                    <a href="{{ url('admin_participant_show', {'id': participant.id }) }}" class="mbn btn btn-icon btn-eduprat" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
                                    {% if is_granted('ROLE_COORDINATOR') %}
                                        <a href="{{ url('admin_participant_edit', {'id': participant.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                        {% if not participant.isProspect and participant.user is not null %}
                                            <a href="{{ url('admin_user_simulation', {'id': participant.user.id, role: "participant" }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.simulate'|trans }}"><i class="fa fa-user"></i></a>
                                        {% endif %}
                                        <a href="{{ url('admin_participant_delete', {'id': participant.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <a data-url="{{ mergeUrl }}" href="#" id="participant-fusion" class="btn btn-eduprat hide">Fusionner les participants selectionnés</a>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    {% set searchPage = search.getParams() %}
                    {% if sib %}
                        {% set searchPage = searchPage|merge({"sendinblue": 1}) %}
                    {% endif %}
                    {% if npages > 1 %}
                        {% include 'crm/common/pagination.html.twig' with {urlName : 'admin_participant_index', extraParams : searchPage } %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.quicksearch.js') }}"></script>
    <script type="text/javascript">
        var $participantBtn = $('#participant-fusion');

        $('.participant-fusion').change(function (e) {
            console.log($(this).data('id'));
	        var $participant = $('.participant-fusion:checked');
	        var isValid = $participant.length === 2;
	        var isProspect = $(this).data("prospect") === 1;
	        
	        $participantBtn.toggleClass('hide', !isValid);
	        if (isValid) {
		        var ids = $participant.map(function() { return $(this).data('id');}).get().sort();
		        $participantBtn.attr('href', $participantBtn.data('url').replace('__1__', ids[0]).replace('__2__', ids[1]));
            }
        });

        $('[name=csvAll]').click(function (e) {
	        var $this = $(this);
	        if ($this.data('started') === true) return false;
	        e.preventDefault();
	        jQuery.ajax({
		        url: $this.data('href'),
                type: "POST",
		        data: $('#eduprat-participant-search').serialize(),
		        success: function(data, textStatus, xhr) {
			        if (data.status == "ok") {
				        $('#generate-participant-export-loading').removeClass('hidden');
				        $('#generate-participant-export-download').addClass('hidden');
				        $('#generate-participant-export-error').addClass('hidden');
			        }
		        },
		        error: function(xhr, textStatus, errorThrown) {
			        console.log(errorThrown);
		        }
	        });
	        $this.data('started', true);
        });

        $( document ).ready(function() {
	        var formType = {{ sib }}
		        $('#eduprat_participant_search_formType').val(formType);
	        var multiselectInitialized = false;
	        initMultiselect();
	        function initMultiselect() {
		        if (!multiselectInitialized) {
			        $('#eduprat_participant_search_departement').multiSelect({
				        selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				        selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				        afterInit: function(ms){
					        var that = this,
						        $selectableSearch = that.$selectableUl.prev(),
						        $selectionSearch = that.$selectionUl.prev(),
						        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
						        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';
					        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
						        .on('keydown', function(e){
							        if (e.which === 40){
								        that.$selectableUl.focus();
								        return false;
							        }
						        });
					        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
						        .on('keydown', function(e){
							        if (e.which == 40){
								        that.$selectionUl.focus();
								        return false;
							        }
						        });
					        multiselectInitialized = true;
				        },
				        afterSelect: function(){
					        this.qs1.cache();
					        this.qs2.cache();
				        },
				        afterDeselect: function(){
					        this.qs1.cache();
					        this.qs2.cache();
				        }
			        });
		        }
	        }
	        $('#eduprat_participant_search_departement').multiSelect({
		        selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
		        selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
		        afterInit: function(ms){
			        var that = this,
				        $selectableSearch = that.$selectableUl.prev(),
				        $selectionSearch = that.$selectionUl.prev(),
				        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
				        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';
			        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
				        .on('keydown', function(e){
					        if (e.which === 40){
						        that.$selectableUl.focus();
						        return false;
					        }
				        });
			        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
				        .on('keydown', function(e){
					        if (e.which == 40){
						        that.$selectionUl.focus();
						        return false;
					        }
				        });
		        },
		        afterSelect: function(){
			        this.qs1.cache();
			        this.qs2.cache();
		        },
		        afterDeselect: function(){
			        this.qs1.cache();
			        this.qs2.cache();
		        }
	        });

	        const category = document.querySelector("#eduprat_participant_search_category");
	        const speciality = document.querySelector("#eduprat_participant_search_speciality");

	        category.onchange = function(e) {
		        const selected = e.target.options[e.target.selectedIndex].text;
		        speciality.querySelectorAll("optgroup").forEach((opt) => {
			        opt.classList.toggle("hidden", selected !== "Tous" && opt.label !== selected);
		        });
	        }

	        category.dispatchEvent(new Event('change'));

            $firstChange = true;
            $(".statusPs").change(function() {
                console.log($firstChange);
                var statusPsId = '#statusPs-' + $(this).attr("data-participantid");
                var select = 'select#statusPs-' + $(this).attr("data-participantid");
                var stateCellId = '#stateCell-'  + $(this).attr("data-participantid");
                $(statusPsId).val($(this).children("option:selected").val());
                var statusSelected = $(this).children("option:selected").val();

                switch(statusSelected) {
                case 'Actif':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Actif Remplaçant':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Retraité actif':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Retraité inactif':
                    $(select).css("background-color", "#CC5000");
                    break;
                case 'Décédé':
                    $(select).css("background-color", "#CC5000");
                    break;
                case 'Autre inactif':
                    $(select).css("background-color", "#CC5000");
                    break;
                }

                if (!$firstChange) {
                    jQuery.ajax({
                        url: $(this).data('url') + "/" + statusSelected,
                        type: "POST",
                        data: '',
                        success: function(data, textStatus, xhr) {
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            alert("Error");
                        }
                    });
                }
                
            });
                
            $(".statusPs").change();
            $firstChange = false;

        });

    </script>
{% endblock %}
