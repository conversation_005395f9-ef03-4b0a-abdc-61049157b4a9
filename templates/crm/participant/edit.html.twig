{% extends 'crm/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'admin_participant_create' %}
        {{ 'participant.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_participant_edit'%}
        {{ 'participant.titles.edit'|trans }}
    {% endif %}
{% endblock %}

{% block body %}
{% if is_granted('ROLE_COORDINATOR') %}
    {% if edit is defined and edit == true and not participant.isProspect %}
        <div class="alert alert-crm">
            Attention, toutes les informations liées au participant doivent être reportées sur le compte DPC du professionnel de santé
        </div>
    {% endif %}
    <div class="box box-info">
        <div class="box-body">
            {% if not form.vars.valid %}
                <div class="callout callout-danger">
                    {% if form.vars.errors|length %}
                        {{ form_errors(form) }}
                    {% else %}
                        {{ 'participant.edit.error'|trans }}
                    {% endif %}
                </div>
            {% endif %}
            {{ form_start(form) }}
            {{ form_row(form.civility) }}
            {{ form_row(form.lastname) }}
            {{ form_row(form.birthName) }}
            {{ form_row(form.firstname) }}
            {{ form_row(form.birthdate) }}
            {{ form_row(form.address) }}
            {{ form_row(form.address2) }}
            {{ form_row(form.zipCode) }}
            {{ form_row(form.city) }}
            {{ form_row(form.region) }}
            {{ form_row(form.uga) }}
            <div class="form-group">
                <div class="btn btn-eduprat" id="process-uga" ><i class="hidden fa fa-spinner fa-spin"></i> Mettre à jour l'UGA à partir de l'adresse renseignée</div>
                <p id="process-uga-error" class="text-danger"></p>
            </div>
            {{ form_row(form.status) }}
            {{ form_row(form.phone) }}
            {{ form_row(form.mobile) }}
            {{ form_row(form.rpps) }}
            {{ form_row(form.adeli) }}
            {{ form_row(form.email) }}
            {{ form_row(form.category) }}
            {{ form_row(form.speciality) }}
			{% if form.coordinator is defined %}
				{{ form_row(form.coordinator) }}
			{% endif %}
            {{ form_row(form.comments) }}
            {{ form_row(form.noMailing) }}


            {{ form_row(form.partenariat) }}
            {{ form_row(form.ots) }}
            {{ form_row(form.leadStatus) }}
            <div class="lead-block">
                <div class="box box-solid box-primary">
                    <div class="box-header">
                        <h3 class="box-title">Lead</h3>
                    </div>
                    <div class="box-body">
                        {{ form_row(form.leadType) }}
                        {{ form_row(form.advisor) }}
                        {{ form_row(form.leadContactDate) }}
                        {{ form_row(form.leadComment) }}
                        {{ form_row(form.leadCommentEduprat) }}
                        {{ form_row(form.gpmMemberNumber) }}
                        {{ form_row(form.leadReferent) }}
                        {{ form_row(form.leadState) }}
                    </div>
                </div>
            </div>
            {{ form_rest(form) }}
            <div class="form-group">
                <button type="submit" class="btn-eduprat btn">{{ "admin.global.save"|trans }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>
{% endif %}
{% if edit is defined and edit == true and is_granted('ROLE_COORDINATOR') %}
    {% if formPass is defined %}
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{{ "participant.edit.password" | trans }}</h3>
            </div>
            <div class="box-body">
                {% if not formPass.vars.valid %}
                    <div class="callout callout-danger">
                        {% if formPass.vars.errors|length %}
                            {{ form_errors(formPass) }}
                        {% else %}
                            {{ 'participant.edit.error'|trans }}
                        {% endif %}
                    </div>
                {% endif %}
                {{ form_start(formPass) }}
                <div class="form-group {% if not formPass.plainPassword.first.vars.valid %}has-error{% endif %}">
                    {{ form_label(formPass.plainPassword.first) }}
                    {{ form_widget(formPass.plainPassword.first) }}
                    {% if formPass.plainPassword.first.vars.valid %}
                        <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
                    {% endif %}
                    {{ form_errors(formPass.plainPassword.first) }}
                </div>
                <div class="form-group">
                    {{ form_label(formPass.plainPassword.second) }}
                    {{ form_widget(formPass.plainPassword.second) }}
                    {{ form_errors(formPass.plainPassword.second) }}
                </div>
                {{ form_rest(formPass) }}
                <div class="form-group">
                    <button type="submit" class="btn-eduprat btn">{{ "admin.global.save_password"|trans }}</button>
                </div>
                {{ form_end(formPass) }}
            </div>
        </div>
    {% else %}
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{{ "participant.edit.password" | trans }}</h3>
            </div>
            <div class="box-body">
                <p class="text text-danger">
                    <i class="fa fa-warning"></i> {{ "participant.edit.warning"|trans }}
                </p>
            </div>
        </div>
    {% endif %}
{% endif %}

{% endblock %}
{% block javascripts %}
    <script>
	    jQuery(document).ready(function () {
		    var processUGAUrl = "{{ url("admin_participant_process_uga") }}";
		    var $processUgaBtn = $("#process-uga");
		    var $processUgaError = $("#process-uga-error");
		    let $uga = $("#eduprat_domainbundle_participant_uga");
		    var correctUGA = false;

		    var processUGA = function (force = true) {
			    $processUgaBtn.addClass("disabled").attr("disabled", "disabled");
			    $processUgaBtn.find("i").removeClass("hidden");
			    $processUgaError.addClass("hidden").empty();
			    return jQuery.ajax({
				    url: processUGAUrl,
				    type: 'GET',
				    data: {
				    	address: $("#eduprat_domainbundle_participant_address").val(),
					    zipCode: $("#eduprat_domainbundle_participant_zipCode").val().replace(/^0+/, ''),
					    city: $("#eduprat_domainbundle_participant_city").val(),
                    },
				    success: function (data, textStatus, xhr) {
					    if (data.uga) {
					    	if (force === true) {
							    $uga.val(data.uga.id);
                            }
						    correctUGA = data.uga.id;
                        } else {
					    	if (force === true) {
							    $uga.val("");
							    $processUgaError.removeClass("hidden").html("Aucun UGA n'a pu être associé à l'adresse renseignée");
						    }
						    correctUGA = null;
                        }
				    },
				    error: function (data, textStatus, xhr) {
					    console.log(data);
				    },
				    complete: function (data, textStatus, xhr) {
					    $processUgaBtn.removeClass("disabled").removeAttr("disabled");
					    $processUgaBtn.find("i").addClass("hidden");
				    },
			    });
		    };

		    $processUgaBtn.click(() => processUGA(true));

		    $("#eduprat_domainbundle_participant_city").change(function () {
		        if (!$uga.val() || $uga.val() === "") {
			        $processUgaBtn.click();
                }
            })

            $("#eduprat_domainbundle_participant_leadStatus").change(function () {
                if ($("#eduprat_domainbundle_participant_leadStatus").val() != "") {
                    $('.lead-block').show();
                } else {
                    $('.lead-block').hide();
                }
            })
            $("#eduprat_domainbundle_participant_leadStatus").change();

            $("#eduprat_domainbundle_participant_leadType").change(function () {
                $("#eduprat_domainbundle_participant_partenariat").val($("#eduprat_domainbundle_participant_leadType").val()) 
            })

		    let $form = $('form[name=eduprat_domainbundle_participant]');
		    $form.submit(function(e){
		    	if (correctUGA === false) {
				    e.preventDefault();
				    return processUGA(false).then(function () {
                        $form.submit();
				    });
                }

		    	var valid = true;
			    if (correctUGA && $uga.val() !== correctUGA) {
				    valid = confirm("Attention l'UGA associé ne correspond pas à l'adresse renseignée, souhaitez-vous tout de même enregistrer ?");
			    }

			    if (!valid) {
			    	e.preventDefault();
			    	return false;
                }
		    })

		    const category = document.querySelector("#eduprat_domainbundle_participant_category");
		    const speciality = document.querySelector("#eduprat_domainbundle_participant_speciality");

		    category.onchange = function(e) {
			    const selected = e.target.options[e.target.selectedIndex].text;
			    speciality.querySelectorAll("optgroup").forEach((opt) => {
				    opt.classList.toggle("hidden", selected !== "Tous" && opt.label !== selected);
			    });
		    }

		    category.dispatchEvent(new Event('change'));
	    });

    </script>
{% endblock %}
