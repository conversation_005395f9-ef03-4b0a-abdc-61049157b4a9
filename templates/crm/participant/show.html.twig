{% extends 'crm/base.html.twig' %}

{% block title %}{{ 'participant.titles.show'|trans }}
    {% if is_granted('ROLE_COORDINATOR') %}
        <span class="mls">
            <a href="{{ url('admin_participant_edit', {'id': participant.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
            {% if participant.user is not null %}
                <a href="{{ url('admin_user_simulation', {'id': participant.user.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.simulate'|trans }}"><i class="fa fa-user"></i></a>
                <a href="{{ url("admin_participant_export_emails", { id: participant.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.formation.mail.export'|trans }}"><i class="fa fa-envelope"></i></a>
            {% endif %}
        </span>
    {% endif %}
{% endblock %}

{% block body %}
    {% for formAttestation in forms_attestation_honneur %}
        {{ form_errors(formAttestation) }}
    {% endfor %}
    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.participant.detail.coord'|trans }}</h2>
                </div>
                <div class="row">
                    <div class="box-body">
                        <div class="col-md-6">
                            <table class="table table-bordered table-hover">
                                <tr>
                                    <th>{{ 'admin.participant.civility'|trans }}</th>
                                    <td>{{ participant.civility }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.lastname'|trans }}</th>
                                    <td>{{ participant.lastname }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.firstname'|trans }}</th>
                                    <td>{{ participant.firstname }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.birthname'|trans }}</th>
                                    <td>{{ participant.birthname }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.birthdate'|trans }}</th>
                                    <td>{% if participant.birthdate %}{{ participant.birthdate.format('d-m-Y') }}{% endif %}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.createdAt'|trans }}</th>
                                    <td>{% if participant.createdAt %}{{ participant.createdAt.format('d-m-Y') }}{% endif %}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered table-hover">
                                <tr>
                                    <th>{{ 'admin.participant.address'|trans }}</th>
                                    <td>{{ participant.address }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.zipCode'|trans }}</th>
                                    <td>{{ participant.zipCode }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.city'|trans }}</th>
                                    <td>{{ participant.city }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.region'|trans }}</th>
                                    <td>{{ participant.region }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.uga'|trans }}</th>
                                    <td>
                                        {% if crm_uga[participant.uga] is defined %}
                                            {{ crm_uga[participant.uga].label }}
                                        {% else %}
                                            {{participant.uga }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.phone'|trans }}</th>
                                    <td>{{ participant.phone }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.mobile'|trans }}</th>
                                    <td>{{ participant.mobile }}</td>
                                </tr>
                                <tr>
                                    <th>{{ 'admin.participant.email'|trans }}</th>
                                    <td>{{ participant.email }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="box box-info">
                <div class="row">
                    <div class="box-body">
                        <div class="col-md-5">
                            <div class="box-header">
                                <h2 class="box-title">{{ 'admin.participant.detail.info'|trans }}</h2>
                            </div>
                            <div class="box-body">
                                <table class="table table-bordered table-hover">
                                    {% if participant.login %}
                                        <tr>
                                            <th>{{ 'admin.participant.login'|trans }}</th>
                                            <td>{{ participant.login }}</td>
                                        </tr>
                                    {% endif %}
                                    <tr>
                                        <th>{{ 'admin.participant.rpps'|trans }}</th>
                                        <td>{{ participant.rpps }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.adeli'|trans }}</th>
                                        <td>{{ participant.adeli }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.category'|trans }}</th>
                                        <td>{{ participant.category }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.speciality'|trans }}</th>
                                        <td>{{ participant.speciality }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.statusPs'|trans }}</th>
                                        <td>{{ participant.status }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.coordinator'|trans }}</th>
                                        <td>
                                            {% if participant.coordinator %}
                                                {{ participant.coordinator.fullname }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.comments'|trans }}</th>
                                        <td>
                                            {{ participant.comments|nl2br }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.partenariat'|trans }}</th>
                                        <td>
                                            {{ participant.partenariat }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.ots'|trans }}</th>
                                        <td>
                                            {{ participant.ots }}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="box-header">
                                <h2 class="box-title">{{ 'admin.participant.detail.lead'|trans }}</h2>
                            </div>
                            <div class="box-body">
                                <table class="table table-bordered table-hover">
                                    <tr>
                                        <th>{{ 'admin.participant.leadStatus'|trans }}</th>
                                        <td>{{ participant.leadStatus }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.leadType'|trans }}</th>
                                        <td>{{ participant.leadType }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.advisor'|trans }}</th>
                                        <td>
                                            {% if participant.advisor %}
                                                {{ participant.advisor.fullname }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.leadCreationDate'|trans }}</th>
                                        <td>
                                            {% if participant.leadCreationDate %}
                                                {{ participant.leadCreationDate | date ('d/m/Y') }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{{ 'admin.participant.leadReferent'|trans }}</th>
                                        <td>
                                            {% if participant.leadReferent %}
                                                {{ participant.leadReferent.fullname }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                                {% if participant.leadHistories|length > 0 %} 
                                <div class="panel box box-primary box-collapse-orange">
                                    <div class="box-header with-border" style="padding: 5px;">
                                        <h4 class="box-title">
                                            <a style="color:white; font-size:14px" data-toggle="collapse" data-parent="#accordion" href="#collapse-coord-leadHistory" aria-expanded="true" class="">
                                                <i class="fa fa-angle-down"></i> Historique du lead
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse-coord-leadHistory" class="panel-collapse collapse" aria-expanded="true" style="">
                                        <div class="box-body">
                                            <table class="table table-bordered table-responsive">
                                                <tr>
                                                    <th>Réception</th>
                                                    <th>Type</th>
                                                    <th>{{ "admin.participant.advisorShort"|trans }}</th>
                                                    <th>{{ "admin.participant.leadReferent"|trans }}</th>
                                                    <th>Recontacter</th>
                                                    <th>Commentaire</th>
                                                    <th>État</th>
                                                    <th>Comentaire Eduprat</th>
                                                    <th></th>
                                                <tr>
                                                {% for leadHistory in participant.leadHistories %}
                                                    <tr>
                                                        <td>{% if leadHistory.leadCreationDate %}{{ leadHistory.leadCreationDate | date ('d/m/Y') }}{% endif %}</td>
                                                        <td>{{ leadHistory.leadType }}</td>
                                                        <td>{% if leadHistory.advisor %}{{ leadHistory.advisor.fullname }}{% endif %}</td>                                                        
                                                        <td>{% if leadHistory.leadReferent %}{{ leadHistory.leadReferent.fullname }}{% endif %}</td>
                                                        <td>{{ leadHistory.leadContactDate }}</td>
                                                        <td>{{ leadHistory.leadComment }}</td>
                                                        <td>{{ leadHistory.leadState }}</td>    
                                                        <td>{{ leadHistory.leadCommentEduprat }}</td>
                                                        <td><a href="{{ url('admin_lead_hisory_delete', {'id': leadHistory.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a></td>
                                                    </tr>
                                                {% endfor %}
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.participant.detail.stats'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th class="text-center">Année</th>
                            <th class="text-center">Nb Heures</th>
                            <th class="text-center">Nb sessions</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for year, stat in participant.formationsStats %}
                            <tr>
                                <td class="text-center">{{ year }}</td>
                                <td class="text-center">{{ stat.hours }}</td>
                                <td class="text-center {{ stat.formations == 0 ? "bg-red" : stat.formations == 1 ? "bg-orange" : stat.formations == 2 ? "bg-green" : "bg-dark-green" }}">{{ stat.formations }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.participant.detail.formations'|trans }}</h2><br><br>
                    <a href="{{ url('admin_formation_export_participations_histories', {'id': participant.id }) }}" target="_blank">{{ "admin.participant.downloadParticipationHistories"|trans }}</a>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-stacktable" data-display-header="false">
                            <thead>
                            <tr>
                                <th>{{ 'admin.participant.detail.ref'|trans }}</th>
                                <th>{{ 'admin.participant.detail.session'|trans }}</th>
                                <th>{{ 'admin.formation.title'|trans }}</th>
                                <th>{{ 'admin.participant.partenariat'|trans }}</th>
                                <th>{{ 'admin.formation.theme'|trans }}</th>
                                <th>{{ 'admin.formation.startDate'|trans }}</th>
                                <th>Attestations</th>
                                {% if not app.user.isCoordinatorLbi %}
                                    <th class="text-center">{{ 'admin.participant.detail.audits'|trans }}</th>
                                    <th class="text-center">{{ 'admin.participant.detail.downloads'|trans }}</th>
                                {% endif %}
                            </tr>
                            </thead>
                            <tbody>
                            {% for participation in participations %}
                                <tr>
                                    <td>
                                        {% if is_granted('ROLE_COORDINATOR_LBI') %}
                                            <a href="{{ url('admin_formation_show', { id: participation.formation.id }) }}">{{ participation.formation.programme.reference }}</a>
                                        {% else %}
                                            {{ participation.formation.programme.reference }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ participation.formation.sessionNumber }}
                                    </td>
                                    <td>{{ participation.formation.programme.title }}</td>
                                    <td>{{ participation.partenariat }}</td>
                                    <td>{{ participation.formation.subject }}</td>
                                    <td>
                                        {% if participation.formation.searchStartDate|date('d/m/Y') != participation.formation.searchEndDate|date('d/m/Y') %}
                                            du {{ participation.formation.searchStartDate | date('d/m/Y') }}
                                            au {{ participation.formation.searchEndDate | date('d/m/Y') }}
                                        {% else %}
                                            {{ participation.formation.searchStartDate | date('d/m/Y') }}
                                        {% endif %}
                                    </td>
                                    <td>
                                    {% if not participation.formation.programme.isFormatPresentiel %}
                                            {% if participation.attestationHonneur %}
                                                <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                            {% else %}
                                                <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                            {% endif %}
                                    {% endif %}
                                    </td>
                                    {% if not app.user.isCoordinatorLbi %}
                                        <td>
                                            {% for i in 1..2 %}
                                                <div class="col-lg-6 text-center">
                                                    {% if participation.formation.isAudit and participation.formation.hasLinkedForm %}
                                                        {% if participation|auditCompleted(i) %}
                                                            <a href="{{ url('admin_participation_audit_show', {id: participation.id, auditId: i }) }}">{% if participation.formation.isPredefined %}Cas cliniques{% else %}{{ ("admin.formation.type." ~ participation.formation.displayType) | trans }}{% endif %} {{ i }} (consultation)</a>
                                                        {% elseif is_granted('ROLE_COORDINATOR') %}
                                                            <a href="{{ url('eduprat_audit_answer', {id: participation.id, auditId: i }) }}">{% if participation.formation.isPredefined %}Cas cliniques{% else %}{{ ("admin.formation.type." ~ participation.formation.displayType) | trans }}{% endif %} {{ i }} (édition)</a>
                                                        {% endif %}
                                                    {% elseif participation.formation.isPresentielle and participation.formation.hasLinkedForm %}
                                                        {% if participation|auditCompleted(i) %}
                                                            <a href="{{ url('admin_participation_survey_show', {id: participation.id, surveyId: i }) }}">Questionnaire {{ i }} (consultation)</a>
                                                        {% elseif is_granted('ROLE_COORDINATOR') %}
                                                            <a href="{{ url('eduprat_survey_answer', {id: participation.id, surveyId: i }) }}">Questionnaire {{ i }} (édition)</a>
                                                        {% endif %}
                                                    {% else %}
                                                        /
                                                    {% endif %}
                                                </div>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            <a href="#" class="btn btn-eduprat btn-icon btn-download"><i class="fa fa-download"></i></a>
                                        </td>
                                    {% endif %}
                                </tr>
                                <tr class="row-downloads hide" id="row-downloads-{{ participation.id }}">
                                    <td colspan="6">
                                        <ul>
                                            <li>
                                                {% if not participation.formation.programme.isFormatPresentiel and participation.formation.attestationMandatory %}
                                                    Attestations sur l'honneur :
                                                    <ul>
                                                            {% set pluriAnuelle = participation.formation.isPluriannuelle() %}
                                                            <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }}{% if pluriAnuelle %} de {{ participation.formation.startDate|date('Y')}}{% endif %}</a>
                                                            <br>
                                                            {% if pluriAnuelle %}
                                                                <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null", n1 : "true" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} de {{ (participation.formation.startDate|date('Y') +1) }}</a>
                                                                <br>
                                                            {% endif %}
                                                            <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneur %}true{%else%}false{%endif%}" id="attestationHonneurFile_{{ participation.id }}">
                                                                <i class="fa fa-download  btn-attestation" ></i>
                                                                <div class="import-attestation">{{ 'admin.participation.attestation_honneur.import'|trans }} {% if pluriAnuelle %} de {{ participation.formation.startDate|date('Y')}}{% endif %}</div>
                                                            </div>
                                                            {% if participation.attestationHonneur %}
                                                                <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id }) }}">{{ 'admin.participation.attestation_honneur.view'|trans }} {% if pluriAnuelle %} de {{ participation.formation.startDate|date('Y')}}{% endif %}</a><br>
                                                                <i class="fa fa-trash-o btn-attestation"></i><a style="margin-left: 4px !important;" class="downloadFile delete-attestation" href="{{ url('admin_attestation_delete_file', {'id' : participation.id, redirect: app.request.uri }) }}">{{ 'admin.participation.attestation_honneur.delete'|trans }} {% if pluriAnuelle %} de {{ participation.formation.startDate|date('Y')}}{% endif %}</a><br>
                                                            {% endif %}
                                                            <div id="attestationHonneurFile" class="downloadFile">
                                                                {{ form_start(forms_attestation_honneur[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                                {{ form_widget(forms_attestation_honneur[participation.id].attestationHonneurFile) }}
                                                                {{ form_end(forms_attestation_honneur[participation.id]) }}
                                                            </div>
                                                    </ul>
                                                {% endif %}
                                                Attestations de participations :
                                                <ul>
                                                    {% if is_granted(constant('Eduprat\\AuditBundle\\Security\\ParticipationVoter::DOWNLOAD_PDF_CERTIFICATE_PARTICIPATION'), participation) %}
                                                        <li>
                                                            <a target="_blank" href="{{ url('pdf_certificate_participation_pdf', { id: participation.id, token: participation.token }) }}">{{ "admin.participant.download.attest" | trans }}</a>
                                                            {% if dates[participation.formation.id] is defined and dates[participation.formation.id] and dates[participation.formation.id].participation %}
                                                                <span class="participant-date-download">{{ dates[participation.formation.id].participation|date("d/m/Y") }}</span>
                                                            {% endif %}
                                                        </li>
                                                    {% endif %}
                                                    {% if participation.formation.closed == 1 or is_granted('ROLE_WEBMASTER') %}
                                                        <li>
                                                            <a target="_blank" href="{{ url('pdf_certificate_participation_horary_pdf', { id: participation.id, token: participation.token }) }}">{{ "admin.participant.download.attest_horary" | trans }}</a>
                                                            {% if dates[participation.formation.id] is defined and dates[participation.formation.id] and dates[participation.formation.id].participation %}
                                                                <span class="participant-date-download">{{ dates[participation.formation.id].participation|date("d/m/Y") }}</span>
                                                            {% endif %}
                                                        </li>
                                                    {% endif %}
                                                </ul>
                                            </li>
                                            {% if participation.formation.topoFiles is not null %}
                                                <li>
                                                    Topos :
                                                    <ul>
                                                        {% for file in participation.formation.topoFiles %}
                                                            {% if file.topo is not null %}
                                                                <li class="participant-download-topo">
                                                                    {% set name = "admin.formation.topo"|trans %}
                                                                    {% if file.topoOriginalName %}
                                                                        {% set name = file.topoOriginalName %}
                                                                    {% endif %}
                                                                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('admin_topo_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                                                    {% if dates[participation.formation.id] is defined and dates[participation.formation.id] %}
                                                                        {% if dates[participation.formation.id]['topos'][file.id] is defined and dates[participation.formation.id]['topos'][file.id] %}
                                                                            <span class="participant-date-download">{{ dates[participation.formation.id]['topos'][file.id]['date']|date("d/m/Y") }}</span>
                                                                        {% endif %}
                                                                    {% endif %}
                                                                </li>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </ul>
                                                </li>
                                            {% endif %}
                                            <li>
                                                Restitutions :
                                                <ul>
                                                    {% if participation.formation.isAudit or participation.formation.isPresentielle %}
                                                        {% if not participation.formation.isVfc %}
                                                            <li>
                                                                <a target="_blank" href="{{ url('pdf_audit_restitution_groupe_individuelle_pdf', { id: participation.id, token: participation.token }) }}">{{ "admin.participant.download.prerestitution" | trans }}</a>
                                                            </li>
                                                            <li>
                                                                <a target="_blank" href="{{ url('pdf_restitution_audit_pdf', { id: participation.id, token: participation.token }) }}">{{ "admin.participant.download.restitution" | trans }}</a>
                                                                {% if dates[participation.formation.id] is defined and dates[participation.formation.id] and dates[participation.formation.id].restitution %}
                                                                    <span class="participant-date-download">{{ dates[participation.formation.id].restitution|date("d/m/Y") }}</span>
                                                                {% endif %}
                                                            </li>
                                                        {% endif %}
                                                    {% endif %}
                                                </ul>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}

    <script type="text/javascript">
	    $(document).on('click', '.btn-download', function (e) {
		    e.preventDefault();
		    $(this).closest('tr').next('tr.row-downloads').toggleClass('hide');
	    });

        $(document).ready(function() {

            $('.btn-download-file-attestation').click(function () {
                if($(this).data("attestation")) {
                    if(confirm("En choisissant un fichier, vous écraserez l'attestation actuelle")) {
                        $('form[name="'+$(this).attr('id')+'"]:visible').find('input[type=file]').click();
                    }
                } else {
                    $('form[name="'+$(this).attr('id')+'"]:visible').find('input[type=file]').click();
                }
            });

            $('.delete-attestation').click(function () {
                if(confirm("Supprimer l'attestation ?")) {
                    return true;
                } else {
                    return false;
                }
            });

            $('form.formDownloadFile').on('change', 'input[type="file"]', function () {
                $(this).closest("form").submit();
            });

        });
    </script>

{% endblock %}
