{% extends 'crm/base.html.twig' %}

{% block title %}Professionnels de santé{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    {% if form is defined %}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <h3 class="box-title">Analyse des participants</h3>
            </div>
            <div class="box-body eduprat-search">

                {% if not form.vars.valid %}
                    {% for error in form.vars.errors.form.getErrors(true) %}
                        <div class="callout callout-danger">{{ (error.cause.propertyPath|replace({'data.': 'admin.participant.'}))|trans }} : {{ error.message }}</div>
                    {% endfor %}
                {% endif %}

                {{ form_start(form, {'attr': {'id': 'eduprat-participant-search', 'class': 'form-inline'}}) }}
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="form-group mrm">
                                {{ form_label(form.ugaName) }}<br>
                                {{ form_widget(form.ugaName) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.lastname) }}<br>
                                {{ form_widget(form.lastname) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.zipCode) }}<br>
                                {{ form_widget(form.zipCode, {'attr': {maxlength: '5', pattern: "[0-9]*"}}) }}
                            </div>
                            <br>
                            <div class="form-group mrm">
                                {{ form_label(form.category) }}<br>
                                {{ form_widget(form.category) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.speciality) }}<br>
                                {{ form_widget(form.speciality) }}
                            </div>
                            {% if form.coordinator is defined %}
                                <br>
                                <div class="form-group mrm">
                                    {{ form_label(form.coordinator) }}<br>
                                    {{ form_widget(form.coordinator) }}
                                </div>
                            {% endif %}
							<div class="text-right">
								{% if form|length > 2 %} <br>{% endif %}
								<button class="btn btn-eduprat" type="submit">
									<span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
								</button>
								{% if not app.user.isCoordinatorLbi %}
									<button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{{ url('participant_generate_csv_export_analysis', { sortBy: extraParams.sortBy, order: extraParams.order}) }}">
										<span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }}
									</button>
								{% endif %}
							</div>
							<div class="text-right">
								<div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
									<span>{{ "admin.formation.generation"|trans }}</span>
								</div>
								{% if hasGenerated %}
									{% if hasFinished %}
										<div id="generate-participant-export-download" class="text-right">
											<a href="{{ url('csv_participant_analysis_file_get') }}" target="_blank">{{ "admin.participant.export"|trans }}</a>
										</div>
									{% endif %}
								{% endif %}
								{% if hasError %}
									<div id="generate-participant-export-error" class="text-right">
										<span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
									</div>
								{% endif %}
							</div>
                        </div>
                    </div>
                </div>
                {{ form_widget(form._token) }}
                {{ form_end(form, {'render_rest': false}) }}
            </div>
        </div>
    {% endif %}
    <div class="box box-primary">
        <div class="box-header">
            <h3 class="box-title">{{ "participant.titles.index"|trans }}</h3>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-stacktable" data-display-header="false" data-header-index="1">
                            <thead>
                            <tr>
                                <th>
                                    <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: "p.uga", order: extraParams.sortBy == "p.uga" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                        {{ "admin.participant.uga"|trans }}
                                        {% if extraParams.sortBy == "p.uga" %}
                                            <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>
                                    <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: "p.lastname", order: extraParams.sortBy == "p.lastname" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                        {{ "admin.participant.fullname"|trans }}
                                        {% if extraParams.sortBy == "p.lastname" %}
                                            <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>
                                    <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: "p.zipCode", order: extraParams.sortBy == "p.zipCode" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                        {{ "admin.participant.zipCode"|trans }}
                                        {% if extraParams.sortBy == "p.zipCode" %}
                                            <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>
                                    <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: "p.city", order: extraParams.sortBy == "p.city" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                        {{ "admin.participant.city"|trans }}
                                        {% if extraParams.sortBy == "p.city" %}
                                            <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                        {% endif %}
                                    </a>
                                </th>
                                <th>{{ "admin.participant.phone"|trans }}</th>
                                <th>{{ "admin.participant.email"|trans }}</th>
                                <th>{{ "admin.participant.statut"|trans }}</th>
                                {% set endYear = "now"|date('Y') %}
                                {% set startYear = endYear - 2 %}
                                {% for year in startYear..endYear %}
                                    <th class="text-center">
                                        <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: ("hours_" ~ year), order: extraParams.sortBy == ("hours_" ~ year) ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                            Heures <br>{{ year }}
                                            {% if extraParams.sortBy == ("hours_" ~ year) %}
                                                <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                {% endfor %}
                                {% for year in startYear..endYear %}
                                    <th class="text-center">
                                        <a href="{{ url('crm_participant_analyse', extraParams|merge({ page: page, sortBy: ("formations_" ~ year), order: extraParams.sortBy == ("formations_" ~ year) ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                            Nb sessions <br>{{ year }}
                                            {% if extraParams.sortBy == ("formations_" ~ year) %}
                                                <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                {% endfor %}
                            </tr>
                            </thead>
                            <tbody>
                            {% for participant in participants %}
                                <tr>
                                    <td>{{ participant.uga }}</td>
                                    <td>
                                        <a href="{{ url('admin_participant_show', {'id': participant.id }) }}" title="{{ 'admin.global.more'|trans }}">{{ participant.invertedFullname }}</a>
                                    </td>
                                    <td>{{ participant.zipcode }}</td>
                                    <td>{{ participant.city }}</td>
                                    <td>{{ participant.phone }}</td>
                                    <td>{{ participant.email }}</td>
                                    <td id="stateCell-{{participant.id}}">
                                        <select style="color:white" data-participantid="{{ participant.id }}" data-url="{{ url('participant_updateStatus', {'id': participant.id }) }}" class="statusPs form-control"  id="statusPs-{{ participant.id }}" value="{{participant.status}}" style="margin-top: 8px;">
                                            <option class="statusPsOption" {% if participant.status == "Actif" %} selected="selected" {% endif %} value="Actif">Actif</option>
                                            <option class="statusPsOption" {% if participant.status == "Actif Remplaçant" %} selected="selected" {% endif %} value="Actif Remplaçant">Actif Remplaçant</option>
                                            <option class="statusPsOption" {% if participant.status == "Retraité actif" %} selected="selected" {% endif %} value="Retraité actif">Retraité actif</option>
                                            <option class="statusPsOption" {% if participant.status == "Retraité inactif" %} selected="selected" {% endif %} value="Retraité inactif">Retraité inactif</option>
                                            <option class="statusPsOption" {% if participant.status == "Décédé" %} selected="selected" {% endif %} value="Décédé">Décédé</option>
                                            <option class="statusPsOption" {% if participant.status == "Autre inactif" %} selected="selected" {% endif %} value="Autre inactif">Autre inactif</option>                                            
                                        </select>
                                    </td>
                                    {% set stats = participant.formationsStats %}
                                    {% for year in startYear..endYear %}
                                        <td class="text-center">{{ stats[year].hours }}</td>
                                    {% endfor %}
                                    {% for year in startYear..endYear %}
                                        <td class="text-center {{ stats[year].formations == 0 ? "bg-red" : stats[year].formations == 1 ? "bg-orange" : stats[year].formations == 2 ? "bg-green" : "bg-dark-green" }}">{{ stats[year].formations }}</td>
                                    {% endfor %}
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    {% if npages > 1 %}
                        {% include 'crm/common/pagination.html.twig' with {urlName : 'crm_participant_analyse', extraParams : search.toArray } %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">

        $('[name=csvAll]').click(function (e) {
	        var $this = $(this);
	        if ($this.data('started') === true) return false;
	        e.preventDefault();
	        jQuery.ajax({
		        url: $this.data('href'),
                type: "POST",
		        data: $('#eduprat-participant-search').serialize(),
		        success: function(data, textStatus, xhr) {
			        if (data.status == "ok") {
				        $('#generate-participant-export-loading').removeClass('hidden');
				        $('#generate-participant-export-download').addClass('hidden');
				        $('#generate-participant-export-error').addClass('hidden');
			        }
		        },
		        error: function(xhr, textStatus, errorThrown) {
			        console.log(errorThrown);
		        }
	        });
	        $this.data('started', true);
        });

        const category = document.querySelector("#eduprat_participant_search_category");
        const speciality = document.querySelector("#eduprat_participant_search_speciality");

        category.onchange = function(e) {
	        const selected = e.target.options[e.target.selectedIndex].text;
	        speciality.querySelectorAll("optgroup").forEach((opt) => {
		        opt.classList.toggle("hidden", selected !== "Tous" && opt.label !== selected);
	        });
        }

        category.dispatchEvent(new Event('change'));

        $( document ).ready(function() {
            $firstChange = true;
            $(".statusPs").change(function() {
                console.log($firstChange);
                var statusPsId = '#statusPs-' + $(this).attr("data-participantid");
                var select = 'select#statusPs-' + $(this).attr("data-participantid");
                var stateCellId = '#stateCell-'  + $(this).attr("data-participantid");
                $(statusPsId).val($(this).children("option:selected").val());
                var statusSelected = $(this).children("option:selected").val();

                switch(statusSelected) {
                case 'Actif':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Actif Remplaçant':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Retraité actif':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Retraité inactif':
                    $(select).css("background-color", "#CC5000");
                    break;
                case 'Décédé':
                    $(select).css("background-color", "#CC5000");
                    break;
                case 'Autre inactif':
                    $(select).css("background-color", "#CC5000");
                    break;
                }

                if (!$firstChange) {
                    jQuery.ajax({
                        url: $(this).data('url') + "/" + statusSelected,
                        type: "POST",
                        data: '',
                        success: function(data, textStatus, xhr) {
                        },
                        error: function(xhr, textStatus, errorThrown) {
                            alert("Error");
                        }
                    });
                }
                
            });
                
            $(".statusPs").change();
            $firstChange = false;

        });

    </script>
{% endblock %}
