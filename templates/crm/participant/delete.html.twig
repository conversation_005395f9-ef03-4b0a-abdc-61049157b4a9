{% extends 'crm/base.html.twig' %}

{% block title %}{{ 'participant.delete.title'|trans }}{% endblock %}

{% block body %}
    <div class="box">
        <div class="box-header">
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    {{ form_start(form) }}
                    <div class="alert alert-danger">
                        Attention, toutes les informations liées au participant seront supprimées ainsi que son compte extranet Eduprat
                    </div>
                    <p>{{ 'participant.delete.message'|trans({'%participant%' : participant.fullname}) }}</p>
                    <p>{{ "participant.delete.formation" | trans }}</p>
                    <ul>
                        {% for participation in participant.participations %}
                            <li>{{ participation.formation.programme.title }} - {{ participation.formation.programme.reference }}</li>
                        {% endfor %}
                    </ul>
                    <button type="submit" class="btn btn-danger">{{ 'admin.global.delete'|trans }}</button>
                    {% set referer = app.request.headers.get('referer')|default(url('admin_participant_index')) %}
                    <a class="btn btn-default" href="{{ referer }}" title="Annuler">Annuler</a>
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}