{% extends 'crm/base.html.twig' %}

{% block title %}Fusion participants{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.participant.merge.old'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ 'admin.participant.civility'|trans }}</th>
                            <td>{{ participant1.civility }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.lastname'|trans }}</th>
                            <td>{{ participant1.lastname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.firstname'|trans }}</th>
                            <td>{{ participant1.firstname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.birthname'|trans }}</th>
                            <td>{{ participant1.birthname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.address'|trans }}</th>
                            <td>{{ participant1.address }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.zipCode'|trans }}</th>
                            <td>{{ participant1.zipCode }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.city'|trans }}</th>
                            <td>{{ participant1.city }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.phone'|trans }}</th>
                            <td>{{ participant1.phone }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.email'|trans }}</th>
                            <td>{{ participant1.email }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.rpps'|trans }}</th>
                            <td>{{ participant1.rpps }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.adeli'|trans }}</th>
                            <td>{{ participant1.adeli }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.category'|trans }}</th>
                            <td>{{ participant1.category }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.speciality'|trans }}</th>
                            <td>{{ participant1.speciality }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.uga'|trans }}</th>
                            <td>{{ participant1.uga }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.isProspect'|trans }}</th>
                            <td>
                                {% if participant1.prospect  %}
                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                {% else %}
                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadStatus'|trans }}</th>
                            <td>{{ participant1.leadStatus }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadType'|trans }}</th>
                            <td>{{ participant1.leadType }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.advisor'|trans }}</th>
                            <td>{% if participant1.advisor %}{{ participant1.advisor.fullname }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadCreationDate'|trans }}</th>
                            <td>{% if participant1.leadCreationDate %}{{ participant1.leadCreationDate | date ('d/m/Y') }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadReferent'|trans }}</th>
                            <td>{% if participant1.leadReferent %}{{ participant1.leadReferent.fullname }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th colspan="2">{{ 'admin.participant.detail.formations'|trans }}</th>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th>{{ 'admin.participant.detail.ref'|trans }}</th>
                                        <th>{{ 'admin.formation.title'|trans }}</th>
                                        <th>{{ 'admin.formation.startDate'|trans }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for participation in oldParticipations %}
                                    <tr>
                                        <td>{{ participation.formation.programme.reference }}</td>
                                        <td>{{ participation.formation.programme.title }}</td>
                                        <td>{{ participation.formation.startDate | date ('d/m/Y') }}</td>
                                    </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.participant.merge.new'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ 'admin.participant.civility'|trans }}</th>
                            <td class="{% if "civility" in edited %}text-info{% elseif "civility" in added %}text-success{% endif %}">{{ participant2.civility }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.lastname'|trans }}</th>
                            <td class="{% if "lastname" in edited %}text-info{% elseif "lastname" in added %}text-success{% endif %}">{{ participant2.lastname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.firstname'|trans }}</th>
                            <td class="{% if "firstname" in edited %}text-info{% elseif "firstname" in added %}text-success{% endif %}">{{ participant2.firstname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.birthname'|trans }}</th>
                            <td class="{% if "birthname" in edited %}text-info{% elseif "birthname" in added %}text-success{% endif %}">{{ participant2.birthname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.address'|trans }}</th>
                            <td class="{% if "address" in edited %}text-info{% elseif "address" in added %}text-success{% endif %}">{{ participant2.address }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.zipCode'|trans }}</th>
                            <td class="{% if "zipCode" in edited %}text-info{% elseif "zipCode" in added %}text-success{% endif %}">{{ participant2.zipCode }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.city'|trans }}</th>
                            <td class="{% if "city" in edited %}text-info{% elseif "city" in added %}text-success{% endif %}">{{ participant2.city }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.phone'|trans }}</th>
                            <td class="{% if "phone" in edited %}text-info{% elseif "phone" in added %}text-success{% endif %}">{{ participant2.phone }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.email'|trans }}</th>
                            <td class="{% if "email" in edited %}text-info{% elseif "email" in added %}text-success{% endif %}">{{ participant2.email }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.rpps'|trans }}</th>
                            <td class="{% if "rpps" in edited %}text-info{% elseif "rpps" in added %}text-success{% endif %}">{{ participant2.rpps }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.adeli'|trans }}</th>
                            <td class="{% if "adeli" in edited %}text-info{% elseif "adeli" in added %}text-success{% endif %}">{{ participant2.adeli }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.category'|trans }}</th>
                            <td class="{% if "category" in edited %}text-info{% elseif "category" in added %}text-success{% endif %}">{{ participant2.category }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.speciality'|trans }}</th>
                            <td class="{% if "speciality" in edited %}text-info{% elseif "speciality" in added %}text-success{% endif %}">{{ participant2.speciality }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.uga'|trans }}</th>
                            <td class="{% if "uga" in edited %}text-info{% elseif "uga" in added %}text-success{% endif %}">{{ participant2.uga }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.isProspect'|trans }}</th>
                            <td class="{% if "prospect" in edited %}text-info{% elseif "prospect" in added %}text-success{% endif %}">
                                {% if participant2.prospect  %}
                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                {% else %}
                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadStatus'|trans }}</th>
                            <td class="{% if "leadStatus" in edited %}text-info{% elseif "leadStatus" in added %}text-success{% endif %}">{{ participant2.leadStatus }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadType'|trans }}</th>
                            <td class="{% if "leadType" in edited %}text-info{% elseif "leadType" in added %}text-success{% endif %}">{{ participant2.leadType }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.advisor'|trans }}</th>
                            <td class="{% if "advisor" in edited %}text-info{% elseif "advisor" in added %}text-success{% endif %}">{% if participant2.advisor %}{{ participant2.advisor.fullname }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadCreationDate'|trans }}</th>
                            <td class="{% if "leadCreationDate" in edited %}text-info{% elseif "leadCreationDate" in added %}text-success{% endif %}">{% if participant2.leadCreationDate %}{{ participant2.leadCreationDate | date ('d/m/Y') }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.participant.leadReferent'|trans }}</th>
                            <td class="{% if "leadReferent" in edited %}text-info{% elseif "leadReferent" in added %}text-success{% endif %}">{% if participant2.leadReferent %}{{ participant2.leadReferent.fullname }}{% endif %}</td>
                        </tr>
                        <tr>
                            <th colspan="2">{{ 'admin.participant.detail.formations'|trans }}</th>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th>{{ 'admin.participant.detail.ref'|trans }}</th>
                                        <th>{{ 'admin.formation.title'|trans }}</th>
                                        <th>{{ 'admin.formation.startDate'|trans }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for participation in participant2.participations %}
                                        <tr {% if participation.id in participations %}class="text-success"{% endif %}>
                                            <td>{{ participation.formation.programme.reference }}</td>
                                            <td>{{ participation.formation.programme.title }}</td>
                                            <td>{{ participation.formation.startDate | date ('d/m/Y') }}</td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-12">
            {{ form_start(form) }}
            {% if participant1.user and participant1.user.role and accountToRemove is null %}
                <div class="alert alert-danger">
                    Le participant qui sera supprimé est relié à un compte extranet "{{ ("user.role." ~ participant1.user.role)|trans }}", la fusion n'est pas possible dans ce cas.
                </div>
            {% endif %}
            <button id="fusion-submit" type="submit" class="btn btn-eduprat" {% if participant1.user and participant1.user.role and accountToRemove is null %}disabled="disabled"{% endif %}>{{ 'participant.merge.btn'|trans }}</button>
            {{ form_rest(form) }}
            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script>
		$(document).ready(function() {
			$('#fusion-submit').on('click', function (e) {
				this.disabled = true;
				this.textContent = "Fusion en cours ...";
				this.form.submit();
			});
		});
    </script>
{% endblock %}
