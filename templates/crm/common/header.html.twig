<header class="main-header">
    <a href="{{ url('admin_index') }}" class="logo">
        eduprat
    </a>
    <nav class="navbar navbar-static-top" role="navigation">
        <!-- Sidebar toggle button-->
        <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
            <span class="sr-only">Toggle navigation</span>
        </a>
        {% if app.user %}
            <div class="navbar-right">
                <ul class="nav navbar-nav">
                    <li class="hidden-xs">
                        <a href="{{ url('admin_personal_data') }}">Données personnelles</a>
                    </li>
                    <!-- User Account: style can be found in dropdown.less -->
                    <li class="dropdown user user-menu">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                            <i class="glyphicon glyphicon-user"></i>
                            <span>{% if app.user is not null %} {{ app.user.username }} {% endif %} <i class="caret"></i></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="user-footer">
                                <a href="{{ url('eduprat_password_edit') }}" class="btn btn-default btn-flat">Modifier mon mot de passe</a>
                            </li>
                            {% if app.session.get('real_user') %}
                                <li class="user-footer">
                                    <a href="{{ url('admin_user_simulation_exit') }}" class="btn btn-default btn-flat">Quitter la simulation</a>
                                </li>
                            {% endif %}
                            <li class="user-footer">
                                <a href="{{ url('eduprat_crm_logout') }}" class="btn btn-default btn-flat">Déconnexion</a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        {% endif %}
    </nav>
</header>