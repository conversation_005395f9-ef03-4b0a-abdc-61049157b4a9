<aside class="left-side sidebar-offcanvas">

    <section class="sidebar">
        <ul class="sidebar-menu">
            <li class="treeview active acces-extranet">
                <a href="{{ url('admin_index') }}">
                    <i class="fa fa-share"></i>
                    <span>Extranet</span>
                </a>
            </li>
            {% if is_granted('ROLE_COORDINATOR_LBI') %}
                {% if app.user and app.user.isPharmacieFormer %}
                    <li class="treeview active">
                        <a href="{{ url('admin_coordinator_dashboard', { id: app.user.id, year: "now"|date('Y')}) }}">
                            <i class="fa fa-bar-chart-o"></i>
                            <span>Tableau de bord</span>
                        </a>
                    </li>
                    <li class="treeview active">
                        <a href="{{ url('admin_participant_index', {status: ['Actif', 'Actif Remplaçant', 'Retraité actif']}) }}">
                            <i class="fa fa-users"></i>
                            <span>Participants</span>
                        </a>
                    </li>
                {% else %}
                    <li class="treeview active">
                        <a href="#">
                            <i class="fa fa-users"></i>
                            <span>Professionnels de santé</span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_participant_index', { isProspect: is_granted("ROLE_SUPERVISOR") or is_granted("ROLE_COORDINATOR") ? "false" : null, status: ['Actif', 'Actif Remplaçant', 'Retraité actif']}) }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                        </ul>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_participant_create') }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                {% endif %}
            {% endif %}
            {% if is_granted('ROLE_WEBMASTER') and not app.user.isWebmasterCompta %}
                {% if "rapport" in crm_sections %}
                    <li class="treeview active">
                        <a href="#">
                            <i class="fa fa-dashboard"></i>
                            <span>Rapport d'activité</span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('crm_reporting_actions') }}">
                                    <i class="fa fa-folder"></i>
                                    <span>Actions</span>
                                </a>
                            </li>
                        </ul>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('crm_reporting_participants', {financeMode: 4}) }}">
                                    <i class="fa fa-users"></i>
                                    <span>Professionnels de santé</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                {% endif %}
                {% if "analysis" in crm_sections %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-dashboard"></i>
                        <span>Analyse commerciale</span>
                    </a>
                    <ul class="treeview-menu">
                        <li>
                            <a href="{{ url('crm_analysis_config', { year: "now"|date('Y') }) }}">
                                <i class="fa fa-cogs"></i>
                                <span>Paramétrage objectifs globaux</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_show', { type: "ca" }) }}">
                                <i class="fa fa-euro"></i>
                                <span>Part de marché CA</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_show', { type: "hours" }) }}">
                                <i class="fa fa-clock-o"></i>
                                <span>Part de marché Heures</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_show', { type: "global" }) }}">
                                <i class="fa fa-euro"></i>
                                <span>CA global</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_show', { type: "participants" }) }}">
                                <i class="fa fa-users"></i>
                                <span>Participants</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_show', { type: "coordinators" }) }}">
                                <i class="fa fa-users"></i>
                                <span>Coordinateurs</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('crm_analysis_commissions', { year: "now"|date('Y') }) }}">
                                <i class="fa fa-bar-chart-o"></i>
                                <span>Commissions</span>
                            </a>
                        </li>
                    </ul>
                </li>
                {% endif %}
            {% endif %}
            {% if is_granted('ROLE_COORDINATOR_LBI') and not app.user.isWebmasterCompta and "analysis_participants" in crm_sections %}
                <li class="treeview active">
                    <a href="{{ url('crm_participant_analyse') }}">
                        <i class="fa fa-dashboard"></i>
                        <span>Analyse des participants</span>
                    </a>
                </li>
            {% endif %}
            {% if is_granted('ROLE_COORDINATOR') and not app.user.isCoordinatorLbi and "suivi_commercial" in crm_sections %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-dashboard"></i>
                        <span>Suivi commercial</span>
                    </a>
                    <ul class="treeview-menu">
                        <li>
                            <a href="{{ url('admin_crm_comptabilite', { year: "now"|date('Y') }) }}">
                                <i class="fa fa-files-o"></i>
                                <span>Modules manquants</span>
                            </a>
                        </li>
                        <li>
                        {% set now = "now"|date('Y-m-d') %}
                            <a href="{{ url('admin_crm_comptabilite_inscriptions', { startInscription: now|date_modify("-3 day")|date("Y-m-d"), endInscription: now }) }}">
                                <i class="fa fa-users"></i>
                                <span>Suivi des inscriptions</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('admin_crm_comptabilite_files', { year: "now"|date('Y') }) }}">
                                <i class="fa fa-files-o"></i>
                                <span>Documents manquants</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('admin_crm_comptabilite_attestation_files', { year: "now"|date('Y') }) }}">
                                <i class="fa fa-files-o"></i>
                                <span>Attestations manquantes</span>
                            </a>
                        </li>
                    </ul>
                </li>
            {% endif %}
            {% if is_granted('ROLE_COORDINATOR') %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-dashboard"></i>
                        <span>Partenariats</span>
                    </a>
                    <ul class="treeview-menu">
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <li>
                                <a href="#">
                                    <i class="fa fa-plus"></i>
                                    Ajouter des leads
                                    <span class="pull-right-container">
                                    <i class="fa fa-angle-left pull-right"></i>
                                    </span>
                                </a>
                                <ul class="treeview-menu">
                                    {% for partner in constant('Eduprat\\CrmBundle\\Services\\LeadsService::PART_IMPORTABLES') %}
                                        <li>
                                            <a href="{{ url('crm_leads_import', { partner: partner }) }}">
                                                <i class="fa fa-align-justify"></i>
                                                <span>{{ partner }}</span>
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </li>
                        {% endif %}
                        <li>
                            <a href="{{ url('crm_leads_suivi') }}">
                                <i class="fa fa-users"></i>
                                <span>Suivi des leads</span>
                            </a>
                        </li>
                    </ul>
                </li>
            {% endif %}
            {% if is_granted('ROLE_SUPERVISOR') and not app.user.isWebmasterCompta %}
                <li class="treeview active">
                    <a href="{{ url('admin_participant_index',{'sendinblue':'1', firstLoad: true, status: ['Actif', 'Actif Remplaçant', 'Retraité actif']}) }}"><i class="fa fa-circle-o"></i>
                        <span>Export Sendinblue</span>
                    </a>
                </li>
            {% endif %}
        </ul>
    </section>
    <!-- /.sidebar -->
</aside>