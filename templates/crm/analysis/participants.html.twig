{% extends 'crm/base.html.twig' %}

{% block title %}Analyse commerciale - Participants{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}

	<div {{ stimulus_controller("crm/participants", { type: type, url: url("crm_analysis_data", { type: type }) }) }}>

		{% if form is defined %}
			<div class="box box-solid box-primary">
				<div class="box-header">
					<h3 class="box-title">Filtres</h3>
				</div>
				<div class="box-body eduprat-search">
					{{ form_start(form, {'attr': {'id': 'eduprat-analysis'}}) }}
					<div class="row">
						<div class="col-sm-12">
							<div class="">
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect" data-multiselect-limit-value="10">
										{{ form_label(form.categories) }}<br>
										{{ form_widget(form.categories) }}
									</div>
								</div>
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect" data-multiselect-limit-value="10">
										{{ form_label(form.coordinatorsId) }}<br>
										{{ form_widget(form.coordinatorsId) }}
									</div>
								</div>
								<div class="col-lg-12">
									<div class="form-group mrm">
										{{ form_label(form.presence) }}<br>
										{{ form_widget(form.presence) }}
									</div>

									<div class="form-group mrm">
										{{ form_label(form.exerciseMode) }}<br>
										{{ form_widget(form.exerciseMode) }}
									</div>
									<div class="form-group mrm">
										{{ form_label(form.filters) }}<br>
										{{ form_widget(form.filters) }}
									</div>
									<div class="form-group mrm">
										{{ form_label(form.display) }}<br>
										{{ form_widget(form.display) }}
									</div>
									<div class="form-group mrm hidden">
										{{ form_label(form.display2) }}<br>
										{{ form_widget(form.display2) }}
									</div>
									<div class="form-group mrm mbn">
										{{ form_label(form.period) }}<br>
										{{ form_widget(form.period) }}
									</div>
									<div class="text-right">
										<div class="mrm" style="display: inline-block">
											{{ form_label(form.cumulated) }}<br>
											{{ form_widget(form.cumulated) }}
										</div>
										<div class="mrm" style="display: inline-block">
											{{ form_label(form.future) }}<br>
											{{ form_widget(form.future, { label: (("crm.analysis.include_future_" ~ type)|trans) }) }}
										</div>

										<div class="btn btn-default" style="margin:0 10px 4px 0" id="filters-reset" data-action="click->crm--participants#resetFilters">
											<span class="fa fa-filter"></span> {{ "crm.analysis.reset_filters"|trans }}
										</div>
										<div class="btn btn-eduprat" id="analysis-filter" data-action="click->crm--participants#update">
											<span class="fa fa-search"></span> {{ "admin.global.apply"|trans }}
										</div>
										<div class="btn btn-eduprat" id="analysis-filter" data-action="click->crm--participants#generateParticipantsCsv">
											<span class="fa fa-search"></span> {{ "crm.analysis.exportCaCsv"|trans }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					{{ form_end(form, {'render_rest': false}) }}
				</div>
			</div>
		{% endif %}

		<div id="analysis-container" class="row analysis-container">
			<div class="col-lg-12">
				<div class="analysis-loading">
					<i class="fa fa-spin fa-spinner mbs"></i>
					Chargement des données ...
					<div class="progress progress-xs mtl">
						<div id="analysis-loading-progress" class="progress-bar progress-bar-danger progress-bar-striped" role="progressbar" style="width: 1%">
							<span class="sr-only"></span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<template id="analysis-table">
			<div class="col-lg-12">
				<div class="box box-info">
					<div class="box-header with-border">
						<h3 class="box-title">__title__</h3>
					</div>
					<div class="box-body">
						<ul class="nav nav-tabs">
							<li class="active"><a href="#tab_table-__id__" data-toggle="tab" aria-expanded="true">Tableaux</a></li>
							<li class=""><a href="#tab_graph-__id__" data-toggle="tab" aria-expanded="true">Graphique</a></li>
						</ul>
						<div class="tab-content">
							<div class="tab-pane active" id="tab_table-__id__">
								<div class="table-responsive table-striped">
									<table class="table no-margin table-stacktable">
										<thead>
										<tr>
											<th style="width: 16%"></th>
											<th style="width: 28%" class="text-right">Année en cours</th>
											<th style="width: 28%" class="text-right">Année N-1</th>
											<th style="width: 28%" class="text-right">Année N-2</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td>Participants</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0] }}">__participants_{{ years[0] }}__</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0]-1 }}">__participants_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0]-2 }}">__participants_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Participations</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0] }}">__participations_{{ years[0] }}__</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0]-1 }}">__participations_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0]-2 }}">__participations_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Ratio Participations / Participants %</td>
											<td class="text-right analysis-table-ratio-__id__-{{ years[0] }}">__ratio_{{ years[0] }}__</td>
											<td class="text-right analysis-table-ratio-__id__-{{ years[0]-1 }}">__ratio_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-ratio-__id__-{{ years[0]-2 }}">__ratio_{{ years[0]-2 }}__</td>
										</tr>
										</tbody>
									</table>
								</div>
								<hr>
								<div class="table-responsive table-striped">
									<table class="table no-margin table-stacktable">
										<thead>
										<tr>
											<th style="width: 16%"></th>
											<th style="width: 28%" class="text-right">Année en cours</th>
											<th style="width: 28%" class="text-right">Année N-1</th>
											<th style="width: 28%" class="text-right">Année N-2</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td>Participants</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0] }}">__participants_{{ years[0] }}__</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0]-1 }}">__participants_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-participants-__id__-{{ years[0]-2 }}">__participants_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Moyenne des participants par session</td>
											<td class="text-right analysis-table-p-avg-__id__-{{ years[0] }}">__p_avg_{{ years[0] }}__</td>
											<td class="text-right analysis-table-p-avg-__id__-{{ years[0]-1 }}">__p_avg_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-p-avg-__id__-{{ years[0]-2 }}">__p_avg_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Evolution %</td>
											<td class="text-right analysis-table-p-evol-__id__-{{ years[0] }}">__p_evol_{{ years[0] }}__</td>
											<td class="text-right analysis-table-p-evol-__id__-{{ years[0]-1 }}">__p_evol_{{ years[0]-1 }}__</td>
											<td class="text-right">Non affichable</td>
										</tr>
										<tr>
											<td>Part de marché</td>
											<td class="text-right analysis-table-part-__id__-{{ years[0] }}">__part_{{ years[0] }}__</td>
											<td class="text-right analysis-table-part-__id__-{{ years[0]-1 }}">__part_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-part-__id__-{{ years[0]-2 }}">__part_{{ years[0]-2 }}__</td>
										</tr>
										</tbody>
									</table>
								</div>
								<hr>
								<div class="table-responsive table-striped">
									<table class="table no-margin table-stacktable">
										<thead>
										<tr>
											<th style="width: 16%"></th>
											<th style="width: 28%" class="text-right">Année en cours</th>
											<th style="width: 28%" class="text-right">Année N-1</th>
											<th style="width: 28%" class="text-right">Année N-2</th>
										</tr>
										</thead>
										<tbody>
										<tr>
											<td>Participations</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0] }}">__participations_{{ years[0] }}__</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0]-1 }}">__participations_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-participations-__id__-{{ years[0]-2 }}">__participations_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Moyenne des participations par session</td>
											<td class="text-right analysis-table-pa-avg-__id__-{{ years[0] }}">__pa_avg_{{ years[0] }}__</td>
											<td class="text-right analysis-table-pa-avg-__id__-{{ years[0]-1 }}">__pa_avg_{{ years[0]-1 }}__</td>
											<td class="text-right analysis-table-pa-avg-__id__-{{ years[0]-2 }}">__pa_avg_{{ years[0]-2 }}__</td>
										</tr>
										<tr>
											<td>Evolution %</td>
											<td class="text-right analysis-table-pa-evol-__id__-{{ years[0] }}">__pa_evol_{{ years[0] }}__</td>
											<td class="text-right analysis-table-pa-evol-__id__-{{ years[0]-1 }}">__pa_evol_{{ years[0]-1 }}__</td>
											<td class="text-right">Non affichable</td>
										</tr>
										</tbody>
									</table>
								</div>
							</div>
							<div class="tab-pane" id="tab_graph-__id__">
								<div class="chart-infos">
									Veuillez sélectionner une période d'affichage autre que "Année" pour générer le graphique
								</div>
								<div id="participant-chart-container-__id__" class="hidden">
									<canvas id="participant-chart-__id__"></canvas>
								</div>
							</div>
							<div class="analysis-container-sub"></div>
						</div>
					</div>
				</div>
			</div>
		</template>

	</div>

{% endblock %}

{% block javascripts %}
	<script src="{{ asset('admin/plugins/sparkline/jquery.sparkline.min.js') }}"></script>
	{{ encore_entry_script_tags('app') }}
{% endblock %}