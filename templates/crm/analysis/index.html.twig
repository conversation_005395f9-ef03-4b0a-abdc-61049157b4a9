{% extends 'crm/base.html.twig' %}

{% block title %}Analyse commerciale - {% if type == "hours" %}Part de marché Heures{% elseif type == "ca" %}Part de marché CA{% else %}CA global{% endif %}{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}

	<div {{ stimulus_controller("crm/analysis", { type: type, url: url("crm_analysis_data", { type: type }) }) }}>

		{% if form is defined %}
			<div class="box box-solid box-primary">
				<div class="box-header">
					<h3 class="box-title">Filtres</h3>
				</div>
				<div class="box-body eduprat-search">
					{{ form_start(form, {'attr': {'id': 'eduprat-analysis'}}) }}
					<div class="row">
						<div class="col-sm-12">
							<div class="">
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect" data-multiselect-limit-value="10">
										{{ form_label(form.categories) }}<br>
										{{ form_widget(form.categories) }}
									</div>
								</div>
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect" data-multiselect-limit-value="10">
										{{ form_label(form.coordinators) }}<br>
										{{ form_widget(form.coordinators) }}
									</div>
								</div>
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect" data-multiselect-limit-value="10">
										{{ form_label(form.ugas) }}<br>
										{{ form_widget(form.ugas) }}
									</div>
								</div>
								<div class="col-lg-12">
									<div class="form-group mrm">
										{{ form_label(form.exerciseMode) }}<br>
										{{ form_widget(form.exerciseMode) }}
									</div>
									<div class="form-group mrm">
										{{ form_label(form.filters) }}<br>
										{{ form_widget(form.filters) }}
									</div>
									<div class="form-group mrm">
										{{ form_label(form.display) }}<br>
										{{ form_widget(form.display) }}
									</div>
									<div class="form-group mrm hidden">
										{{ form_label(form.display2) }}<br>
										{{ form_widget(form.display2) }}
									</div>
									<div class="form-group mrm hidden">
										{{ form_label(form.display3) }}<br>
										{{ form_widget(form.display3) }}
									</div>
									<div class="form-group mrm mbn">
										{{ form_label(form.period) }}<br>
										{{ form_widget(form.period) }}
									</div>
									<div class="text-right">
										<div class="mrm" style="display: inline-block">
											{{ form_label(form.cumulated) }}<br>
											{{ form_widget(form.cumulated) }}
										</div>
										<div class="mrm" style="display: inline-block">
											{{ form_label(form.future) }}<br>
											{{ form_widget(form.future, { label: (("crm.analysis.include_future_" ~ type)|trans) }) }}
										</div>
										<div class="btn btn-default" style="margin:0 10px 4px 0" id="filters-reset" data-action="click->crm--analysis#resetFilters">
											<span class="fa fa-filter"></span> {{ "crm.analysis.reset_filters"|trans }}
										</div>
										<div class="btn btn-eduprat" id="analysis-filter" data-action="click->crm--analysis#update">
											<span class="fa fa-search"></span> {{ "admin.global.apply"|trans }}
										</div>
										<div class="btn btn-eduprat" id="analysis-filter" data-action="click->crm--analysis#generateCsv">
											<span class="fa fa-search"></span> {{ "crm.analysis.exportCaCsv"|trans }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					{{ form_end(form, {'render_rest': false}) }}
				</div>
			</div>
		{% endif %}

		<div id="analysis-container" class="row analysis-container">
			<div class="col-lg-12">
				<div class="analysis-loading">
					<i class="fa fa-spin fa-spinner mbs"></i>
					Chargement des données ...
					<div class="progress progress-xs mtl">
						<div id="analysis-loading-progress" class="progress-bar progress-bar-danger progress-bar-striped" role="progressbar" style="width: 1%">
							<span class="sr-only"></span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<template id="analysis-table">
			<div class="col-lg-12">
				<div class="col-lg-12">
					<div class="box box-info">
						<div class="box-header with-border">
							<h3 class="box-title">__title__</h3>
						</div>
						<div class="box-body">
							{% if type == "global" %}
							<ul class="nav nav-tabs">
								<li class="active"><a href="#tab_table" data-toggle="tab" aria-expanded="true">Tableaux</a></li>
								<li class=""><a href="#tab_graph" data-toggle="tab" aria-expanded="true">Graphique</a></li>
							</ul>
							{% endif %}
							<div class="tab-content">
								<div class="tab-pane active" id="tab_table">
									{% if type == "global" %}
										<div class="table-responsive table-striped">
											<table class="table no-margin table-stacktable">
												<thead>
												<tr>
													<th style="width: 16%"></th>
													<th style="width: 28%" class="text-right">CA</th>
													<th style="width: 28%" class="text-right">Ca Moyen / session</th>
													<th style="width: 28%" class="text-right">Evolution %</th>
												</tr>
												</thead>
												<tbody>
												<tr>
													<td>Année en cours</td>
													<td class="text-right analysis-table-val-{{ years[0] }}">__val_{{ years[0] }}__</td>
													<td class="text-right analysis-table-avg-{{ years[0] }}">__avg_{{ years[0] }}__</td>
													<td class="text-right analysis-table-evol-{{ years[0] }}">__evol_{{ years[0] }}__</td>
												</tr>
												<tr>
													<td>Année N-1</td>
													<td class="text-right analysis-table-val-{{ years[0]-1 }}">__val_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-avg-{{ years[0]-1 }}">__avg_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-evol-{{ years[0]-1 }}">__evol_{{ years[0]-1 }}__</td>
												</tr>
												<tr>
													<td>Année N-2</td>
													<td class="text-right analysis-table-val-{{ years[0]-2 }}">__val_{{ years[0]-2 }}__</td>
													<td class="text-right analysis-table-avg-{{ years[0]-2 }}">__avg_{{ years[0]-2 }}__</td>
													<td class="text-right analysis-table-evol-{{ years[0]-2 }}">Non affichable</td>
												</tr>
												</tbody>
											</table>
										</div>
									{% else %}
										<div class="table-responsive table-striped">
											<table class="table no-margin table-stacktable">
												<thead>
												<tr>
													<th style="width: 16%"></th>
													<th style="width: 28%" class="text-right">Année en cours</th>
													<th style="width: 28%" class="text-right">Année N-1</th>
													<th style="width: 28%" class="text-right">Année N-2</th>
												</tr>
												</thead>
												<tbody>
												<tr>
													<td>{% if type == "ca" %}Objetif CA{% else %}Objectif Heurse déclarés{% endif %}</td>
													<td class="text-right analysis-table-obj-{{ years[0] }}">__obj_{{ years[0] }}__</td>
													<td class="text-right analysis-table-obj-{{ years[0]-1 }}">__obj_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-obj-{{ years[0]-2 }}">__obj_{{ years[0]-2 }}__</td>
												</tr>
												<tr>
													<td>{% if type == "ca" %}CA réalisé{% else %}Nombre d'heures réalisées{% endif %}</td>
													<td class="text-right analysis-table-real-{{ years[0] }}">__val_{{ years[0] }}__</td>
													<td class="text-right analysis-table-real-{{ years[0]-1 }}">__val_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-real-{{ years[0]-2 }}">__val_{{ years[0]-2 }}__</td>
												</tr>
												<tr>
													<td>% d'atteinte de l'objectif (part de marché)</td>
													<td class="text-right analysis-table-percent-{{ years[0] }}">__percent_{{ years[0] }}__</td>
													<td class="text-right analysis-table-percent-{{ years[0]-1 }}">__percent_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-percent-{{ years[0]-2 }}">__percent_{{ years[0]-2 }}__</td>
												</tr>
												<tr>
													<td>Evolution en % sur l'Objectif</td>
													<td class="text-right analysis-table-evol-{{ years[0] }}">__evol_{{ years[0] }}__</td>
													<td class="text-right analysis-table-evol-{{ years[0]-1 }}">__evol_{{ years[0]-1 }}__</td>
													<td class="text-right analysis-table-evol-{{ years[0]-2 }}">Non affichable</td>
												</tr>
												</tbody>
											</table>
										</div>
									{% endif %}
								</div>
								<div class="tab-pane" id="tab_graph">
									<div class="chart-infos">
										Veuillez sélectionner une période d'affichage autre que "Année" pour générer le graphique
									</div>
									<div class="ca-chart-container hidden">
										<canvas></canvas>
									</div>
								</div>
							</div>
							<div class="analysis-container-sub"></div>
						</div>
					</div>
				</div>
			</div>
		</template>

	</div>

{% endblock %}

{% block javascripts %}
	<script src="{{ asset('admin/plugins/sparkline/jquery.sparkline.min.js') }}"></script>
	{{ encore_entry_script_tags('app') }}
{% endblock %}