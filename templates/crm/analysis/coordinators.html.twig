{% extends 'crm/base.html.twig' %}

{% block title %}Analyse commerciale - Coordinateurs{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}

	<div {{ stimulus_controller("crm/coordinators", { type: type, url: url("crm_analysis_data", { type: type }) }) }}>

		{% if form is defined %}
			<div class="box box-solid box-primary">
				<div class="box-header">
					<h3 class="box-title">Filtres</h3>
				</div>
				<div class="box-body eduprat-search">
					{{ form_start(form, {'attr': {'id': 'eduprat-analysis'}}) }}
					<div class="row">
						<div class="col-sm-12">
							<div class="">
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect">
										{{ form_label(form.categories) }}<br>
										{{ form_widget(form.categories) }}
									</div>
								</div>
								<div class="col-lg-12">
									<div class="form-group mrm">
										{{ form_label(form.exerciseMode) }}<br>
										{{ form_widget(form.exerciseMode) }}
									</div>

									<div class="text-right">
										<div class="mrm" style="display: inline-block">
											<br>
											<div class="checkbox"><label for="eduprat_analysis_show" class="required"><input type="checkbox" id="eduprat_analysis_show" name="eduprat_analysis[show]" required="required" data-action="click->crm--coordinators#toggleEmptyLines"> Masquer les lignes sans données</label></div>
										</div>
										<div class="btn btn-eduprat" id="analysis-filter" data-action="click->crm--coordinators#update">
											<span class="fa fa-search"></span> {{ "admin.global.apply"|trans }}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					{{ form_end(form, {'render_rest': false}) }}
				</div>
			</div>
		{% endif %}

		<div id="analysis-container" class="hidden">
			<div class="col-lg-12">
				<div class="analysis-loading">
					<i class="fa fa-spin fa-spinner mbs"></i>
					Chargement des données ...
					<div class="progress progress-xs mtl">
						<div id="analysis-loading-progress" class="progress-bar progress-bar-danger progress-bar-striped" role="progressbar" style="width: 1%">
							<span class="sr-only"></span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div id="analysis-container" class="row analysis-container">
			<div class="col-lg-12">
				<div class="col-lg-12">
					<div class="box box-info">
						<div class="box-header with-border">
							<h3 class="box-title">Formations à l'initiative du coordinateur</h3>
						</div>
						<div class="box-body">
							<div class="table-responsive table-striped">
								<table id="analysis-coordinator-table" class="table no-margin table-stacktable">
									<thead>
									<tr>
										<th style="width: 16%">Coordinateur</th>
										<th style="width: 28%" class="text-right">Année en cours</th>
										<th style="width: 28%" class="text-right">Année N-1</th>
										<th style="width: 28%" class="text-right">Année N-2</th>
									</tr>
									</thead>
									<tbody>
									{% for person in coordinators %}
										<tr class="analysis-table-coordinator" data-id="{{ person.id }}">
											<td>{{ person.invertedFullname }}</td>
											<td class="text-right analysis-table-initiator-{{ person.id }}-{{ years[0] }}"></td>
											<td class="text-right analysis-table-initiator-{{ person.id }}-{{ years[0]-1 }}"></td>
											<td class="text-right analysis-table-initiator-{{ person.id }}-{{ years[0]-2 }}"></td>
										</tr>
									{% endfor %}
									</tbody>
								</table>
							</div>

						</div>
					</div>
				</div>
			</div>
		</div>

	</div>

{% endblock %}

{% block javascripts %}
	<script src="{{ asset('admin/plugins/sparkline/jquery.sparkline.min.js') }}"></script>
	{{ encore_entry_script_tags('app') }}
{% endblock %}