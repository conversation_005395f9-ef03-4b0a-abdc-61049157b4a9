{% extends 'crm/base.html.twig' %}

{% block title %}Paramétrage objectifs globaux{% endblock %}

{% block body %}
    <p>
        <em>Modification des paramètres pour l'année sélectionnée :</em>
    </p>
    <ul class="pagination">
        {% set maxYear = "now"|date('Y') + 1 %}
        {% for dateYear in 2017..maxYear %}
            <li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('crm_analysis_config', {year: dateYear}) }}">{{ dateYear }}</a></li>
        {% endfor %}
    </ul>

    <div class="box box-info">
        <div class="box-body">
            {% if not form.vars.valid %}
                <div class="callout callout-danger">
                    {% if form.vars.errors|length %}
                        {{ form_errors(form) }}
                    {% else %}
                        {{ 'participant.edit.error'|trans }}
                    {% endif %}
                </div>
            {% endif %}
            {{ form_start(form) }}
            {% for index, config in configs %}
                <div class="form-inline mbm pbm" style="border-bottom: 1px solid #eee">
                    <label for="" style="width: 30%">{{ config.category }}</label>
                    <div class="hidden-lg"><br></div>
                    {{ form_row(form.configs[index].ca, {'attr': {'class': 'mrl'}}) }}
                    <div class="hidden-lg"><br></div>
                    {{ form_row(form.configs[index].hours, {'attr': {'class': 'mrl'}}) }}
                </div>
            {% endfor %}
{#            {{ form_rest(form) }}#}
            <div class="form-group">
                <button type="submit" class="btn-eduprat btn">{{ "admin.global.save"|trans }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>

{% endblock %}

{% block javascripts %}

    <script type="text/javascript">

    </script>

{% endblock %}