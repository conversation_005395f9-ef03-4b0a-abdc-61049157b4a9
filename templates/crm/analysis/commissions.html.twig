{% extends 'crm/base.html.twig' %}

{% block title %}Analyse commerciale - Commissions coordinateurs{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
	<link href="{{ asset('admin/plugins/datatables/jquery.dataTables.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}

	<div>

		<ul class="pagination">
			{% set maxYear = "now"|date('Y') + 1 %}
			{% for dateYear in 2017..maxYear %}
				<li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('crm_analysis_commissions', {year: dateYear}) }}">{{ dateYear }}</a></li>
			{% endfor %}
		</ul>

		{% if form is defined %}
			<div class="box box-solid box-primary">
				<div class="box-header">
					<h3 class="box-title">Filtres</h3>
				</div>
				<div class="box-body eduprat-search">
					{{ form_start(form, {'attr': {'id': 'eduprat-analysis'}}) }}
					<div class="row">
						<div class="col-sm-12">
							<div class="">
								<div class="col-lg-4">
									<div class="form-group mrm" data-controller="multiselect">
										{{ form_label(form.coordinatorsId) }}<br>
										{{ form_widget(form.coordinatorsId) }}
									</div>
								</div>
							</div>
						</div>
					</div>
					{{ form_end(form, {'render_rest': false}) }}
				</div>
			</div>
		{% endif %}

		<div id="analysis-container" class="row analysis-container">
			<div class="col-lg-12">
				<div class="col-lg-12">
					<div class="box box-info">
						<div class="box-header with-border">
							<h3 class="box-title">Commissions coordinateurs</h3>
						</div>
						<div class="box-body">
							<div class="table-responsive table-striped">
								<table id="table-commissions" class="table no-margin table-stacktable">
									<thead>
									<tr>
										<th style="width: 16%">Coordinateur</th>
										<th style="width: 21%" class="text-right">Cumul des marges</th>
										<th style="width: 21%" class="text-right">Commissions taux de base</th>
										<th style="width: 21%" class="text-right">Commission exceptionnelle</th>
										<th style="width: 21%" class="text-right">Commissions comptabilisées</th>
									</tr>
									</thead>
									<tbody>
									{% for c in commissions %}
										<tr class="analysis-table-coordinator" data-id="{{ c.person.id }}">
											<td><a href="{{ url("admin_coordinator_dashboard", { id: c.person.id, year: year }) }}">{{ c.person.invertedFullname }}</a></td>
											{% if c.commission %}
												<td class="text-right" data-order="{{ c.commission.marges }}">{{ c.commission.marges|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="{{ c.commission.commissionBase }}">{{ c.commission.commissionBase|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="{{ c.commission.commissionExceptionnelle }}">{{ c.commission.commissionExceptionnelle|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="{{ c.commission.commissionsComptabilisees }}">{{ c.commission.commissionsComptabilisees|number_format(2, '.', ' ') }} €</td>
											{% else %}
												<td class="text-right" data-order="0">{{ 0|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="0">{{ 0|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="0">{{ 0|number_format(2, '.', ' ') }} €</td>
												<td class="text-right" data-order="0">{{ 0|number_format(2, '.', ' ') }} €</td>
											{% endif %}
										</tr>
									{% endfor %}
									</tbody>
								</table>
							</div>

						</div>
					</div>
				</div>
			</div>
		</div>

	</div>

{% endblock %}

{% block javascripts %}
	{{ encore_entry_script_tags('app') }}
	<script type="text/javascript" src="{{ asset('admin/plugins/datatables/jquery.dataTables.js') }}"></script>
	<script>

		$(document).ready(function () {

			$.extend( true, $.fn.dataTable.defaults, {
				'paging'      : false,
				'lengthChange': false,
				'searching'   : false,
				'ordering'    : true,
				'info'        : false,
				'autoWidth'   : false,
				'orderSequence' : ["desc"]
			} );

			$('.reporting-row-empty').remove();

			try {
				$('#table-commissions').DataTable();
			} catch(e) {
				console.error(e);
			}

			$('#eduprat_analysis_coordinatorsId').change(function() {
				let ids = $(this).val();
				if (ids && ids.length) {
					$('.analysis-table-coordinator').each(function() {
						$(this).toggleClass("hidden", ids.indexOf($(this).data("id").toString()) === -1);
					})
				} else {
					$('.analysis-table-coordinator').removeClass("hidden");
				}
			})

		});
	</script>
{% endblock %}