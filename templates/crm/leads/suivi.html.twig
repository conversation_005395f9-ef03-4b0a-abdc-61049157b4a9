{% extends 'crm/base.html.twig' %}

{% block title %}<PERSON><PERSON><PERSON> des leads{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    {% if form is defined %}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <h3 class="box-title">Rechercher un professionnel de santé</h3>
            </div>
            <div class="box-body eduprat-search">

                {% if not form.vars.valid %}
                    {% for error in form.vars.errors.form.getErrors(true) %}
                        <div class="callout callout-danger">{{ (error.cause.propertyPath|replace({'data.': 'admin.participant.'}))|trans }} : {{ error.message }}</div>
                    {% endfor %}
                {% endif %}

                {{ form_start(form, {'attr': {'id': 'eduprat-participant-search', 'class': 'form-inline'}}) }}
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="form-group mrm">
                                {{ form_label(form.firstname) }}<br>
                                {{ form_widget(form.firstname) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.lastname) }}<br>
                                {{ form_widget(form.lastname) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.start) }}<br>
                                {{ form_widget(form.start) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.end) }}<br>
                                {{ form_widget(form.end) }}
                            </div><br>
                            <div class="form-group mrm">
                                {{ form_label(form.leadType) }}<br>
                                {{ form_widget(form.leadType) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.leadStatus) }}<br>
                                {{ form_widget(form.leadStatus) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.isProspect) }}<br>
                                {{ form_widget(form.isProspect) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.advisor) }}<br>
                                {{ form_widget(form.advisor) }}
                            </div>
                            {% if form.coordinator is defined %}
                                <div class="form-group mrm">
                                    {{ form_label(form.coordinator) }}<br>
                                    {{ form_widget(form.coordinator) }}
                                </div>
                            {% endif %}
                            {% if form.webmaster is defined %}
                                <div class="form-group mrm">
                                    {{ form_label(form.webmaster) }}<br>
                                    {{ form_widget(form.webmaster) }}
                                </div>
                            {% endif %}
                            <br>
                            <div class="form-group mrm">
                                {{ form_label(form.category) }}<br>
                                {{ form_widget(form.category) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.speciality) }}<br>
                                {{ form_widget(form.speciality) }}
                            </div>
                            <div class="form-group mrm">
                                {{ form_label(form.leadState) }}<br>
                                {{ form_widget(form.leadState) }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            {% if not sib %}
                                <div class="text-right">
                                    {% if form|length > 2 %} <br>{% endif %}
                                    <button class="btn btn-eduprat" type="submit">
                                        <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                                    </button>
                                    {% if not app.user.isCoordinatorLbi %}
                                        <button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{{ url('lead_generate_csv_export') }}">
                                            <span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }}
                                        </button>
                                    {% endif %}
                                </div>
                                <div class="text-right">
                                    <div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
                                        <span>{{ "admin.formation.generation"|trans }}</span>
                                    </div>
                                    {% if hasGenerated %}
                                        {% if hasFinished %}
                                            <div id="generate-participant-export-download" class="text-right">
                                                <a href="{{ url('csv_lead_file_get') }}" target="_blank">{{ "admin.participant.export"|trans }}</a>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                    {% if hasError %}
                                        <div id="generate-participant-export-error" class="text-right">
                                            <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {{ form_widget(form._token) }}
                {{ form_end(form, {'render_rest': false}) }}
            </div>
        </div>
    {% endif %}
    <div class="box box-primary">
        <div class="box-header">
            <div class="row">
                <div class="col-sm-4"><h3 class="box-title">{{ "participant.titles.index"|trans }}</h3></div>
                <div class="col-sm-4 text-center"><h3 class="box-title red-message">Cliquez sur "Enregistrer" pour toute modification effectuée</h3></div>
                <div class="col-sm-4"></div>
            </div>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-stacktable" data-display-header="false" data-header-index="2">
                        <thead>
                        <tr>
                            {# <th>{{ "admin.participant.leadStatusShort"|trans }}</th> #}
                            <th>{{ "admin.participant.leadCreationDateShort"|trans }}</th>
                            <th>{{ "admin.participant.leadType"|trans }}</th>
                            <th>{{ "admin.participant.advisorShort"|trans }}</th>
                            <th>{{ "admin.participant.leadReferent"|trans }}</th>
                            <th>{{ "admin.participant.ps"|trans }}</th>
                            <th>{{ "admin.participant.speciality"|trans }}</th>
                            <th>{{ "admin.participant.leadContactDate"|trans }}</th>
                            <th width="20%">{{ "admin.participant.leadComment"|trans }}</th>
                            <th>{{ "admin.participant.leadState"|trans }}</th>
                            <th>{{ "admin.participant.leadCommentEduprat"|trans }}</th>
                            <th width="6%">{{ "admin.global.actions"|trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for participant in participants %}
                            <tr {% if participant.isProspect %}class="table-row-participant-prospect"{% endif %}>
                                {# <td style="text-align:center">
                                    {% if participant.isLeadOn %}
                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                    {% else %}
                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                    {% endif %}
                                </td> #}
                                <td>{% if participant.leadCreationDate %}{{ participant.leadCreationDate| date ('d/m/Y') }} {% endif %}</td>
                                <td>{{ participant.leadType }}</td>
                                <td>{% if participant.advisor %}{{ participant.advisor.firstname }}<br>{{ participant.advisor.lastname }}{% endif %}</td>
                                <td>{% if participant.leadReferent %}{{ participant.leadReferent.firstname }}<br>{{ participant.leadReferent.lastname }}{% endif %}</td>
                                <td>{{ participant.firstname }}<br>{{participant.lastname}}</td>
                                <td>{{ participant.speciality }}</td>
                                <td><textarea type="text" data-participantid="{{ participant.id }}" id="leadContactDate-{{ participant.id }}" class="leadContactDate form-control" value="{{ participant.leadContactDate }}">{{ participant.leadContactDate }}</textarea></td>
                                <td>
                                    <textarea style="width:100% {% if not is_granted('ROLE_WEBMASTER') %};display:none{% endif %}" data-participantid="{{ participant.id }}" class="leadComment" id="leadComment-{{ participant.id }}">{{ participant.leadComment }}</textarea>
                                    {% if not is_granted('ROLE_WEBMASTER') %} {{ participant.leadComment }} {% endif %}
                                </td>
                                <td id="stateCell-{{participant.id}}">
                                    <select style="color:white" data-participantid="{{ participant.id }}" class="leadState form-control"  id="leadState-{{ participant.id }}" value="{{participant.leadState}}" style="margin-top: 8px;">
                                        <option class="leadStateOption" {% if participant.leadState == "A traiter" %} selected="selected" {% endif %} value="A traiter">A traiter</option>
                                        <option class="leadStateOption" {% if participant.leadState == "En cours" %} selected="selected" {% endif %} value="En cours">En cours</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Inscrit" %} selected="selected" {% endif %} value="Inscrit">Inscrit</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Déjà client" %} selected="selected" {% endif %} value="Déjà client">Déjà client</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Non intéressé" %} selected="selected" {% endif %} value="Non intéressé">Non intéressé</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Non joignable" %} selected="selected" {% endif %} value="Non joignable">Non joignable</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Non éligible" %} selected="selected" {% endif %} value="Non éligible">Non éligible</option>
                                        <option class="leadStateOption" {% if participant.leadState == "Rien à proposer" %} selected="selected" {% endif %} value="Rien à proposer">Rien à proposer</option>
                                    </select>
                                </td>
                                <td><textarea data-participantid="{{ participant.id }}" class="leadCommentEduprat" id="leadCommentEduprat-{{ participant.id }}">{{ participant.leadCommentEduprat }}</textarea></td>
                                <td id="action-{{ participant.id }}">
                                    <a class="mbn btn btn-icon btn-eduprat evalListLink" title="Informations du Lead"><i class="fa fa-plus"></i></a>
                                    <a href="{{ url('admin_participant_show', {'id': participant.id }) }}" class="mbn btn btn-icon btn-eduprat" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
                                    <a id="{{ participant.id }}" class="mbn btn btn-eduprat btn-update-lead update-lead " data-url="{{ url('crm_updateLead', {'id': participant.id }) }}" title="Mettre à jour le Lead">Enregistrer</i></a>
                                    {% set leadHistories = participant.getLeadHistoriesBetweenDates(search.start, search.end) %}
                                    {% if leadHistories|length > 0 %} 
                                        <a style="width:72px" class="mbn btn btn-eduprat btn-update-lead leadHistoryListLink" title="Historique du Lead">Historique</i></a>
                                    {% endif %}
                                </td>
                                <td id="fakeAction-{{ participant.id }}" style="display:none">
                                    <a class="mbn btn btn-icon btn-eduprat evalListLink" title="Informations du Lead"><i class="fa fa-plus"></i></a>
                                    <a class="mbn btn btn-icon btn-eduprat" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
                                    <a disabled class="mbn btn btn-icon btn-eduprat btn-update-lead" title="Mettre à jour le Lead"><i class="fa fa-spin fa-spinner"></i></a>
                                </td>
                            </tr>
                            <tr class="sessionsWrap">
                                <td colspan="9">
                                    <div class="evalList">
                                        <div class="collaps-list collaps-list-session">
                                            <table class="table table-bordered table-responsive">
                                                <tr>
                                                    <th>{{ "admin.participant.adressePost"|trans }}</th>
                                                    <th>{{ "admin.participant.zipCode"|trans }}</th>
                                                    <th>{{ "admin.participant.city"|trans }}</th>
                                                    <th>{{ "admin.participant.email"|trans }}</th>
                                                    <th>{{ "admin.participant.phone"|trans }}</th>
                                                    <th>{{ "admin.participant.type"|trans }}</th>
                                                    <th>{{ "admin.participant.exerciseMode"|trans }}</th>
                                                <tr>
                                                <tr>
                                                    <td>{{ participant.address }}</td>
                                                    <td>{{ participant.zipCode }}</td>
                                                    <td>{{ participant.city }}</td>
                                                    <td>{{ participant.email }}</td>
                                                    <td>{{ participant.phone }}</td>
                                                    <td>{% if not participant.isProspect %}Participant{% else %} Prospect {% endif %}</td>
                                                    <td>{{ participant.exerciceMode }}</td>
                                                <tr>
                                            </table>
                                        </div>
                                    </div>
                                    {% if leadHistories|length > 0 %}
                                        <div class="leadHistoryList">
                                            <div class="collaps-list collaps-list-session">
                                                <table class="table table-bordered table-responsive">
                                                    <tr>
                                                        <th>{{ "admin.participant.leadCreationDateShort"|trans }}</th>
                                                        <th>{{ "admin.participant.leadType"|trans }}</th>
                                                        <th>{{ "admin.participant.advisorShort"|trans }}</th>
                                                        <th>{{ "admin.participant.leadReferent"|trans }}</th>
                                                        <th>{{ "admin.participant.leadContactDate"|trans }}</th>
                                                        <th width="20%">{{ "admin.participant.leadComment"|trans }}</th>
                                                        <th>{{ "admin.participant.leadState"|trans }}</th>
                                                        <th>{{ "admin.participant.leadCommentEduprat"|trans }}</th>
                                                        <th></th>
                                                    <tr>
                                                    {% for leadHistory in leadHistories %}
                                                        <tr>
                                                            <td>{% if leadHistory.leadCreationDate %}{{ leadHistory.leadCreationDate | date ('d/m/Y') }}{% endif %}</td>
                                                            <td>{{ leadHistory.leadType }}</td>
                                                            <td>{% if leadHistory.advisor %}{{ leadHistory.advisor.fullname }}{% endif %}</td>                                                        
                                                            <td>{% if leadHistory.leadReferent %}{{ leadHistory.leadReferent.fullname }}{% endif %}</td>
                                                            <td>{{ leadHistory.leadContactDate }}</td>
                                                            <td>{{ leadHistory.leadComment }}</td>
                                                            <td>{{ leadHistory.leadState }}</td>    
                                                            <td>{{ leadHistory.leadCommentEduprat }}</td>
                                                            <td><a href="{{ url('admin_lead_hisory_delete', {'id': leadHistory.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a></td>
                                                        </tr>
                                                    {% endfor %}
                                                </table>
                                            </div>
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    {% set searchPage = search.getParams() %}
                    {% if sib %}
                        {% set searchPage = searchPage|merge({"sendinblue": 1}) %}
                    {% endif %}
                    {% if npages > 1 %}
                        {% include 'crm/common/pagination.html.twig' with {urlName : 'crm_leads_suivi', extraParams : searchPage } %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.quicksearch.js') }}"></script>
    <script type="text/javascript">
        $('[name=csvAll]').click(function (e) {
	        var $this = $(this);
	        if ($this.data('started') === true) return false;
	        e.preventDefault();
	        jQuery.ajax({
		        url: $this.data('href'),
                type: "POST",
		        data: $('#eduprat-participant-search').serialize(),
		        success: function(data, textStatus, xhr) {
			        if (data.status == "ok") {
				        $('#generate-participant-export-loading').removeClass('hidden');
				        $('#generate-participant-export-download').addClass('hidden');
				        $('#generate-participant-export-error').addClass('hidden');
			        }
		        },
		        error: function(xhr, textStatus, errorThrown) {
			        console.log(errorThrown);
		        }
	        });
	        $this.data('started', true);
        });

        $( document ).ready(function() {
           
            $('.update-lead').click(function (e) {
                var leadContactDateId = '#leadContactDate-' + this.id;
                var leadCommentId = '#leadComment-' + this.id;
                var leadCommentEdupratId = '#leadCommentEduprat-' + this.id;
                var leadStateId = '#leadState-' + this.id;
                var realAction = '#action-' + this.id;
                var fakeAction = '#fakeAction-' + this.id;

                $(realAction).hide();
                $(fakeAction).show();

                leadContactDate = $(leadContactDateId).val() != "" ? $(leadContactDateId).val().replaceAll('/', '_s_') : "null";
                leadComment = $(leadCommentId).val() != "" ? $(leadCommentId).val().replaceAll('/', '_s_') : "null";
                leadCommentEduprat = $(leadCommentEdupratId).val() != "" ? $(leadCommentEdupratId).val().replaceAll('/', '_s_') : "null";
                
                jQuery.ajax({
                    url: $(this).data('url') + "/" + leadContactDate + "/" + leadComment + '/' + leadCommentEduprat + '/' + $(leadStateId).val(),
                    type: "POST",
                    data: '',
                    success: function(data, textStatus, xhr) {
                        $(fakeAction).hide();
                        $(realAction).show();
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        alert("Error");
                    }
                });
            });

            $(".leadState").change(function(){

                var leadStateId = '#leadState-' + $(this).attr("data-participantid");
                var select = 'select#leadState-' + $(this).attr("data-participantid");
                var stateCellId = '#stateCell-'  + $(this).attr("data-participantid");
                $(leadStateId).val($(this).children("option:selected").val());



                switch($(this).children("option:selected").val()) {
                case 'A traiter':
                    $(select).css("background-color", "#DF2100");
                    break;
                case 'En cours':
                    $(select).css("background-color", "#CC5000");
                    break;
                case 'Inscrit':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Déjà client':
                    $(select).css("background-color", "#3FA20D");
                    break;
                case 'Non intéressé':
                    $(select).css("background-color", "#a3a3a3");
                    break;
                case 'Non joignable':
                    $(select).css("background-color", "#a3a3a3");
                    break;
                case 'Non éligible':
                    $(select).css("background-color", "#a3a3a3");
                    break;
                case 'Rien à proposer':
                    $(select).css("background-color", "#a3a3a3");
                    break;
                }
            });

            $(".leadState").change();

            $(".leadComment").change(function(){
                var leadCommentId = '#leadComment-' + $(this).attr("data-participantid");
                $(leadCommentId).val(this.value);
            });

            $(".leadCommentEduprat").change(function(){
                var leadCommentEdupratId = '#leadCommentEduprat-' + $(this).attr("data-participantid");
                $(leadCommentEdupratId).val(this.value);
            });

            $(".leadContactDate").change(function(){
                var leadContactDateId = '#leadContactDate-' + $(this).attr("data-participantid");
                $(leadContactDateId).val(this.value);
            });

	        var formType = {{ sib }}
		    $('#eduprat_lead_search_formType').val(formType);
	        var multiselectInitialized = false;
			initMultiselect();
			function initMultiselect() {
				if (!multiselectInitialized) {
					$('#eduprat_lead_search_leadState').multiSelect({
						selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						afterInit: function(ms){
							var that = this,
								$selectableSearch = that.$selectableUl.prev(),
								$selectionSearch = that.$selectionUl.prev(),
								selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
								selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

							that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
								.on('keydown', function(e){
									if (e.which === 40){
										that.$selectableUl.focus();
										return false;
									}
								});

							that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
								.on('keydown', function(e){
									if (e.which == 40){
										that.$selectionUl.focus();
										return false;
									}
								});
							multiselectInitialized = true;
						},
						afterSelect: function(){
							this.qs1.cache();
							this.qs2.cache();
						},
						afterDeselect: function(){
							this.qs1.cache();
							this.qs2.cache();
						}
					});

                    $('#eduprat_lead_search_category').multiSelect({
						selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						afterInit: function(ms){
							var that = this,
								$selectableSearch = that.$selectableUl.prev(),
								$selectionSearch = that.$selectionUl.prev(),
								selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
								selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

							that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
								.on('keydown', function(e){
									if (e.which === 40){
										that.$selectableUl.focus();
										return false;
									}
								});

							that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
								.on('keydown', function(e){
									if (e.which == 40){
										that.$selectionUl.focus();
										return false;
									}
								});
							multiselectInitialized = true;
						},
						afterSelect: function(){
							this.qs1.cache();
							this.qs2.cache();
						},
						afterDeselect: function(){
							this.qs1.cache();
							this.qs2.cache();
						}
					});

                    $('#eduprat_lead_search_speciality').multiSelect({
						selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
						afterInit: function(ms){
							var that = this,
								$selectableSearch = that.$selectableUl.prev(),
								$selectionSearch = that.$selectionUl.prev(),
								selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
								selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

							that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
								.on('keydown', function(e){
									if (e.which === 40){
										that.$selectableUl.focus();
										return false;
									}
								});

							that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
								.on('keydown', function(e){
									if (e.which == 40){
										that.$selectionUl.focus();
										return false;
									}
								});
							multiselectInitialized = true;
						},
						afterSelect: function(){
							this.qs1.cache();
							this.qs2.cache();
						},
						afterDeselect: function(){
							this.qs1.cache();
							this.qs2.cache();
						}
					});
				}
			}

			$('#eduprat_lead_search_leadState').multiSelect({
				selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				afterInit: function(ms){
					var that = this,
						$selectableSearch = that.$selectableUl.prev(),
						$selectionSearch = that.$selectionUl.prev(),
						selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
						selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

					that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
						.on('keydown', function(e){
							if (e.which === 40){
								that.$selectableUl.focus();
								return false;
							}
						});

					that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
						.on('keydown', function(e){
							if (e.which == 40){
								that.$selectionUl.focus();
								return false;
							}
						});
				},
				afterSelect: function(){
					this.qs1.cache();
					this.qs2.cache();
				},
				afterDeselect: function(){
					this.qs1.cache();
					this.qs2.cache();
				}
			});

            $('#eduprat_lead_search_category').multiSelect({
				selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				afterInit: function(ms){
					var that = this,
						$selectableSearch = that.$selectableUl.prev(),
						$selectionSearch = that.$selectionUl.prev(),
						selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
						selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

					that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
						.on('keydown', function(e){
							if (e.which === 40){
								that.$selectableUl.focus();
								return false;
							}
						});

					that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
						.on('keydown', function(e){
							if (e.which == 40){
								that.$selectionUl.focus();
								return false;
							}
						});
				},
				afterSelect: function(){
					this.qs1.cache();
					this.qs2.cache();
				},
				afterDeselect: function(){
					this.qs1.cache();
					this.qs2.cache();
				}
			});

            $('#eduprat_lead_search_speciality').multiSelect({
				selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px; width:100%' autocomplete='off' placeholder='Recherche ...'>",
				afterInit: function(ms){
					var that = this,
						$selectableSearch = that.$selectableUl.prev(),
						$selectionSearch = that.$selectionUl.prev(),
						selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
						selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

					that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
						.on('keydown', function(e){
							if (e.which === 40){
								that.$selectableUl.focus();
								return false;
							}
						});

					that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
						.on('keydown', function(e){
							if (e.which == 40){
								that.$selectionUl.focus();
								return false;
							}
						});
				},
				afterSelect: function(){
					this.qs1.cache();
					this.qs2.cache();
				},
				afterDeselect: function(){
					this.qs1.cache();
					this.qs2.cache();
				}
			});

	        // const category = document.querySelector("#eduprat_participant_search_category");
	        // const speciality = document.querySelector("#eduprat_participant_search_speciality");

	        // category.onchange = function(e) {
		       // const selected = e.target.options[e.target.selectedIndex].text;
		        //speciality.querySelectorAll("optgroup").forEach((opt) => {
			      //  opt.classList.toggle("hidden", selected !== "Tous" && opt.label !== selected);
		        //});
	        //}

	        // category.dispatchEvent(new Event('change'));

        });

    </script>
{% endblock %}
