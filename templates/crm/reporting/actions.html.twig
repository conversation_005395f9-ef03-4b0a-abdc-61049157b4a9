{% extends 'crm/base.html.twig' %}

{% block title %}Rapport d'activité - Actions{% endblock %}

{% block body %}
    <div class="row">
        {% for key, andpcStatus in reporting.andpcStatuses[years[0]] %}
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box bg-{{ andpcStatus.color }}">
                <span class="info-box-icon"><i class="fa fa-{{ andpcStatus.icon }}"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">{{ andpcStatus.label }}</span>
                    {% for year, statuses in reporting.andpcStatuses %}
                        <span class="{% if loop.first %}info-box-number{% else %}progress-description{% endif %}">{{ year }} : {{ statuses[key].total }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="col-lg-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">Top 10 du nombre d'actions pluriprofessionnelles</h3>
                        <div class="box-tools">
                            <div class="input-group input-group-sm" style="width: 150px;">
                                <select id="pluri-year-select" class="form-control pull-right year-select" data-table="pluri-year-table">
                                    {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        {% for y, professional in reporting.professional %}
                            <div class="table-responsive table-striped pluri-year-table {% if not loop.first %}hidden{% endif %} " data-year="{{ y }}">
                                <table class="table no-margin table-stacktable">
                                    <thead>
                                    <tr>
                                        <th style="width: 60%">Combinaisons de professions</th>
                                        <th class="text-right" style="width: 20%">Nombre d'actions</th>
                                        <th class="text-right" style="width: 20%">% par rapport au total</th>
                                    </tr>
                                    <tr>
                                        <th>Total des actions pluriprofessionnelles</th>
                                        <th class="text-right">{{ professional.totalActionsPluri }}</th>
                                        <th class="text-right">100%</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for key, item in professional.categoriesGroup|slice(0, 10) %}
                                        <tr>
                                            <td class="table-cell-ellipsis" title="{{ item.categories|join(" + ") }}">{{ item.categories|join(" + ") }}</td>
                                            <td class="text-right">{{ item.total }}</td>
                                            <td class="text-right">{{ item.percent|number_format(2, '.', ' ') }}%</td>
                                        </tr>
                                    {% endfor %}
                                    {% if professional.categoriesGroup|length < 10 %}
                                        {% for i in 1..(10-professional.categoriesGroup|length) %}
                                            <tr data-empty-row="true"><td>&nbsp</td><td></td><td></td></tr>
                                        {% endfor %}
                                    {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">Top 10 du nombre d'actions monoprofessionnelles</h3>
                        <div class="box-tools">
                            <div class="input-group input-group-sm" style="width: 150px;">
                                <select id="mono-year-select" class="form-control pull-right year-select" data-table="mono-year-table">
                                    {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        {% for y, professional in reporting.professional %}
                            <div class="table-responsive table-striped mono-year-table {% if not loop.first %}hidden{% endif %}" data-year="{{ y }}">
                                <table class="table no-margin table-stacktable">
                                    <thead>
                                    <tr>
                                        <th style="width: 60%">Catégories professionnelles</th>
                                        <th class="text-right" style="width: 20%">Nombre d'actions</th>
                                        <th class="text-right" style="width: 20%">% par rapport au total</th>
                                    </tr>
                                    <tr>
                                        <th>Total des actions monoprofessionnelles</th>
                                        <th class="text-right">{{ professional.totalActionsMono }}</th>
                                        <th class="text-right">100%</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for key, item in professional.categoriesMono|slice(0, 10) %}
                                        <tr>
                                            <td>{{ item.categories|join(" + ") }}</td>
                                            <td class="text-right">{{ item.total }}</td>
                                            <td class="text-right">{{ item.percent|number_format(2, '.', ' ') }}%</td>
                                        </tr>
                                    {% endfor %}
                                    {% if professional.categoriesMono|length < 10 %}
                                        {% for i in 1..(10-professional.categoriesMono|length) %}
                                            <tr data-empty-row="true"><td>&nbsp</td><td></td><td></td></tr>
                                        {% endfor %}
                                    {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-12">
            <div class="col-lg-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">Top 10 des publics Programme intégré</h3>
                        <div class="box-tools">
                            <div class="input-group input-group-sm" style="width: 150px;">
                                <select id="method-integre-year-select" class="form-control pull-right year-select" data-table="method-integre-year-table">
                                    {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        {% for y, methods in reporting.methods %}
                            <div class="table-responsive table-striped method-integre-year-table {% if not loop.first %}hidden{% endif %}" data-year="{{ y }}">
                                <table class="table no-margin table-stacktable">
                                    <thead>
                                    <tr>
                                        <th style="width: 60%">Catégories professionnelles</th>
                                        <th class="text-right" style="width: 20%">Nombre d'actions</th>
                                        <th class="text-right" style="width: 20%">% par rapport au total</th>
                                    </tr>
                                    <tr>
                                        <th>Total des actions Programme intégré</th>
                                        <th class="text-right">{{ methods.integre.total }}</th>
                                        <th class="text-right">100%</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for item in methods.integre.categories|slice(0, 10) %}
                                        <tr>
                                            <td>{{ item.category }}</td>
                                            <td class="text-right">{{ item.total }}</td>
                                            <td class="text-right">{{ item.percent|number_format(2, '.', ' ') }}%</td>
                                        </tr>
                                    {% endfor %}
                                    {% if methods.integre.categories|length < 10 %}
                                        {% for i in 1..(10-methods.integre.categories|length) %}
                                            <tr data-empty-row="true"><td>&nbsp</td><td></td><td></td></tr>
                                        {% endfor %}
                                    {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">Top 10 des publics Formation continue</h3>
                        <div class="box-tools">
                            <div class="input-group input-group-sm" style="width: 150px;">
                                <select id="method-continue-year-select" class="form-control pull-right year-select" data-table="method-continue-year-table">
                                    {% for year in years %}
                                        <option value="{{ year }}">{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        {% for y, methods in reporting.methods %}
                            <div class="table-responsive table-striped method-continue-year-table {% if not loop.first %}hidden{% endif %}" data-year="{{ y }}">
                                <table class="table no-margin table-stacktable">
                                    <thead>
                                    <tr>
                                        <th style="width: 60%">Catégories professionnelles</th>
                                        <th class="text-right" style="width: 20%">Nombre d'actions</th>
                                        <th class="text-right" style="width: 20%">% par rapport au total</th>
                                    </tr>
                                    <tr>
                                        <th>Total des actions Formation continue</th>
                                        <th class="text-right">{{ methods.continue.total }}</th>
                                        <th class="text-right">100%</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for item in methods.continue.categories|slice(0, 10) %}
                                        <tr>
                                            <td>{{ item.category }}</td>
                                            <td class="text-right">{{ item.total }}</td>
                                            <td class="text-right">{{ item.percent|number_format(2, '.', ' ') }}%</td>
                                        </tr>
                                    {% endfor %}
                                    {% if methods.continue.categories|length < 10 %}
                                        {% for i in 1..(10-methods.continue.categories|length) %}
                                            <tr data-empty-row="true"><td>&nbsp</td><td></td><td></td></tr>
                                        {% endfor %}
                                    {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Répartition du nombre d'actions publiées par format</h3>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table no-margin table-stacktable">
                            <thead>
                            <tr>
                                <th>Année</th>
                                {% for format in reporting.formats[reporting.formats|keys[0]]|keys %}
                                    <th class="text-right">{{ format }}</th>
                                {% endfor %}
                            </tr>
                            </thead>
                            <tbody>
                            {% for y, formats in reporting.formats %}
                                <tr>
                                    <th>{{ y }}</th>
                                    {% for format, value in formats %}
                                        <td class="text-right">{{ value }}</td>
                                    {% endfor %}
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">Répartition du nombre d'actions publiées par mode d'exercice</h3>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table no-margin table-stacktable">
                            <thead>
                            <tr>
                                <th>Année</th>
                                {% for exerciseMode in reporting.exercises[reporting.exercises|keys[0]] %}
                                    <th class="text-right">{{ exerciseMode.label }}</th>
                                {% endfor %}
                            </tr>
                            </thead>
                            <tbody>
                            {% for y, exercisesMode in reporting.exercises %}
                                <tr>
                                    <th>{{ y }}</th>
                                    {% for exerciseMode in exercisesMode %}
                                        <td class="text-right">{{ exerciseMode.total }}</td>
                                    {% endfor %}
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    <script>
        $(document).ready(function () {

	        $('.table-cell-ellipsis').filter(function(i, e) {
		        return $(e).innerWidth() < $(e)[0].scrollWidth;
	        }).tooltip({
		        container: "body",
		        placement: function() {
			        return "top";
		        }
	        });

	        let $yearSelect = $(".year-select");

	        function updateTables(year) {
		        $yearSelect.each(function() {
			        let $this = $(this);
			        $("." + $this.data("table")).each(function() {
				        $(this).toggleClass("hidden", $(this).data("year") !== year)
			        });
                })
            }

        	$yearSelect.change(function () {
		        let $this = $(this);
		        var year = parseInt($this.val());
		        updateTables(year)
		        $yearSelect.not($this).val($this.val());
            })
        });
    </script>
{% endblock %}