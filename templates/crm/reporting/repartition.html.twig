<table id="reporting-table-ps-engage" class="table no-margin table-stacktable" data-column-defs='[{"sortable": false, "targets": [1]}]'>
    <thead>
    <tr>
        <th><PERSON><PERSON><PERSON><PERSON>es</th>
        <th class="hidden-sm hidden-xs"></th>
        <th class="text-right">Jan</th>
        <th class="text-right">Fev</th>
        <th class="text-right">Mar</th>
        <th class="text-right">T1</th>
        <th class="text-right">Avr</th>
        <th class="text-right">Mai</th>
        <th class="text-right">Juin</th>
        <th class="text-right">T2</th>
        <th class="text-right">S1</th>
        <th class="text-right">Juil</th>
        <th class="text-right">Aout</th>
        <th class="text-right">Sep</th>
        <th class="text-right">T3</th>
        <th class="text-right">Oct</th>
        <th class="text-right">Nov</th>
        <th class="text-right">Dec</th>
        <th class="text-right">T4</th>
        <th class="text-right">S2</th>
        <th class="text-right">Total</th>
    </tr>
    </thead>
    <tbody>
    {% for category, months in reporting.categories %}
        <tr {% if months["total"][years[1]]["total"] == 0 and months["total"][years[0]]["total"] == 0 %}class="reporting-row-empty"{% endif %}>
            <td style="vertical-align:middle;">{{ category }}</td>
            <td class="hidden-sm hidden-xs" style="text-align: right;">2021 : <br>2020 :<br>Evolution</td>
            {% for month, y in months %}
                <td class="text-right bg-{{ y[years[0]].color }}">
                    {% for year, values in y %}
                        <span class="hidden-md hidden-lg">{% if not loop.last %}{{ year }}{% else %}Evolution{% endif %} : </span>{{ values.total }} {% if not loop.last %}<br>{% endif %}
                    {% endfor %}
                </td>
            {% endfor %}
        </tr>
    {% endfor %}
    </tbody>
</table>