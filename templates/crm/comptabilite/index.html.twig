{% extends 'crm/base.html.twig' %}

{% block title %}Modules manquants{% endblock %}

{% block body %}

{% include "crm/comptabilite/form.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Liste des modules manquants</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Module manquant</th>
          <th>Etape</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Type de session</th>
          {% if is_granted("ROLE_SUPERVISOR") %}
            <th>Coordinateur</th>
          {% endif %}
          <th>Echéance</th>
          <th>Parcours obligatoire</th>
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for result in report.participations.result %}
          <tr {% if result.dateModuleManquantExpiration|date("Ymd") <= "now"|date("Ymd") %}class="participation-echeance-expired" {% endif %}>
            <td>{{ result.participation.participant.lastname }}</td>
            <td>{{ result.participation.participant.firstname }}</td>
            <td>{{ result.moduleName }}</td>
            <td>{{ result.moduleStep }}</td>
            <td>{{ result.participation.formation.programme.reference }}</td>
            <td>{{ result.participation.formation.sessionNumber }}</td>
            <td>
              {% if result.participation.formation.isElearning %}
                {{ ("admin.formation.type.formation_elearning") | trans }}
              {% else %}
                {{ ("admin.formation.type." ~ result.participation.formation.displayType) | trans }}
              {% endif %}
            </td>
            {% if is_granted("ROLE_SUPERVISOR") %}
              <td>{% if result.participation.associatedCoordinator %}{{ result.participation.associatedCoordinator.person.invertedFullname }}{% endif %}</td>
            {% endif %}
            <td>{% if result.dateModuleManquantExpiration %}{{ result.dateModuleManquantExpiration|date("d/m/Y") }}{% else %}-{% endif %}</td>
            <td>{% if result.participation.financeSousMode.isHorsDPC %}Parcours non obligatoire{% else %}Parcours obligatoire{% endif %}</td>
            <td data-name="{{ result.participation.participant.fullname }}">
              <a href="{{ url('admin_formation_show', {'id': result.participation.formation.id, _fragment: "parcours-" ~ result.participation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
              {% if result.participation.participant.email %}
                {% if (result.moduleStep == "Etape 1" or result.moduleStep == "Etape 2") and result.participation.formation.isElearning and result.participation.formation.programme.isElearning %}
                  <a class="btn btn-eduprat btn-icon btn-email" title='{{ "admin.participation.email.relanceModulesElearning" | trans }}' href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_MODULES_ELEARNING')}) }}"><i class="fa fa-envelope"></i></a>
                {% elseif result.formType == "elearning" %}
                  <a class="btn btn-eduprat btn-icon btn-email" title='{{ "admin.participation.email.relanceElearning" | trans }}' href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ELEARNING'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% elseif result.moduleStep == "Etape 1" %}
                  <a class="btn btn-eduprat btn-icon btn-email" title="{{ "admin.participation.email.relancePreSession" | trans }}" href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_PRE_FORMATION'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% else %}
                  <a class="btn btn-eduprat btn-icon btn-email" title="Est-vous sûr de vouloir envoyer le mail de relance parcours post-session" href="{{ url('admin_participation_email', {id: result.participation.id, type: constant('Eduprat\\DomainBundle\\Services\\Email\\EmailFormationJP1Relance::ALIAS'), referer: app.request.uri }) }}"><i class="fa fa-envelope"></i></a>
                {% endif %}
              {% endif %}
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-sm-12">
        {% if npages > 1 %}
          {% include 'crm/common/pagination.html.twig' with {urlName : app.request.get('_route'), extraParams : search.getParams() } %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
<script>
  $('.btn-email').on('click', function(e) {
      let name = $(this).closest("td").data("name");
      var valid = confirm('Est-vous sûr de vouloir ' + this.title.toLowerCase() + ' à ' + name + " ?");
      if (!valid) {
          e.preventDefault();
      }
  });

  $('[name=csvAll]').click(function (e) {
	        var $this = $(this);
	        if ($this.data('started') === true) return false;
	        e.preventDefault();
	        jQuery.ajax({
		        url: $this.data('href'),
                type: "POST",
		        data: $('#eduprat_comptabilite_search').serialize(),
		        success: function(data, textStatus, xhr) {
              console.log(data);
			        if (data.status == "ok") {
				        $('#generate-participant-export-loading').removeClass('hidden');
				        $('#generate-participant-export-download').addClass('hidden');
				        $('#generate-participant-export-error').addClass('hidden');
			        }
		        },
		        error: function(xhr, textStatus, errorThrown) {
			        console.log(errorThrown);
		        }
	        });
	        $this.data('started', true);
        });
</script>
{% endblock %}
