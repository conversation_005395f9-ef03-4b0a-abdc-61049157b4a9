{% extends 'crm/base.html.twig' %}

{% block title %}Documents manquants{% endblock %}

{% block body %}

{% include "crm/comptabilite/form.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Documents manquants</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Documents manquants</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Type de session</th>
          <th>Gestion</th>
          <th>Échéance</th>
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for formation in report.files.result %}
          {% set coordinator = search.coordinator ? formation.getCoordinatorByPerson(search.coordinator) : null %}
          {% set files = formation.missingFiles(coordinator, null, search.webmaster) %}
          {% if files|length %}
            <tr {% if formation.closingDate|date("Ymd") <= "now"|date("Ymd") %}class="participation-echeance-expired" {% endif %}>
              <td>
                {% for file in files %}
                  {{ ("crm.comptabilite.files.labels." ~ file.type)|trans }} {% if file.person is defined %} : {{ file.person.invertedFullName }}{% endif %}
                  {% if file.type == "topo" and formation.coordinators.count == 1 %} {% for coordinator in formation.coordinators %} : {{coordinator.person.invertedFullName}} {% endfor %}{% endif %}
                  {% if not loop.last %}<br>{% endif %}
                {% endfor %}
              </td>
              <td>{{ formation.programme.reference }}</td>
              <td>{{ formation.sessionNumber }}</td>
              <td>
                {% if formation.isElearning %}
                  {{ ("admin.formation.type.formation_elearning") | trans }}
                {% else %}
                  {{ ("admin.formation.type." ~ formation.displayType) | trans }}</td>
                {% endif %}
              </td>
              <td>{% if formation.coordinators.count > 1 %}Multi CR{% else %}Individuelle{% endif %}</td>
              <td>{{ formation.closingDate|date("d/m/Y") }}</td>
              <td>
                <a href="{{ url('admin_formation_show', {'id': formation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
              </td>
            </tr>
          {% endif %}
        {% endfor %}
        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-sm-12">
        {% if npages > 1 %}
          {% include 'crm/common/pagination.html.twig' with {urlName : app.request.get('_route'), extraParams : search.getParams() } %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
{% endblock %}