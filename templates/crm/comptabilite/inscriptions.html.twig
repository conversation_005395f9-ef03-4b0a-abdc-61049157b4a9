{% extends 'crm/base.html.twig' %}

{% block title %}Suivi des inscriptions{% endblock %}

{% block body %}

{% include "crm/comptabilite/formInscription.html.twig" %}

<div class="box box-solid box-primary">
  <div class="box-header">
    <h3 class="box-title">Suivi des inscriptions</h3>
  </div>
  <div class="box-body">
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
        <tr>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Coût</th>
          <th>Type</th>
          <th>Le</th>
          <th>Référence</th>
          <th>Session</th>
          <th>Gestion</th>
          {% if is_granted("ROLE_SUPERVISOR") %}
            <th>Coordinateur</th>
          {% endif %}
          <th>Partenariat</th>
          <th>Actions</th>
        </tr>
        </thead>
        <tbody>
        {% for participationHistory in report %}
          <tr>
            <td>{{ participationHistory.participation.participant.lastname }}</td>
            <td>{{ participationHistory.participation.participant.firstname }}</td>
            <td>{{ participationHistory.price|default(participationHistory.participation.price) }}</td>
            <td>{{ participationHistory.action }} {% if participationHistory.type %} ({{participationHistory.type}}) {% endif %}</td>
            <td>{{ participationHistory.createdAt|date("d/m/Y H:i:s") }}</td>
            <td>{{ participationHistory.participation.formation.programme.reference }}</td>
            <td>{{ participationHistory.participation.formation.sessionNumber }}</td>
            <td>{% if participationHistory.participation.formation.coordinators.count > 1 %}Multi CR{% else %}Individuelle{% endif %}</td>
            {% if is_granted("ROLE_SUPERVISOR") %}
              <td>{% if participationHistory.participation.coordinator %}{{ participationHistory.participation.coordinator.person.invertedFullname }}{% endif %}</td>
            {% endif %}
            <td>
              {{ participationHistory.participation.partenariat }}
            </td>
            <td>
              {% if participationHistory.action == "Désinscription" %}
                {% set url = url('admin_participant_show', {'id': participationHistory.participation.participant.id }) %}
              {% else %}
                {% set url = url('admin_formation_show', {'id': participationHistory.participation.formation.id, '_fragment': ("row-participation-" ~ participationHistory.participation.id) }) %}
              {% endif %}
              <a href="{{ url }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
            </td>
          </tr>
        {% endfor %}
        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-sm-12">
        {% if npages > 1 %}
          {% include 'crm/common/pagination.html.twig' with {urlName : app.request.get('_route'), extraParams : search.getParams() } %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">
        $('[name=csvAll]').click(function (e) {
          console.log($('#eduprat_comptabilite_search').serialize());
	        var $this = $(this);
	        if ($this.data('started') === true) return false;
	        e.preventDefault();
	        jQuery.ajax({
		        url: $this.data('href'),
                type: "POST",
		        data: $('#eduprat_comptabilite_search').serialize(),
		        success: function(data, textStatus, xhr) {
			        if (data.status == "ok") {
				        $('#generate-participant-export-loading').removeClass('hidden');
				        $('#generate-participant-export-download').addClass('hidden');
				        $('#generate-participant-export-error').addClass('hidden');
			        }
		        },
		        error: function(xhr, textStatus, errorThrown) {
			        console.log(errorThrown);
		        }
	        });
	        $this.data('started', true);
        });

    </script>
{% endblock %}
