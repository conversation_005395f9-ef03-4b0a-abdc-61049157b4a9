{% if form is defined and form.children|length > 1 %}
  <div class="box box-solid box-primary">
    <div class="box-header">
      <h3 class="box-title">Filtrer par coordinateur</h3>
    </div>
    <div class="box-body eduprat-search">
      {{ form_start(form, {'attr': {'id': 'eduprat_comptabilite_search', 'class': 'form-inline'}}) }}
      <div class="row">
        <div class="col-sm-12">
          <div class="form-inline">
            {% if form.startInscription is defined %}
              <div class="form-group mrm">
                {{ form_label(form.startInscription) }}<br>
                {{ form_widget(form.startInscription) }}
                <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
              </div>
              <div class="form-group mrm">
                {{ form_label(form.endInscription) }}<br>
                {{ form_widget(form.endInscription) }}
                <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
              </div>
            {% endif %}
            {% if form.coordinator is defined %}
              <div class="form-group mrm">
                {{ form_label(form.coordinator) }}<br>
                {{ form_widget(form.coordinator) }}
              </div>
            {% endif %}
            {% if form.webmaster is defined %}
              <div class="form-group mrm">
                {{ form_label(form.webmaster) }}<br>
                {{ form_widget(form.webmaster) }}
              </div>
            {% endif %}
            {% if form.partenariat is defined %}
              <div class="form-group mrm">
                {{ form_label(form.partenariat) }}<br>
                {{ form_widget(form.partenariat) }}
              </div>
            {% endif %}
            {% if form.parcoursMandatory is defined %}
              <div class="form-group mrm">
                {{ form_label(form.parcoursMandatory) }}<br>
                {{ form_widget(form.parcoursMandatory) }}
              </div>
            {% endif %}
            {% if form.type is defined %}
              <div class="form-group mrm">
                {{ form_label(form.type) }}<br>
                {{ form_widget(form.type) }}
              </div>
            {% endif %}
            <div class="text-right" style="margin-top:20px">
              <button class="btn btn-eduprat" type="submit">
                <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
              </button>
              {% if hasGenerated  is defined %}
                <button class="btn btn-eduprat" type="submit" name="csvAll" value="true" data-href="{% if history is defined %}{{ url('participant_history_generate_csv_export') }}{% else %}{{ url('participation_generate_missing_modules') }}{% endif %}">
                  <span class="fa fa-download"></span> {{ "admin.participant.csvBtn"|trans }}
                </button>
              {% endif %}
            </div>
            {% if hasGenerated  is defined %}
              <div class="text-right">
                  <div id="generate-participant-export-loading" {% if ((hasGenerated and hasFinished) or (not hasGenerated)) or hasError %}class="hidden text-right"{% endif %} class="text-right">
                    <span>{{ "admin.formation.generation"|trans }}</span>
                  </div>
                  {% if hasGenerated %}
                    {% if hasFinished %}
                      <div id="generate-participant-export-download" class="text-right">
                        <a href="{% if history is defined %} {{ url('csv_participations_history_file_get') }} {% else %}{{ url('csv_missing_modules_file_get') }}{% endif %}" target="_blank">{{ "admin.participant.export"|trans }}</a>
                      </div>
                    {% endif %}
                  {% endif %}
                  {% if hasError %}
                      <div id="generate-participant-export-error" class="text-right">
                          <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                      </div>
                  {% endif %}
              </div>
            {% endif %}
          </div>
        </div>
      </div>
      {{ form_end(form)}}
    </div>
  </div>
{% endif %}
