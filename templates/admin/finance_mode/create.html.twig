{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'alienor_finance_mode_create' %}
        {{ 'admin.financeMode.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_finance_mode_edit' %}
        {{ 'admin.financeMode.titles.edit'|trans }}
    {% endif %}
{% endblock %}
{% block body %}
<div class="box box-info">
    <div class="box-body">
        {{ form_start(form) }}
        {{ form_rest(form) }}
        <button type="submit" class="btn btn-eduprat pull-right">{{ "admin.global.save"|trans }}</button>
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}