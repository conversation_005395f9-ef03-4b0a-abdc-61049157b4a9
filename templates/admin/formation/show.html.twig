{% extends 'admin/base.html.twig' %}

{% block title %}
    Suivi session : {{ formation.programme.title }} {% if formation.isArchived %} - Session archivée {% endif %}
    {% if is_granted('ROLE_WEBMASTER') %}
        <span class="mls">
            <a href="{{ url('admin_formation_edit', {'id': formation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
            {% if not formation.isArchived %} <a class="btn btn-danger action-btn btn-icon" href="{{ url('admin_formation_delete', {'id': formation.id }) }}" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>{% endif %}
        </span>
    {% endif %}
{% endblock %}

{% block stylesheets %}
    <link href='https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.2/dragula.css' rel="stylesheet"/>
{% endblock %}

{% block body %}
    {% if formation.commentaireNotif and (is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')) %}<div class="alert alert-info">{{ formation.commentaireNotif }}</div>{% endif %}
    {% for formAttestation in forms_attestation_honneur %}
        {{ form_errors(formAttestation) }}
    {% endfor %}
    <div class="box box-info" xmlns="http://www.w3.org/1999/html">
        <div class="box-body">
            <div class="box-eduprat">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.programme.reference"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">{{ formation.programme.reference }}</div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.sessionNumber"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">{{ formation.sessionNumber }}</div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">
                                {{ "admin.formation.title"|trans }}&nbsp;:
                                {% if is_granted('ROLE_WEBMASTER') %}
                                    <span class="mls">
                                        <a href="{{ url('admin_programme_edit', {'id': formation.programme.id }) }}" class="btn btn-eduprat btn-icon btn-icon-mini" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                    </span>
                                {% endif %}
                            </div>
                            <div class="col-xs-6">
                                {{ formation.programme.title }}
                            </div>
                        </div>
                        {% if not formation.isElearningWithThreeUnity %}
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.startDate"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">
                                    {% set hoursStart = formation.startDate|date('H\\hi') == '00h00' ? null : formation.startDate|date('H\\hi') %}
                                    {% set hoursEnd = formation.endDate|date('H\\hi') == '00h00' ? null : formation.endDate|date('H\\hi') %}
                                    {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
                                        Le {{ formation.startDate | date('d/m/Y') }} {% if hoursStart %}de {{ hoursStart }} à {{ hoursEnd }}{% endif %}
                                    {% else %}
                                        Du {{ formation.startDate | date('d/m/Y') }}{% if hoursStart %} à {{ hoursStart }}{% endif %} au {{ formation.endDate | date('d/m/Y') }}{% if hoursEnd %} à {{ hoursEnd }}{% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.programme.sessionType.label"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">
                                {% if formation.isCoordinatorLbi %}
                                    {{ ("admin.formation.type.formation_lbi") | trans }} -
                                {% endif %}
                                {% if formation.isElearning %}
                                    {{ ("admin.formation.type.formation_elearning") | trans }} {% if formation.displayTypeSuffix %}{{ ("admin.formation.suffix." ~ formation.displayTypeSuffix) | trans }}{% endif %}
                                {% else %}
                                    {{ ("admin.formation.type." ~ formation.displayType) | trans }} {% if formation.displayTypeSuffix %}{{ ("admin.formation.suffix." ~ formation.displayTypeSuffix) | trans }}{% endif %}
                                {% endif %}
                            </div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.nbParticipants"|trans }}</div>
                            <div class="col-xs-6">{{ nbParticipants }}</div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.profession"|trans }}</div>
                            <div class="col-xs-6">
                            {% if professions|length %}
                                {% for profession in professions %}
                                    {% if not loop.last %}
                                        {{ profession ~ ',' }}
                                    {% else %}
                                        {{ profession }}
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                /
                            {% endif %}
                            </div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.emailing"|trans }}</div>
                            <div class="col-xs-6">{{ (formation.noMailing ? "admin.global.no" : "admin.global.yes")|trans }}</div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ ("admin.formation.type."  ~ formation.formTypePre) | trans }} {% if formation.hasFormPost %} pré-session {% endif %}&nbsp;:</div>
                            {% if formation.hasLinkedForm %}
                                {% if not formation.isFormPresentielle %}
                                    <div class="col-xs-6">{{ formation.audit.label }}</div>
                                {% elseif formation.isFormPresentielle %}
                                    <div class="col-xs-6">{{ formation.questionnaire.label }}</div>
                                {% endif %}
                            {% endif %}
                        </div>
                        {% if formation.hasFormPost() %}
                            <div class="infos row">
                                <div class="header col-xs-5">{{ ("admin.formation.type."  ~ formation.formTypePost) | trans }} post-session&nbsp;:</div>
                                {% if formation.audit2 is defined and formation.audit2 is not null  %}
                                        <div class="col-xs-6">{{ formation.audit2.label }}</div>
                                {% elseif formation.audit2 is not defined and not formation.isFormPresentielle and formation.audit is not null %}
                                    <div class="col-xs-6">{{ formation.audit.label }}</div>
                                {% else %}
                                    {% if formation.questionnaire is defined and formation.questionnaire is not null %}
                                        <div class="col-xs-6">{{ formation.questionnaire.label }}</div>
                                    {% endif %}
                                {% endif %}
                            </div>
                        {% endif %}
                        {% if formation.isElearning and formation.elearning is not null %}
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.elearning"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">{{ formation.elearning.label }}</div>
                            </div>
                        {% endif %}
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.nombrePlacesInitiales"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">{{ formation.nombrePlacesInitiales }}</div>
                        </div>
                    </div>
                    <div class="col-lg-2">
                    </div>
                    <div class="col-lg-6">
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.former.title"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">
                                {% if formateurs|length %}
                                    {% for formateur in formateurs %}
                                        {% set formateurName = formateur.person.firstname ~ ' ' ~ formateur.person.lastname %}
                                        {% if not loop.last %}
                                            {{ formateurName ~ ',' }}
                                        {% else %}
                                            {{ formateurName }}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    /
                                {% endif %}
                            </div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.coordinators.title"|trans }}&nbsp;:</div>
                            <div class="col-xs-6">
                                {% if coordinators|length %}
                                    {% for coordinator in coordinators %}
                                        {% set coordinateurName = coordinator.person.firstname ~ ' ' ~ coordinator.person.lastname %}
                                        {% if not loop.last %}
                                            {{ coordinateurName ~ ',' }}
                                        {% else %}
                                            {{ coordinateurName }}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    /
                                {% endif %}
                            </div>
                        </div>
                        {% if formation.isElearningWithThreeUnity %}
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.elearningModuleDates"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">Du {{ formation.elearningModuleOpeningDate|date("d/m/Y") }} au {{ formation.elearningModuleClosingDate|date("d/m/Y") }}</div>
                            </div>
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.parcoursBloque"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">Du {{ formation.elearningModuleClosingDate|date_modify("+1 day")|date("d/m/Y") }} au {{ formation.postCourseOpeningDate|date_modify("-1 day")|date("d/m/Y") }}</div>
                            </div>
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.postAccess"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">Du {{ formation.postCourseOpeningDate|date("d/m/Y") }} au {{ formation.postCourseClosingDate|date("d/m/Y") }}</div>
                            </div>
                        {% else %}
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.openingDate"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">{{ formation.openingDate|date("d/m/Y") }}</div>
                            </div>
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.closingDate"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">{{ formation.closingDate|date("d/m/Y") }}</div>
                            </div>
                            <div class="infos row">
                                <div class="header col-xs-5">{{ "admin.formation.addressFormation"|trans }}&nbsp;:</div>
                                <div class="col-xs-6">
                                    {{ formation.address }}{% if formation.address2 %}<br>{% endif %}
                                    {{ formation.address2 }}{% if formation.zipCode or formation.city %}<br>{% endif %}
                                    {{ formation.zipCode }} {{ formation.city }}
                                </div>
                            </div>
                        {% endif %}
                        {% if formation.programme.isSurSite() %}
                            <div class="infos row">
                                <div class="header col-xs-5">Journée/Congrès&nbsp;:</div>
                                <div class="col-xs-6">
                                    {{ formation.formatPeriod }}
                                </div>
                            </div>
                        {% endif %}
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.price"|trans }}</div>
                            <div class="col-xs-6">
                                {{ formation.cost }} €
                            </div>
                        </div>
                        <div class="infos row">
                            <div class="header col-xs-5">{{ "admin.formation.price"|trans }}</div>
                            <div class="col-xs-6">
                                {{ formation.cost }} €
                            </div>
                        </div>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <div class="infos row">
                                <div class="header col-xs-5">Cloturée&nbsp;:</div>
                                <div class="col-xs-6">
                                    {% if formation.closed %}
                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                    {% else %}
                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                    {% endif %}
                                </div>
                            </div>
                            <div class="infos row" id="billed" data-url="{{ url("allIsFactured", { id: formation.id }) }}">
                                <div class="header col-xs-5">Facturée&nbsp;:</div>
                                <div class="col-xs-6">
                                    <img style="display:none" class="isNotBilled" src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                    <img class="isBilled"src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                </div>
                            </div>
                            {% if formation.isPluriAnnuelle() %}
                                <div class="infos row">
                                    <div class="header col-xs-5">Comptabilisée mi-parcours&nbsp;:</div>
                                    <div class="col-xs-6">
                                        {% if formation.accountedMidCourse %}
                                            <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                        {% else %}
                                            <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                            <div class="infos row">
                                <div class="header col-xs-5">Comptabilisée&nbsp;:</div>
                                <div class="col-xs-6">
                                    {% if formation.accounted %}
                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                    {% else %}
                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                {% if is_granted('ROLE_WEBMASTER') %}
                <div class="btn-row row">
                    <div class="col-lg-6 col-md-0"></div>
                    <div class="col-lg-6 col-md-12">
                        {% if not formation.featureDisabledBecauseAccounted() %} 
                            <div class="downloadFile">
                                <div class="btn btn-default btn-default-eduprat btn-download-file" id="andpc"><span>{{ "admin.formation.fichierAndpc"|trans }}</span></div>
                                {{ form(form_andpc) }}
                            </div>
                        {% endif %}
                        {% if formation.closed != true or is_granted('ROLE_WEBMASTER_COMPTA') %}
                            <a href="{{ url('admin_formation_close_formation', {'id': formation.id }) }}" data-value="{{ formation.closed }}" class="btn {% if formation.closed %}btn-eduprat{% else %}btn-danger{% endif %} btn-default-eduprat closeFormation"><span>{% if formation.closed %}{{ "admin.formation.decloture"|trans }}{% else %}{{ "admin.formation.cloture"|trans }}{% endif %}</span></a>
                        {% endif %}
                        {% if is_granted('ROLE_WEBMASTER_COMPTA') %}
                            <a style="display:none" class="btn btn-eduprat btn-default-eduprat billFormation isNotBilled"><span>{{ "admin.formation.bill_session"|trans }}</span></a>
                            <a class="btn btn-eduprat btn-default-eduprat billFormation billFormationClick isBilled"><span>{{ "admin.formation.unbill_session"|trans }}</span></a>
                            {% if formation.isPluriAnnuelle() %}
                                <a href="{{ url('admin_formation_account', {'id': formation.id, 'mid': 1, state: formation.accountedMidCourse ? 0 : 1 }) }}" class="btn btn-eduprat btn-default-eduprat accountFormation"><span>{% if formation.accountedMidCourse %}{{ "admin.formation.unaccount_session_mid_course"|trans }}{% else %}{{ "admin.formation.account_session_mid_course"|trans }}{% endif %}</span></a>
                            {% endif %}
                            <a href="{{ url('admin_formation_account', {'id': formation.id, 'mid': 0, state: formation.accounted ? 0 : 1 }) }}" class="btn btn-eduprat btn-default-eduprat accountFormation"><span>{% if formation.accounted %}{{ "admin.formation.unaccount_session"|trans }}{% else %}{{ "admin.formation.account_session"|trans }}{% endif %}</span></a>
                        {% endif %}
                        {% if(formation.lastImportDate) %}<div class="lastImportDate"> {{ "admin.formation.lastImportDate"|trans }} {{ formation.lastImportDate|date("d/m/Y à H:i") }}</div>{% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="box box-body box-comments">
                {% include 'admin/formation/files.html.twig' %}
            </div>
            {% set formPostAvailable = ("now"|date("Y-m-d")) <= formation.formClosingDate|date("Y-m-d") %}
            {% if participations|length > 0 %}
                {% if is_granted('ROLE_COORDINATOR') %}
                    {% if formation.hasLinkedForm %}
                        <a id="btn-email-global-export" href="{{ url("admin_formation_export_emails", { id: formation.id }) }}" class="btn btn-eduprat" title="{{ 'admin.formation.mail.export'|trans }}"><i class="fa fa-download"></i> {{ 'admin.formation.mail.export'|trans }}</a>
                        <a id="btn-email-global" href="#" class="btn btn-eduprat" title="{{ 'admin.formation.mail.btn'|trans }}"><i class="fa fa-envelope"></i> {{ 'admin.formation.mail.label'|trans }}</a>
                        <div id="box-email" class="box box-body box-comments hide">
                            <ul>
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_CREATE_ACCOUNT')}) }}">{{ "admin.formation.mail.account" | trans }}</a></li>
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_PARTICIPATION')}) }}">{{ "admin.participation.email.participation" | trans }}</a></li>
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\Email\\EmailFormationOpened::ALIAS')}) }}">{{ "admin.participation.email.opened" | trans }}</li>
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\Email\\EmailFormationConvocation::ALIAS')}) }}">{{ "admin.participation.email.convocation" | trans }}</a></li>
                                {% if not formation.isElearning or formation.programme.isElearningTwoUnity %}
                                    {% set available = ("now"|date("Y-m-d")) >= formation.formOpeningDate|date("Y-m-d")  %}
                                    <li><a class="{% if not available %}link-disabled{% endif %}" {% if available %}href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_PRE_FORMATION')}) }}"{% endif %}>{{ "admin.participation.email.relancePreSession" | trans }}</a> {% if not available %}(Le questionnaire sera disponible à partir du {{ formation.formOpeningDate|date("d/m/Y") }}){% endif %}</li>
                                {% endif %}
                                {% if formation.isElearning and formation.programme.isElearning %}<li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_MODULES_ELEARNING')}) }}">{{ "admin.participation.email.relanceModulesElearning" | trans }}</a></li>{% endif %}
                                {% if formation.hasFormPost() %}
                                    <li>
                                        <a {% if formPostAvailable %}href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\Email\\EmailFormationJP1Relance::ALIAS')}) }}"{% else %}class="link-disabled"{% endif %}>
                                            Envoyer le mail de relance parcours post-session
                                        </a>
                                    </li>
                                {% endif %}
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_CLOSE')}) }}">{{ "admin.participation.email.close" | trans }}</a></li>
                                <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_EVALUATION')}) }}">{{ "admin.participation.email.relanceEvaluation" | trans }}</a></li>
                                {% if formation.hasFormPost() %}
                                    {% if not formation.programme.isFormatPresentiel %}
                                        <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ATTESTATION')}) }}">{{ "admin.participation.email.relanceAttestation" | trans }}</a></li>
                                    {% endif %}
                                    {% if formation.isPluriAnnuelle() %}
                                        <li><a href="{{ url('admin_formation_email', {id: formation.id, type: constant('Eduprat\\DomainBundle\\Services\\EmailSender::MAIL_RELANCE_ATTESTATION_MI_PARCOURS')}) }}">{{ "admin.participation.email.attestationMiParcours" | trans }}</a></li>
                                    {% endif %}
                                {% endif %}
                            </ul>
                        </div>
                    {% else %}
                            <a id="btn-email-global-export" href="{{ url("admin_formation_export_emails", { id: formation.id }) }}" class="btn btn-eduprat" title="{{ 'admin.formation.mail.export'|trans }}"><i class="fa fa-download"></i> {{ 'admin.formation.mail.export'|trans }}</a>
                    {% endif %}
            {% endif %}

            {% if (is_granted('ROLE_SUPER_ADMIN') or is_granted('ROLE_WEBMASTER')) and formation.factureState %}
                <a id="btn-facturation-state" href="#" class="btn btn-eduprat" title="Etat Factu (admin)"><i class="fa fa-euro"></i> Etat Factu (admin)</a>
                <div id="box-facturation" class="box box-body box-comments hide">
                    {% include 'admin/formation/facture_rules.html.twig' %}
                </div>
            {% endif %}
            <div class="box-body">
                {% include 'admin/formation/participants.html.twig' %}
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    <script src='https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.2/dragula.min.js'></script>
    <script>
        $(document).ready(function() {

            function checkIfAllIsFactured(init = false) {
                jQuery.ajax({
		            url: $('#billed').data('url'),
		            type: 'GET',
		            dataType: 'html',
		            success: function(data, textStatus, xhr) {
                        data = jQuery.parseJSON(data);
                        if (data == true) {
                            $('.isBilled').hide();
                            $('.isNotBilled').show();                            
                        }  
                        if(data == false ) {
                            $('.isBilled').show();
                            $('.isNotBilled').hide(); 
                        }
		            },
	            });
            }
            checkIfAllIsFactured(true);

            $(document).on('click', ".paid", function() {
                var element = '#' + $(this).data('id');
                var dateLocation = '#' + $(this).data('id') + '-date';
                var isPaid = $(this).hasClass('isPaid');
                var $div = $(element);
                var $divDate = $(dateLocation);
                var $id = $(this).data('id');
                var $now = new Date();

                $day = $now.getDate();
                $mounth = $now.getMonth() + 1;
                $year = $now.getFullYear();

                $mounth = $mounth < 10 ? "0" + $mounth : $mounth;
                $day = $day < 10 ? "0" + $day : $day;

                var go = true;
                if (isPaid) {
                    if (confirm("Êtes-vous sûr de vouloir décocher ?") == false) {
                        go = false;
                    } else {
                        $(this).removeClass('isPaid');
                    }
                } else {
                    $(this).addClass('isPaid');
                }

                if (go) {
                    jQuery.ajax({
                        url: $(this).data('url'),
                        type: 'GET',
                        dataType: 'html',
                        success: function(data, textStatus, xhr) {
                            data = jQuery.parseJSON(data);
                            console.log($div);
                            if (data == true) {
                                $div.replaceWith('<img id="'+ $id +'" src="/img/checked.png" width="16" height="16" alt="Oui">');
                                console.log($mounth);
                                $divDate.replaceWith('<div id="'+ $id +'-date" style="margin-left:0.5em">'+$day+'/'+$mounth+'/'+$year+'</div>');
                            }  
                            if(data == false ) {
                                $div.replaceWith('<img id="'+ $id +'" src="/img/unchecked.png" width="16" height="16" alt="Oui">');
                                $divDate.replaceWith('<div id="'+ $id +'-date" style="margin-left:0.5em"></div>');
                            }
                        },
                    });
                }
            });

            $(document).on('click', ".facture", function() {
                var element = '#' + $(this).data('id');
                var dateLocation = '#' + $(this).data('id') + '-date';
                var isPaid = $(this).hasClass('isPaid');
                var $div = $(element);
                var $divDate = $(dateLocation);
                var $id = $(this).data('id');
                var $now = new Date();
                var buttonElement = '#' + $(this).data('id') + '-button';
                var $button = $(buttonElement);

                $day = $now.getDate();
                $mounth = $now.getMonth() + 1;
                $year = $now.getFullYear();
                $mounth = $mounth < 10 ? "0" + $mounth : $mounth;
                $day = $day < 10 ? "0" + $day : $day;

                var go = true;
                if (isPaid) {
                    if (confirm("Etes-vous sur de vouloir décocher ?") == false) {
                        go = false;
                    } else {
                        $(this).removeClass('isPaid');
                    }
                } else {
                    $(this).addClass('isPaid');
                }

                if (go) {
                    jQuery.ajax({
                        url: $(this).data('url'),
                        type: 'GET',
                        dataType: 'html',
                        success: function(data, textStatus, xhr) {
                            data = jQuery.parseJSON(data);
                            if (data == true) {
                                $button.attr('data-facture', 'oui');
                                $div.replaceWith('<img id="'+ $id +'" src="/img/checked.png" width="16" height="16" alt="Oui">');
                                $divDate.replaceWith('<div id="'+ $id +'-date" style="margin-left:0.5em">'+$day+'/'+$mounth+'/'+$year+'</div>');
                            }  
                            if(data == false ) {
                                $button.attr('data-facture', 'non');
                                $div.replaceWith('<img id="'+ $id +'" src="/img/unchecked.png" width="16" height="16" alt="Oui">');
                                $divDate.replaceWith('<div id="'+ $id +'-date" style="margin-left:0.5em"></div>');
                            }
                            checkIfAllIsFactured();
                        },
                    });
                }
            });

            $(document).on('click', ".billFormationClick", function() {
                var url = $(this).data('url');
                $(".facture" ).each(function(index) {
                    if($(this).attr('data-facture') == "non") {
                        $(this).click();
                    }
                })
            });
            

	        var hash = window.location.hash.substr(1);
            if (hash.startsWith("row-participation-")) {
				$("#" + hash).find(".attestationListLink").click();
            }

	        if (hash.startsWith("parcours-")) {
		        $("#" + hash).prevAll(".row-participations:first").find(".parcoursListLink").click();
	        }

	        $('[data-toggle="tooltip-tab"]').not('.timeline-disabled').tooltip({
		        placement: function() {
			        return "top";
		        }
	        });

            $('.closeFormation').on('click', function (e) {
                $msg = $(this).attr('data-value') ?  "Êtes-vous sûr de vouloir déclore la formation ?" : "Êtes-vous sûr de vouloir clore la formation ?";
                if (!confirm($msg)) {
                    e.preventDefault();
                }
            });

			$('.closeAndpcFormation').on('click', function (e) {
				if (!confirm('Êtes-vous sûr de vouloir effectuer la clôture ANDPC de la formation ?')) {
					e.preventDefault();
				}
			});

            $('#andpc').click(function () {
                $('form[name="form"]').find('input').click();
            });

            $('form[name="form"]').on('change', "input#form_andpcFile", function (e) {
                e.preventDefault();
                $('form[name="form"]').submit();
                $("input#form_andpcFile").hide();
            });
            $('.btn-download-file').click(function () {
                $('form[name="'+$(this).attr('id')+'"]').find('input').click();
            });

            $(document).on("click", '.btn-download-file-attestation', function () {
                if($(this).data("attestation")) {
                    if(confirm("En choisissant un fichier, vous écraserez l'attestation actuelle")) {
                        $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                    }
                } else {
                    $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                }
            });

            $('.delete-attestation').click(function () {
                if(confirm("Supprimer l'attestation ?")) {
                    return true;
                } else {
                    return false;
                }
            });

	        $(document).on('change', 'form.formDownloadFile input[type="file"]', function () {
                $(this).closest("form").submit();
            });

            $(document).on("click", '#delete-default-topo', function (e) {
                e.preventDefault()
                if (confirm('Êtes-vous sûr de vouloir supprimer le topo par défaut "'+$(this).data("name")+'" pour cette session ?') == false) {
                    return;
                } else {
                    window.location.replace($(this).attr('href'));
                }
            });

            /* $('.no-emargement').click(function (e) {
                e.preventDefault();
                alert("Nous ne pouvons générer la feuille d'émargement en raison d'un nombre de jours trop important.");
            }); */

            $('.generate-certificate-participation').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                console.log($this.data('href'));
                e.preventDefault();
                jQuery.ajax({
                  url: $this.data('href'),
                  dataType: 'json',
                  success: function(data, textStatus, xhr) {
                      if (data.status == "ok") {
                          $('#generate-certificate-participation-loading-' + id).removeClass('hidden');
                          $('#generate-certificate-participation-download-' + id).addClass('hidden');
                          $('#generate-certificate-participation-error-' + id).addClass('hidden');
                      }
                  },
                  error: function(xhr, textStatus, errorThrown) {
                    console.log(errorThrown);
                  }
                });
                $this.data('started', true);
            });

	        $('.generate-certificate-participation-horary').click(function (e) {
		        var $this = $(this);
		        var id = $(this).data('id');
		        if ($this.data('started') === true) return false;
		        console.log($this.data('href'));
		        e.preventDefault();
		        jQuery.ajax({
			        url: $this.data('href'),
			        dataType: 'json',
			        success: function(data, textStatus, xhr) {
				        if (data.status == "ok") {
					        $('#generate-certificate-participation-horary-loading-' + id).removeClass('hidden');
					        $('#generate-certificate-participation-horary-download-' + id).addClass('hidden');
					        $('#generate-certificate-participation-horary-error-' + id).addClass('hidden');
				        }
			        },
			        error: function(xhr, textStatus, errorThrown) {
				        console.log(errorThrown);
			        }
		        });
		        $this.data('started', true);
	        });

            $('.generate-realisation-docs').click(function (e) {
		        var $this = $(this);
		        var id = $(this).data('id');
		        if ($this.data('started') === true) return false;
		        console.log($this.data('href'));
		        e.preventDefault();
		        jQuery.ajax({
			        url: $this.data('href'),
			        dataType: 'json',
			        success: function(data, textStatus, xhr) {
				        if (data.status == "ok") {
					        $('#generate-realisation-docs-loading-' + id).removeClass('hidden');
					        $('#generate-realisation-docs-download-' + id).addClass('hidden');
					        $('#generate-realisation-docs-error-' + id).addClass('hidden');
				        }
			        },
			        error: function(xhr, textStatus, errorThrown) {
				        console.log(errorThrown);
			        }
		        });
		        $this.data('started', true);
	        });

            $('.generate-attestation-presence').click(function (e) {
		        var $this = $(this);
		        var id = $(this).data('id');
		        if ($this.data('started') === true) return false;
		        console.log($this.data('href'));
		        e.preventDefault();
		        jQuery.ajax({
			        url: $this.data('href'),
			        dataType: 'json',
			        success: function(data, textStatus, xhr) {
				        if (data.status == "ok") {
					        $('#generate-attestation-presence-loading-' + id).removeClass('hidden');
					        $('#generate-attestation-presence-download-' + id).addClass('hidden');
					        $('#generate-attestation-presence-error-' + id).addClass('hidden');
				        }
			        },
			        error: function(xhr, textStatus, errorThrown) {
				        console.log(errorThrown);
			        }
		        });
		        $this.data('started', true);
	        });


            $('.generate-answers-audit1').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-answers-audit1-loading-' + id).removeClass('hidden');
                            $('#generate-answers-audit1-download-' + id).addClass('hidden');
                            $('#generate-answers-audit1-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

            $('.generate-answers-audit2').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-answers-audit2-loading-' + id).removeClass('hidden');
                            $('#generate-answers-audit2-download-' + id).addClass('hidden');
                            $('#generate-answers-audit2-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

			$('.generate-answers-fusion').click(function (e) {
				var $this = $(this);
				var id = $(this).data('id');
				if ($this.data('started') === true) return false;
				e.preventDefault();
				jQuery.ajax({
					url: $this.data('href'),
					dataType: 'json',
					success: function(data, textStatus, xhr) {
						if (data.status == "ok") {
							$('#generate-answers-fusion-loading-' + id).removeClass('hidden');
							$('#generate-answers-fusion-download-' + id).addClass('hidden');
							$('#generate-answers-fusion-error-' + id).addClass('hidden');
						}
					},
					error: function(xhr, textStatus, errorThrown) {
						console.log(errorThrown);
					}
				});
				$this.data('started', true);
			});

	        $('.generate-file-error').click(function (e) {
		        $(this).closest('ul').prev('a').click();
		        e.preventDefault();
		        return false;
	        });

            $('.generate-answers-survey1').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-answers-survey1-loading-' + id).removeClass('hidden');
                            $('#generate-answers-survey1-download-' + id).addClass('hidden');
                            $('#generate-answers-survey1-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

            $('.generate-answers-survey2').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-answers-survey2-loading-' + id).removeClass('hidden');
                            $('#generate-answers-survey2-download-' + id).addClass('hidden');
                            $('#generate-answers-survey2-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

            $('.generate-restitution-audit').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-restitution-audit-loading-' + id).removeClass('hidden');
                            $('#generate-restitution-audit-download-' + id).addClass('hidden');
                            $('#generate-restitution-audit-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

            $('.generate-prerestitution-audit').click(function (e) {
                var $this = $(this);
                var id = $(this).data('id');
                if ($this.data('started') === true) return false;
                e.preventDefault();
                jQuery.ajax({
                    url: $this.data('href'),
                    dataType: 'json',
                    success: function(data, textStatus, xhr) {
                        if (data.status == "ok") {
                            $('#generate-prerestitution-audit-loading-' + id).removeClass('hidden');
                            $('#generate-prerestitution-audit-download-' + id).addClass('hidden');
                            $('#generate-prerestitution-audit-error-' + id).addClass('hidden');
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        console.log(errorThrown);
                    }
                });
                $this.data('started', true);
            });

	        $('.generate-attestation-honneur-participation').click(function (e) {
		        var $this = $(this);
		        var id = $(this).data('id');
		        if ($this.data('started') === true) return false;
		        e.preventDefault();
		        jQuery.ajax({
			        url: $this.data('href'),
			        dataType: 'json',
			        success: function(data, textStatus, xhr) {
				        if (data.status === "ok") {
					        $('#generate-attestation-honneur-participation-loading').removeClass('hidden');
					        $('#generate-attestation-honneur-participation-download').addClass('hidden');
					        $('#generate-attestation-honneur-participation-error').addClass('hidden');
				        }
			        },
			        error: function(xhr, textStatus, errorThrown) {
				        console.log(errorThrown);
			        }
		        });
		        $this.data('started', true);
	        });

            $('.generate-attestation-honneur-participation-n1').click(function (e) {
		        var $this = $(this);
		        var id = $(this).data('id');
                console.log($(this).data('data-n1'))
		        if ($this.data('started') === true) return false;
		        e.preventDefault();
		        jQuery.ajax({
			        url: $this.data('href'),
			        dataType: 'json',
			        success: function(data, textStatus, xhr) {
				        if (data.status === "ok") {
					        $('#generate-attestation-honneur-participation-n1-loading').removeClass('hidden');
					        $('#generate-attestation-honneur-participation-n1-download').addClass('hidden');
					        $('#generate-attestation-honneur-participation-n1-error').addClass('hidden');
				        }
			        },
			        error: function(xhr, textStatus, errorThrown) {
				        console.log(errorThrown);
			        }
		        });
		        $this.data('started', true);
	        });

            var participation_edit_url = "{{ url("admin_participation_edit", {id : "__id__"}) }}";
            $(document).on('click', '.edit', function (e) {
                e.preventDefault();
                var $this = $(this);
                var $form = $this.siblings('.inputs');
                if (!$this.data('editing')) {
                    var id = $this.data('id');
                    var url = participation_edit_url.replace('__id__', id);
                    var current = $this.data('price');
                    var disabled = ""
                    {% if formation.isPluriAnnuelle() and formation.isLockedMidCourse() %}
                        disabled = "disabled";
                    {% endif %}
                    var $input = $('<input class="form-control" type="number" '+disabled+' value="'+current+'"/>');
                    $this.siblings('.edit-val').html('');
                    {% if formation.isPluriAnnuelle() %}
                        var $label = $('<label for="'+id+'">Coût '+ $this.data('yearstart') +'</label>');
                        var $label2 = $('<label for="'+id+'n1">Coût '+ $this.data('yearend') +'</label>');
                        var currentN1 = $this.data('pricen1');

                        var $input2 = $('<input class="form-control n1" name="'+id+'n1" type="number" value="'+currentN1+'"/>');
                        var $total = $('<span class="total"></span>');
                        $form.html($label)
                            .append($input)
                            .append($label2)
                            .append($input2)
                        ;
                        var updateTotal = function() {
                            let res = Math.round((parseFloat($input.val()) + parseFloat($input2.val())) * 100) / 100;
                            $total.html('<strong>Total</strong> : ' + (!Number.isNaN(res) ? res : ""));
                        };
                        $form.append($total);
                        updateTotal();
                        $input.on('keyup', updateTotal);
                        $input2.on('keyup', updateTotal);
                        $input.on('change', updateTotal);
                        $input2.on('change', updateTotal);
                    {% else %}
                        $form.html($input);
                    {% endif %}
                    $this.find('.edit-text').text('Enregistrer');
                    $this.find('i').removeClass('fa-pencil').addClass('fa-check');
                    $this.data('editing', true);
                    var $cancel = $('<a href="#" class="label label-danger cancel-edit-price mls"><i class="fa fa-times"></i><span class="edit-text">Annuler</span></a>');
                    $this.after($cancel);
                    $cancel.click(function (e) {
                        e.preventDefault();
                        var $that = $(this).prev('.edit');
                        var val = $this.data('price');
                        var $editVal = $(this).siblings('.edit-val');

                        {% if formation.isPluriAnnuelle() %}
                            var valn1 = $this.data('pricen1') ;
                            let res = parseFloat(valn1);
                            let total = 0;
                            if (Number.isNaN(res)) {
                                total = Math.round((parseFloat(val)) * 100) / 100;
                            } else {
                                total = Math.round((parseFloat(val) + res) * 100) / 100;
                            }
                            $editVal.html(
                                $('<span><strong>'+
                                    total + '</strong><br>' +
                                    $that.data('yearstart') + ' : ' + parseFloat(val) + '<br>' +
                                    $that.data('yearend') + ' : ' + (!Number.isNaN(res) ? res : 0) +
                                '</span>')
                            );
                        {% else %}
                            $editVal.html(Math.round(parseFloat(val) * 100) / 100);
                        {% endif %}
                        $that.find('i').removeClass('fa-check').addClass('fa-pencil');
                        $that.find('.edit-text').text('');
                        $that.data('editing', false);
                        $(this).remove();
                        $form.html('');
                        $this.off('click.save');
                    });
                    $this.one('click.save', function() {
                        var val = parseFloat($form.find('input').not('.n1').val());
                        var valn1 = parseFloat($form.find('input.n1').val());
                        if (Number.isNaN(val)) {
                            val = 0;
                        }
                        if (Number.isNaN(valn1)) {
                            valn1 = 0;
                        }
                        var field = $this.data('field');
                        $this.data('price', val);
                        $this.data('pricen1', valn1);

                        dataVal = {};
                        dataVal[field] = val;
                        dataVal[field+"YearN1"] = valn1;

                        jQuery.ajax({
                            url: url,
                            type: 'POST',
                            dataType: 'json',
                            data: dataVal,
                            success: function(data, textStatus, xhr) {
                                {% if formation.isPluriAnnuelle() %}
                                    var valn1 = $this.data('pricen1') ;
                                    $this.siblings('.edit-val').html(
                                        $('<span><strong>'+
                                            Math.round((val + valn1) * 100) / 100 + '</strong><br>' +
                                            $this.data('yearstart') + ' : ' + val + '<br>' +
                                            $this.data('yearend') + ' : ' + valn1 +
                                        '</span>')
                                    );
                                {% else %}
                                    $this.siblings('.edit-val').html(val);
                                {% endif %}
                                $this.find('i').removeClass('fa-check').addClass('fa-pencil');
                                $this.find('.edit-text').text('');
                                $this.data('editing', false);
                                $form.html('');
                                $cancel.remove();
                            },
                            error: function(xhr, textStatus, errorThrown) {
                                //called when there is an error
                            }
                        });
                    });
                }
            });

            function editParticipation($this, id, field) {
                editParticipationValue(id, field, $this.val());
            }

            function editParticipationValue(id, field, value) {
	            var edit_url = "{{ url("admin_participation_edit", {id : "__id__"}) }}";
	            var url = edit_url.replace('__id__', id);

	            dataVal = {};
	            dataVal[field] = value;

	            return jQuery.ajax({
		            url: url,
		            type: 'POST',
		            dataType: 'json',
		            data: dataVal,
		            success: function(data, textStatus, xhr) {
			            console.log('ok')
		            },
		            error: function(xhr, textStatus, errorThrown) {
			            //called when there is an error
		            }
	            });
            }

	        $(document).on('change', '.edit_coordinator', function (e) {
                e.preventDefault();
                $this = $(this);
                editParticipation($this, $this.data('id'), $this.find(':selected').data('field'))
            });

            $(document).on('change', '.edit_partenariat', function (e) {
                e.preventDefault();
                $this = $(this);
                editParticipation($this, $this.data('id'), $this.find(':selected').data('field'))
            });

	        $(document).on('change', '.edit_exercise_mode', function (e) {
                e.preventDefault();
                $this = $(this);
                editParticipation($this, $this.data('id'), $this.find(':selected').data('field')) 
            });

	        $(document).on('change', '.edit_uga', function (e) {
                e.preventDefault();
                $this = $(this);
                editParticipation($this, $this.data('id'), $this.find(':selected').data('field'))
            });

	        $(document).on('click', '.btn-email', function (e) {
                e.preventDefault();
                $(this).closest('tr').next('tr.row-email').toggleClass('hide');
            });

            $('#btn-email-global').click(function (e) {
                e.preventDefault();
                $('#box-email').toggleClass('hide');
            });

            $('#btn-facturation-state').click(function (e) {
                e.preventDefault();
                $('#box-facturation').toggleClass('hide');
            });

            $(document).on('click', 'ul.pagination > li:not(.active)', function(e) {
            	e.preventDefault();
            	var page = $(this).data('page');
            	var url = '{{ url("admin_formation_participants", {id: formation.id, page: "__page__"}) }}';
            	url = url.replace('__page__', page);
	            jQuery.ajax({
		            url: url,
		            type: 'GET',
		            dataType: 'html',
		            success: function(data, textStatus, xhr) {
			            $('#box-participants').replaceWith(data);
			            window.history.replaceState(null, null, url.replace('participants', 'show'));
			            reloadDragAndDrop();
			            updateParticipationsTables();
		            },
		            error: function(xhr, textStatus, errorThrown) {
			            $('#box-participants table tbody').html('<td colspan="9" class="spinner">Une erreur s\'est produite, veuillez réessayer</td>');
		            }
	            });
	            $('.table-finance-sous-mode').css('display', "");
	            $('#box-participants table tbody').html('<td colspan="9" class="spinner"> <div class="bounce1"></div> <div class="bounce2"></div> <div class="bounce3"></div> </div>');
	            $('#box-participants table tbody').find('.spinner').each(function(i, e) {
	            	$(e).attr('colspan', $(e).closest('table').find('> thead > tr > th').length);
                });
            })

	        function reloadDragAndDrop() {
		        dragula(Array.prototype.map.call(document.getElementsByClassName("table-finance-sous-mode"), function (t) {
			        return document.getElementById(t.id)
		        }), {
			        revertOnSpill: true,
			        moves: function (el, container, handle) {
				        return handle.classList.contains('handle');
			        }
		        }).on('drop', function (el) {
			        editParticipationValue(el.dataset.id, "financeSousMode", el.closest("tbody").dataset.id);
			        setTimeout(updateParticipationsTables, 100);
		        });
	        }

            function updateParticipationsTables() {
	            Array.prototype.forEach.call(document.getElementsByClassName("row-participations"), function(el) {
		            $("#row-email-" + el.dataset.id).insertAfter($(el));
	            });
	            Array.prototype.forEach.call(document.getElementsByClassName("table-finance-sous-mode"), function(el) {
		            if (el.getElementsByTagName("tr").length === 0) {
			            el.style.display = "block";
			            el.style.minHeight = "80px";
                    } else {
			            el.style.display = null;
			            el.style.minHeight = null;
                    }
	            });
            }

	        updateParticipationsTables();
	        reloadDragAndDrop();

        });
    </script>
{% endblock %}
