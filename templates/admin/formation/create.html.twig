{% extends 'admin/base.html.twig' %}

{% form_theme form 'admin/form/fields.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'admin_programme_create' %}
        {{ 'formation.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_programme_edit'%}
        {{ 'formation.titles.edit'|trans }}
    {% endif %}
{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet">
    <style>
        .select2-container--default .select2-results__option[aria-disabled=true] {
            display: none;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #109199;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: #fff;
        }
    </style>
{% endblock %}

{% block body %}

    {% if programme is defined and (is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')) %}
        <section class="page-header mbn mtn" style="font-size: 16px">
            <p>Titre : <span class="text-bold">{{ programme.title }}</span><br>Référence : <span class="text-bold">{{ programme.reference }}</span></p>
        </section>
        {% if programme.commentaireNotif %}<div class="alert alert-info">{{ programme.commentaireNotif }}</div>{% endif %}
    {% endif %}

    {{ form_start(form) }}
    <div id="showArchivedAlert" data-show="{{showArchivedAlert}}"></div>
    <div class="nav-tabs-custom">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#tab_administrative" data-toggle="tab" aria-expanded="true">Administratif</a></li>
            <li class=""><a href="#tab_frais" data-toggle="tab" aria-expanded="false">Frais Formation</a></li>
            <li class=""><a style="max-width:none" href="#tab_module_times" data-toggle="tab" aria-expanded="false">Temps déclaratif des modules</a></li>
            <li class=""><a style="max-width:none" href="#tab_module_min_times" data-toggle="tab" aria-expanded="false">Temps minimum des modules</a></li>
        </ul>
        <div class="tab-content">
            <div class="tab-pane active" id="tab_administrative">

                        {% if not form.vars.valid %}
                            <div class="callout callout-danger">
                                {{ 'formation.error'|trans }}
                            </div>
                        {% endif %}

                        {% if formation is defined and formation.id is not null %}
                            <div class="data-formation"
                                 data-ca="{{ formation.caTotal() }}"
                                 data-id="{{ formation.getId() }}"
                            ></div>
                        {% endif %}

                        <div class="box box-info">
                            <div class="box-body">
                                {{ form_row(form.sessionNumber) }}
                                {{ form_row(form.nombrePlacesInitiales) }}
                                {{ form_row(form.private) }}

                                {# Formulaire des coordinateurs #}
                                <div class="form-group{% if form.coordinators.vars.errors|length > 0 %} has-error{% endif %}">
                                    <label class="control-label">{{ 'admin.formation.coordinators.title'|trans }}</label> <span class="required" title="Ce champ est obligatoire">*</span>
                                    {{ form_errors(form.coordinators) }}
                                    <ul class="coordinators" data-prototype="{% apply escape %}{{ include('admin/formation/coordinator_prototype.html.twig', { 'coordinator': form.coordinators.vars.prototype }) }}{% endapply %}">
                                        {% for coordinator in form.coordinators %}
                                            {{ include('admin/formation/coordinator_prototype.html.twig', { 'coordinator': coordinator, 'loop' : loop }) }}
                                        {% endfor %}
                                    </ul>
                                </div>
                                {# Formulaire des formateurs #}
                                <div class="form-group{% if form.formateurs.vars.errors|length > 0 %} has-error{% endif %}">
                                    <label class="control-label">{{ 'admin.formation.former.title'|trans }}</label>
                                    {{ form_errors(form.formateurs) }}
                                    <ul class="formateurs" data-prototype="{% apply escape %}{{ include('admin/formation/formateur_prototype.html.twig', { 'formateur': form.formateurs.vars.prototype }) }}{% endapply %}">
                                        {% for formateur in form.formateurs %}
                                            {{ include('admin/formation/formateur_prototype.html.twig', { 'formateur': formateur, 'loop' : loop }) }}
                                        {% endfor %}
                                    </ul>
                                </div>

                                {{ form_row(form.financeModes) }}
                                {{ form_row(form.financeSousModes) }}
                            </div>
                        </div>
                        <div class="box box-info">
                            <div class="box-body">
                                {# Formulaire des unités #}
                                <div class="unities-forms form-group{% if form.unities.vars.errors|length > 0 %} has-error{% endif %}">
                                    <label class="control-label">{{ 'admin.formation.unities.title'|trans }}</label>
                                    <a data-toggle="collapse" href="#collapse-a-unities">
                                        <i class="fa fa-caret-down"></i> <span class="activity-index"></span>
                                    </a>
                                    <div id="collapse-a-unities" class="panel-collapse collapse in" aria-expanded="true" style="">
                                        {{ form_errors(form.unities) }}
                                        <ul class="unities" {% if form.unities.vars.prototype is defined %}data-prototype="{% apply escape %}{{ include('admin/formation/unity_prototype.html.twig', { 'unity': form.unities.vars.prototype, 'init_tinymce' : true }) }}{% endapply %}"{% endif %}>
                                            {% for unity in form.unities %}
                                                {{ include('admin/formation/unity_prototype.html.twig', { 'unity': unity, 'loop' : loop, 'disabledDate': programme.disabledDatesForUnity(loop.index), 'displayHours': programme.disabledHoursForUnity(loop.index)  }) }}
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                                {# {{ form_row(form.startDate) }}
                                {{ form_row(form.startTime) }}
                                {{ form_row(form.endDate) }}
                                {{ form_row(form.endTime) }}
                                {{ form_row(form.openingDate) }}
                                {{ form_row(form.closingDate) }} #}
                            </div>
                        </div>
                        <div class="box box-info">
                            <div class="box-body">
                                {% if form.national is defined %}
                                    {{ form_row(form.national) }}
                                {% endif %}
                                {{ form_row(form.address) }}
                                {{ form_row(form.address2) }}
                                {{ form_row(form.zipCode) }}
                                {{ form_row(form.city) }}
                            </div>
                        </div>
                        <div class="box box-info">
                            <div class="box-body">
                                {{ form_row(form.expert) }}
                                {{ form_row(form.noMailing) }}
                            </div>
                        </div>
                        <div class="box box-info">
                            <div class="box-body">
                                <div id="render-rest-position"></div>
                            </div>
                        </div>
                        <div class="box box-solid box-primary">
                            <div class="box-header">
                                <h3 class="box-title">Convention</h3>
                            </div>
                            <div class="box-body">
                                {{ form_row(form.manualParticipantCount) }}
                                {{ form_row(form.cost) }}
                            </div>
                        </div>
                        <div class="box box-solid box-primary {% if not programme.isClasseVirtuelle %}hidden{% endif %}">
                            <div class="box-header">
                                <h3 class="box-title">Class virtuelle - formation Zoom</h3>
                            </div>
                            <div class="box-body">
                                {{ form_row(form.zoomId) }}
                                {{ form_row(form.zoomLink) }}
                            </div>
                        </div>
                        <div class="box-body">
                            {{ form_row(form.partenariat) }}
                            {{ form_row(form.ots) }}
                        </div>
                        <div class="box-body">
                            {{ form_row(form.commentaireNotif) }}
                        </div>
                        {{ form_widget(form._token) }}
            </div>
            <div class="tab-pane" id="tab_frais">
                <div class="alert alert-warning">
                    Attention, si vous surchargez les coûts d'un CR, les coûts des autres CR doivent être modifiés également. La totalité du coût formation doit être attribué aux CR.
                </div>
                <div class="panel box box-primary box-collapse-blue">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseCost" aria-expanded="true" class="">
                                <i class="fa fa-angle-down"></i> Coût formation
                            </a>
                        </h4>
                    </div>

                    <div id="collapseCost" class="panel-collapse collapse in" aria-expanded="true" style="">
                        <div class="box-body">
                            {# Formulaire des formateurs #}
                            <div class="form-group">
                                <label class="control-label">Formateurs</label>
                                <ul class="formateurs-surcharge">
                                </ul>
                                {{ form_row(form.costKilometres, { 'attr': {'class': 'update-all-honorary'}}) }}
                                {{ form_row(form.costBadges, { 'attr': {'class': 'update-all-honorary'}}) }}
                                {{ form_row(form.costRetrocessions, { 'attr': {'class': 'update-all-honorary'}}) }}
                                {{ form_row(form.costMateriel, { 'attr': {'class': 'update-all-honorary'}}) }}
                                {{ form_row(form.costDivers, { 'attr': {'class': 'update-all-honorary'}}) }}

                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel box box-primary box-collapse-blue">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseAvances" aria-expanded="true" class="">
                                <i class="fa fa-angle-down"></i> Avances formation
                            </a>
                        </h4>
                    </div>
                    {# Formulaire des coordinateurs #}
                    <div id="collapseAvances" class="panel-collapse collapse in" aria-expanded="true" style="">
                        <div class="box-body">
                            <div>
                                <ul class="coordinators-surcharge coordinators-surcharge-avances">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel box box-primary box-collapse-blue">
                    <div class="box-header with-border">
                        <h4 class="box-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseSurchageCost" aria-expanded="false" class="">
                                <i class="fa fa-angle-down"></i> Surcharge coûts formation
                            </a>
                        </h4>
                    </div>
                    {# Formulaire des coordinateurs #}
                    <div id="collapseSurchageCost" class="panel-collapse collapse" aria-expanded="false" style="">
                        <div class="box-body">
                            <div>
                                <ul class="coordinators-surcharge coordinators-surcharge-cost">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane" id="tab_module_times">
                {% for moduleTime in form.moduleTimes %}
                    {{ include('admin/formation/module_times_prototype.html.twig', { 'moduleTime': moduleTime, 'loop' : loop, 'modules' : modules }) }}
                {% endfor %}
                 {{ form_row(form.applyTimesToAll) }}
            </div>
            <div class="tab-pane" id="tab_module_min_times">
                {% for moduleMinTime in form.moduleMinTimes %}
                    {{ include('admin/formation/module_min_times_prototype.html.twig', { 'moduleMinTime': moduleMinTime, 'loop' : loop, 'modules' : modules }) }}
                {% endfor %}
            </div>
            <div id="render_rest">
                {{ form_end(form, {'render_rest': true}) }}
                {% if formation is defined and formation.isFormFormation and formation.formAlreadyAnswered %}
                    {% if formation.isFormVignette %}
                        {% if formation.hasFormPost and formation.form2AlreadyAnswered %}
                            <div class="alert alert-danger">Impossible de modifier les formulaires pré et post formation, des participants ont déjà démarré le parcours.</div>
                        {% else %}
                             <div class="alert alert-danger">Impossible de modifier le formulaire pré formation, des participants ont déjà démarré le parcours.</div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-danger">Impossible de modifier le formulaire, des participants ont déjà démarré le parcours.</div>
                    {% endif %}
                {% endif %}
                {% if formation is defined and formation.isElearning %}
                    {% if not form.elearning is defined %}
                        <div class="alert alert-danger">Impossible de modifier le formulaire e-elearning, des participants ont déjà démarré le parcours.</div>
                    {% endif %}
                {% endif %}
            </div>
            <div class="form-group">
                <button type="submit" class="btn-eduprat btn">{{ "admin.global.save"|trans }}</button>
            </div>
        </div>
    </div>



{% endblock %}

{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script>
	    var programmeTitle = "{{ programme.title|escape("js") }}";
	    var programmeId = "{{ programme.id|escape("js") }}";
        var unityStartEnd = "{{ unityStartEnd is defined ? unityStartEnd|json_encode|escape('js')  : false }}";
        var threeUnityComposed = "{{ threeUnityComposed == true ? 'true' : 'false' }}"
        var nbDays = "{{ nbDays != false ? nbDays : 'false' }}"
        var elearningOneUnity = "{{ elearningOneUnity != false ? elearningOneUnity : 'false' }}"

        $(document).ready(function() {
            $('#render-rest-position').append($('#render_rest').html());
            $('#render_rest').remove();
            $('#eduprat_domainbundle_formation_financeModes').multiSelect({});

            const select = document.getElementById('eduprat_domainbundle_formation_financeSousModes');
            const results = [];

            // Parcourir les optgroup
            for (const optgroup of select.children) {
                if (optgroup.tagName === 'OPTGROUP') {
                    const groupe = {
                        // text: optgroup.label,
                        text: optgroup.label,
                        children: []
                    };

                    // Parcourir les options de chaque optgroup
                    for (const option of optgroup.children) {
                        const enfant = {
                            id: parseInt(option.value),
                            text: option.text,
                        };
                        groupe.children.push(enfant);
                    }
                    results.push(groupe);
                }
            }

            updateFinanceSousMode();
            $('#eduprat_domainbundle_formation_financeModes').change(updateFinanceSousMode);

            function updateFinanceSousMode() {
                var selectedFM = $('#eduprat_domainbundle_formation_financeModes').find('option:selected').map(function (i, n) {
                    return $(n).text();
                }).get();

                const selectedValuesFSM = $('#eduprat_domainbundle_formation_financeSousModes').val();
                results.forEach(function(optoption) {
                    optoption.disabled = selectedFM.indexOf(optoption.text) === -1;
                    optoption.children.forEach(function(option) {
                        option.selected = selectedValuesFSM != null && selectedValuesFSM.indexOf(""+option.id) !== -1 && !optoption.disabled;
                    });
                });

                $('#eduprat_domainbundle_formation_financeSousModes').empty().select2({data: results});
            }

            {% if programme.isOneUnity() and programme.getUnityByPosition(1) and programme.getUnityByPosition(1).hasDays() %}
                $firstUnityStartDate = $("#eduprat_domainbundle_formation_unities_0_unitySessionDates_0_startDate");
            {% else %}
                $firstUnityStartDate = $("#eduprat_domainbundle_formation_unities_0_openingDate");
            {% endif %}

            let nb_unities = {{ programme.getNbUnities() }} - 1;
            let nb_day_last_unity = {{ programme.getNbDayLastUnity() }} - 1;
            {% if programme.getLastUnity() and programme.getLastUnity().hasDays() %}
                $lastUnityEndDate = $("#eduprat_domainbundle_formation_unities_"+nb_unities+"_unitySessionDates_"+nb_day_last_unity+"_endDate");
            {% else %}
                $lastUnityEndDate = $("#eduprat_domainbundle_formation_unities_"+nb_unities+"_closingDate");
            {% endif %}

            $firstUnityStartDate.change(function() {
                synchroFormOpeningDate($(this).val(), {{ programme.getDelaiStartDate() }});
            });

            $lastUnityEndDate.change(function() {
                synchroFormClosingDate($(this).val(), {{ programme.getDelaiEndDate() }});
            });

            let synchroFormOpeningDate = function(val, delay) {
                $('#eduprat_domainbundle_formation_formOpeningDate').datepicker( 'setDate', calculateDateWithDelay(val, delay));
            }

            let synchroFormClosingDate = function(val, delay) {
                $('#eduprat_domainbundle_formation_formClosingDate').datepicker( 'setDate', calculateDateWithDelay(val, delay));
            }

            let calculateDateWithDelay = function(val, delay) {
                let splitDate = val.split('/');
                if (splitDate.length !== 3) {
                    return;
                }
                let date = new Date(Date.parse(splitDate[2]+' '+splitDate[1]+ ' '+ splitDate[0]));
                date.setDate(date.getDate() + delay);
                let month = date.getMonth() + 1;
                month = month < 10 ? '0'+ month: month;
                let day = date.getDate() < 10 ? '0'+ date.getDate(): date.getDate();
                return day + '/'+ month + '/'+ date.getFullYear();
            }

            {% if programme.unities|length == 3 and programme.disabledDatesForUnity(3) %}
            $(".unity--2").on('change', '.startDate .startDateUnity, .endDate .endDateUnity', function() {
                $cibleDate = $("#"+$(this).attr('id').replace('_unities_1_unitySessionDates_', '_unities_2_unitySessionDates_'));
                $cibleDate.val($(this).val()).trigger('change');
            });
            {% endif %}

	        $('#eduprat_domainbundle_formation_actaliansPdf').change(function (e) {
		        if ($(this).is(':checked')) {
			        $('.actalians-group').slideDown();
			        $('.actalians-label').each(function(i, d) {
			        	$(d).html($(d).data('actalians'));
                    })
		        } else {
			        $('.actalians-label').each(function(i, d) {
				        $(d).html($(d).data('original'));
			        })
			        $('.actalians-group').slideUp(200, function(){
				        $('.actalians-group').find('input,textarea').val("");
			        });
		        }
	        });
	        $('#eduprat_domainbundle_formation_actaliansPdf').change();

	        $(".coordinator-initiator").change(function() {
	        	var checked = $(this).prop("checked");
		        $(".coordinator-initiator").prop("checked", false);
		        $(this).prop("checked", checked);
            });

        });
    </script>
    {{ tinymce_init() }}
    <script>
        var editor;
        var $formateursCollectionHolder;
        var $formateursSurchargeCollectionHolder;

        var checkDuplicateUrl = "{{ url("admin_formation_duplicate_check") }}";
        var isDuplicated = false;

        // formateurs
        var $addFormateurLink = $('<a href="#" class="add_formateur_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter un formateur</a>');
        var $newFormateurLinkLi = $('<div></div>').append($addFormateurLink);

        jQuery(document).ready(function () {

            if($("#showArchivedAlert").attr("data-show")){
                $('form[name="eduprat_domainbundle_formation"]').submit(function(e){
                    var form = $('#eduprat_domainbundle_formation_audit').val() === undefined ? $('#eduprat_domainbundle_formation_questionnaire') : $('#eduprat_domainbundle_formation_audit');
                    if(form.val() == "") {
                        if (confirm("Des réponses ont été potentiellement données au formulaire archivé. En continuant, vous supprimez toutes les réponses et retrouverez une session non débutée") == false) {
                            e.preventDefault();
                        }
                    }
                });
            }

            // formateurs
            $formateursCollectionHolder = $('ul.formateurs');
	        $formateursSurchargeCollectionHolder = $('ul.formateurs-surcharge');

	        $formateursCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLink($(this));
		        moveSurchargeFormateur($(this));
            });
            $formateursCollectionHolder.after($newFormateurLinkLi);
            $formateursCollectionHolder.data('index', $formateursCollectionHolder.find('> li.panel').length + 1);
            $addFormateurLink.on('click', function (e) {
                e.preventDefault();
                addItemForm($formateursCollectionHolder);
            });

	        $("form input, form select").on("invalid", function() {
		        var id = $(this).closest('.tab-pane').attr('id');
		        $('.nav-tabs a[href="#' + id + '"]').tab('show');
	        });

        });

        function addItemForm($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLink($newFormLi);
	        moveSurchargeFormateur($newFormLi);
        }

        function addItemFormDeleteLink($itemFormLi) {
	        var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer le formateur</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $('.formateur-surcharge[data-index="' + index + '"]').remove();
	            $itemFormLi.remove();
            });
        }

        var $coordinatorsCollectionHolder;
        var $coordinatorsSurchargeCostCollectionHolder;
        var $coordinatorsSurchargeAvancesCollectionHolder;

        // coordinators
        var $addCoordinatorLink = $('<a href="#" class="add_coordinator_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter un coordinateur</a>');
        var $newCoordinatorLinkLi = $('<div></div>').append($addCoordinatorLink);

        jQuery(document).ready(function () {
            // coordinators
            $coordinatorsCollectionHolder = $('ul.coordinators');
	        $coordinatorsSurchargeCostCollectionHolder = $('ul.coordinators-surcharge-cost');
	        $coordinatorsSurchargeAvancesCollectionHolder = $('ul.coordinators-surcharge-avances');
            $coordinatorsCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLinkCoordinator($(this));
                moveSurchargeCoordinator($(this));
                var id = $(".form-group:first", $(this)).children().attr('id');
            });
            $coordinatorsCollectionHolder.after($newCoordinatorLinkLi);
            $coordinatorsCollectionHolder.data('index', $coordinatorsCollectionHolder.find('> li.panel').length + 1);
            $addCoordinatorLink.on('click', function (e) {
                e.preventDefault();
                addItemFormCoordinator($coordinatorsCollectionHolder);
            });

            if ($('.data-formation').length) {
	            $(document).on('change', '.update-honorary', function(e) {
		            previewCoordinatorCost($(this).closest('li[data-index]').data('index'));
	            });

	            $(document).on('change', '.update-cost', function(e) {
		            previewFormationCost($(this).closest('li[data-index]').data('index'));
	            });

	            $(document).on('change', '.update-all-honorary', function(e) {
		            previewAllCoordinatorCost();
		            previewAllFormationCost();
	            });

	            previewAllCoordinatorCost();
	            previewAllFormationCost();
            } else {
	            $('form[name=eduprat_domainbundle_formation]').submit(function(e){
	            	var prevent = isDuplicated && $('#eduprat_domainbundle_formation_financeModes > option:selected').text().includes("Agence Nationale du DPC");
	            	if (prevent) {
			            alert('Une session avec le même n° de session existe déjà au sein de cette formation, il n\'est pas possible de créer des sessions en doublons pour les formation ANDPC');
			            e.preventDefault();
			            return false;
                    } else if (isDuplicated && !confirm('Une session avec le même n° de session existe déjà au sein de cette formation, êtes vous sûr de vouloir enregistrer ? Attention la création de doublon peut entraîner des problèmes d\'analyse de certaines données')) {
			            e.preventDefault();
			            return false;
		            }
	            })
            }

            if(unityStartEnd) {
                $.each(JSON.parse(unityStartEnd), function( key, unity ) {
                    var startTimeHour = $("#eduprat_domainbundle_formation_unities_"+unity["unity"]+"_unitySessionDates_"+unity["unitySessionDate"]+"_startTime_hour");
                    var startTimeMin = $("#eduprat_domainbundle_formation_unities_"+unity["unity"]+"_unitySessionDates_"+unity["unitySessionDate"]+"_startTime_minute");
                    var endTimeHour = $("#eduprat_domainbundle_formation_unities_"+unity["unity"]+"_unitySessionDates_"+unity["unitySessionDate"]+"_endTime_hour");
                    var endTimeMin = $("#eduprat_domainbundle_formation_unities_"+unity["unity"]+"_unitySessionDates_"+unity["unitySessionDate"]+"_endTime_minute");
                    startTimeHour.val(unity["startTimeHour"]);
                    startTimeMin.val(unity["startTimeMin"]);
                    endTimeHour.val(unity["endTimeHour"]);
                    endTimeMin.val(unity["endTimeMin"]);
                });
            }
        });


        function addItemFormCoordinator($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);

            newForm = $(newForm);

            $collectionHolder.data('index', index + 1);
            var $newFormLi = newForm;
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLinkCoordinator($newFormLi);
	        moveSurchargeCoordinator($newFormLi);
        }

        function moveSurchargeCoordinator($itemFormLi) {
        	$itemFormLi.find('.coordinator-surcharge-name').val($itemFormLi.find('select').find('option:selected').text());
	        $coordinatorsSurchargeCostCollectionHolder.append($itemFormLi.find('.coordinator-surcharge-cost'));
	        $coordinatorsSurchargeAvancesCollectionHolder.append($itemFormLi.find('.coordinator-surcharge-avances'));
        }

        function moveSurchargeFormateur($itemFormLi) {
        	$itemFormLi.find('.formateur-surcharge-name').val($itemFormLi.find('select').find('option:selected').text());
	        $formateursSurchargeCollectionHolder.append($itemFormLi.find('.formateur-surcharge'));
        }

        function addItemFormDeleteLinkCoordinator($itemFormLi) {
        	var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer le coordinateur</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $('.coordinator-surcharge-avances[data-index="' + index + '"]').remove();
	            $('.coordinator-surcharge-cost[data-index="' + index + '"]').remove();
                $itemFormLi.remove();
            });
        }

        $(document).on('change', '.formateur-person-select', function(e) {
	        var index = $(this).closest('.panel').data('index');
	        $('.formateur-surcharge[data-index="' + index + '"]').find('.update-all-honorary').val($(this).find('option:selected').data('price'));
	        $('.formateur-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());

        });

        $(document).on('change', '.coordinator-input', function(e) {
        	var index = $(this).closest('.panel').data('index');
	        $('.coordinator-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());
        });

        $('#eduprat_domainbundle_formation_sessionNumber').on("keyup change", function(e) {
	        checkDuplicate(programmeId, $(this).val());
        });

        $('#eduprat_domainbundle_formation_sessionNumber').change();

        function getFormationCost() {
            var $costInputs = $('#collapseCost').find('input');
            var cost = 0;
            $.each($costInputs, function(key, input) {
            	cost += parseFloat($.isNumeric($(input).val()) ? $(input).val() : 0);
            });
            return cost;
        }

        function getCoordinatorAvances(index) {
	        var $costInputs = $('#collapse-coord-' + index).find('input');
	        var cost = 0;
	        $.each($costInputs, function(key, input) {
		        cost += $.isNumeric(parseFloat($(input).val())) ? parseFloat($(input).val()) : 0;
	        });
	        return cost;
        }

        function previewAllCoordinatorCost() {
	        $.each($('ul.coordinators > li.panel'), function(index, coord) {
		        previewCoordinatorCost($(coord).data('index'));
	        });
        }

        function previewCoordinatorCost(index) {
        	var route = '{{ url('admin_formation_preview_honorary', {formation: '__formation__', person: '__person__', type: 'honorary'}) }}';
        	var person = $('.panel[data-index='+index+']').find('select').find('option:selected').val();
	        var $form = $('form[name=eduprat_domainbundle_formation]');
	        var url = route.replace('__formation__', $('.data-formation').data('id')).replace('__person__', person);
	        if ($('.honorary-preview-' + index).length === 0) {
		        $(".coordinator-surcharge-cost[data-index=" + index + "]").find('.honorary-input').closest('.form-group').append('<span class="help-block">Honoraires coordinateur actuels : <span class="honorary-preview honorary-preview-' + index + '"></span></span>');
                $(".coordinator-surcharge-cost[data-index=" + index + "]").find('.honorary-input-n1').closest('.form-group').append('<span class="help-block">Honoraires coordinateur actuels N1 : <span class="honorary-preview honorary-preview-n1-' + index + '"></span></span>');
		        $(".coordinator-surcharge-avances[data-index=" + index + "]").find('.coordinator-surcharge-name').closest('.form-group').append('<span class="help-block">Honoraires coordinateur actuels : <span class="honorary-preview honorary-preview-' + index + '"></span></span>');
	        }
	        $('.honorary-preview-' + index).html("calcul en cours...");
            $('.honorary-preview-n1-' + index).html("calcul en cours...");
	        var request = $.ajax({
		        url: url,
		        method: 'POST',
		        data: $form.serialize(),
		        success: function(data) {
			        var total = data.honorary.total;
                    var totalN1 = data.honoraryn1.total;
			        $('.honorary-preview-' + index).html(new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(total));
                    $('.honorary-preview-n1-' + index).html(new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(totalN1));
		        },
		        error: function(jqXHR, status, error) {
			        console.log(error);
			        $('.honorary-preview-' + index).html("Une erreur s'est produite");
		        }
	        });
        }

        function previewAllFormationCost() {
	        $.each($('ul.coordinators > li.panel'), function(index, coord) {
		        previewFormationCost($(coord).data('index'));
	        });
        }

        function previewFormationCost(index) {
        	var route = '{{ url('admin_formation_preview_honorary', {formation: '__formation__', person: '__person__', type: 'cost'}) }}';
        	var person = $('.panel[data-index='+index+']').find('select').find('option:selected').val();
	        var $form = $('form[name=eduprat_domainbundle_formation]');
	        var url = route.replace('__formation__', $('.data-formation').data('id')).replace('__person__', person);
	        if ($('.cost-preview-' + index).length === 0) {
		        $(".coordinator-surcharge-cost[data-index=" + index + "]").find('.cost-preview-group').append('<span class="help-block">Coût formation actuels pour ce coordinateur : <span class="cost-preview cost-preview-' + index + '"></span></span>');
	        }
	        $('.cost-preview-' + index).html("calcul en cours...");
	        var request = $.ajax({
		        url: url,
		        method: 'POST',
		        data: $form.serialize(),
		        success: function(data) {
			        var total = data.cost;
			        $('.cost-preview-' + index).html(new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(total));
		        },
		        error: function(jqXHR, status, error) {
			        console.log(error);
			        $('.cost-preview-' + index).html("Une erreur s'est produite");
		        }
	        });
        }

        function checkDuplicate(programme, sessionNumber) {
	        jQuery.ajax({
		        url: checkDuplicateUrl,
		        type: 'POST',
		        data: {programme: programme, sessionNumber: sessionNumber},
		        success: function(data, textStatus, xhr) {
			        isDuplicated = data.duplicated;
		        },
		        error: function(data, textStatus, xhr) {
			        console.log(data);
		        },
	        });
        }
    </script>
{% endblock %}
