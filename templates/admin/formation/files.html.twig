{% macro displayCoordinatorFileOrForm(fichiersCoordinator, idCoordinator, coordinator, formation, forms_facture_coordinator, n1) %}
    <li>
        {% if (not n1 and fichiersCoordinator[idCoordinator].factureCoordinator is defined) or
              (    n1 and fichiersCoordinator[idCoordinator].factureCoordinatorN1 is defined) %}
        {% if (not n1 and fichiersCoordinator[idCoordinator].factureCoordinator is not null) or
              (    n1 and fichiersCoordinator[idCoordinator].factureCoordinatorN1 is not null) %}
        <a target="_blank" href="{{ url('admin_coordinator_file', {'id' : fichiersCoordinator[idCoordinator].id, 'fileField' : 'factureCoordinatorFile', 'n1': n1}) }}">
            {% endif %}
            {% endif %}
            {{ coordinator.person.firstname }} {{ coordinator.person.lastname }}
            {% if formation.shouldDisplayAttestationN1() %}
                {% if n1 %} {{ formation.closingDate|date('Y') }} {% else %} {{ formation.openingDate|date('Y') }} {% endif %}
            {% endif %}
        {% if (not n1 and fichiersCoordinator[idCoordinator].factureCoordinator is defined) or
            (      n1 and fichiersCoordinator[idCoordinator].factureCoordinatorN1 is defined) %}
        {% if (not n1 and fichiersCoordinator[idCoordinator].factureCoordinator is not null) or
            (      n1 and fichiersCoordinator[idCoordinator].factureCoordinatorN1 is not null) %}
        </a>
        {% endif %}
        {% endif %}
        {% set display = true %}
        {# Impossibilité d'upload la facture de l'année N si la formation pluriannuelle est comptabilisée mi-parcours #}
        {% if formation.isPluriannuelle and not n1 %}
            {% set display = not formation.lockedMidCourse %}
        {% endif %}
        {% if not formation.locked and display %}
            <i class="fa fa-download btn-download-file" id="factureCoordinatorFile{% if n1 %}N1{% endif %}_{{ idCoordinator }}"></i>
            <div id="factureCoordinatorFile" class="downloadFile downloadFile-depot downloadFileTopo" >
                {% form_theme forms_facture_coordinator[coordinator.id] 'admin/form/no_errors.html.twig' %}
                {{ form_start(forms_facture_coordinator[coordinator.id],{'attr': {'class': 'formDownloadFile'}}) }}
                {% if forms_facture_coordinator[coordinator.id].factureCoordinatorFile is defined %}
                    {{ form_widget(forms_facture_coordinator[coordinator.id].factureCoordinatorFile) }}
                {% elseif n1 and forms_facture_coordinator[coordinator.id].factureCoordinatorFileN1 is defined %}
                    {{ form_widget(forms_facture_coordinator[coordinator.id].factureCoordinatorFileN1) }}
                {% endif %}
                {% if forms_facture_coordinator[coordinator.id].submit is defined %}
                    {{ form_widget(forms_facture_coordinator[coordinator.id].submit) }}
                {% endif %}
                {{ form_widget(forms_facture_coordinator[coordinator.id]._token) }}
                {{ form_end(forms_facture_coordinator[coordinator.id], {'render_rest': false}) }}
            </div>
        {% endif %}
        {% if not n1 and fichiersCoordinator[idCoordinator].factureCoordinator is defined %}
            {% if fichiersCoordinator[idCoordinator].factureCoordinator is not null %}
                ({{ "admin.formation.depot"|trans }} {{ fichiersCoordinator[idCoordinator].updatedAt | date ('d/m/Y') }})
            {% endif %}
        {% endif %}
        {% if n1 and fichiersCoordinator[idCoordinator].factureCoordinatorN1 is defined %}
            {% if fichiersCoordinator[idCoordinator].factureCoordinatorN1 is not null %}
                ({{ "admin.formation.depot"|trans }} {{ fichiersCoordinator[idCoordinator].updatedAtN1 | date ('d/m/Y') }})
            {% endif %}
        {% endif %}
    </li>
{% endmacro %}

{% set preSufix = formation.hasFormPost ? "pré-session" : "" %}
<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        <li class="active"><a href="#tab_global" data-toggle="tab" aria-expanded="true">Documents communs</a></li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane active" id="tab_global">
            <div class="row">
                <div class="col-lg-12">
                    {# Remontée de toutes les erreurs d'upload #}
                    {% set errorDisplayed = false %}
                    {% set pluriAnuelle = formation.isPluriannuelle() %}
                    {# Emargement #}
                    {% if formation.financeSousModes.count %}
                        {% for financeSousMode in formation.financeSousModes %}
                            {% for formEmargement in forms_emargement[financeSousMode.id] %}
                                {% if not errorDisplayed and formEmargement.vars.errors|length > 0 %}
                                    {{ form_errors(formEmargement) }}
                                    {% set errorDisplayed = true %}
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                    {% endif %}
                    {# Coordinateurs #}
                    {% if formation.coordinators.count and not app.user.isPharmacieFormer %}
                        {% for coordinator in formation.coordinators %}
                            {% set idCoordinator = coordinator.id %}
                            {% if not errorDisplayed and forms_facture_coordinator[coordinator.id].vars.errors|length > 0 %}
                                {{ form_errors(forms_facture_coordinator[coordinator.id]) }}
                                {% set errorDisplayed = true %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    {# Formateurs #}
                    {% if formation.formateurs.count %}
                        {% for formateur in formation.formateurs %}
                            {% set idFormateur = formateur.id %}
                            {% if not errorDisplayed and forms_contrat_formateur[formateur.id].vars.errors|length > 0 %}
                                {{ form_errors(forms_contrat_formateur[formateur.id]) }}
                                {% set errorDisplayed = true %}
                            {% endif %}
                            {% if not errorDisplayed and forms_facture_formateur[formateur.id].vars.errors|length > 0 %}
                                {{ form_errors(forms_facture_formateur[formateur.id]) }}
                                {% set errorDisplayed = true %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    {# Facture restauration #}
                    {% if not errorDisplayed and form_facture_resto.vars.errors|length > 0 %}
                        {{ form_errors(form_facture_resto) }}
                        {% set errorDisplayed = true %}
                    {% endif %}
                    {# Topos #}
                    {% if fichiersTopo is not empty %}
                        {% for file in fichiersTopo %}
                            {% if file.topo is not null %}
                                {% if not errorDisplayed and forms_topo[file.id].vars.errors|length > 0 %}
                                    {{ form_errors(forms_topo[file.id]) }}
                                    {% set errorDisplayed = true %}
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    {# Topo new #}
                    {% if not errorDisplayed and forms_topo_new.vars.errors|length > 0 %}
                        {{ form_errors(forms_topo_new) }}
                        {% set errorDisplayed = true %}
                    {% endif %}
                </div>
                <div class="col-lg-3">
                    <b>Documents de préparation de la formation</b>
                    <ul>
                        {% if course %}
                            <li>
                                Documents {{ preSufix }} vierges :
                                <ul>
                                    {% if formation.isTcs %}
                                        <li><a href="{{ url('pdf_tcs_empty_pdf', {'id': formation.id, 'token': formation.token, auditId: 1 }) }}" target="_blank">{{ ("admin.formation.type."  ~ formation.formTypePre) | trans }} {{ preSufix }}</a></li>
                                    {% elseif not formation.isFormPresentielle and formation.hasLinkedForm %}
                                        <li><a href="{{ url('pdf_audit_empty_pdf', {'id': formation.id, 'token': formation.token, auditId: 1 }) }}" target="_blank">{{ ("admin.formation.type."  ~ formation.formTypePre) | trans }} {{ preSufix }}</a></li>
                                    {% endif %}
                                    {% if formation.isFormPresentielle and formation.hasLinkedForm %}
                                        <li><a href="{{ url('pdf_survey_empty_pdf', {'id': formation.id, 'token': formation.token, surveyId: 1 }) }}" target="_blank">{{ ("admin.formation.type."  ~ formation.formTypePre) | trans }} {{ preSufix }}</a></li>
                                    {% endif %}
                                    {% if not oldCourse and courseManager.hasModule(course, "etutorat_1") and formation.programme.hasEtutorat %}
                                        <li><a href="{{ url('pdf_etutorat_empty_pdf', {'id': formation.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.etutoratEmpty"|trans }}</a></li>
                                    {% endif %}
                                </ul>
                            </li>
                            <li>
                                Documents post-session vierges :
                                <ul>
                                    {% if formation.hasFormPost() %}
                                        {% if not formation.isFormPresentielle and formation.hasLinkedForm %}
                                            <li><a href="{{ url('pdf_audit_empty_pdf', {'id': formation.id, 'token': formation.token, auditId: 2 }) }}" target="_blank">{{ ("admin.formation.type."  ~ formation.formTypePost) | trans }} post-session</a></li>
                                        {% else %}
                                            <li><a href="{{ url('pdf_survey_empty_pdf', {'id': formation.id, 'token': formation.token, surveyId: 2 }) }}" target="_blank">{{ ("admin.formation.type."  ~ formation.formTypePost) | trans }} post-session</a></li>
                                        {% endif %}
                                    {% endif %}
                                    {% if not oldCourse %}
                                        {% if courseManager.hasModule(course, "synthese") %}
                                            <li><a href="{{ url('pdf_synthese_empty_pdf', {'id': formation.id, 'participation': "null", 'token': formation.token }) }}" target="_blank">{{ "admin.formation.syntheseEmpty"|trans }}</a></li>
                                        {% endif %}
                                        {% if courseManager.hasModule(course, "progression") %}
                                            <li><a href="{{ url('pdf_progression_empty_pdf', {'id': formation.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.progressionEmpty"|trans }}</a></li>
                                        {% endif %}
                                        {% if courseManager.hasModule(course, "fiche_action_1") %}
                                            <li><a href="{{ url('pdf_action_empty_pdf', {'id': formation.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.actionEmpty"|trans }}</a></li>
                                        {% endif %}
                                    {% endif %}
                                </ul>
                            </li>
                        {% endif %}

                        {% if is_granted('ROLE_WEBMASTER') %}
                            <li><a href="{{ url('pdf_convention_empty_pdf', {'id': formation.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.conventionEmpty"|trans }}</a></li>
                        {% endif %}
                        {% if not formation.isSedd %}
                            {% if formation.formateurs.count %}
                                <li>
                                    {{ "admin.formation.generer"|trans }} {{ "admin.formation.consultContratFormateur"|trans }}
                                    <ul>
                                        {% set nbFormer = 0 %}
                                        {% set index = 0 %}
                                        {% set formerName = [] %}
                                        {% set firstFormerName = '' %}
                                        {% for formateur in formation.formateurs %}
                                            {% set nbFormer = loop.length %}
                                            {% set index = loop.index %}
                                            {% if index == 1 %}
                                                {% set firstFormerName = formateur.person.fullname %}
                                            {% endif %}
                                            {% set formerName = formerName|merge([formateur.person.fullname]) %}
                                            <li><a target="_blank" href="{{ url('pdf_contract_former_pdf', {'id': formateur.id, 'formation': formation.id, 'token' : formation.token}) }}">{{ formateur.person.fullname }}</a></li>
                                        {% endfor %}
                                    </ul>
                                </li>
                            {% endif %}
                        {% endif %}
                         <li>
                            <a target="_blank" href="{{ url('pdf_restitution_audit_groupe_pdf', { id: formation.id, token: formation.token }) }}">
                                {{ "admin.formation.generer"|trans }}
                                {% if formation.isVignette() %}
                                    {{ "admin.formation.prescoringGroupe" | trans({"%type%": formation.isPredefined ? "Cas cliniques" : "Audit"}) }}
                                {% else %}
                                    {{ "admin.formation.prerestitutionGroupe" | trans({"%type%": formation.isPredefined ? "Cas cliniques" : "Audit"}) }}
                                {% endif %}
                            </a>
                        </li>
                        <li>
                            <a href="{{ url('pdf_auto_evaluations_pdf', {'id': formation.id, token: formation.token }) }}" target="_blank">{{ "admin.formation.downloadSyntheseAutoEval"|trans }}</a>
                        </li>
                        {% if not formation.programme.isFormatPresentiel %}
                            {% if is_granted('ROLE_WEBMASTER') %}
                                <li>
                                    <a target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: formation.id, token: formation.token, participant: "null", person : "null" }) }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationParticipationParticipants" | trans }}{% if pluriAnuelle %} de {{ formation.openingDate|date('Y')}}{% endif %}</a>
                                </li>
                                {% if pluriAnuelle %}
                                    <li>
                                        <a target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: formation.id, token: formation.token, participant: "null", person : "null", n1 : "true" }) }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationParticipationParticipants" | trans }} de {{ (formation.openingDate|date('Y') + 1)}}</a>
                                    </li>
                                {% endif %}
                            {% elseif app.user.isCoordinator %}
                                <li>
                                    <a target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: formation.id, token: formation.token, participant: "null", person : app.user.id  }) }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationParticipationParticipants" | trans }}{% if pluriAnuelle %} de {{ formation.openingDate|date('Y')}}{% endif %}</a>
                                </li>
                                {% if pluriAnuelle %}
                                    <li>
                                        <a target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: formation.id, token: formation.token, participant: "null", person : app.user.id, n1 : "true"  }) }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationParticipationParticipants" | trans }} de {{ (formation.openingDate|date('Y') + 1)}}</a>
                                    </li>
                                {% endif %}
                            {% endif %}
                        {% endif %}
                    </ul>
                    <b>Documents de facturation</b>
                    <ul>
                        {% if is_granted('ROLE_COORDINATOR_LBI') and not app.user.isPharmacieFormer %}
                            {% for coordinator in formation.coordinators %}
                                {% if displayPdfCoordinatorHonorary[coordinator.id] is defined and displayPdfCoordinatorHonorary[coordinator.id] and (is_granted("ROLE_WEBMASTER") or coordinator.person == app.user) %}
                                    <li>
                                        <a href="{{ url('pdf_budget_cr_total_pdf', {'formation': formation.id, 'tokenFormation': formation.token, 'coordinator': coordinator.id, 'token': coordinator.person.token }) }}" target="_blank" title="{{ 'admin.user.budgetCrTotal'|trans }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.coordinatorHonoraryPdf"|trans }} - {{ coordinator.person.fullname }} {% if pluriAnuelle %} de {{ formation.openingDate|date('Y')}}{% endif %}</a>
                                    </li>
                                    {% if pluriAnuelle %}
                                        <li>
                                            <a href="{{ url('pdf_budget_cr_total_pdf', {'formation': formation.id, 'tokenFormation': formation.token, 'coordinator': coordinator.id, 'n1' : true, 'token': coordinator.person.token }) }}" target="_blank" title="{{ 'admin.user.budgetCrTotal'|trans }}">{{ "admin.formation.generer"|trans }} {{ "admin.formation.coordinatorHonoraryPdf"|trans }} - {{ coordinator.person.fullname }} de {{ formation.openingDate|date('Y') + 1}}</a>
                                        </li>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        {% if is_granted("ROLE_WEBMASTER") %}
                            {% if formation.programme.unities|length %}
                                {% if formation.programme.isElearningOneUnity %}
                                    <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 1 }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogs"|trans }} </a></li>
                                {% elseif formation.programme.isElearningTwoUnity %}
                                    <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 1 }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} 1</a></li>
                                    <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 2 }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} 2</a></li>
                                {% else %}
                                    {% for unity in formation.programme.unities %}
                                        {% if unity.isElearning or unity.isVirtuelle %}
                                            <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : loop.index }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} {% if loop.index == 3 and formation.isFourUnity %}4{% else %}{{ loop.index }}{% endif %}</a></li>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            {% else %}
                                <a href="{{ url('admin_formation_export_participation_logs', {'id': formation.id }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogs"|trans }}</a>
                            {% endif %}
                        {% endif %}
                        {% if is_granted("ROLE_WEBMASTER") and not formation.programme.isFormatPresentiel and formation.hasOneAttestationHonneurUploaded %}
                            <li>
                                <a class="generate-attestation-honneur-participation" id="generate-attestation-honneur-participation" data-started="false" {% if not (formation.hasGeneratedFile('attestation_honneur') and not formation.generateFileIsFinished('attestation_honneur')) or formation.hasGeneratedFileError('attestation_honneur') %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, financeSousMode: formation.financeSousModes.first.id, 'type': 'attestation_honneur', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationHonneur"|trans }} {% if pluriAnuelle %} de {{ formation.openingDate|date('Y') }} {% endif %} {% if formation.hasGeneratedFile('attestation_honneur') and formation.generateFileIsFinished('attestation_honneur') %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                <ul>
                                    <li id="generate-attestation-honneur-participation-loading" {% if ((formation.hasGeneratedFile('attestation_honneur') and formation.generateFileIsFinished('attestation_honneur')) or (not formation.hasGeneratedFile('attestation_honneur'))) or formation.hasGeneratedFileError('attestation_honneur') %}class="hidden"{% endif %}>
                                        <span>{{ "admin.formation.generation"|trans }}</span>
                                    </li>
                                    {% if formation.hasGeneratedFile('attestation_honneur') %}
                                        {% if formation.generateFileIsFinished('attestation_honneur') %}
                                            <li id="generate-attestation-honneur-participation-download">
                                                <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id,  'type': 'attestation_honneur','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.attestationHonneur"|trans }} {% if pluriAnuelle %} de {{ formation.openingDate|date('Y') }} {% endif %}</a>
                                            </li>
                                        {% endif %}
                                    {% endif %}
                                    {% if formation.hasGeneratedFileError('attestation_honneur') %}
                                        <li id="generate-attestation-honneur-participation-error">
                                            <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                        </li>
                                    {% endif %}
                                </ul>
                            </li>
                            {% if pluriAnuelle %}
                                <li>
                                    <a class="generate-attestation-honneur-participation-n1" id="generate-attestation-honneur-participation-n1" data-started="false" {% if not (formation.hasGeneratedFile('attestation_honneur_n1') and not formation.generateFileIsFinished('attestation_honneur_n1')) or formation.hasGeneratedFileError('attestation_honneur_n1') %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, financeSousMode: formation.financeSousModes.first.id, 'type': 'attestation_honneur_n1', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.attestationHonneur"|trans }} de {{ formation.closingDate|date('Y') }} {% if formation.hasGeneratedFile('attestation_honneur_n1') and formation.generateFileIsFinished('attestation_honneur_n1') %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                    <ul>
                                        <li id="generate-attestation-honneur-participation-n1-loading" {% if ((formation.hasGeneratedFile('attestation_honneur_n1') and formation.generateFileIsFinished('attestation_honneur_n1')) or (not formation.hasGeneratedFile('attestation_honneur_n1'))) or formation.hasGeneratedFileError('attestation_honneur_n1') %}class="hidden"{% endif %}>
                                            <span>{{ "admin.formation.generation"|trans }}</span>
                                        </li>
                                        {% if formation.hasGeneratedFile('attestation_honneur_n1') %}
                                            {% if formation.generateFileIsFinished('attestation_honneur_n1') %}
                                                <li id="generate-attestation-honneur-participation-n1-download">
                                                    <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id,  'type': 'attestation_honneur_n1','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.attestationHonneur"|trans }} de {{ formation.closingDate|date('Y') }}</a>
                                                </li>
                                            {% endif %}
                                        {% endif %}
                                        {% if formation.hasGeneratedFileError('attestation_honneur_n1') %}
                                            <li id="generate-attestation-honneur-participation-n1-error">
                                                <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </li>
                            {% endif %}
                        {% endif %}
                    </ul>
                </div>
                <div class="col-lg-3"><b>Divers</b>
                <ul>
                {% if is_granted('ROLE_WEBMASTER') or app.user.isCoordinator %}
                    <li>
                        <a href="{{ url('admin_formation_export_formation', {'id': formation.id}) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.parfticipantsFile"|trans }}</a>
                    </li>
                    {% if formation.hasFormPost() %}
                        <li>
                            <a target="_blank" href="{{ url('pdf_restitution_audit_groupe_2_pdf', { id: formation.id, token: formation.token }) }}">{{ "admin.formation.generer"|trans }}
                                {% if formation.isVignette() %}
                                    {{ "admin.formation.scoringGroupe" | trans({"%type%": formation.isPredefined ? "Cas cliniques" : "Audit"}) }}
                                {% else %}
                                    {{ "admin.formation.restitutionGroupe" | trans({"%type%": formation.isPredefined ? "Cas cliniques" : "Audit"}) }}
                                {% endif %}
                            </a>
                        </li>
                    {% endif %}
                {% endif %}
                {% if is_granted("ROLE_WEBMASTER") %}
                    {% if formation.programme.unities|length %}
                        {% if formation.programme.isElearningOneUnity %}
                            <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 1, 'withIp' : true }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogs"|trans }} (avec ip)</a></li>
                        {% elseif formation.programme.isElearningTwoUnity %}
                            <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 1, 'withIp': true }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} 1 (avec ip)</a></li>
                            <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : 2, 'withIp': true }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} 2 (avec ip)</a></li>
                        {% else %}
                            {% for unity in formation.programme.unities %}
                                {% if unity.isElearning or unity.isVirtuelle %}
                                    <li><a href="{{ url('admin_formation_export_participation_logs_unity', {'id': formation.id, 'unity' : loop.index, 'withIp' : true }) }}" target="_blank">{{ "admin.formation.downloadParticipationLogsUnity"|trans }} {% if loop.index == 3 and formation.isFourUnity %}4{% else %}{{ loop.index }}{% endif %} (avec ip)</a></li>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endif %}
                {% endif %}
                </ul>
                </div>
                <div class="col-lg-3">
                    <b>Documents importés</b><br>
                    <ul class="uploadList" data-formationID="{{ formation.id }}">
                        {% if formation.formateurs.count %}
                            {% set idFormateur = 0 %}
                            <li>
                                {{ "admin.formation.deposer"|trans }} {{ "admin.formation.consultFactureFormateur" | trans }}
                                <ul>
                                    {% for formateur in formation.formateurs %}
                                        {% set idFormateur = formateur.id %}
                                        <li>
                                            {% if fichiersFormateur[idFormateur].facture is defined %}
                                            {% if fichiersFormateur[idFormateur].facture is not null %}
                                            <a target="_blank" href="{{ url('admin_formateur_file', {'id' : fichiersFormateur[idFormateur].id, 'fileField' : 'factureFile'}) }}">
                                                {% endif %}
                                                {% endif %}
                                                {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                                                {% if fichiersFormateur[idFormateur].facture is defined %}
                                                {% if fichiersFormateur[idFormateur].facture is not null %}
                                            </a>
                                            {% endif %}
                                            {% endif %}
                                            {% if not formation.featureDisabledBecauseAccounted() %}
                                                <i class="fa fa-download btn-download-file" id="factureFile_{{ idFormateur }}"></i>
                                                <div id="factureFile" class="downloadFile downloadFile-depot downloadFileTopo">
                                                    {% form_theme forms_facture_formateur[formateur.id] 'admin/form/no_errors.html.twig' %}
                                                    {{ form_start(forms_facture_formateur[formateur.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                    {{ form_widget(forms_facture_formateur[formateur.id].factureFile) }}
                                                    {% if forms_facture_formateur[formateur.id].submit is defined %}
                                                        {{ form_widget(forms_facture_formateur[formateur.id].submit) }}
                                                    {% endif %}
                                                    {{ form_widget(forms_facture_formateur[formateur.id]) }}
                                                    {{ form_end(forms_facture_formateur[formateur.id], {'render_rest': false}) }}
                                                </div>
                                            {% endif %}
                                            {% if fichiersFormateur[idFormateur].facture is defined %}
                                                {% if fichiersFormateur[idFormateur].facture is not null %}
                                                    ({{ "admin.formation.depot"|trans }} {{ fichiersFormateur[idFormateur].updatedAt|date ('d/m/Y') }})
                                                {% endif %}
                                            {% endif %}
                                        </li>
                                    {% endfor %}

                                </ul>
                            </li>
                        {% endif %}
                        {# A SUPPRIMER LORSQUE TOUS LES CONTRATS ET FACTURE FORMATEUR AURONT ETE RECUPERES ET REDEPOSES #}
                        {% if formation.factureFormer is not null %}
                            <li>
                                [ <a target="_blank" href="{{ url('admin_formation_file', {'id' : formation.id, 'fileField' : 'factureFormerFile'}) }}">Récupérer {{ "admin.formation.consultFactureFormateur" | trans }} {{ firstFormerName }} ({{ "admin.formation.depot"|trans }} {{ uploadFileDates.factureFormer }})</a> ]
                            </li>
                        {% endif %}
                        {##}
                        {% if formation.coordinators.count and not app.user.isPharmacieFormer %}
                            {% set idCoordinator = 0 %}
                            <li>
                                {{ "admin.formation.deposer"|trans }} {{ "admin.formation.factureCoordinateur" | trans }}
                                <ul>
                                    {% if app.user.isCoordinator %}
                                        {% set coordinator = app.user.coordinatorByFormation(formation) %}
                                        {% set idCoordinator = coordinator.id %}
                                        {{ _self.displayCoordinatorFileOrForm(fichiersCoordinator, idCoordinator, coordinator, formation, forms_facture_coordinator) }}
                                        {% if formation.shouldDisplayAttestationN1 %}
                                            {{ _self.displayCoordinatorFileOrForm(fichiersCoordinatorN1, idCoordinator, coordinator, formation, forms_facture_coordinatorN1, true) }}
                                        {% endif %}
                                    {% else %}
                                        {% for coordinator in formation.coordinators %}
                                            {% set idCoordinator = coordinator.id %}
                                            {{ _self.displayCoordinatorFileOrForm(fichiersCoordinator, idCoordinator, coordinator, formation, forms_facture_coordinator) }}
                                            {% if formation.shouldDisplayAttestationN1 %}
                                                {{ _self.displayCoordinatorFileOrForm(fichiersCoordinatorN1, idCoordinator, coordinator, formation, forms_facture_coordinatorN1, true) }}
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}

                                </ul>
                            </li>
                        {% endif %}
                        <li>
                            {% if formation.factureRestauration is not null %}
                                <a target="_blank" href="{{ url('admin_formation_file', {'id' : formation.id, 'fileField' : 'factureRestaurationFile'}) }}">{{ "admin.formation.consulter"|trans }} {{ "admin.formation.factureRestaurateur" | trans }}</a>
                            {% else %}
                                {{ "admin.formation.deposer"|trans }} {{ "admin.formation.factureRestaurateur"|trans }}
                            {% endif %}
                            {% if not formation.locked %}
                                <div id="factureRestaurationFile" class="downloadFile">
                                    <i class="fa fa-download btn-download-file" id="{{ form_facture_resto.vars.name }}"></i>
                                    {% form_theme form_facture_resto 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(form_facture_resto,{'attr': {'class': 'formDownloadFile'}}) }}
                                    {{ form_widget(form_facture_resto.factureRestaurationFile) }}
                                    {% if form_facture_resto.submit is defined %}
                                        {{ form_widget(form_facture_resto.submit) }}
                                    {% endif %}
                                    {{ form_widget(form_facture_resto._token) }}
                                    {{ form_end(form_facture_resto, {'render_rest': false}) }}
                                </div>
                            {% endif %}
                            {% if formation.factureRestauration is not null %}({{ "admin.formation.depot"|trans }} {{ uploadFileDates.factureRestauration }}){% endif %}
                        </li>
                        <li>
                            {{ "admin.formation.toposProgramme" | trans }}
                            <ul>
                                {% for file in formation.topoProgrammeFiles %}
                                    {% if file.topo is not null %}
                                        <li>
                                            <div style="display:flex;margin-top:0.5em;">
                                                <a class="link-topo" title="{{ file.topoOriginalName }}" href="{{ url('admin_topo_programme_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ file.topoOriginalName }}</a>
                                                <a id="delete-default-topo" data-name="{{ file.topoOriginalName }}" href="{{ url('admin_topo_programme_delete_file', {'id' : file.id, 'formation' : formation.id }) }}" style="padding:0; margin-left:1em; margin-top:-5px" type="submit" id="{{ file.id }}" class="btn-default btn">Supprimer</a>
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </li>
                        <li>
                            {{ "admin.formation.topos" | trans }}
                            <ul>
                                {% if fichiersTopo is not empty %}
                                    {% for file in fichiersTopo %}
                                        {% if file.topo is not null %}
                                            <li>
                                                {% set name = "admin.formation.topo"|trans %}
                                                {% if file.topoOriginalName %}
                                                    {% set name = file.topoOriginalName %}
                                                {% endif %}
                                                <a class="link-topo" target="_blank" title="{{ name }}" href="{{ url('admin_topo_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                                <div id="{{ "topoFile" ~ file.id }}" class="downloadFile downloadFile-depot downloadFileTopo">
                                                    {% form_theme forms_topo[file.id] 'admin/form/no_errors.html.twig' %}
                                                    {{ form_start(forms_topo[file.id],{'attr': {'class': 'formDownloadFile formDownloadFileTopos'}}) }}
                                                    {{ form_widget(forms_topo[file.id].topoFile) }}
                                                    {% if forms_topo[file.id].submit is defined %}
                                                        {{ form_widget(forms_topo[file.id].submit) }}
                                                    {% endif %}
                                                    {{ form_widget(forms_topo[file.id]._token) }}
                                                    {{ form_end(forms_topo[file.id], {'render_rest': false}) }}
                                                </div>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                                <li>{{ "admin.formation.deposer"|trans }}
                                    <div id="topoFile" class="downloadFile downloadFile-depot downloadFileTopo upload-topo">
                                        <i class="fa fa-download btn-download-file" id="{{ forms_topo_new.vars.name }}"></i>
                                        {% form_theme forms_topo_new 'admin/form/no_errors.html.twig' %}
                                        {{ form_start(forms_topo_new,{'attr': {'class': 'formDownloadFile formDownloadFileTopos'}}) }}
                                        {{ form_widget(forms_topo_new.topoFile) }}
                                        {% if forms_topo_new.submit is defined %}
                                            {{ form_widget(forms_topo_new.submit) }}
                                        {% endif %}
                                        {{ form_widget(forms_topo_new._token) }}
                                        {{ form_end(forms_topo_new, {'render_rest': false}) }}
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <li>
                            {{ "admin.formation.tools" | trans }}
                            <ul>
                                {% for file in formation.programme.toolProgrammeFiles %}
                                    {% if file.topo is not null %}
                                        <li>
                                            <div style="display:flex;margin-top:0.5em;">
                                                <a class="link-topo" title="{{ file.topoOriginalName }}" href="{{ url('admin_topo_tool_programme_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ file.topoOriginalName }}</a>
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </li>
                        <li>
                            {{ "admin.formation.documentsPedagogiques" | trans }}
                            <ul>
                                {% for file in formation.programme.documentsPedagogiquesFiles %}
                                    {% if file.topo is not null %}
                                        <li>
                                            <div style="display:flex;margin-top:0.5em;">
                                                <a class="link-topo" title="{{ file.topoOriginalName }}" href="{{ url('admin_document_pedagogique_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ file.topoOriginalName }}</a>
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </li>
                        {% if not formation.isSedd %}
                            {% if formation.formateurs.count %}
                                <li>
                                    {{ "admin.formation.deposer"|trans }} {{ "admin.formation.consultContratFormateur" | trans }}
                                    <ul>
                                        {% for formateur in formation.formateurs %}
                                            {% set idFormateur = formateur.id %}
                                            <li>
                                                {% if fichiersFormateur[idFormateur].contrat is defined %}
                                                {% if fichiersFormateur[idFormateur].contrat is not null %}
                                                <a target="_blank" href="{{ url('admin_formateur_file', {'id' : fichiersFormateur[idFormateur].id, 'fileField' : 'contratFile'}) }}">
                                                    {% endif %}
                                                    {% endif %}
                                                    {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                                                    {% if fichiersFormateur[idFormateur].contrat is defined %}
                                                    {% if fichiersFormateur[idFormateur].contrat is not null %}
                                                </a>
                                                {% endif %}
                                                {% endif %}
                                                <i class="fa fa-download btn-download-file" id="contratFile_{{ idFormateur }}"></i>
                                                <div id="contratFile" class="downloadFile downloadFile-depot downloadFileTopo">
                                                    {% form_theme forms_contrat_formateur[formateur.id] 'admin/form/no_errors.html.twig' %}
                                                    {{ form_start(forms_contrat_formateur[formateur.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                    {{ form_widget(forms_contrat_formateur[formateur.id].contratFile) }}
                                                    {% if forms_contrat_formateur[formateur.id].submit is defined %}
                                                        {{ form_widget(forms_contrat_formateur[formateur.id].submit) }}
                                                    {% endif %}
                                                    {{ form_widget(forms_contrat_formateur[formateur.id]._token) }}
                                                    {{ form_end(forms_contrat_formateur[formateur.id], {'render_rest': false}) }}
                                                </div>
                                                {% if fichiersFormateur[idFormateur].contrat is defined %}
                                                    {% if fichiersFormateur[idFormateur].contrat is not null %}
                                                        ({{ "admin.formation.depot"|trans }} {{ fichiersFormateur[idFormateur].updatedAt | date ('d/m/Y') }})
                                                    {% endif %}
                                                {% endif %}
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </li>
                            {% endif %}
                            {# A SUPPRIMER LORSQUE TOUS LES CONTRATS ET FACTURE FORMATEUR AURONT ETE RECUPERES ET REDEPOSES #}
                            {% if formation.contratFormateur is not null %}
                                <li>
                                    [ <a target="_blank" href="{{ url('admin_formation_file', {'id' : formation.id, 'fileField' : 'contratFormateurFile'}) }}">Récupérer {{ "admin.formation.consultContratFormateur" | trans }} {{ firstFormerName }} ({{ "admin.formation.depot"|trans }} {{ uploadFileDates.contratFormateur }})</a> ]
                                </li>
                            {% endif %}
                            {##}
                        {% endif %}
                        {% if is_granted("ROLE_WEBMASTER") and formation.programme.isClasseVirtuelle %}
                            <li>
                                {{ "admin.formation.importZoomFile"|trans }}
                                <div id="zoomFile" class="downloadFile downloadFile-depot">
                                    <i class="fa fa-download btn-download-file" id="{{ form_zoom.vars.name }}"></i>
                                    {% form_theme form_zoom 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(form_zoom,{'attr': {'class': 'formDownloadFile'}}) }}
                                    {{ form_widget(form_zoom.zoomFile) }}
                                    {{ form_widget(form_zoom._token) }}
                                    {{ form_end(form_zoom, {'render_rest': false}) }}
                                </div>
                            </li>
                        {% endif %}
                    </ul>
                </div>
                <div class="col-lg-3">
                    <b>Paiements</b><br>
                    <ul class="uploadList">
                        {% if is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA') %}
                            {% if formation.formateurs.count %}
                                <li>
                                    Formateurs :
                                    <ul>
                                        {% for formateur in formation.formateurs %}
                                            {% set idFormateur = formateur.id %}
                                            {% if app.user.isWebmasterCompta or is_granted('ROLE_SUPER_ADMIN')%}
                                                <li style="display:flex">
                                                    {% if formateur.honorary != 0 %}
                                                        <div style="width:50%;">{{ formateur.person.firstname }} {{ formateur.person.lastname }}</div>
                                                        <div style="cursor:pointer;" data-url="{{ url('paidFormateur', {'id': formateur.id }) }}" class="paid {% if formateur.isPaid %}isPaid{% endif %}" data-id="{{formateur.id}}-formateur-paid" class="facture" title="Facturer">
                                                            {% if formateur.isPaid  == true %}
                                                                <img id="{{formateur.id}}-formateur-paid" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                            {% else %}
                                                                <img id="{{formateur.id}}-formateur-paid" src="{{ asset('img/unchecked.png') }}" width="16" height="16" alt="Non" />
                                                            {% endif %}
                                                        </div>
                                                        {% if formateur.isPaid  == true %}
                                                            <div id="{{formateur.id}}-formateur-paid-date" style="margin-left:0.5em">{{ formateur.paidDate|date("d/m/Y") }}</div>
                                                        {% else %}
                                                            <div id="{{formateur.id}}-formateur-paid-date" style="margin-left:0.5em"></div>
                                                        {% endif %}
                                                    {% endif %}
                                                </li>
                                            {% else %}
                                                <li style="display:flex">
                                                    {% if formateur.honorary != 0 %}
                                                        <div style="width:50%;">{{ formateur.person.firstname }} {{ formateur.person.lastname }}</div>
                                                        <div style="cursor:not-allowed;" title="Facturer">
                                                            {% if formateur.isPaid  == true %}
                                                                <img src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                            {% endif %}
                                                        </div>
                                                        {% if formateur.isPaid  == true %}
                                                            <div style="margin-left:0.5em">{{ formateur.paidDate|date("d/m/Y") }}</div>
                                                        {% endif %}
                                                    {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </li>
                                <br>
                            {% endif %}
                            {% if formation.coordinators.count %}
                                <li>
                                    Coordinateurs régionaux :
                                    <ul>
                                        {% for coordinator in formation.coordinators %}
                                            {% if app.user.isWebmasterCompta or is_granted('ROLE_SUPER_ADMIN')%}
                                                {% if pluriAnuelle %}
                                                    <li style="display:flex">
                                                        {% if coordinator.person.crIsSalarie == false %}
                                                            <div style="width:50%;">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }} {{ formation.openingDate|date("Y")}} </div>
                                                            <div style="cursor:pointer;" data-url="{{ url('paidCoordinator', {'id': coordinator.id, 'midCourse' : 1 }) }}" class="paid {% if coordinator.isPaidMidCourse %}isPaid{% endif %}" data-paid="{% if coordinator.isPaidMidCourse %}oui{% else %}non{% endif %}" data-id="{{coordinator.id}}-coordinator-paidMidCourse" class="facture" title="Facturer">
                                                                {% if coordinator.isPaidMidCourse  == true %}
                                                                    <img id="{{coordinator.id}}-coordinator-paidMidCourse" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                                {% else %}
                                                                    <img id="{{coordinator.id}}-coordinator-paidMidCourse" src="{{ asset('img/unchecked.png') }}" width="16" height="16" alt="Non" />
                                                                {% endif %}
                                                            </div>
                                                            {% if coordinator.isPaidMidCourse  == true %}
                                                                <div id="{{coordinator.id}}-coordinator-paidMidCourse-date" style="margin-left:0.5em">{{ coordinator.paidMidCourseDate|date("d/m/Y") }}</div>
                                                            {% else %}
                                                                <div id="{{coordinator.id}}-coordinator-paidMidCourse-date" style="margin-left:0.5em"></div>
                                                            {% endif %}
                                                        {% endif %}
                                                    </li>
                                                {% endif %}
                                                <li style="display:flex">
                                                    {% if coordinator.person.crIsSalarie == false %}
                                                        <div style="width:50%;">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }} {% if pluriAnuelle %} {{ formation.closingDate|date("Y")}} {% endif %}</div>
                                                        <div style="cursor:pointer;" data-url="{{ url('paidCoordinator', {'id': coordinator.id, 'midCourse' : 0 }) }}" class="paid {% if coordinator.isPaid %}isPaid{% endif %}" data-paid="{% if coordinator.isPaid %}oui{% else %}non{% endif %}" data-id="{{coordinator.id}}-coordinator-paid" class="facture" title="Facturer">
                                                            {% if coordinator.isPaid  == true %}
                                                                <img id="{{coordinator.id}}-coordinator-paid" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                            {% else %}
                                                                <img id="{{coordinator.id}}-coordinator-paid" src="{{ asset('img/unchecked.png') }}" width="16" height="16" alt="Non" />
                                                            {% endif %}
                                                        </div>
                                                        {% if coordinator.isPaid  == true %}
                                                            <div id="{{coordinator.id}}-coordinator-paid-date" style="margin-left:0.5em">{{ coordinator.paidDate|date("d/m/Y") }}</div>
                                                        {% else %}
                                                            <div id="{{coordinator.id}}-coordinator-paid-date" style="margin-left:0.5em"></div>
                                                        {% endif %}
                                                    {% endif %}
                                                </li>
                                            {% else %}
                                                {% if pluriAnuelle %}
                                                    <li style="display:flex">
                                                        {% if coordinator.person.crIsSalarie == false %}
                                                            <div style="width:50%;">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }} {{ formation.openingDate|date("Y") }}</div>
                                                            <div style="cursor:not-allowed;" title="Facturer">
                                                                {% if coordinator.isPaidMidCourse  == true %}
                                                                    <img src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                                {% endif %}
                                                            </div>
                                                            {% if coordinator.isPaidMidCourse  == true %}
                                                                <div style="margin-left:0.5em">{{ coordinator.paidMidCourseDate|date("d/m/Y") }}</div>
                                                            {% endif %}
                                                        {% endif %}
                                                    </li>
                                                {% endif %}
                                                <li style="display:flex">
                                                    {% if coordinator.person.crIsSalarie == false %}
                                                        <div style="width:50%;">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }} {% if pluriAnuelle %} {{ formation.closingDate|date("Y")}} {% endif %}</div>
                                                        <div style="cursor:not-allowed;" title="Facturer">
                                                            {% if coordinator.isPaid  == true %}
                                                                <img src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                            {% endif %}
                                                        </div>
                                                        {% if coordinator.isPaid  == true %}
                                                            <div style="margin-left:0.5em">{{ coordinator.paidDate|date("d/m/Y") }}</div>
                                                        {% endif %}
                                                    {% endif %}
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </li>
                            {% endif %}
                        {% elseif app.user.isCoordinator %}
                            {% if formation.formateurs.count %}
                                <li>
                                    Formateurs :
                                    <ul>
                                        {% for formateur in formation.formateurs %}
                                            {% if formateur.honorary != 0 %}
                                                    <li style="display:flex">
                                                        <div style="width:50%">{{ formateur.person.firstname }} {{ formateur.person.lastname }}</div>
                                                        <div>
                                                            {% if formateur.isPaid  == true %}
                                                                <img src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                            {% endif %}
                                                        </div>
                                                        <div>
                                                            {% if formateur.isPaid  == true %}
                                                                <div style="margin-left:0.5em">{{ formateur.paidDate|date("d/m/Y") }}</div>
                                                            {% endif %}
                                                        </div>
                                                    </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </li>
                                <br>
                            {% endif %}
                            {% if formation.coordinators.count and not app.user.crIsSalarie %}
                                <li>
                                    Coordinateurs régionaux :
                                    <ul>
                                        {% for coordinator in formation.coordinators %}
                                            {% if coordinator.person == app.user %}
                                                {% if coordinator.person.crIsSalarie == false %}
                                                        <li style="display:flex">
                                                            <div style="width:50%">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }}</div>
                                                            <div>
                                                                {% if coordinator.isPaid  == true %}
                                                                    <img src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                                                {% endif %}
                                                            </div>
                                                            <div>
                                                                {% if coordinator.isPaid  == true %}
                                                                    <div id="{{coordinator.id}}-coordinator-paid-date" style="margin-left:0.5em">{{ coordinator.paidDate|date("d/m/Y") }}</div>
                                                                {% endif %}
                                                            </div>
                                                        </li>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                </li>
                            {% endif %}
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="nav-tabs-custom">
    <ul class="nav nav-tabs">
        {% for financeSousMode in formation.financeSousModes %}
            <li class="{% if loop.first %}active{% endif %}{% if financeSousMode.isActalians %} actalians-tab{% endif %}" data-toggle="tooltip-tab" title="{{ financeSousMode.identifiant ? financeSousMode.identifiant : financeSousMode.name }}" ><a href="#tab_{{ loop.index }}" data-toggle="tab" aria-expanded="{% if loop.first %}true{% else %}false{% endif %}">{{ financeSousMode.identifiant ? financeSousMode.identifiant : financeSousMode.name }}</a></li>
        {% endfor %}
    </ul>
    <div class="tab-content">
        {% for financeSousMode in formation.financeSousModes %}
            <div class="tab-pane {% if loop.first %}active{% endif %}{% if financeSousMode.isActalians %} actalians-tab{% endif %}" id="tab_{{ loop.index }}">
                {% if is_granted('ROLE_WEBMASTER_COMPTA') or is_granted('ROLE_WEBMASTER') %}
                    {% if app.user.isWebmasterCompta or is_granted('ROLE_SUPER_ADMIN')%}
                        <div style="display:flex;">
                            <div><b> Facturation auprès de {{ financeSousMode.name }} {% if pluriAnuelle %} pour {{ formation.openingDate|date('Y') }} {% endif %}</b></div>
                            <div style="margin-left:1em;cursor:pointer;" id="{{financeSousMode.id}}-financesousmode-facture-button" data-url="{{ url('factureFinanceSousMode', {'formation': formation.id, 'financeSousMode': financeSousMode.id }) }}" class="facture {% if formation.isFacturedFinanceSousMode(financeSousMode.id) %}isPaid{% endif%}" data-id="{{financeSousMode.id}}-financesousmode-facture" data-facture="{% if formation.isFacturedFinanceSousMode(financeSousMode.id) %}oui{% else %}non{% endif %}">
                                {% if formation.isFacturedFinanceSousMode(financeSousMode.id) %}
                                    <img id="{{financeSousMode.id}}-financesousmode-facture" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                {% else %}
                                    <img id="{{financeSousMode.id}}-financesousmode-facture" src="{{ asset('img/unchecked.png') }}" width="16" height="16" alt="Non" />
                                {% endif %}
                            </div>
                            {% if formation.isFacturedFinanceSousMode(financeSousMode.id)  == true %}
                                <div id="{{financeSousMode.id}}-financesousmode-facture-date" style="margin-left:0.5em">{{ formation.getFacturedFinanceSousModeDate(financeSousMode.id) }}</div>
                            {% else %}
                                <div id="{{financeSousMode.id}}-financesousmode-facture-date" style="margin-left:0.5em"></div>
                            {% endif %}
                            {% if pluriAnuelle %}
                                {% set fsmIdN1 = financeSousMode.id ~ "-n1" %}
                                <div style="margin-left:5em"><b> Facturation auprès de {{ financeSousMode.name }} {% if pluriAnuelle %} pour {{ formation.openingDate|date('Y') + 1}} {% endif %}</b></div>
                                <div style="margin-left:1em;cursor:pointer;" id="{{financeSousMode.id}}-n1-financesousmode-facture-button" data-url="{{ url('factureFinanceSousMode', {'formation': formation.id, 'financeSousMode': financeSousMode.id, 'n1' : true }) }}" class="facture {% if formation.isFacturedFinanceSousMode(financeSousMode.id, true) %}isPaid{% endif%}" data-id="{{financeSousMode.id}}-n1-financesousmode-facture" data-facture="{% if formation.isFacturedFinanceSousMode(financeSousMode.id, true) %}oui{% else %}non{% endif %}">
                                    {% if formation.isFacturedFinanceSousMode(financeSousMode.id, true) %}
                                        <img id="{{financeSousMode.id}}-n1-financesousmode-facture" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                    {% else %}
                                        <img id="{{financeSousMode.id}}-n1-financesousmode-facture" src="{{ asset('img/unchecked.png') }}" width="16" height="16" alt="Non" />
                                    {% endif %}
                                </div>
                                {% if formation.isFacturedFinanceSousMode(financeSousMode.id, true)  == true %}
                                    <div id="{{financeSousMode.id}}-n1-financesousmode-facture-date" style="margin-left:0.5em">{{ formation.getFacturedFinanceSousModeDate(financeSousMode.id, true) }}</div>
                                {% else %}
                                    <div id="{{financeSousMode.id}}-n1-financesousmode-facture-date" style="margin-left:0.5em"></div>
                                {% endif %}
                            {% endif %}
                        </div>
                        <br>
                    {% else %}
                        {% if formation.isFacturedFinanceSousMode(financeSousMode.id) %}
                            <div style="display:flex;">
                                <div><b> Facturation auprès de {{ financeSousMode.name }} </b></div>
                                <div style="margin-left:1em;cursor:not-allowed;">
                                    <img id="{{financeSousMode.id}}-financesousmode-facture" src="{{ asset('img/checked.png') }}" width="16" height="16" alt="Oui" />
                                </div>
                                <div style="margin-left:0.5em">{{ formation.getFacturedFinanceSousModeDate(financeSousMode.id) }}</div>
                            </div><br>
                        {% endif %}
                    {% endif %}
                {% endif %}
                <div class="row">
                    <div class="col-lg-4">
                        <b>Documents de préparation de la formation</b>
                        <ul>
                            {% if not formation.isSedd %}
                                {% if not formation.isvfc() and not formation.isTCS() %}
                                <li>
                                    <a href="{{ url('pdf_emargement_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">
                                        {{ "admin.formation.generer"|trans }} {{ "admin.formation.emarg"|trans }}
                                    </a>
                                </li>
                                {% else %}
                                    <li>
                                        <a href="{{ url('pdf_emargement_force_unity_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, 'unity': 2 }) }}" target="_blank">
                                            {{ "admin.formation.generer"|trans }} {{ "admin.formation.emarg"|trans }} de l'unité 2 {% if formation.isvfc() %}VC{% elseif formation.isTCS() %}TCS{% endif %}
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ url('pdf_emargement_force_unity_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, 'unity': 3 }) }}" target="_blank">
                                            {{ "admin.formation.generer"|trans }} {{ "admin.formation.emarg"|trans }} de l'unité 3 FC
                                        </a>
                                    </li>
                                {% endif %}
                                {% if financeSousMode.isActalians %}
                                    <li><a class="link-actalians" href="{{ url('pdf_convention_pharmacie_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.conventionPharmacie"|trans }}</a></li>
                                {% elseif is_granted('ROLE_WEBMASTER') %}
                                    <li><a href="{{ url('pdf_convention_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.convention"|trans }}</a></li>
                                {% endif %}
                            {% endif %}
                        </ul>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <b>Documents de facturation</b>
                            <ul>
                                <li><a href="{{ url('pdf_invoice_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.factureClient"|trans }} {% if pluriAnuelle %} de {{ (formation.openingDate|date('Y'))}} {% endif %}</a></li>
                                {% if pluriAnuelle %}<li><a href="{{ url('pdf_invoice_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, 'n1' : true }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.factureClient"|trans }} de {{ (formation.openingDate|date('Y') + 1)}}</a></li>{% endif %}
                                <li><a href="{{ url('pdf_invoice_proforma_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.factureClientProforma"|trans }} {% if pluriAnuelle %} de {{ (formation.openingDate|date('Y'))}} {% endif %}</a></li>
                                {% if pluriAnuelle %}<li><a href="{{ url('pdf_invoice_proforma_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, 'n1' : true }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.factureClientProforma"|trans }} de {{ (formation.openingDate|date('Y') + 1)}}</a></li>{% endif %}
                                {% if not formation.isSedd %}
                                    {% if formation.programme.unities|length and not financeSousMode.isActalians %}
                                        {% for unity in formation.programme.unities %}
                                            {% if unity.isElearning %}
                                                <li><a href="{{ url('pdf_traceability_document_unity_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'unity' : loop.index, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.documentTracabiliteUnity"|trans }} {% if loop.index == 3 and formation.isFourUnity %}4{% else %}{{ loop.index }}{% endif %}</a></li>
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <li><a href="{{ url('pdf_traceability_document_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.documentTracabilite"|trans }}</a></li>
                                        <li><a href="{{ url('pdf_traceability_document_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, "empty": true }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.documentTracabiliteEmpty"|trans }}</a></li>
                                    {% endif %}
                                    {% if formation.programme.isClasseVirtuelle %}
                                        <li><a href="{{ url('pdf_traceability_document_pdf', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'token': formation.token, "zoom": true }) }}" target="_blank">{{ "admin.formation.generer"|trans }} {{ "admin.formation.documentTracabiliteZoom"|trans }}</a></li>
                                    {% endif %}

                                    {% if not financeSousMode.isHorsDpc %}
                                        <li>
                                            <a class="generate-certificate-participation" id="generate-certificate-participation-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('participation', financeSousMode) and not formation.generateFileIsFinished('participation', financeSousMode)) or formation.hasGeneratedFileError('participation', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.attest"|trans }} {% if formation.hasGeneratedFile('participation', financeSousMode) and formation.generateFileIsFinished('participation', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                            <ul>
                                                <li id="generate-certificate-participation-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('participation', financeSousMode) and formation.generateFileIsFinished('participation', financeSousMode)) or (not formation.hasGeneratedFile('participation', financeSousMode))) or formation.hasGeneratedFileError('participation', financeSousMode) %}class="hidden"{% endif %}>
                                                    <span>{{ "admin.formation.generation"|trans }}</span>
                                                </li>
                                                {% if formation.hasGeneratedFile('participation', financeSousMode) %}
                                                    {% if formation.generateFileIsFinished('participation', financeSousMode) %}
                                                        <li id="generate-certificate-participation-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                            <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.attest"|trans }}</a>
                                                        </li>
                                                    {% endif %}
                                                {% endif %}
                                                {% if formation.hasGeneratedFileError('participation', financeSousMode) %}
                                                    <li id="generate-certificate-participation-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </li>
                                    {% else %}
                                        <li>
                                            <a class="generate-certificate-participation-horary" id="generate-certificate-participation-horary-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('participation_horary', financeSousMode) and not formation.generateFileIsFinished('participation_horary', financeSousMode)) or formation.hasGeneratedFileError('participation_horary', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation_horary', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.attest_horary"|trans }} {% if formation.hasGeneratedFile('participation_horary', financeSousMode) and formation.generateFileIsFinished('participation_horary', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                            <ul>
                                                <li id="generate-certificate-participation-horary-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('participation_horary', financeSousMode) and formation.generateFileIsFinished('participation_horary', financeSousMode)) or (not formation.hasGeneratedFile('participation_horary', financeSousMode))) or formation.hasGeneratedFileError('participation_horary', financeSousMode) %}class="hidden"{% endif %}>
                                                    <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('participation_horary', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('participation_horary', financeSousMode) %}
                                                    <li id="generate-certificate-participation-horary-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation_horary','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.attest_horary"|trans }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('participation_horary', financeSousMode) %}
                                                <li id="generate-certificate-participation-horary-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                            </ul>
                                        </li>
                                    {% endif %}
                                {% endif %}
                                {% if financeSousMode.isActalians %}
                                   <li>
                                        <a class="generate-realisation-docs" id="generate-realisation-docs-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('realisation', financeSousMode) and not formation.generateFileIsFinished('realisation', financeSousMode)) or formation.hasGeneratedFileError('realisation', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'realisation', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.realDoc"|trans }} {% if formation.hasGeneratedFile('realisation', financeSousMode) and formation.generateFileIsFinished('realisation', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-realisation-docs-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('realisation', financeSousMode) and formation.generateFileIsFinished('realisation', financeSousMode)) or (not formation.hasGeneratedFile('realisation', financeSousMode))) or formation.hasGeneratedFileError('realisation', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('realisation', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('realisation', financeSousMode) %}
                                                    <li id="generate-realisation-docs-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'realisation','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.realDoc"|trans }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('realisation', financeSousMode) %}
                                                <li id="generate-realisation-docs-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                {% endif %}
                                {% if financeSousMode.isFif %}
                                   <li>
                                        <a class="generate-attestation-presence" id="generate-attestation-presence-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('presence', financeSousMode) and not formation.generateFileIsFinished('presence', financeSousMode)) or formation.hasGeneratedFileError('presence', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'presence', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.presence"|trans }} {% if formation.hasGeneratedFile('presence', financeSousMode) and formation.generateFileIsFinished('presence', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-attestation-presence-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('presence', financeSousMode) and formation.generateFileIsFinished('presence', financeSousMode)) or (not formation.hasGeneratedFile('presence', financeSousMode))) or formation.hasGeneratedFileError('presence', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('presence', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('presence', financeSousMode) %}
                                                    <li id="generate-attestation-presence-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'presence','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.presence"|trans }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('presence', financeSousMode) %}
                                                <li id="generate-attestation-presence-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                            {% endif %}
                            </ul>
                        {% endif %}
                    </div>
                    <div class="col-lg-4">
                        <b>Divers</b>
                        <ul>
                            {% if not formation.isSedd %}
                                {% if is_granted('ROLE_WEBMASTER') %}
                                    {% if not financeSousMode.isHorsDpc %}
                                        <li>
                                            <a class="generate-certificate-participation-horary" id="generate-certificate-participation-horary-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('participation_horary', financeSousMode) and not formation.generateFileIsFinished('participation_horary', financeSousMode)) or formation.hasGeneratedFileError('participation_horary', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation_horary', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} {{ "admin.formation.attest_horary"|trans }} {% if formation.hasGeneratedFile('participation_horary', financeSousMode) and formation.generateFileIsFinished('participation_horary', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                                <ul>
                                                    <li id="generate-certificate-participation-horary-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('participation_horary', financeSousMode) and formation.generateFileIsFinished('participation_horary', financeSousMode)) or (not formation.hasGeneratedFile('participation_horary', financeSousMode))) or formation.hasGeneratedFileError('participation_horary', financeSousMode) %}class="hidden"{% endif %}>
                                                        <span>{{ "admin.formation.generation"|trans }}</span>
                                                </li>
                                                {% if formation.hasGeneratedFile('participation_horary', financeSousMode) %}
                                                    {% if formation.generateFileIsFinished('participation_horary', financeSousMode) %}
                                                        <li id="generate-certificate-participation-horary-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                            <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'participation_horary','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ "admin.formation.attest_horary"|trans }}</a>
                                                        </li>
                                                    {% endif %}
                                                {% endif %}
                                                {% if formation.hasGeneratedFileError('participation_horary', financeSousMode) %}
                                                    <li id="generate-certificate-participation-horary-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                    </li>
                                                {% endif %}
                                                </ul>
                                        </li>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                            {% if formation.hasLinkedForm %}
                                {% if not formation.isFormPresentielle and is_granted('ROLE_COORDINATOR')%}
                                    <li>
                                        <a class="generate-answers-audit1" id="generate-answers-audit1-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('audit1', financeSousMode) and not formation.generateFileIsFinished('audit1', financeSousMode)) or formation.hasGeneratedFileError('audit1', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'audit1', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePre) | trans }} {{ preSufix }} {% if formation.hasGeneratedFile('audit1', financeSousMode) and formation.generateFileIsFinished('audit1', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-answers-audit1-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('audit1', financeSousMode) and formation.generateFileIsFinished('audit1', financeSousMode)) or (not formation.hasGeneratedFile('audit1', financeSousMode))) or formation.hasGeneratedFileError('audit1', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('audit1', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('audit1', financeSousMode) %}
                                                    <li id="generate-answers-audit1-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id,  'type': 'audit1','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePre) | trans }} {{ preSufix }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('audit1', financeSousMode) %}
                                                <li id="generate-answers-audit1-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                    {% if formation.hasFormPost() %}
                                        <li>
                                            <a class="generate-answers-audit2" id="generate-answers-audit2-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('audit2', financeSousMode) and not formation.generateFileIsFinished('audit2', financeSousMode)) or formation.hasGeneratedFileError('audit2', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'audit2', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePost) | trans }} post-session {% if formation.hasGeneratedFile('audit2', financeSousMode) and formation.generateFileIsFinished('audit2', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                            <ul>
                                                <li id="generate-answers-audit2-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if (formation.hasGeneratedFile('audit2', financeSousMode) and formation.generateFileIsFinished('audit2', financeSousMode)) or (not formation.hasGeneratedFile('audit2', financeSousMode)) or formation.hasGeneratedFileError('audit2', financeSousMode) %}class="hidden"{% endif %}>
                                                    <span>{{ "admin.formation.generation"|trans }}</span>
                                                </li>
                                                {% if formation.hasGeneratedFile('audit2', financeSousMode) %}
                                                    {% if formation.generateFileIsFinished('audit2', financeSousMode) %}
                                                        <li id="generate-answers-audit2-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                            <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'audit2','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePost) | trans }} post-session</a>
                                                        </li>
                                                    {% endif %}
                                                {% endif %}
                                                {% if formation.hasGeneratedFileError('audit2', financeSousMode) %}
                                                    <li id="generate-answers-audit2-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </li>
                                    {% endif %}
                                {% elseif formation.isFormPresentielle and is_granted('ROLE_WEBMASTER') %}

                                    <li>
                                        <a class="generate-answers-survey1" id="generate-answers-survey1-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('survey1', financeSousMode) and not formation.generateFileIsFinished('survey1', financeSousMode)) or formation.hasGeneratedFileError('survey1', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'survey1', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePre) | trans }} {{ preSufix }} {% if formation.hasGeneratedFile('survey1', financeSousMode) and formation.generateFileIsFinished('survey1', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-answers-survey1-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('survey1', financeSousMode) and formation.generateFileIsFinished('survey1', financeSousMode)) or (not formation.hasGeneratedFile('survey1', financeSousMode))) or formation.hasGeneratedFileError('survey1', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('survey1', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('survey1', financeSousMode) %}
                                                    <li id="generate-answers-survey1-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'survey1','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePre) | trans }} {{ preSufix }}</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('survey1', financeSousMode) %}
                                                <li id="generate-answers-survey1-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                    <li>
                                        <a class="generate-answers-survey2" id="generate-answers-survey2-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('survey2', financeSousMode) and not formation.generateFileIsFinished('survey2', financeSousMode)) or formation.hasGeneratedFileError('survey2', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'survey2', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePost) | trans }} post-session {% if formation.hasGeneratedFile('survey2', financeSousMode) and formation.generateFileIsFinished('survey2', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-answers-survey2-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('survey2', financeSousMode) and formation.generateFileIsFinished('survey2', financeSousMode)) or (not formation.hasGeneratedFile('survey2', financeSousMode))) or formation.hasGeneratedFileError('survey2', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('survey2', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('survey2', financeSousMode) %}
                                                    <li id="generate-answers-survey2-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'survey2','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} toutes les réponses {{ ("admin.formation.generateAnswers."  ~ formation.formTypePost) | trans }} post-session</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('survey2', financeSousMode) %}
                                                <li id="generate-answers-survey2-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                    <li>
                                        {{ "admin.formation.generer"|trans }}
                                        {% if formation.isVignette() %}
                                            {{ "admin.formation.prescoringFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        {% else %}
                                            {{ "admin.formation.prerestitutionFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        {% endif %}
                                        <ul>
                                            {% for formateur in formation.formateurs %}
                                                <li><a target="_blank" href="{{ url('pdf_restitution_audit_groupe_pdf', { id: formation.id, former: formateur.id, financeSousMode: financeSousMode.id, token: formation.token }) }}">{{ formateur.person.fullname }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                    {% if formation.hasFormPost() %}
                                    <li>
                                        {{ "admin.formation.generer"|trans }}
                                        {% if formation.isVignette() %}
                                            {{ "admin.formation.scoringFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        {% else %}
                                            {{ "admin.formation.restitutionFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        {% endif %}
                                        <ul>
                                            {% for formateur in formation.formateurs %}
                                                <li><a target="_blank" href="{{ url('pdf_restitution_audit_groupe_2_pdf', { id: formation.id, former: formateur.id, financeSousMode: financeSousMode.id, token: formation.token }) }}">{{ formateur.person.fullname }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                    {% endif %}
                                {% endif %}
                                {% if is_granted('ROLE_WEBMASTER') and (formation.isAudit or formation.isPresentielle) %}
                                    <li>
                                        <a class="generate-answers-fusion" id="generate-answers-fusion-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('fusion', financeSousMode) and not formation.generateFileIsFinished('fusion', financeSousMode)) or formation.hasGeneratedFileError('fusion', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'fusion', 'token' : formation.token}) }}"{% endif %}>{{ "admin.formation.generer"|trans }} le document fusionné {% if formation.hasGeneratedFile('fusion', financeSousMode) and formation.generateFileIsFinished('fusion', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}</a>
                                        <ul>
                                            <li id="generate-answers-fusion-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('fusion', financeSousMode) and formation.generateFileIsFinished('fusion', financeSousMode)) or (not formation.hasGeneratedFile('fusion', financeSousMode))) or formation.hasGeneratedFileError('fusion', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('fusion', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('fusion', financeSousMode) %}
                                                    <li id="generate-answers-fusion-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'fusion','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} le document fusionné</a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('fusion', financeSousMode) %}
                                                <li id="generate-answers-fusion-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error generate-answers-fusion-retry" id="generate-answers-fusion-retry-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                {% endif %}
                                {# {% if financeSousMode.isActalians %}
                                    <li>
                                        {{ "admin.formation.generer"|trans }} {{ "admin.formation.prerestitutionFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        <ul>
                                            {% for formateur in formation.formateurs %}
                                                <li><a target="_blank" href="{{ url('pdf_restitution_audit_groupe_pdf', { id: formation.id, former: formateur.id, financeSousMode: financeSousMode.id, token: formation.token }) }}">{{ formateur.person.fullname }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                    <li>
                                        {{ "admin.formation.generer"|trans }} {{ "admin.formation.restitutionFormateur" | trans({"%type%": "Questionnaire"}) }}
                                        <ul>
                                            {% for formateur in formation.formateurs %}
                                                <li><a target="_blank" href="{{ url('pdf_restitution_audit_groupe_2_pdf', { id: formation.id, former: formateur.id, financeSousMode: financeSousMode.id, token: formation.token }) }}">{{ formateur.person.fullname }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </li>
                                {% endif %} #}
                                {% if formation.isAudit and is_granted('ROLE_COORDINATOR') %}
                                    <li>
                                        <a class="generate-prerestitution-audit" id="generate-prerestitution-audit-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('prerestitution_audit', financeSousMode) and not formation.generateFileIsFinished('prerestitution_audit', financeSousMode)) or formation.hasGeneratedFileError('prerestitution_audit', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'prerestitution_audit', 'token' : formation.token}) }}"{% endif %}>
                                            {{ "admin.formation.generer"|trans }}
                                            {% if formation.isVignette() %}{{ "admin.formation.files.tousLesPreScoringIndividuels"|trans }}{% else %}{{ "admin.formation.files.toutesLesPreRestitutionsIndividuelles"|trans }}{% endif %}
                                            {% if formation.hasGeneratedFile('prerestitution_audit', financeSousMode) and formation.generateFileIsFinished('prerestitution_audit', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}
                                        </a>
                                        <ul>
                                            <li id="generate-prerestitution-audit-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('prerestitution_audit', financeSousMode) and formation.generateFileIsFinished('prerestitution_audit', financeSousMode)) or (not formation.hasGeneratedFile('prerestitution_audit', financeSousMode))) or formation.hasGeneratedFileError('prerestitution_audit', financeSousMode) %}class="hidden"{% endif %}>
                                                <span>{{ "admin.formation.generation"|trans }}</span>
                                            </li>
                                            {% if formation.hasGeneratedFile('prerestitution_audit', financeSousMode) %}
                                                {% if formation.generateFileIsFinished('prerestitution_audit', financeSousMode) %}
                                                    <li id="generate-prerestitution-audit-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'prerestitution_audit','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }}
                                                            {% if formation.isVignette() %}
                                                                {{ "admin.formation.files.tousLesPreScoringIndividuels"|trans }}
                                                            {% else %}
                                                                {{ "admin.formation.files.toutesLesPreRestitutionsIndividuelles"|trans }}
                                                            {% endif %}
                                                        </a>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            {% if formation.hasGeneratedFileError('prerestitution_audit', financeSousMode) %}
                                                <li id="generate-prerestitution-audit-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                    <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </li>
                                    {% if formation.hasFormPost() %}
                                        <li>
                                            <a class="generate-restitution-audit" id="generate-restitution-audit-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" data-started="false" {% if not (formation.hasGeneratedFile('restitution_audit', financeSousMode) and not formation.generateFileIsFinished('restitution_audit', financeSousMode)) or formation.hasGeneratedFileError('restitution_audit', financeSousMode) %}href="#" data-href="{{ url('pdf_formation_generate_file', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'restitution_audit', 'token' : formation.token}) }}"{% endif %}>
                                                {{ "admin.formation.generer"|trans }}
                                                {% if formation.isVignette() %}{{ "admin.formation.files.tousLesPostScoringIndividuels"|trans }}{% else %}{{ "admin.formation.files.toutesLesRestitutionsIndividuelles"|trans }}{% endif %}
                                                {% if formation.hasGeneratedFile('restitution_audit', financeSousMode) and formation.generateFileIsFinished('restitution_audit', financeSousMode) %}{{ "admin.formation.overwrite" | trans }}{% endif %}
                                            </a>
                                            <ul>
                                                <li id="generate-restitution-audit-loading-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}" {% if ((formation.hasGeneratedFile('restitution_audit', financeSousMode) and formation.generateFileIsFinished('restitution_audit', financeSousMode)) or (not formation.hasGeneratedFile('restitution_audit', financeSousMode))) or formation.hasGeneratedFileError('restitution_audit', financeSousMode) %}class="hidden"{% endif %}>
                                                    <span>{{ "admin.formation.generation"|trans }}</span>
                                                </li>
                                                {% if formation.hasGeneratedFile('restitution_audit', financeSousMode) %}
                                                    {% if formation.generateFileIsFinished('restitution_audit', financeSousMode) %}
                                                        <li id="generate-restitution-audit-download-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                            <a href="{{ url('pdf_formation_generate_file_get', {'id': formation.id, 'financeSousMode': financeSousMode.id, 'type': 'restitution_audit','token': formation.token }) }}" target="_blank">{{ "admin.formation.telecharger"|trans }}
                                                                {% if formation.isVignette() %}
                                                                    {{ "admin.formation.files.tousLesPostScoringIndividuels"|trans }}
                                                                {% else %}
                                                                    {{ "admin.formation.files.toutesLesRestitutionsIndividuelles"|trans }}
                                                                {% endif %}
                                                            </a>
                                                        </li>
                                                    {% endif %}
                                                {% endif %}
                                                {% if formation.hasGeneratedFileError('restitution_audit', financeSousMode) %}
                                                    <li id="generate-restitution-audit-error-{{ financeSousMode.id }}" data-id="{{ financeSousMode.id }}">
                                                        <span>{{ "admin.formation.generation_error"|trans }} <a href="#" class="generate-file-error">Veuillez réessayer.</a></span>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </li>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-lg-4">
                        <b>Documents importés</b>
                        <ul class="uploadList" data-formationID="{{ formation.id }}">
                            {% if not formation.isSedd %}
                                {% for key, fichierEmargements in fichiersEmargement[financeSousMode.id] %}
                                    <li>
                                        {% if fichierEmargements.emargement is defined and fichierEmargements.emargement is not null %}
                                            <a target="_blank" href="{{ url('admin_emargement_file', {'id' : fichierEmargements.id }) }}">
                                                {{ "admin.formation.consulter"|trans }} {{ "admin.formation.emarg" | trans }}
                                                {% if formation.isVfc() and key == 0 %}de l'unité 2 VC{% endif %}
                                                {% if formation.isTCS() and key == 0 %}de l'unité 2 TCS{% endif %}
                                                {% if key == 1 %}
                                                    {% if formation.isVfc() or formation.isTCS() %}
                                                        de l'unité 3 FC
                                                    {% endif %}
                                                {% endif %}
                                            </a>
                                        {% else %}
                                            {{ "admin.formation.deposer"|trans }} {{ "admin.formation.emarg"|trans }}
                                            {% if formation.isVfc() and key == 0 %}de l'unité 2 VC{% endif %}
                                            {% if formation.isTCS() and key == 0 %}de l'unité 2 TCS{% endif %}
                                            {% if key == 1 %}
                                                {% if formation.isVfc() or formation.isTCS() %}
                                                    de l'unité 3 FC
                                                {% endif %}
                                            {% endif %}
                                        {% endif %}
                                        <i class="fa fa-download btn-download-file" id="emargementFile_{{ financeSousMode.id }}_{{ key }}"></i>
                                        <div id="emargementFile" class="downloadFile downloadFile-depot downloadFileTopo">
                                            {% form_theme forms_emargement[financeSousMode.id][key] 'admin/form/no_errors.html.twig' %}
                                            {{ form_start(forms_emargement[financeSousMode.id][key],{'attr': {'class': 'formDownloadFile'}}) }}
                                            {{ form_widget(forms_emargement[financeSousMode.id][key].emargementFile) }}
                                            {% if forms_emargement[financeSousMode.id][key].submit is defined %}
                                                {{ form_widget(forms_emargement[financeSousMode.id][key].submit) }}
                                            {% endif %}
                                            {{ form_widget(forms_emargement[financeSousMode.id][key]._token) }}
                                            {{ form_end(forms_emargement[financeSousMode.id][key], {'render_rest': false}) }}
                                        </div>
                                        {% if fichierEmargements.emargement is defined %}
                                            {% if fichierEmargements.emargement is not null %}
                                                ({{ "admin.formation.depot"|trans }} {{ fichierEmargements.updatedAt | date ('d/m/Y') }})
                                            {% endif %}
                                        {% endif %}
                                    </li>
                                {% endfor %}
                            {% endif %}
                            <li>
                                {% if fichiersConvention[financeSousMode.id].convention is defined and fichiersConvention[financeSousMode.id].convention is not null %}
                                    <a target="_blank" href="{{ url('admin_convention_file', {'id' : fichiersConvention[financeSousMode.id].id }) }}">
                                        {{ "admin.formation.consulter"|trans }} {{ "admin.formation.conventionSigned" | trans }}
                                    </a>
                                {% else %}
                                    {{ "admin.formation.deposer"|trans }} {{ "admin.formation.conventionSigned"|trans }}
                                {% endif %}
                                <i class="fa fa-download btn-download-file" id="conventionFile_{{ financeSousMode.id }}"></i>
                                <div id="conventionFile" class="downloadFile downloadFile-depot downloadFileTopo">
                                    {% form_theme forms_convention[financeSousMode.id] 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(forms_convention[financeSousMode.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                    {{ form_widget(forms_convention[financeSousMode.id].conventionFile) }}
                                    {% if forms_convention[financeSousMode.id].submit is defined %}
                                        {{ form_widget(forms_convention[financeSousMode.id].submit) }}
                                    {% endif %}
                                    {{ form_widget(forms_convention[financeSousMode.id]._token) }}
                                    {{ form_end(forms_convention[financeSousMode.id], {'render_rest': false}) }}
                                </div>
                                {% if fichiersConvention[financeSousMode.id].convention is defined %}
                                    {% if fichiersConvention[financeSousMode.id].convention is not null %}
                                        ({{ "admin.formation.depot"|trans }} {{ fichiersConvention[financeSousMode.id].updatedAt | date ('d/m/Y') }})
                                    {% endif %}
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
