{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'admin_participant_create' %}
        {{ 'participant.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_participant_edit'%}
        {{ 'participant.titles.edit'|trans }}
    {% endif %}
{% endblock %}

{% block body %}
{% if is_granted('ROLE_WEBMASTER') %}
    <div class="box box-info">
        <div class="box-body">
            {% if not form.vars.valid %}
                <div class="callout callout-danger">
                    {% if form.vars.errors|length %}
                        {{ form_errors(form) }}
                    {% else %}
                        {{ 'participant.edit.error'|trans }}
                    {% endif %}
                </div>
            {% endif %}
            {{ form_start(form) }}
            {{ form_rest(form) }}
            <div class="form-group">
                <button type="submit" class="btn-eduprat btn">{{ "admin.global.save"|trans }}</button>
            </div>
            {{ form_end(form) }}
        </div>
    </div>
{% endif %}
{% if is_granted('ROLE_COORDINATOR') %}
    {% if formPass is defined %}
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{{ "participant.edit.password" | trans }}</h3>
            </div>
            <div class="box-body">
                {% if not formPass.vars.valid %}
                    <div class="callout callout-danger">
                        {% if formPass.vars.errors|length %}
                            {{ form_errors(formPass) }}
                        {% else %}
                            {{ 'participant.edit.error'|trans }}
                        {% endif %}
                    </div>
                {% endif %}
                {{ form_start(formPass) }}
                <div class="form-group {% if not formPass.plainPassword.first.vars.valid %}has-error{% endif %}">
                    {{ form_label(formPass.plainPassword.first) }}
                    {{ form_widget(formPass.plainPassword.first) }}
                    {% if formPass.plainPassword.first.vars.valid %}
                        <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
                    {% endif %}
                    {{ form_errors(formPass.plainPassword.first) }}
                </div>
                <div class="form-group">
                    {{ form_label(formPass.plainPassword.second) }}
                    {{ form_widget(formPass.plainPassword.second) }}
                    {{ form_errors(formPass.plainPassword.second) }}
                </div>
                {{ form_rest(formPass) }}
                <div class="form-group">
                    <button type="submit" class="btn-eduprat btn">{{ "admin.global.save_password"|trans }}</button>
                </div>
                {{ form_end(formPass) }}
            </div>
        </div>
    {% else %}
        <div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">{{ "participant.edit.password" | trans }}</h3>
            </div>
            <div class="box-body">
                <p class="text text-danger">
                    <i class="fa fa-warning"></i> {{ "participant.edit.warning"|trans }}
                </p>
            </div>
        </div>
    {% endif %}
{% endif %}

{% endblock %}
{% block javascripts %}
    <script>

    </script>
{% endblock %}
