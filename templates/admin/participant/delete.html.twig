{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'participant.delete.title'|trans }}{% endblock %}

{% block body %}
    <div class="box">
        <div class="box-header">
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    {{ form_start(form) }}
                    <p>{{ 'participant.delete.message'|trans({'%participant%' : participant.fullname}) }}</p>
                    <p>{{ "participant.delete.formation" | trans }}</p>
                    <ul>
                        {% for participation in participant.participations %}
                            <li>{{ participation.formation.programme.title }} - {{ participation.formation.programme.reference }}</li>
                        {% endfor %}
                    </ul>
                    <button type="submit" class="btn btn-danger">{{ 'admin.global.delete'|trans }}</button>
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}