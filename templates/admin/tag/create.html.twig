{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'alienor_tag_create' %}
        {{ 'admin.tag.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_tag_edit' %}
        {{ 'admin.tag.titles.edit'|trans }}
    {% endif %}
{% endblock %}
{% block body %}
<div class="box box-info">
    <div class="box-body">
        {{ form_start(form) }}
            {{ form_row(form.name) }}
        <button type="submit" class="btn btn-eduprat pull-right">{{ "admin.global.save"|trans }}</button>
        {{ form_widget(form._token) }}
        {{ form_end(form, {'render_rest': false}) }}
    </div>
</div>
{% endblock %}