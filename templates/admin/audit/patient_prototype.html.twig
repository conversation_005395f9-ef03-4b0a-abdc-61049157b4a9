<li class="panel" data-index="{% if loop is defined %}{{ loop.index }}{% else %}__pname__{% endif %}">
    <h4 class="box-title">
         {{ audit.isPredefinedType ? "Patient" : "Vignette clinique" }} # <span class="patient-index">{% if loop is defined %}{{ loop.index }}{% else %}__pname__{% endif %}</span>
    </h4>
    <div id="collapse-p{% if loop is defined %}{{ loop.index }}{% else %}__pname__{% endif %}">
        <div class="box-body">
            {% if loop is defined and descriptions[loop.index0] is defined %}
                {% set descriptionForm = descriptions[loop.index0].createView() %}
                {{ form_row(descriptionForm.description) }}

                <ul class="patients-images" data-prototype="{{ form_widget(descriptionForm.images.vars.prototype)|e('html_attr') }}">
                    {% for image in descriptionForm.images %}
                        <li>
                            <label>{{ image.vars.value.name }}</label>
                            <br>
                            <img src="{{ image.vars.value.relativeUrl }}" alt="">
                            {{ form_row(image) }}
                        </li>
                    {% endfor %}
                </ul>

                {{ form_row(descriptionForm.patient) }}
            {% else %}
                {{ ((form_row(form.patientDescriptions.vars.prototype.description))|replace({'__name__': '__pname__'}))|raw }}
                <ul class="patients-images" data-prototype="{{ ((form_widget(form.patientDescriptions.vars.prototype.images.vars.prototype)|e('html_attr'))|replace({'__name__': '__pname__'}))|raw }}"></ul>
                {{ ((form_row(form.patientDescriptions.vars.prototype.patient))|replace({'__name__': '__pname__'}))|raw }}
            {% endif %}
            {% set proto = form.surveyQuestions.vars.prototype %}
            <ul class="questions" data-prototype="{% apply escape %}{{ include('admin/survey/question_prototype.html.twig', { 'question': proto, 'prototype': true }) }}{% endapply %}">
                {% if questions is defined %}
                    {% for question in questions %}
                        {{ include('admin/survey/question_prototype.html.twig', { 'question': question.createView(), 'loop' : loop }) }}
                    {% endfor %}
                {% endif %}
            </ul>
        </div>
    </div>
</li>

