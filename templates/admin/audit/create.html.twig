{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'alienor_audit_create' %}
        {{ 'audit.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_audit_edit' %}
        {{ 'audit.titles.edit'|trans }}
    {% endif %}
{% endblock %}

{% block stylesheets %}
    <link href='https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.2/dragula.css' rel="stylesheet"/>
{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-body">
            {% set form = formRaw %}
            {{ form_start(form, { attr: { class: "form-collapse-validate" } }) }}
            {{ form_row(form.label) }}
            <div class="" data-type="pdf">
                <div class="form-group vich-file">
                    <label class="control-label">Fichier descriptif de l'audit {% if audit.pdfFile %} (<a target="_blank" href="{{ url('eduprat_audit_descriptif_audit_download', {'audit': audit.id, 'token': audit.token} ) }}">Visualiser le fichier actuel</a>) {% endif %}</label>
                    {{ form_row(form.pdfFile) }}
                </div>
            </div>
            {{ form_row(form.year) }}
            {{ form_row(form.type) }}
            {{ form_row(form.category) }}
            {{ form_row(form.nbPatients) }}
            {{ form_row(form.introduction) }}
            <div class="box box-info">
                <div class="box-body">
                    <h4>Questions :</h4>
                    <ul id="questions" class="questions" data-prototype="{% apply escape %}{{ include('admin/audit/question_prototype.html.twig', { 'question': form.questions.vars.prototype }) }}{% endapply %}">
                        {% for question in form.questions %}
                            {{ include('admin/audit/question_prototype.html.twig', { 'question': question, 'loop' : loop }) }}
                        {% endfor %}
                    </ul>
                </div>
            </div>
            <button type="submit" class="btn btn-eduprat pull-right">{{ "admin.global.save"|trans }}</button>
            {{ form_end(form) }}
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ tinymce_init() }}
    <script src='https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.2/dragula.min.js'></script>
    <script src="{{ asset('js/survey-form.js') }}"></script>
    <script>
        var $collectionHolder;

        var $addQuestionLink = $('<a href="#" class="add_question_link btn btn-eduprat">Ajouter une question</a>');
        var $newLinkLi = $('<div></div>').append($addQuestionLink);

        jQuery(document).ready(function () {
            $collectionHolder = $('ul.questions');

            $collectionHolder.find('> li').each(function () {
                addQuestionFormDeleteLink($(this));
            });

	        $collectionHolder.find('ul.images').each(function () {
		        addImageFormAddLink($(this));
		        $(this).data('index', $(this).find('> li').length);

		        $(this).find('> li').each(function () {
			        addImageFormDeleteLink($(this));
		        });
	        });

            $collectionHolder.after($newLinkLi);

            $collectionHolder.data('index', $collectionHolder.find('> li').length + 1);

            $addQuestionLink.on('click', function (e) {
                e.preventDefault();
                addQuestionForm($collectionHolder);
            });

	        dragula([document.getElementsByClassName('questions')[0]], {
		        moves: function (el, container, handle) {
			        return handle.classList.contains('handle');
		        }
	        }).on('drop', function (el) {
		        updatePositions();
	        });

	        $(document).on('change', '.question-label', updatePositions);

	        updatePositions();
        });

        function addQuestionForm($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);

	        var $images = $newFormLi.find('.images');
	        var imagePrototype = $images.data('prototype');
	        $images.replaceWith($('<ul class="images"></ul>').data('prototype', imagePrototype));
	        var $imageHolder = $newFormLi.find('ul.images');
	        $imageHolder.data('index', 0);

	        $newFormLi.find('input.question-required[type=checkbox]').prop('checked', true);

            initTinyMCE();
	        updatePositions();
            addQuestionFormDeleteLink($newFormLi);
	        addImageFormAddLink($imageHolder);
        }

    </script>
{% endblock %}
