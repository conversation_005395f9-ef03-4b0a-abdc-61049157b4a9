<li class="panel" data-index="{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
    <h4 class="box-title">
        <a data-toggle="collapse" data-parent="#questions" href="#collapse-q{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
            <i class="fa fa-caret-down"></i> Question # <span class="question-index">{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}</span>
        </a>
    </h4>
    <div id="collapse-q{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}" class="panel-collapse collapse">
        <div class="box-body">
            {{ form_row(question.label) }}
            {{ form_row(question.position) }}
            {{ form_row(question.categoryQuestion) }}
            <ul class="images" data-prototype="{{ form_widget(question.images.vars.prototype)|e('html_attr') }}">
                {% for image in question.images %}
                    <li>
                        <label>{{ image.vars.value.name }}</label>
                        <br>
                        <img src="{{ image.vars.value.relativeUrl }}" alt="">
                        {{ form_row(image) }}
                    </li>
                {% endfor %}
            </ul>
            {{ form_row(question.interpretationTrueTrue) }}
            {{ form_row(question.interpretationTrueFalse) }}
            {{ form_row(question.interpretationFalseFalse) }}
            {{ form_row(question.interpretationFalseTrue) }}
            {{ form_row(question.interpretation) }}
            {{ form_row(question.answer) }}
            {{ form_row(question.required) }}
        </div>
    </div>
</li>