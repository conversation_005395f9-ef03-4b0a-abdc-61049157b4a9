{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'admin.participation.delete.title'|trans }}{% endblock %}

{% block body %}
    <div class="box">
        <div class="box-header">
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    {{ form_start(form) }}
                    <p>{{ 'admin.participation.delete.message'|trans({'%participantFirstname%' : participation.participant.firstname,'%participantLastname%': participation.participant.lastname}) }}</p>
                    {{ form_row(form.motif) }}
                    {{ form_row(form.commentaire) }}
                    <button type="submit" class="btn btn-danger">{{ 'admin.global.delete'|trans }}</button>
                    <a class="btn btn-default" href="{{ url('admin_formation_show', {'id': participation.formation.id }) }}" title="Annuler">Annuler</a>
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}