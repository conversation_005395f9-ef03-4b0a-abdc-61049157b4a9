{% extends 'admin/base.html.twig' %}

{% block title %}Transfert de participants{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-header">
            {{ 'user.transfer.message'|trans({'%person%' : person.invertedFullname, '%size%' : person.participants|length}) }}
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    {{ form_start(form) }}
                    {{ form_row(form.coordinator) }}
                    <button type="submit" class="btn btn-danger">{{ 'admin.global.confirm'|trans }}</button>
                    <a class="btn btn-default" href="{{ url('admin_user_edit', { id: person.id }) }}" title="Annuler">Annuler</a>
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
    <div class="box box-info">
        <div class="box-header">
            Liste des participants concernés
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="table-responsive">
                        <table class="table table-bordered table-condensed">
                            <thead>
                            <tr>
                                <th>Nom</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for participant in person.participants %}
                                <tr>
                                    <td>{{ participant.invertedFullname }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}