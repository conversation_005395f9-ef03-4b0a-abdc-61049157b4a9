{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'user.titles.edit_password'|trans }}{% endblock %}

{% block body %}
    {{ form_start(form) }}
    {{ form_row(form.current_password) }}
    <div class="form-group {% if not form.plainPassword.first.vars.valid %}has-error{% endif %}">
        {{ form_label(form.plainPassword.first) }}
        {{ form_widget(form.plainPassword.first) }}
        {% if form.plainPassword.first.vars.valid %}
            <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
        {% endif %}
        {{ form_errors(form.plainPassword.first) }}
    </div>
    <div class="form-group">
        {{ form_label(form.plainPassword.second) }}
        {{ form_widget(form.plainPassword.second) }}
        {{ form_errors(form.plainPassword.second) }}
    </div>
    {{ form_rest(form) }}
    {{ form_end(form) }}
{% endblock body %}
