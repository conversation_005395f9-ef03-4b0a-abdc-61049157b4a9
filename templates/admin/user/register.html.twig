{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'alienor_user_register' %}
        {{ 'user.titles.register'|trans }}
    {% elseif app.request.get('_route') == 'admin_user_edit'%}
        {{ 'user.titles.edit'|trans }}
    {% endif %}
{% endblock %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% form_theme form 'admin/form/avatar.html.twig' %}

{% block body %}
<div class="box box-info">
    <div class="box-body">
        {{ form_start(form) }}

        {% if form.email is defined %}
            {{ form_row(form.email) }}
        {% endif %}
        {% if form.firstname is defined %}
            {{ form_row(form.firstname) }}
        {% endif %}
        {% if form.lastname is defined %}
            {{ form_row(form.lastname) }}
        {% endif %}
        {% if form.roles is defined %}
            {{ form_row(form.roles) }}
        {% endif %}
        {% if form.job is defined %}
            {{ form_row(form.job) }}
        {% endif %}
        {% if form.civility is defined %}
            {{ form_row(form.civility) }}
        {% endif %}
        {% if form.address is defined %}
            {{ form_row(form.address) }}
        {% endif %}
        {% if form.address2 is defined %}
            {{ form_row(form.address2) }}
        {% endif %}
        {% if form.zipCode is defined %}
            {{ form_row(form.zipCode) }}
        {% endif %}
        {% if form.city is defined %}
            {{ form_row(form.city) }}
        {% endif %}
        {% if form.phone is defined %}
            {{ form_row(form.phone) }}
        {% endif %}
        {% if form.crStatus is defined %}
            {{ form_row(form.crStatus) }}
        {% endif %}
        {% if form.crStatusDate is defined %}
            {{ form_row(form.crStatusDate) }}
        {% endif %}
        {% if form.crIdentifier is defined %}
            {{ form_row(form.crIdentifier) }}
        {% endif %}
        {% if form.codeApporteur is defined %}
            {{ form_row(form.codeApporteur) }}
        {% endif %}
        {% if form.departments is defined %}
            {{ form_row(form.departments) }}
        {% endif %}
        {% if form.ugas is defined %}
            {{ form_row(form.ugas) }}
        {% endif %}
        {% if form.companyName is defined %}
            {{ form_row(form.companyName) }}
        {% endif %}
        {% if form.siret is defined %}
            {{ form_row(form.siret) }}
        {% endif %}
        {% if form.rpps is defined %}
            {{ form_row(form.rpps) }}
        {% endif %}
        {% if form.adeli is defined %}
            {{ form_row(form.adeli) }}
        {% endif %}
        {% if form.edupratFormer is defined %}
            {{ form_row(form.edupratFormer) }}
        {% endif %}
        {% if form.unpaidFormer is defined %}
            {{ form_row(form.unpaidFormer) }}
        {% endif %}
        {% if form.supervisor is defined %}
            {{ form_row(form.supervisor) }}
        {% endif %}
        {% if form.webmaster is defined %}
            {{ form_row(form.webmaster) }}
        {% endif %}
        {% if form.coordinatorBinome is defined %}
            {{ form_row(form.coordinatorBinome) }}
        {% endif %}
        {% if form.cms is defined %}
            {{ form_row(form.cms) }}
        {% endif %}
        {% if form.manualEmailReminder is defined %}
            {{ form_row(form.manualEmailReminder) }}
        {% endif %}
        {% if form.isArchived is defined %}
            {{ form_row(form.isArchived) }}
        {% endif %}
        {% if form.allowShareAdress is defined %}
            {{ form_row(form.allowShareAdress) }}
        {% endif %}
        {% if form.avatarFile is defined %}
            {{ form_row(form.avatarFile) }}
        {% endif %}
        {% if form.plainPassword is defined %}
            <div class="form-group {% if not form.plainPassword.first.vars.valid %}has-error{% endif %}">
                {{ form_label(form.plainPassword.first) }}
                {{ form_widget(form.plainPassword.first) }}
                {% if form.plainPassword.first.vars.valid %}
                    <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
                {% endif %}
                {{ form_errors(form.plainPassword.first) }}
            </div>
            <div class="form-group">
                {{ form_label(form.plainPassword.second) }}
                {{ form_widget(form.plainPassword.second) }}
                {{ form_errors(form.plainPassword.second) }}
            </div>
        {% endif %}

        {{ form_rest(form) }}
        <div class="form-group">
            <button type="submit" class="btn-eduprat btn">{{ "admin.global.save"|trans }}</button>
        </div>
        {{ form_end(form) }}
    </div>
</div>
{% if person is defined and person.id and person.isCoordinator %}
    <div class="box box-danger">
        <div class="box-header with-border">
            <h3 class="box-title">{{ "admin.global.actions" | trans }}</h3>
        </div>
        <div class="box-body">
            <a class="btn btn-danger" href="{{ url("admin_user_transfer", {id: person.id}) }}">Transférer les participants liés vers un autre coordinateur</a>
        </div>
    </div>
{% endif %}
{% if formPass is defined %}
    <div class="box box-info">
        <div class="box-header with-border">
            <h3 class="box-title">{{ "participant.edit.password" | trans }}</h3>
        </div>
        <div class="box-body">
            {{ form_start(formPass) }}
            <div class="form-group {% if not formPass.plainPassword.first.vars.valid %}has-error{% endif %}">
                {{ form_label(formPass.plainPassword.first) }}
                {{ form_widget(formPass.plainPassword.first) }}
                {% if formPass.plainPassword.first.vars.valid %}
                    <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
                {% endif %}
                {{ form_errors(formPass.plainPassword.first) }}
            </div>
            <div class="form-group">
                {{ form_label(formPass.plainPassword.second) }}
                {{ form_widget(formPass.plainPassword.second) }}
                {{ form_errors(formPass.plainPassword.second) }}
            </div>
            {{ form_rest(formPass) }}
            <div class="form-group">
                <button type="submit" class="btn-eduprat btn">{{ "admin.global.save_password"|trans }}</button>
            </div>
            {{ form_end(formPass) }}
        </div>
    </div>
{% endif %}
{% endblock %}
{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.quicksearch.js') }}"></script>
    <script>
        $(document).ready(function() {
        	$('#eduprat_person_roles').change(function () {
                var selectedRole = $(this).val();
                $.each($('[data-role]'), function(index, el) {
                    var $el = $(el);
                    var $group = $el.closest('.form-group');
                    if ($el.data('role').indexOf(selectedRole) === -1) {
                        if ($el.attr('type') === "checkbox") {
                            if (typeof $el.data('hidden-value') !== "undefined") {
                                $el.attr('checked', $el.data('hidden-value')).attr('required', false);
                            }
                        } else {
                            $el.val(typeof $el.data('hidden-value') === "undefined" ? "" : $el.data('hidden-value')).attr('required', false);
                        }
                        $group.hide();
                    } else {
                        var required = typeof $el.data('required') === "undefined" ? true : JSON.parse($el.data('required'));
                        $el.attr('required', required);
                        $group.show();
                    }
                });
                $("#eduprat_person_crStatus").change(function (e) {
                    $("#eduprat_person_crStatusDate").closest(".form-group").toggleClass("hidden", $(this).val() === "Indépendant");
                    if($(this).val() == "Salarié") {
                        $("#eduprat_person_crStatusDate").attr("required", "required");
                        $("#eduprat_person_crStatusDate").prev('span').show();
                    } else {
                        $("#eduprat_person_crStatusDate").removeAttr("required");
                        $("#eduprat_person_crStatusDate").prev('span').hide();
                    }
                });
		        initMultiselect();
            }).trigger('change');

        	$('#eduprat_person_manualEmailReminder').change(function (e) {
        		if ($(this).is(':checked')) {
        			if (!confirm("Êtes-vous sûr de vouloir faire vos relance manuellement ?")) {
        				$(this).prop('checked', false);
                    }
                }
            });

        	$("#eduprat_person_crStatus").change(function (e) {
        		$("#eduprat_person_crStatusDate").closest(".form-group").toggleClass("hidden", $(this).val() === "Indépendant");
        		if ($("#eduprat_person_crStatusDate").is(':visible')) {
			        $("#eduprat_person_crStatusDate").attr("required", "required");
                } else {
			        $("#eduprat_person_crStatusDate").removeAttr("required");
                }

                if($(this).val() == "Salarié") {
                    $("#eduprat_person_crStatusDate").attr("required", "required");
                    $("#eduprat_person_crStatusDate").prev('span').show();
                } else {
                    $("#eduprat_person_crStatusDate").removeAttr("required");
                    $("#eduprat_person_crStatusDate").prev('span').hide();
                }
            });
	        $("#eduprat_person_crStatus").change();

        	var multiselectInitialized = false;

        	function getMultiSelectConfig () {
        		return {
			        selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
			        selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
			        afterInit: function(ms){
				        var that = this,
					        $selectableSearch = that.$selectableUl.prev(),
					        $selectionSearch = that.$selectionUl.prev(),
					        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
					        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

				        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
					        .on('keydown', function(e){
						        if (e.which === 40){
							        that.$selectableUl.focus();
							        return false;
						        }
					        });

				        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
					        .on('keydown', function(e){
						        if (e.which == 40){
							        that.$selectionUl.focus();
							        return false;
						        }
					        });
				        multiselectInitialized = true;
			        },
			        afterSelect: function(){
				        this.qs1.cache();
				        this.qs2.cache();
			        },
			        afterDeselect: function(){
				        this.qs1.cache();
				        this.qs2.cache();
			        }
		        };
            }

        	function initMultiselect() {
        		if (!multiselectInitialized) {
			        $('#eduprat_person_departments').multiSelect(getMultiSelectConfig());
					$('#eduprat_person_ugas').multiSelect(getMultiSelectConfig());
                }
            }

	        $('#eduprat_person_departments').multiSelect(getMultiSelectConfig());
			$('#eduprat_person_ugas').multiSelect(getMultiSelectConfig());
        });
    </script>
{% endblock %}
