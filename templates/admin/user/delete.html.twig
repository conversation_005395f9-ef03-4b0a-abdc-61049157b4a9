{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'user.delete.title'|trans }}{% endblock %}

{% block body %}
    <div class="box">
        <div class="box-header">
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                {% if errorMessage %}
                    <div class="alert alert-error">{{ errorMessage|trans({'%person%' : person.username}) }}</div>
                    <div><a class="btn btn-default" href="{{ url('admin_user_index') }}" title="Retour">Retour</a></div>
                {% else %}
                    {{ form_start(form) }}
                    <p>{{ 'user.delete.message'|trans({'%person%' : person.username}) }}</p>
                    <button type="submit" class="btn btn-danger">{{ 'admin.global.delete'|trans }}</button>
                    <a class="btn btn-default" href="{{ url('admin_user_index') }}" title="Annuler">Annuler</a>
                    {{ form_rest(form) }}
                    {{ form_end(form) }}
                {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}