{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'admin.user.evaluation.title'|trans }}{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.eval'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ "admin.formation.title" | trans }}</th>
                            <td>{{ formation.programme.title }}</td>
                        </tr>
                        <tr>
                            <th>{{ "admin.user.evaluation.former" | trans }}</th>
                            <td>{{ user.fullname }}</td>
                        </tr>
                    </table>
                    <br>
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.user.evaluation.question" | trans }}</th>
                            <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                            {# <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "admin.user.evaluation.avg" | trans }}</th> #}
                        </tr>
                        </thead>
                        <tbody>
                        {% if evaluation|length %}
                            {% for i in 1..evaluation|length %}
                            <tr>
                                <td>{{ ("evaluation.former.question." ~ i ~".label") | trans }}</td>
                                <td>
                                    {% if evaluation[i] is defined %}
                                        {{ evaluation[i].count }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </td>
                                <td>
                                    {% if evaluation[i] is defined %}
                                        {{ evaluation[i].avg | round(2) }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </td>

                                {# <td>
                                    {% if evaluationByCoordinators[i] is defined %}
                                        {{ evaluationByCoordinators[i].count }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </td>
                                <td>
                                    {% if evaluationByCoordinators[i] is defined %}
                                        {{ evaluationByCoordinators[i].avg | round(2) }}
                                    {% else %}
                                        0
                                    {% endif %}
                                </td> #}
                            </tr>
                        {% endfor %}
                        {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}