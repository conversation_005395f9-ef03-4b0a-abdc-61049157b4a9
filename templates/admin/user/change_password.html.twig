{% extends 'admin/base_login.html.twig' %}

{% block body %}
    {% if error is defined %}
        <p class="login-box-msg">{{ error | trans }}</p>
        <div class="text-center">
            <a href="{{ url('admin_user_reset') }}">Mot de passe oublié ?</a>
        </div>
    {% else %}
        {% if register %}
            {% if person.fullname %}
                <p class="login-box-msg"><b>{{ person.fullname }}</b></p>
            {% endif %}
            <p class="login-box-msg">Création du mot de passe</p>
        {% else %}
            <p class="login-box-msg">Création ou modification de mot de passe</p>
        {% endif %}
        {{ form_start(form) }}
        <div class="form-group {% if not form.plainPassword.first.vars.valid %}has-error{% endif %}">
            {{ form_label(form.plainPassword.first) }}
            {{ form_widget(form.plainPassword.first) }}
            <span class="help-block">{{ "password.edit.constraints" | trans({}, "validators") }}</span>
            {{ form_errors(form.plainPassword.first) }}
        </div>
        <div class="form-group">
            {{ form_label(form.plainPassword.second) }}
            {{ form_widget(form.plainPassword.second) }}
            {{ form_errors(form.plainPassword.second) }}
        </div>
        {{ form_rest(form) }}
        <div class="row">
            <div class="col-xs-4 pull-right">
                <button type="submit" class="btn btn-eduprat btn-block btn-flat">{{ "admin.global.save"|trans }}</button>
            </div>
        </div>
        {{ form_end(form) }}
    {% endif %}

{% endblock body %}
