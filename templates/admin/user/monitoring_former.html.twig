{% extends 'admin/base.html.twig' %}

{% form_theme formCv 'admin/form/fields.html.twig' %}
{% form_theme formDli 'admin/form/fields.html.twig' %}

{% block title %}
    {% if user.isPharmacieFormer %}
        {{ 'admin.user.detail.formerPharmarcie'|trans }}
    {% else %}
        {{ 'admin.user.detail.former'|trans }}
    {% endif %}
{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.info'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ 'login.lastname'|trans }}</th>
                            <td>{{ user.lastname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'login.firstname'|trans }}</th>
                            <td>{{ user.firstname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.address'|trans }}</th>
                            <td>{{ user.address }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.zipCode'|trans }}</th>
                            <td>{{ user.zipCode }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.city'|trans }}</th>
                            <td>{{ user.city }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.phone'|trans }}</th>
                            <td>{{ user.phone }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'login.email'|trans }}</th>
                            <td>{{ user.email }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.edupratFormer'|trans }}</th>
                            <td>{{ ("admin.global." ~ (user.edupratFormer ? "yes" : "no")) | trans }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.job'|trans }}</th>
                            <td>{{ user.job }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.siret'|trans }}</th>
                            <td>{{ user.siret }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.rpps'|trans }}</th>
                            <td>{{ user.rpps }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.adeli'|trans }}</th>
                            <td>{{ user.adeli }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.info'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ 'admin.user.cv'|trans }}</th>
                            <td>
                                {% if user.cv is not null %}
                                    <div class="col-lg-12 form-group">
                                        <a class="btn btn-eduprat" target="_blank" href="{{ url('admin_user_file_cv', {'id' : user.id}) }}">{{ "admin.global.consult" | trans }}</a>
                                    </div>
                                {% endif %}

                                {{ form_start(formCv) }}
                                <div class="col-lg-8">
                                    {{ form_row(formCv.cvFile) }}
                                </div>
                                <div class="col-lg-4 text-right">
                                    {{ form_row(formCv.submit) }}
                                </div>
                                {{ form_rest(formCv) }}
                                {{ form_end(formCv) }}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.dli'|trans }}</th>
                            <td>
                                {% if user.dli is not null %}
                                    <div class="col-lg-12 form-group">
                                        <a class="btn btn-eduprat" target="_blank" href="{{ url('admin_user_file_dli', {'id' : user.id}) }}">{{ "admin.global.consult" | trans }}</a>
                                    </div>
                                {% endif %}
                                {{ form_start(formDli) }}
                                <div class="col-lg-8">
                                    {{ form_row(formDli.dliFile) }}
                                </div>
                                <div class="col-lg-4 text-right">
                                    {{ form_row(formDli.submit) }}
                                </div>
                                {{ form_rest(formDli) }}
                                {{ form_end(formDli) }}
                            </td>
                        </tr>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            {% if user.createdAt < date("2018-01-01") %}
                                <tr>
                                    <th>{{ "admin.user.detail.recap" | trans }}</th>
                                    <td>
                                        <a class="btn btn-eduprat" target="_blank" href="{{ url('admin_user_evaluation_recap', {'id': user.id, 'year': 2017}) }}">{{ "admin.global.consult" | trans }}</a>
                                    </td>
                                </tr>
                            {% endif %}
                            <tr>
                                <th>{{ "admin.user.detail.report" | trans }}</th>
                                <td>
                                    {% set maxYear = "now"|date('Y') %}
                                    {% for year in 2018..maxYear %}
                                        <a class="btn btn-eduprat" target="_blank" href="{{ url('evaluation_global_former_year', {'person': user.id, 'year': year}) }}">{{ year }}</a>
                                    {% endfor %}
                                </td>
                            </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.formations'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ 'admin.formation.title'|trans }}</th>
                            <th>{{ 'admin.programme.reference'|trans }}</th>
                            <th>{{ 'admin.global.actions'|trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for programme in programmes %}
                            <tr>
                                <td>{{ programme.title }}</td>
                                <td>{{ programme.reference }}</td>
                                <td><a href="#" class="mrm formationsListLink btn btn-eduprat"><i class="fa fa-angle-right"></i> Détails</a></td>
                            </tr>
                            <tr class="formationsWrap">
                                <td colspan="4">
                                    <div class="formationsList">
                                        <table class="table table-bordered table-responsive table-hover table-bilan">
                                            <thead>
                                            <tr>
                                                <th>{{ 'admin.formation.startDate'|trans }}</th>
                                                <th>{{ 'admin.formation.sessionNumber'|trans }}</th>
                                                <th>{{ 'admin.user.download.topo'|trans }}</th>
                                                <th>{{ 'admin.user.download.preRestitution'|trans }}</th>
                                                <th>{{ 'admin.user.download.satisfaction'|trans }}</th>
                                            </tr>
                                            </thead>
                                            {% for formateur in user.formers %}
                                                {% if formateur.formation is not null and formateur.formation.programme.id == programme.id %}
                                                    {% set formation = formateur.formation %}
                                                    {% set newEvaluation = isNewEvaluation(formation.startDate) %}
                                                    <tr>
                                                        <td>
                                                            {% if formation.searchStartDate|date('d/m/Y') == formation.searchEndDate|date('d/m/Y') %}
                                                                {{ formation.searchStartDate | date('d/m/Y') }}
                                                            {% else %}
                                                                Du {{ formation.searchStartDate | date('d/m/Y') }}<br> au {{ formation.searchEndDate | date('d/m/Y') }}
                                                            {% endif %}
                                                        </td>
                                                        <td>{{ formation.sessionNumber }}</td>
                                                        <td>
                                                            {% if formation.topoFiles is not null %}
                                                                {% for file in formation.topoFiles %}
                                                                    <div>
                                                                        {% set name = "admin.formation.topo"|trans %}
                                                                        {% if file.topoOriginalName %}
                                                                            {% set name = file.topoOriginalName %}
                                                                        {% endif %}
                                                                        <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('admin_topo_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                                                    </div>
                                                                {% endfor %}
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            {% if formation.hasLinkedForm and formation.isAudit %}
                                                                {% for financeSousMode in formation.financeSousModes %}
                                                                    <a target="_blank" href="{{ url('pdf_restitution_audit_groupe_pdf', { id: formation.id, financeSousMode: financeSousMode.id, former: formateur.id, token: formation.token }) }}">
                                                                        {% if formation.financeSousModes.count > 1 %}
                                                                            {{ financeSousMode.name }} -
                                                                        {% endif %}
                                                                        {% if formation.isPredefined %}
                                                                            {{ "admin.user.restitutionFormateurPredefined" | trans }}
                                                                        {% elseif formation.isVignette %}
                                                                            {{ "admin.user.restitutionFormateurVignette" | trans }}
                                                                        {% else %}
                                                                            {{ "admin.user.restitutionFormateur" | trans }}
                                                                        {% endif %}
                                                                    </a>
                                                                {% endfor %}
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            {% if "now"|date('Ymd')  > formation.endDate|date('Ymd') and newEvaluation %}
                                                                {% if accessManager.canEvaluateProgramme(formation, formateur.person) %}
                                                                    <a class="btn btn-eduprat" href="{{ url('eduprat_evaluation_global', { formation: formation.id, person: formateur.person.id}) }}">{{ "admin.user.detail.eval" | trans }}</a>
                                                                {% else %}
                                                                    <a class="btn btn-eduprat disabled" >Questionnaire de satisfaction déjà répondu</a>
                                                                {% endif %}
                                                            {% endif %}
                                                            {% if is_granted('ROLE_WEBMASTER') and "now"|date('Ymd')  > formation.endDate|date('Ymd') and newEvaluation %}
                                                                <a class="btn btn-eduprat" href="{{ url('evaluation_formation_global_former', { formation: formation.id, former: formateur.id}) }}">{{ "evaluationGlobal.titles.report" | trans }}</a>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endif %}
                                            {% endfor %}
                                        </table>
                                    </div>
                                </td>
                            </tr>

                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}

    <script type="text/javascript">

    </script>

{% endblock %}