{% extends 'admin/base.html.twig' %}

{% block title %}
    Prévisualisation des emails automatiques
{% endblock %}
{% block body %}
    <div class="box box-info">
        <div class="box-body">
            <a href="{{ url("admin_config_emails_sendall") }}" class="btn btn-eduprat primary">Envoyer tous les emails</a>
        </div>
    </div>
    {% for index, checker in checkers %}
    <div class="box box-info">
        <div class="box-body">
            <p><b>{{ ("admin.emails.aliases." ~ checker.alias) | trans }}</b></p>
            <p>A envoyer aujourd'hui : {{ checker.count }}</p>
            {% if checker.count > 0 %}
                {% set canSendEmails = checker.toSend[0].participant is defined %}
                {% if canSendEmails %}
                    <a href="{{ url("admin_config_emails_send", { alias: checker.alias }) }}" class="btn btn-eduprat primary">Envoyer les emails</a>
                    <br>
                    <br>
                {% endif %}
                <div class="panel panel-eduprat" data-index="{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
                    <h5 class="box-title">
                        <a data-toggle="collapse" data-parent="#questions" href="#collapse-recipient-{{ index }}" aria-expanded="false" class="collapsed">
                            <i class="fa fa-caret-down"></i> Liste des destinataires
                        </a>
                    </h5>
                    <div id="collapse-recipient-{{ index }}" class="panel-collapse collapse" aria-expanded="false">
                        <div class="box-body">
                            <ul>
                                {% for recipient in checker.toSend %}
                                    {% if recipient.participant is defined and recipient.id %}
                                        <li>{{ recipient.participant.fullname }} - {{ recipient.participant.email }} - <a href="{{ url("admin_config_emails_send_participation", { alias: checker.alias, participation: recipient.id }) }}">Envoyer l'email</a></li>
                                    {% elseif recipient.person.email is defined %}
                                        <li>{{ recipient.person.fullname }} - {{ recipient.person.email }}</li>
                                    {% elseif recipient.participant.user.email is defined %}
                                        <li>{{ recipient.participant.user.fullname }} - {{ recipient.participant.user.email }}</li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="panel panel-eduprat" data-index="{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
                    <h5 class="box-title">
                        <a data-toggle="collapse" data-parent="#questions" href="#collapse-preview-{{ index }}" aria-expanded="false" class="collapsed">
                            <i class="fa fa-caret-down"></i> Prévisualisation
                        </a>
                    </h5>
                    <div id="collapse-preview-{{ index }}" class="panel-collapse collapse" aria-expanded="false">
                        <div class="box-body">
                            <p><b>Sujet :</b> {{ checker.subject }}</p>
                            <p><b>Destinataire :</b> {{ checker.recipient }}</p>
                            <p><b>Contenu :</b></p>
                            <div style="border: 1px solid grey">{{ checker.body | raw }}</div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
{% endblock %}

{% block javascripts %}
    <script>
		$(document).ready(function () {

		});
    </script>
{% endblock %}