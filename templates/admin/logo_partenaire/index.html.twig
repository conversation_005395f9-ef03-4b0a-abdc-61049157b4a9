{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'admin.logoPartenaire.titles.index'|trans }}{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-header with-border">
            <h3 class="box-title">{{ 'admin.logoPartenaire.titles.index'|trans }}</h3>
            <div class="box-tools pull-right">
                <a href="{{ path('admin_logo_partenaire_create') }}" class="btn btn-eduprat btn-sm">
                    <i class="fa fa-plus"></i> {{ 'admin.global.add'|trans }}
                </a>
            </div>
        </div>
        <div class="box-body">
            <table class="table table-bordered table-striped datatable">
                <thead>
                <tr>
                    <th>{{ 'admin.logoPartenaire.table.logoName'|trans }}</th>
                    <th>{{ 'admin.logoPartenaire.table.image'|trans }}</th>
                    <th>Usages</th>
                    {% if is_granted('ROLE_WEBMASTER') %}
                        <th>{{ 'admin.global.actions'|trans }}</th>
                    {% endif %}
                </tr>
                </thead>
                <tbody>
                {% for item in logosWithUsages %}
                    {% set logo = item.logo %}
                    {% set usageCount = item.usageCount %}
                    <tr>
                        <td>{{ logo.logoName }}</td>
                        <td>
                            <img src="{{ asset(logo.relativeUrl) }}" alt="{{ logo.logoName }}" style="max-width: 100px; max-height: 50px;">
                        </td>
                        <td>
                            {% if usageCount > 0 %}
                                <span class="label label-warning">{{ usageCount }} usage(s)</span>
                            {% else %}
                                <span class="label label-success">Aucun usage</span>
                            {% endif %}
                        </td>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <td>
                                <div class="btn-group">
                                    <a href="{{ path('admin_logo_partenaire_edit', {'id': logo.id}) }}" class="btn btn-default btn-sm">
                                        <i class="fa fa-edit"></i> {{ 'admin.global.edit'|trans }}
                                    </a>
                                    {% if usageCount == 0 %}
                                        <a href="{{ path('admin_logo_partenaire_delete', {'id': logo.id}) }}" class="btn btn-danger btn-sm" onclick="return confirm('{{ 'admin.global.confirm'|trans }}')">
                                            <i class="fa fa-trash"></i> {{ 'admin.global.delete'|trans }}
                                        </a>
                                    {% else %}
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-danger btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <i class="fa fa-trash"></i> Supprimer <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a href="{{ path('admin_logo_partenaire_delete', {'id': logo.id}) }}">Suppression simple (échouera)</a></li>
                                                <li><a href="{{ path('admin_logo_partenaire_force_delete', {'id': logo.id}) }}" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce logo ET tous ses usages ({{ usageCount }}) ?')">Suppression forcée (+ usages)</a></li>
                                            </ul>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                        {% endif %}
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}