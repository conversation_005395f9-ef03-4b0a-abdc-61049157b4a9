<li class="panel" {% if question.vars.data is not null and question.vars.data.id is not null%}data-id="{{ question.vars.data.id }}"{% endif %} data-index="{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
    <h4 class="box-title">
        <a data-toggle="collapse" data-parent="#questions"
           href="#collapse{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
            <i class="fa fa-caret-down"></i> Question
            # <span class="question-index">{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}</span>
        </a>
    </h4>
    <div id="collapse{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}"
         class="panel-collapse collapse">
        <div class="box-body">
            {{ form_row(question.label) }}
            {{ form_row(question.position) }}
            {% if question.categoryQuestion is defined %}
                {{ form_row(question.categoryQuestion) }}
            {% elseif question.auditCategoryQuestion is defined %}
                {{ form_row(question.auditCategoryQuestion) }}
            {% endif %}
            <ul class="images" data-prototype="{{ form_widget(question.images.vars.prototype)|e('html_attr') }}">
                {% for image in question.images %}
                    <li>
                        <label>{{ image.vars.value.name }}</label>
                        <br>
                        <img src="{{ image.vars.value.relativeUrl }}" alt="">
                        {{ form_row(image) }}
                    </li>
                {% endfor %}
            </ul>
            {{ form_row(question.type) }}
            <div class="hidden form-group" data-type="text">
                {{ form_row(question.answer) }}
            </div>
            <div class="hidden form-group" data-type="radio">
                <label class="control-label required">Réponse</label>
                <span class="required" title="Ce champ est obligatoire">*</span>
                <div><div class="radio">
                        <label class="required">
                            <input type="radio" name="radio-answer-{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}" class="radio-answer" value="1" {% if question.answer.vars.value == "1" %}checked="checked"{% endif %}>
                            Oui
                        </label>
                    </div>
                    <div class="radio">
                        <label class="required">
                            <input type="radio" name="radio-answer-{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}" class="radio-answer" value="0" {% if question.answer.vars.value == "0" %}checked="checked"{% endif %}>
                            Non
                        </label>
                    </div>
                </div>
            </div>
            <div class="hidden form-group" data-type="choice">
                <ul class="choices" data-prototype="{{ form_widget(question.choices.vars.prototype)|e('html_attr') }}">
                    {% for choice in question.choices %}
                        <li>{{ form_row(choice) }}</li>
                    {% endfor %}
                </ul>
            </div>
            {{ form_row(question.comments) }}
            {{ form_row(question.required) }}
            {{ form_row(question.patient) }}
        </div>
    </div>
</li>