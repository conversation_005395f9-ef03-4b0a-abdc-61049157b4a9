{{ form_start(form) }}

    {{ form_row(form.description) }}
    <div class="form-group">
        <label class="control-label required" for="">Image de la description</label>
        {% if tcs is defined and tcs.descriptionImage %}
            <img src="{{ asset('uploads/questionTCSPicture/'~ tcs.descriptionImage) }}" alt="" style="display: block;" width="auto" height="128" />
        {% endif %}
        {{ form_row(form.descriptionImageFile) }}
    </div>

    {{ form_row(form.syntheseEducative) }}
    <div class="form-group">
        <label class="control-label required" for="">Image de la synthèse éducative</label>
        {% if tcs is defined and tcs.syntheseEducativeImage %}
            <img src="{{ asset('uploads/questionTCSPicture/'~ tcs.syntheseEducativeImage) }}" alt="" style="display: block;" width="auto" height="128" />
        {% endif %}
        {{ form_row(form.syntheseEducativeImageFile) }}
    </div>

<button class="btn btn-eduprat">{{ button_label|default('Save') }}</button>
{{ form_end(form) }}
