{% extends 'admin/base.html.twig' %}

{% block title %}{{ questionnaire_tcs.label }} - {{ questionnaire_tcs.thematique.name }}{% endblock %}

{% block header_titre %}

{% endblock %}

{% block body %}
    {% set nbExpertCompletRepondant = questionnaire_tcs.nbExpertCompletRepondant %}
    {% set isEditable = questionnaire_tcs.isEditable %}
    <header class="header-nathan">
        <div class="titre">
            <h1>
                {{ questionnaire_tcs.label }}
            </h1>
            <div class="align-horizontal">
                <p><span class="legend">Catégorie</span> {{ questionnaire_tcs.thematique.name }}</p>
                <p><span class="legend">Nombre d'experts</span> {{ nbExpertCompletRepondant }}</p>
            </div>

        </div>
        <div class="right">
            <a href="{{ path('app_admin_tcs_questionnaire_tcs_edit', {'id': questionnaire_tcs.id}) }}" class="btn btn-eduprat btn-icon" title="Modifier le questionnaire"><i class="fa fa-edit"></i></a>
            {% if isEditable %}<a href="{{ path('app_admin_tcs_questionnaire_tcs_delete', {'id': questionnaire_tcs.id}) }}" class="btn btn-eduprat btn-icon" title="Supprimer le questionnaire"><i class="fa fa-trash-o"></i></a>{% endif %}
        </div>
    </header>
    <div class="block-nathan">
        {% for keyTCS, tcs in questionnaire_tcs.groupeQuestionsTCS %}
            <div class="abime">
                <header class="js-elemOpenner">
                    <h2>
                        <i class="fa fa-angle-up"></i>
                        TCS {{ keyTCS + 1}}
                    </h2>
                        <div class="right">
                            <a href="{{ path('admin_tcs_groupequestiontcs_edit', {'id': tcs.id}) }}" class="btn btn-eduprat btn-icon" title="Modifier"><i class="glyphicon glyphicon-edit"></i></a>
                            {% if isEditable %}
                                <a href="{{ path('admin_tcs_groupequestiontcs_delete', {'id': tcs.id}) }}" class="btn btn-danger action-btn btn-icon" title="Supprimer"><i class="fa fa-trash-o"></i></a>
                            {% endif %}
                        </div>
                </header>
                <div class="js-opennable hide">
                    {% if tcs.descriptionImage %}<img src="{{ asset('uploads/questionTCSPicture/'~ tcs.descriptionImage) }}" alt="" style="display: block;" width="auto" height="128" />{% endif %}
                    <div class="legend">Description</div>
                    <p>{{ tcs.description|striptags|raw }}</p>

                    <div class="parent-block-nathan-blue">
                        {% for keyQuestion, question in tcs.questionsTCS %}
                            <div class="block-nathan-blue">
                                <header class="js-elemOpenner">
                                    <h3>
                                        <i class="fa fa-angle-up"></i>
                                        Question #{{ keyTCS + 1 }}-{{ keyQuestion + 1 }}
                                    </h3>
                                        <div class="right">
                                            <a href="{{ path('app_admin_tcs_question_tcs_edit', {'id': question.id}) }}" class="btn btn-eduprat btn-icon" title="Modifier"><i class="glyphicon glyphicon-edit"></i></a>
                                            {% if isEditable %}
                                                <a href="{{ path('app_admin_tcs_question_tcs_delete', {'id': question.id}) }}" class="btn btn-danger action-btn btn-icon" title="Supprimer"><i class="fa fa-trash-o"></i></a>
                                            {% endif %}
                                        </div>
                                </header>
                                <div class="js-opennable hide">
                                    <table class="table-action rwd-table">
                                        <thead>
                                        <tr>
                                            <th width="33%">Si vous pensez...</th>
                                            <th width="33%">Et qu'alors vous trouvez...</th>
                                            <th width="33%">Votre hypothèse ou option en est ...</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>{{ question.libelleSiVousPensiez|raw }}</td>
                                            <td>{{ question.libelleEtQuAlorsVousTrouvez|raw }}</td>
                                            <td class="reponse">
                                                {% for reponse in question.reponses %}
                                                    <div {% if reponse.isBestAnswer %} style="color:green"{% endif %}>{{ reponse.reponse }} ({{ reponse.nbVoteExpert }}/{{ nbExpertCompletRepondant }})</div><br>
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        {% endfor %}
                        {% if isEditable %}
                            <a href="{{ path('app_admin_tcs_groupe_question_add_question', {'id': tcs.id}) }}" class="btn btn-eduprat" title="Ajouter une question"><i class="fa fa-plus"></i>Ajouter une question</a>
                        {% endif %}
                    </div>
                    <div class="legend">Synthèse Educative</div>
                    <p>{{ tcs.syntheseEducative|raw }}</p>
                    {% if tcs.syntheseEducativeImage %}<img src="{{ asset('uploads/questionTCSPicture/'~ tcs.syntheseEducativeImage) }}" alt="" style="display: block;" width="auto" height="128" />{% endif %}
                </div>
            </div>
        {% endfor %}
        {% if isEditable %}
            <div class="flex-right">
                <a href="{{ path('app_admin_tcs_questionnaire_tcs_add_groupe_question', {'id': questionnaire_tcs.id}) }}" class="btn btn-eduprat"><i class="fa fa-plus"></i>Ajouter un nouveau TCS</a>
            </div>
        {% endif %}

        <div class="abime">
            <header>
                <h2>Experts</h2>
            </header>
            {% for questionnaireTCSExpert in questionnaire_tcs.questionnaireTCSExperts %}
                <div style="display:flex; gap:10px;width: 100%;">
                    <li class="{% if not questionnaireTCSExpert.isReponduCompletement %}tcs_grp_uncomplete{% endif %}" style="text-transform: uppercase; font-weight: bold;margin-top: 1px;width:12%">{{ questionnaireTCSExpert.expert.fullname }}</li>
                        <a href="{{ path('app_admin_tcs_questionnaire_tcs_answer', {'questionnaireTcsExpert': questionnaireTCSExpert.id}) }}" style="margin-top: 2px;"><i class="glyphicon glyphicon-edit"></i></a>
                        {% if isEditable %}
                            <a href="{{ path('app_admin_tcs_question_tcs_expert_answers_delete', {'questionnaireTcsExpert': questionnaireTCSExpert.id}) }}" style="color: #dd4b39; font-size: 16px;"><i class="fa fa-trash-o"></i></a>
                        {% endif %}
                </div>
            {% endfor %}
            {% if isEditable %}
                <div style="display:flex; gap: 10px">
                    {% if experts|length %}
                        <select name="expertsList" id="expertsList" style="max-height:34px">
                            <option value="">Sélectionnez un expert</option>
                            {% for expert in experts %}
                                <option value="{{expert.id}}">{{ expert.fullname }}</option>
                            {% endfor %}
                        </select>
                        <a id="expert_answer" data-href="{{ path('app_admin_tcs_questionnaire_create_tcs_answer', {'id': questionnaire_tcs.id, 'expert': 'idExpert'}) }}"><input class="btn btn-eduprat" type="submit" value="Répondre pour cet expert"></input>
                    {% endif %}
                    <a href="{{ url('app_eduprat_admin_bundle_controller_expert_new', {redirectQuestionnaire: questionnaire_tcs.id}) }}"><input class="btn btn-eduprat" type="submit" value="Créer un expert et répondre"></input></a>
                </div>
            {% endif %}
        </div>
    </div>
    <div class="block-nathan">
        <div class="abime">
            <header>
                <h2>Règles d'utilisation</h2>
            </header>
        
            <ul>
                <li>Vous pouvez supprimer toutes les réponses d'un expert tant que le formulaire n'est pas associé à une session.</li>
                <li>Vous pouvez modifier les justifications des experts à tout moment.</li>
                <li>Vous ne pouvez plus ajouter ou supprimer des réponses d'experts lorsque le formulaire est associé à une session.</li>
                <li>Assurez-vous que le formulaire contient au moins deux réponses d'experts avant de l'associer à une session.</li>
            </ul>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>

    let animationSpeed = $.AdminLTE.options.animationSpeed;
    $(document).off('click',  '.js-elemOpenner')
        .on('click', '.js-elemOpenner', function (e) {
            //Get the clicked link and the next element
            var $this = $(this);
            var checkElement = $this.next();

            //Check if the next element is a menu and is visible
            if ((checkElement.is('.js-opennable')) && (checkElement.is(':visible'))) {
                //Close the menu
                checkElement.slideUp(animationSpeed, function () {
                    checkElement.addClass('hide');
                });
                $this.removeClass("active");
            } else if ((checkElement.is('.js-opennable')) && (!checkElement.is(':visible'))) {
                checkElement.slideUp(animationSpeed);
                checkElement.removeClass('menu-open');

                checkElement.removeClass('hide');
                checkElement.slideDown(animationSpeed, function () {
                    $this.addClass("active");
                });
            }
        });

    $('#expert_answer').click(function (e) {
        var $this = $(this);
        var link = $this.data('href');
        if ($('#expertsList').val()) {
            link = link.replace('idExpert', $('#expertsList').val());
            window.location = link;
        } else {
            alert("Veuillez séléctionner un expert");
            $('#expertsList').focus();
        }
    });

    </script>
{% endblock %}
