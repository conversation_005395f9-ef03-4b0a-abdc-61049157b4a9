{% extends 'admin/base.html.twig' %}

{% block title %}Liste des questionnaires TCS{% endblock %}

{% block body %}
{% if form is defined %}
    <div class="box box-solid box-primary">
        <div class="box-header">
            <h3 class="box-title">Rechercher un questionnaire TCS</h3>
        </div>
        <div class="box-body eduprat-search">
            {{ form_start(form, {'attr': {'class': 'form-inline'}}) }}
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-inline">
                        {% for key,field in form %}
                            {% if key != '_token' %}
                                <div class="form-group mrm">
                                    {{ form_label(field) }}
                                    <br>
                                    {{ form_widget(field) }}
                                </div>
                            {% endif %}
                        {% endfor %}
                        <div class="pull-right">
                            {% if form|length > 2 %} <br>{% endif %}
                            <button class="btn btn-eduprat" type="submit">
                                <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {{ form_end(form) }}
        </div>
    </div>
{% endif %}
<div class="box box-info">
    <div class="box-body">
        <table class="table">
            <thead>
                <tr>
                    <th>Titre</th>
                    <th>Thématique</th>
                    <th>Nb d'experts (complet)</th>
                    <th>Dernière modification</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            {% for questionnaire_tc in questionnaire_tcs %}
                <tr>
                    <td>{{ questionnaire_tc.label }}</td>
                    <td>{{ questionnaire_tc.thematique.name }}</td>
                    <td>{{ questionnaire_tc.nbExpertCompletRepondant }}</td>
                    <td>{% if questionnaire_tc.updatedAt %}{{ questionnaire_tc.updatedAt|date('d/m/Y H:i:s') }}{% endif %}</td>
                    <td>
                        <a href="{{ path('app_admin_tcs_questionnaire_tcs_show', {'id': questionnaire_tc.id}) }}" class="btn btn-eduprat btn-icon" title="Voir">
                            <i class="fa fa-eye"></i>
                        </a>
                        <a href="{{ path('app_admin_tcs_questionnaire_tcs_edit', {'id': questionnaire_tc.id}) }}" class="btn btn-eduprat btn-icon" title="Modifier">
                            <i class="fa fa-edit"></i>
                        </a>
                        <a href="{{ path('app_admin_tcs_questionnaire_tcs_duplicate', {'id': questionnaire_tc.id}) }}" class="btn btn-eduprat btn-icon" title="Dupliquer">
                            <i class="fa fa-files-o"></i>
                        </a>
                        <a href="{{ path('app_admin_tcs_questionnaire_tcs_archive', {'id': questionnaire_tc.id}) }}" class="btn btn-eduprat btn-icon" title="Archiver">
                            <i class="fa fa-archive"></i>
                        </a>
                        {% if questionnaire_tc.isEditable %}
                            <a href="{{ path('app_admin_tcs_questionnaire_tcs_delete', {'id': questionnaire_tc.id}) }}" class="btn btn-eduprat btn-icon" title="Supprimer le questionnaire"><i class="fa fa-trash-o"></i></a>
                        {% endif %}
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td colspan="5">Aucun questionnaire trouvé</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        <div class="row">
            <div class="col-sm-12">
                {% if npages > 1 %}
                    {% include '@AlienorApi/Includes/paginationBootstrap.html.twig' with {urlName : 'app_admin_tcs_questionnaire_tcs_index' } %}
                {% endif %}
            </div>
        </div>
        <a href="{{ path('app_admin_tcs_questionnaire_tcs_new') }}" class="btn btn-eduprat"><i class="fa fa-plus"></i>{{ "admin.global.add_new"|trans }}</a>
    </div>
</div>
{% endblock %}
