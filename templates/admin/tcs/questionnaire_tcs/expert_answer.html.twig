{% extends 'admin/base.html.twig' %}

{% block title %}Réponse de l'expert : <b>{{ questionnaireTcsExpert.expert.fullName }}</b> {% endblock %}

{% block body %}

    <div class="tcs_grp">
        {% set previousTcsComplete = true %}
        {% for tcs in questionnaire_tcs.groupeQuestionsTCS %}
                {% set isCompleted = questionnaireTcsExpert.answerGroupComplete(tcs)%}
                {% set isDisabled = not previousTcsComplete %}
                {% set class = "tcs_grp_complete" %}

                {% if isDisabled %}
                    {% set class = "tcs_grp_disabled" %}
                {% elseif not isCompleted %}
                    {% set class = "tcs_grp_uncomplete" %}
                {% endif %}

                <a {% if not isDisabled %}href="{{ path('app_admin_tcs_questionnaire_tcs_answer_group', {'questionnaireTcsExpert': questionnaireTcsExpert.id, 'groupeQuestionTCS': tcs.id}) }}"{% endif %} class="{{ class }} {% if groupeQuestionTCS == tcs %}tcs_group_selected{% endif %}"> TCS{{ loop.index }}</a>  
                {% set previousTcsComplete = questionnaireTcsExpert.answerGroupComplete(tcs) %}
        {% endfor %}
    </div>

    <p class="text-center">{{ groupeQuestionTCS.description|striptags|raw }}</p>
    {{ form_start(form) }}
    {% set cptAnswer = 0 %}   
    {% set childs = form.children['expertTCSAnswers'] %}
    {% for child in childs %} 
        {% set question = groupeQuestionTCS.questionsTCS[cptAnswer] %}
        <table class="table-action rwd-table">
            <thead>
            <tr>
                <th width="33%">Si vous pensez...</th>
                <th width="33%">Et qu'alors vous trouvez...</th>
                <th width="33%">Votre hypothèse ou option en est...</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>{{ question.libelleSiVousPensiez|raw }}</td>
                <td>{{ question.libelleEtQuAlorsVousTrouvez|raw }}</td>
                <td class="reponse">
                {{ form_row(child.reponseTCS) }}
                </td>
            </tr>
            </tbody>
        </table>
        <label class="control-label required" for="eduprat_domainbundle_elearning_year">Justificatif</label>
        {{ form_row(child.justification) }}
        {% set cptAnswer = cptAnswer + 1 %}  
        {% if childs|length != loop.index %}   
            <br>
        {% endif %}
              
    {% endfor %}

        <button class="btn btn-eduprat">{{ button_label|default('Enregistrer') }}</button>
    {{ form_end(form) }}
{% endblock %}
