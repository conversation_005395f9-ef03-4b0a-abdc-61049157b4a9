{{ form_start(form) }}
    {{ form_row(form.libelleSiVousPensiez) }}
    {{ form_row(form.libelleEtQuAlorsVousTrouvez) }}
    {{ form_row(form.category) }}
    {{ form_label(form.reponses) }}
    {{ form_errors(form.reponses) }}
    <ul class="responses"
        data-index="{{ form.reponses|length > 0 ? form.reponses|last.vars.name + 1 : 0 }}"
{#        data-prototype="{{ form_widget(form.reponses.vars.prototype)|e('html_attr') }}"#}
    >
        {% for reponse in form.reponses %}
            <li>{{ form_row(reponse.reponse) }}</li>
        {% endfor %}
    </ul>
    {% do form.reponses.setRendered %}

{#    {% if questionnaire_tcs.isEditable %}#}
{#        <p>#}
{#            <button type="button" class="add_reponse_link btn btn-eduprat" data-collection-holder-class="responses">Ajouter une réponse</button>#}
{#        </p>#}
{#    {% endif %}#}
    <button class="btn btn-eduprat">{{ button_label|default('Save') }}</button>

{{ form_end(form) }}

{#<script>#}
{#    {% if questionnaire_tcs.isEditable %}#}
{#        document#}
{#            .querySelectorAll('.add_reponse_link')#}
{#            .forEach(btn => {#}
{#                btn.addEventListener("click", addFormToCollection)#}
{#            });#}

{#        document#}
{#            .querySelectorAll('ul.responses li')#}
{#            .forEach((tag) => {#}
{#                addTagFormDeleteLink(tag)#}
{#            });#}

{#        function addFormToCollection(e) {#}
{#            const collectionHolder = document.querySelector('.' + e.currentTarget.dataset.collectionHolderClass);#}

{#            const item = document.createElement('li');#}

{#            item.innerHTML = collectionHolder#}
{#                .dataset#}
{#                .prototype#}
{#                .replace(#}
{#                    /__name__/g,#}
{#                    collectionHolder.dataset.index#}
{#                );#}

{#            collectionHolder.appendChild(item);#}

{#            collectionHolder.dataset.index++;#}

{#            // add a delete link to the new form#}
{#            addTagFormDeleteLink(item);#}
{#        };#}

{#        function addTagFormDeleteLink(item) {#}
{#            const removeFormButton = document.createElement('button');#}
{#            removeFormButton.innerText = 'Supprimer cette réponse';#}

{#            item.append(removeFormButton);#}

{#            removeFormButton.addEventListener('click', (e) => {#}
{#                e.preventDefault();#}
{#                // remove the li for the tag form#}
{#                item.remove();#}
{#            });#}
{#        }#}
{#    {% endif %}#}
{#</script>#}
