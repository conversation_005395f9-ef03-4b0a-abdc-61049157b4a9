{% if npages > 1 %}
    {% if extraParams is not defined %}
        {% set extraParams = {} %}
    {% endif %}
    <div class="mtl text-center">
        <ul class="pagination">
            {% for p in page_range %}
                {% if loop.first and p > 1 %}
                    <li data-page="1"><a href="{{ path(urlName, extraParams) }}">{{ 'global.page' | trans }} 1</a></li>
                    {% if p > 2 %}
                        <li class="disabled"><span>...</span></li>
                    {% endif %}
                {% endif %}
                {% if p == page %}
                    <li class="active">	<span>{{ 'global.page' | trans }} {{ p }}</span></li>
                {% else %}
                    <li data-page="{{ p }}"><a href="{{ path(urlName, { page: p }|merge(extraParams)) }}">{{ 'global.page' | trans }} {{ p }}</a></li>
                {% endif %}
                {% if loop.last and p < npages %}
                    {% if p < (npages-1) %}
                        <li class="disabled"><span>...</span></li>
                    {% endif %}
                    <li data-page="{{ npages }}"><a href="{{ path(urlName, { page: npages }|merge(extraParams)) }}">{{ 'global.page' | trans }} {{ npages }}</a></li>
                {% endif %}
            {% endfor %}
        </ul>
    </div>
{% endif %}
