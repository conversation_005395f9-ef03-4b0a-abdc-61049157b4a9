{% extends 'admin/base.html.twig' %}

{% block title %}Audit{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-header">
            <h3 class="box-title">{{ "audit.category.titles.index"|trans }}</h3>
        </div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.audit.category.name"|trans }}</th>
                            <th>{{ 'admin.global.actions' | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for category in auditCategories %}
                            <tr>
                                <td>{{ category.name }}</td>
                                <td>
                                    <a href="{{ url('admin_audit_category_edit', {'id': category.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                    <a href="{{ url('admin_audit_category_delete', {'id': category.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
