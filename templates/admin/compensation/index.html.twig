{% extends 'admin/base.html.twig' %}

{% block title %}{{ "admin.compensation.title"|trans }}{% endblock %}

{% block body %}
    <div class="box box-primary">
        <div class="box-body">

            {% for profession,value in compensations %}
                <div class="box-header">
                    <h3 class="box-title">{{ "admin.compensation.profession"|trans }} : {{ profession }}</h3>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>{{ "admin.compensation.nbHours"|trans }}</th>
                                    <th>{{ "admin.compensation.compensationParticiant"|trans }}</th>
                                    <th>{{ "admin.compensation.price"|trans }}</th>
                                    <th>{{ "admin.compensation.budgetCR"|trans }}</th>
                                    <th>{{ "admin.global.actions"|trans }}</th>
                                </tr>
                            </thead>
                            <tbody>
                            {% for compensation in value %}
                                <tr>
                                    <td>{{ compensation.nbHours }}</td>
                                    <td>{{ compensation.compensationParticiant }}</td>
                                    <td>{{ compensation.price }}</td>
                                    <td>{{ compensation.budgetCR }}</td>
                                    <td>
                                        {% if is_granted('ROLE_WEBMASTER') %}
                                            <a href="{{ url('admin_compensation_edit', {'id': compensation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                            <a href="{{ url('admin_compensation_delete', {'id': compensation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <a href="{{ url('admin_compensation_create', {'profession': profession }) }}" class="btn btn-eduprat" title="{{ 'admin.global.edit'|trans }}"><i class="fa fa-plus"></i> Ajouter une ligne pour la profession "{{ profession }}"</a>
                        {% endif %}
                    </div>
                </div>
                <br>
            {% endfor %}
        </div>
    </div>
{% endblock %}