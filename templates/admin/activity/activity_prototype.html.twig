<li class="panel" data-index="{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
    <h4 class="box-title">
        <a data-toggle="collapse" data-parent="#activities" href="#collapse-a{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}">
            <i class="fa fa-caret-down"></i> Activité # <span class="activity-index">{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}</span>
        </a>
    </h4>
    <div id="collapse-a{% if prototype is not defined and loop is defined %}{% if loop.parent.loop is defined %}{{ loop.parent.loop.index }}-{% endif %}{{ loop.index }}{% else %}__name__{% endif %}" class="panel-collapse collapse">
        <div class="box-body">
            {{ form_row(activity.label) }}
            {{ form_row(activity.position) }}
            {{ form_row(activity.format) }}
            
            <div class="hidden form-group" data-type="video">
                {{ form_label(activity.videoUrl) }}
                <span class="required" title="Ce champ est obligatoire">*</span>
                {{ form_widget(activity.videoUrl) }}
            </div>
            <div class="hidden" data-type="picture">
                <div class="form-group vich-image">
                    <label class="control-label">Image</label>
                    {{ form_row(activity.pictureFile) }}
                </div>
                {{ form_row(activity.pictureForm) }}
            </div>
            <div class="hidden" data-type="pdf">
                <div class="form-group vich-file">
                    <label class="control-label">Fichier pdf</label>
                    {{ form_row(activity.pdfFile) }}
                </div>
                {{ form_row(activity.isNotDownloadablePdf) }}
            </div>
            <div class="hidden" data-type="powerpoint">
                <div class="form-group vich-file">
                    <label class="control-label">Fichier zip</label>
                    {{ form_row(activity.powerpointFile) }}
                </div>
            </div>
            <div class="hidden form-group" data-type="prezi">
                {{ form_label(activity.preziUrl) }}
                <span class="required" title="Ce champ est obligatoire">*</span>
                {{ form_widget(activity.preziUrl) }}
            </div>
            <div class="hidden form-group" data-type="bibliography">
                <label class="control-label">Bibliographies</label>
                <ul class="bibliographies" data-prototype="{% apply escape %}{{ include('admin/activity/bibliography_prototype.html.twig', { 'bibliography': activity.bibliographies.vars.prototype }) }}{% endapply %}">
                    {% for bibliography in activity.bibliographies %}
                        {{ include('admin/activity/bibliography_prototype.html.twig', { 'bibliography': bibliography, 'loop' : loop  }) }}
                    {% endfor %}
                </ul>
            </div>
            {# <div class="hidden form-group" data-type="bibliography">
                {{ form_label(activity.biblioLabel) }}
                <span class="required" title="Ce champ est obligatoire">*</span>
                {{ form_widget(activity.biblioLabel) }}
                {{ form_label(activity.biblioUrl) }}
                <span class="required" title="Ce champ est obligatoire">*</span>
                {{ form_widget(activity.biblioUrl) }}
            </div> #}
            <div class="hidden form-group" data-type="text">
                <div class="form-group vich-image">
                    <label class="control-label">Image</label>
                    {{ form_row(activity.pictureTextFile) }}
                </div>
                {{ form_row(activity.pictureTextForm) }}
                {{ form_label(activity.text) }}
                <span class="required" title="Ce champ est obligatoire">*</span>
                {{ form_widget(activity.text) }}
            </div>
            <div class="hidden form-group" data-type="quiz">
                <label class="control-label">Questions</label>
                <ul class="questions" data-prototype="{% apply escape %}{{ include('admin/activity/question_prototype.html.twig', { 'question': activity.questions.vars.prototype }) }}{% endapply %}">
                    {% for question in activity.questions %}
                        {{ include('admin/activity/question_prototype.html.twig', { 'question': question, 'loop' : loop  }) }}
                    {% endfor %}
                </ul>
            </div>
            <div class="hidden form-group" data-type="file_list">
                <i>Types de fichiers acceptés : PDF</i>
                <br>
                <ul class="files doc-files" data-prototype="{{ form_widget(activity.files.vars.prototype)|e('html_attr') }}">
                    {% for file in activity.files %}
                        <li class="files_list">
                            <label>{{ file.vars.value.name }}</label>
                            <br>
                            <img src="{{ file.vars.value.relativeUrl }}" alt="">
                            {{ form_row(file) }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
            
            {#
            {{ form_row(question.interpretationTrueTrue) }}
            {{ form_row(question.interpretationTrueFalse) }}
            {{ form_row(question.interpretationFalseFalse) }}
            {{ form_row(question.interpretationFalseTrue) }}
            {{ form_row(question.interpretation) }}
            {{ form_row(question.answer) }}
            {{ form_row(question.required) }} #}
        </div>
    </div>
</li>