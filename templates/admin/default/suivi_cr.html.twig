{% extends 'admin/base.html.twig' %}

{% block title %}Bilan {{ year }}{% endblock %}

{% block body %}
<p>
    <em>Consulter les bilans des années précédentes :</em>
</p>
<ul class="pagination">
    {% set maxYear = "now"|date('Y') + 1 %}
    {% for dateYear in first_year..maxYear %}
        <li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('admin_suivi_cr', {person: supervisor.id, year: dateYear}) }}">{{ dateYear }}</a></li>
    {% endfor %}
</ul>
<div class="box box-info">
    <div class="box-body">
        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-12">
                    <table class="table table-bordered table-responsive table-hover table-totaux">
                        <tr>
                            <th>CA Eduprat France</th>
                            <td>{{ coordinatorsTotalCaSecteurByCoordinateur|number_format(2, '.', ' ') }} €</td>
                        </tr>
                        {#<tr>#}
                        {#<th>Total médecins France</th>#}
                        {#<td>{{ supervisor.coordinatorsParticipationsByCoordinator(year) | length }}</td>#}
                        {#</tr>#}
                        <tr>
                            <th>Coût total orateurs</th>
                            <td data-async="{{ url('admin_suivi_cr_async', {person: supervisor.id, year: year, field: "coordinatorsTotalFormerHonorary"}) }}"><i class="fa fa-spin fa-spinner"></i></td>
                        </tr>
                        <tr>
                            <th>Coût total restaurants</th>
                            <td data-async="{{ url('admin_suivi_cr_async', {person: supervisor.id, year: year, field: "coordinatorsTotalRestaurationHonoraryByCoordinator"}) }}"><i class="fa fa-spin fa-spinner"></i></td>
                        </tr>
                    </table>
                </div>
                <div class="col-lg-6 col-sm-12">
                    <table class="table table-bordered table-responsive table-hover table-totaux3">
                        <tr>
                            <th>Nombre de participations totales</th>
                            <td>{{ counts.totals.participations }}</td>
                        </tr>
                        {% for category, count in counts.categories %}
                            <tr>
                                <th>Participations {{ category }}</th>
                                <td>{{ count.participations }}</td>
                            </tr>
                        {% endfor %}
                    </table>
                </div>
                <div class="col-lg-6 col-sm-12">
                    <table class="table table-bordered table-responsive table-hover table-totaux3">
                        <tr>
                            <th>Nombre de participants uniques totaux</th>
                            <td>{{ counts.totals.participants }}</td>
                        </tr>
                        {% for category, count in counts.categories %}
                            <tr>
                                <th>Participants uniques {{ category }}</th>
                                <td>{{ count.participants }}</td>
                            </tr>
                        {% endfor %}
                    </table>
                </div>
                <div class="col-lg-12">
                    <div class="callout callout-warning">
                        <p>* Les calculs des commissions se basent sur la totalité des formations de l'année.</p>
                    </div>
                </div>
                {% for coordinator in supervisor.coordinatorsPerson %}
                        <table class="table table-bordered table-responsive table-hover table-sessions">
                            <tr class="programmeWrap">
                                <td class="sessionsTitle">{{ coordinator.invertedFullname }}
                                    <div style="float:right;">
                                        <a href="{{ url('admin_coordinator_dashboard', {'id': coordinator.id, 'year': year }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.user.coordinator_dashboard'|trans }}"><i class="fa fa-bar-chart-o"></i></a>
                                        <a href="#" data-url="{{ url('admin_suivi_cr_detail', { person: supervisor.id, year: year, coordinator: coordinator.id }) }}" class="mrm sessionsListLink btn btn-eduprat mbn suivi-load-detail"><i class="fa fa-angle-right"></i> Détails coordinateur</a>
                                    </div>
                                </td>
                            </tr>
                            <tr class="table-loader text-center hidden">
                                <td>
                                    <i class="fa fa-spin fa-spinner"></i>
                                </td>
                            </tr>
                        </table>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock body %}



{% block javascripts %}
    <script>
		$(document).ready(function() {
            $(document).on('click', ".suivi-load-detail", function() {
            	$(this).removeClass('suivi-load-detail');
            	var $table = $(this).closest('table');
	            $table.find('.table-loader').removeClass('hidden');
	            jQuery.ajax({
		            url: $(this).data('url'),
		            type: 'GET',
		            dataType: 'html',
		            success: function(data, textStatus, xhr) {
		            	$data = $(data);
		            	$table.replaceWith($data);

		            	setTimeout(function() {
				            $data.find(".sessionsListLink").off('click').click(function(e){
					            e.preventDefault();
					            $(this).closest('tr').parent().find(".sessionsList").toggle();
					            $(this).toggleClass("active");
					            $(this).parent().parent().toggleClass('current');
				            });

				            $data.find(".formationsList").hide();
				            $data.find(".formationsListLink").off('click').click(function(e){
					            e.preventDefault();

					            $(this).closest('tr').next('.formationsWrap').find(".formationsList").toggle();
					            $(this).toggleClass("active");
					            $(this).parent().parent().toggleClass('current');
				            });
                        }, 20)


		            },
	            });
            });

            $("[data-async]").each(function () {
            	var that = this;
            	$.getJSON($(that).data("async"), function( data ) {
		            $(that).html(data.value.toLocaleString() + " €");
	            });
            })
		});
    </script>
{% endblock %}

