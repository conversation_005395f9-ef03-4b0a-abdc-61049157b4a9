{% extends 'admin/base.html.twig' %}

{% block title %}Tableau de bord {{ year }} - {{ person.fullname }}{% endblock %}

{% block stylesheets %}
    <style>
        .box-widget {
            box-shadow: 0px 3px 6px #00000029;
            border-radius: 19px;
            padding: 30px;
        }
        .box-widget h4 {
            font-size: 19px;
            margin: 0 0 5px;
        }

        .progress-value {
            position: absolute;
            left: 10px;
            color: #fff;
            font-weight: bold;
        }

        .progress-wrapper {
            position: relative;
            width: 100%;
        }

        .objectif-value {
            white-space: nowrap;
            flex: 135px;
            flex-grow: 0;
            flex-shrink: 1;
            margin-left: 10px;
            font-size: 19px;
        }
        .objectif-value-text {
            text-align: left;
            font-weight: normal;
            font-size: 14px;
        }

        small {
            color: #888;
            font-size: 11px;
        }

        .progress-parent {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-top: 5px;
        }

        .progress,
        .progress > .progress-bar,
        .progress > .progress-bar-green {
            height: 36px;
            margin: 0;
            border-radius: 10px;
            font-size: 17px;
            display: flex;
            align-items: center;
        }

        .progress-bar-green {
            background-color: #59AC5F;
        }
        .progress-value {
            color: black;
        }
        .info-block {
            margin-top: 15px;
        }

        /* Progress bar verticale */
        .progress-vertical {
            width: 36px;
            height: 200px;
            background-color: #f5f5f5;
            border-radius: 10px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            margin: 0;
        }

        .progress-bar-vertical {
            width: 100%;
            background-color: #59AC5F;
            border-radius: 0 0 10px 10px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 20px;
        }

        .progress-value-vertical {
            color: white;
            font-weight: bold;
            font-size: 12px;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            white-space: nowrap;
        }

        .progress-parent-vertical {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .progress-wrapper-vertical {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .objectif-value-vertical {
            font-size: 14px;
            font-weight: normal;
            text-align: center;
        }
    </style>
{% endblock %}

{% block body %}
    <p>
        <em>Consulter les tableaux de bords des années précédentes :</em>
    </p>
    <ul class="pagination">
        {% set maxYear = "now"|date('Y') + 1 %}
        {% for dateYear in first_year..maxYear %}
            <li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('admin_coordinator_dashboard', {id: person.id, year: dateYear}) }}">{{ dateYear }}</a></li>
        {% endfor %}
    </ul>
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="callout callout-warning">
                        <p>* Le cumul des marges et les calculs des commissions se basent sur la totalité des formations de l'année.</p>
                    </div>
                    {% if not person.isCoordinatorLbi %}
                        {% if person.crIsSalarie() and person.loadObjectifOf(year) %}
                            <div class="col-sm-12 col-lg-4">
                                <div class="box box-widget">
                                    <h4 class="text-muted">Prévisionnel / réalisé :</h4>

                                    <!-- Chiffre d'affaires -->
                                    <div class="info-block">
                                        <div class="progress-parent">
                                            <div style="width: 100%">
                                                <strong>Chiffre d'affaires</strong>
                                            </div>
                                            <div class="objectif-value objectif-value-text">Objectif</div>
                                        </div>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.CAPercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.CARealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.CAObjectif|number_format(2, '.', ' ') }}€</div>
                                        </div>
                                    </div>

                                    <!-- Cumul de marges -->
                                    <div class="info-block">
                                        <strong>Cumul de marges</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.cumulMargePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.cumulMargeRealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.cumulMargeObjectif|number_format(2, '.', ' ') }}€<sup>**</sup></div>
                                        </div>
                                        <small>**Cumul Marges = CA Total – Coûts Experts – Coûts restauration</small>
                                    </div>

                                    <!-- Prime annuelle -->
                                    <div class="info-block">
                                        <strong>Prime annuelle</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.primeAnnuellePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.primeAnnuelleRealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.primeAnnuelleObjectif|number_format(2, '.', ' ') }}€</div>
                                        </div>
                                    </div>

                                    <!-- Taux primes -->
                                    <div class="info-block">
                                        <strong>Taux primes</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.tauxPrimePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.tauxPrimeRealise }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.tauxPrimeObjectif }}%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                        <div class="col-sm-12 col-lg-4">
                            <div class="box box-widget">
                                <h4 class="text-muted">Prévisionnel / réalisé :</h4>

                                <!-- Chiffre d'affaires -->
                                <div class="info-block">
                                    <table class="table table-bordered table-responsive table-hover table-totaux">
                                        <tr>
                                            <th>Chiffre d'affaires</th>
                                            <td>{{ person.allCoordinatorCaSecteurByCoordinator(year)|number_format(2, '.', ' ') }} €</td>
                                        </tr>
                                        <tr>
                                            <th>Cumul de marges</th>
                                            <td>{{ total.marges|number_format(2, '.', ' ') }} €</td>
                                        </tr>
                                        <tr>
                                            <th>Commissions taux de base (34%)</th>
                                            <td>{{ total.commissionBase|number_format(2, '.', ' ') }} €</td>
                                        </tr>
                                        <tr>
                                            <th>Commission actualisée ({{ (person.getTauxActualise(total.marges)) }}%)</th>
                                            <td>{{ total.commissionActualisee|number_format(2, '.', ' ') }} €</td>
                                        </tr>
                                        <tr>
                                            <th>Commission exceptionnelle*</th>
                                            <td>{{ total.commissionExceptionnelle|number_format(2, '.', ' ') }} €</td>
                                        </tr>
                                        {% if is_granted('ROLE_WEBMASTER') %}
                                            {% set totalHonorary = person.getAllCoordinatorsCommissionsComptabilisees(year) %}
                                            <tr>
                                                <th>Commissions comptabilisées</th>
                                                <td>{{ totalHonorary|number_format(2, '.', ' ') }} €</td>
                                            </tr>
                                            <tr>
                                                <th>Écart commissions</th>
                                                <td>{{ (total.commissionBase - totalHonorary)|number_format(2, '.', ' ') }} €</td>
                                            </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% endif %}
                    <div class="col-sm-12 col-lg-4">
                        <div class="box box-widget">
                            <h4 class="text-muted">Cumul de marges actuel</h4>

                            <!-- Montant principal -->
                            <div style="margin-bottom: 20px;">
                                <strong style="font-size: 28px; font-weight: bold;">{{ CAObjectifRealiseDTO.cumulMargeRealise|number_format(2, ',', ' ') }}€</strong>
                            </div>

                            <div style="display: flex; gap: 20px;">
                                <!-- Section gauche: Plus que -->
                                <div style="flex: 0 0 28%;">
                                    {% if CAObjectifRealiseDTO.nextPalierEcart %}
                                        <div style="margin-top: 2em;">
                                            <strong>Plus que</strong><br><br>
                                            <strong style="font-size: 20px; margin-top: 3em;">{{ CAObjectifRealiseDTO.nextPalierEcart|number_format(2, '.', ' ') }}€ de CA</strong><br>
                                            <strong>pour atteindre le prochain palier</strong>
                                        </div>
                                        {% if CAObjectifRealiseDTO.nbParticipationsToNextPalier %}
                                            <div style="font-weight:  bold; margin-top: 2em;">
                                                Soit environ <br>{{ CAObjectifRealiseDTO.nbParticipationsToNextPalier }} inscription{% if CAObjectifRealiseDTO.nbParticipationsToNextPalier > 1 %}s{% endif %}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                </div>

                                <!-- Section droite: Graphique des paliers -->
                                <div style="flex: 1;margin-top:-15px">
                                    <!-- Labels des tranches en haut -->
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px; font-size: 10px; color: #666;">
                                        {% set paliers = CAObjectifRealiseDTO.paliersRestants|slice(0, 5) %}
                                        {% for seuil, info in paliers %}
                                            <div style="text-align: center; flex: 1;">
                                                <div>{{ info["tranche"]|raw }}</div>
                                            </div>
                                        {% endfor %}
                                    </div>

                                    <!-- Barres verticales -->
                                    {% set maxHeight = 220 %}
                                    <div style="display: flex; justify-content: space-between; align-items: flex-end; height: {{ maxHeight }}px; margin-bottom: 8px;">
                                        {% set currentMarge = CAObjectifRealiseDTO.cumulMargeRealise %}
                                        {% for seuil, info in paliers %}
                                            {% set isCurrentPalier = loop.first %}
                                            {% set nextPalier = paliers[loop.index] is defined ?  paliers[loop.index] : null %}
                                            {% set nextPalierSeuil = nextPalier ? nextPalier.value : null %}
                                            {% set percent =  nextPalierSeuil ? ((currentMarge / nextPalierSeuil) * 100) : 100 %}

                                            {% set hauteur = isCurrentPalier ? (percent / 100) * maxHeight : maxHeight %}
                                            <div style="flex: 1; display: flex; justify-content: center;">
                                                <div style="width: 40px; height: {{ hauteur }}px;
                                                    background-color: {% if isCurrentPalier %}#59AC5F{% else %}#e0e0e0{% endif %};
                                                    border-radius: 20px;">
                                                </div>
                                            </div>
                                            {% if isCurrentPalier %}
                                                <div style="position: absolute;margin-left: 11px; opacity: 0.24;">
                                                    <div style="width: 40px; height: {{ maxHeight }}px;
                                                            background-color: #59AC5F;
                                                            border-radius: 20px;">
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>

                                    <!-- Pourcentages en bas -->
                                    <div style="display: flex; justify-content: space-between; font-size: 11px; font-weight: bold; margin-top: -35px;margin-left: 3px;">
                                        {% for seuil, info in paliers %}
                                            {% set isCurrentPalier = loop.first %}
                                            <div style="text-align: center; flex: 1;">
                                                {{ info.taux }}%
                                            </div>
                                        {% endfor %}
                                    </div>

                                    <!-- Label "TAUX ACTUEL" -->
                                    <div style="text-align: left; margin-top: 20px;font-weight: bold">
                                        <div style="font-size: 10px; color: #59AC5F; font-weight: bold; letter-spacing: 1px;">TAUX ACTUEL</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-lg-4">
                        <div class="box box-widget">
                            <h4 class="text-muted">Indicateurs</h4>
                            <div style="margin-top:15px";>
                                <div style="display: flex; gap: 30px;">
                                    <div class="indicator-box indicator-inscriptions">
                                        <a target="_blank" style="color: initial" href="{{ url('admin_crm_comptabilite_inscriptions', { type: "Inscription" }) }}">
                                            <div>
                                                <strong class="indicators-subTitle">Inscriptions</strong>
                                                <div class="indicator-line indicator-first-line">
                                                    <span class="indicator-sub-text">de la semaine</span>
                                                    <span><strong>{{ indicators.weekRegistrations }}</strong></span>
                                                </div>
                                                <div class="indicator-line">
                                                    <span class="indicator-sub-text">du mois en cours</span>
                                                    <span><strong>{{ indicators.monthRegistrations }}</strong></span>
                                                </div>
                                                <div class="indicator-line">
                                                    <span class="indicator-sub-text">à 0€ (sessions à venir)</span>
                                                    <span><strong>{{ indicators.inscriptionsWithoutCosts }}</strong></span>
                                                </div>
                                            </div>
                                        </a>
                                    </div>

                                    <div style="display: flex; flex-direction: column; gap: 16px;">
                                        <div class="indicator-box indicator-desinscriptions">
                                            <a target="_blank" style="color: initial" href="{{ url('admin_crm_comptabilite_inscriptions', { type: "Désinscription" }) }}">
                                                <div class="indicator-flex-bold">
                                                    {% set s =  indicators.weekUnsubscriptions > 1 or indicators.weekUnsubscriptions == 0 ? "s" : "" %}
                                                    <div class="indicators-subTitleNumber">{{ indicators.weekUnsubscriptions }}</div>
                                                    <div class="indactor-midle-title">Désinscription{{ s }}
                                                    <br><span class="subtext subtext-black">de la semaine</span></div>
                                                </div>
                                            </a>
                                        </div>

                                        <div class="indicator-box indicator-missingfiles">
                                            <a target="_blank" style="color: initial" href="{{ url('admin_crm_comptabilite_files', { year: "now"|date('Y') }) }}">
                                                <div class="indicator-flex-bold">
                                                    <div class="indicators-subTitleNumber" style="margin-top: 4px;">{{ indicators.missingFiles }}</div>
                                                    <div class="indactor-midle-title">
                                                        {% set s =  indicators.missingFiles > 1 or indicators.missingFiles == 0 ? "s" : "" %}
                                                        <p style="margin: 0px;">Document{{ s }}</p>
                                                        <p style="margin: 0px;margin-top: -4px;">manquant{{ s }}</p>
                                                        <span class="subtext subtext-black">(factures, conventions, ...)</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div style="display: flex; gap: 30px; margin-top: 16px;">
                                    <div class="indicator-box indicator-missingmodules">
                                        <a target="_blank" style="color: initial" href="{{ url('admin_crm_comptabilite', { year: "now"|date('Y') }) }}">
                                            <div class="indicator-flex-bold">
                                                <div class="indicators-subTitleNumber" style="margin-top: 4px;">{{ indicators.missingModules }}</div>
                                                <div class="indactor-midle-title">
                                                    {% set s =  indicators.missingModules > 1 or indicators.missingModules == 0 ? "s" : "" %}
                                                    <p style="margin: 0px;">Module{{ s }}</p>
                                                    <p style="margin: 0px;margin-top: -4px;">manquant{{ s }}</p>
                                                    <span class="subtext">TOTAL</span>
                                                </div>
                                            </div>
                                            <div class="indicator-line" style="margin-top: 30px;">
                                                <span class="indicator-sub-text">à échéance 72h</span>
                                                <span class="highlight-red"><strong>{{ indicators.missingModules72Hours }}</strong></span>
                                            </div>
                                        </a>
                                    </div>

                                    <div class="indicator-box indicator-missingmodules">
                                        <a target="_blank" style="color: initial" href="{{ url('admin_crm_comptabilite_attestation_files', { year: "now"|date('Y') }) }}">
                                            <div class="indicator-flex-bold">
                                                <div class="indicators-subTitleNumber" style="margin-top: 4px;">{{ indicators.missingAttestations }}</div>
                                                <div class="indactor-midle-title">
                                                    {% set s =  indicators.missingAttestations > 1 or indicators.missingAttestations == 0 ? "s" : "" %}
                                                    <p style="margin: 0px;">Attestation{{ s }}</p>
                                                    <p style="margin: 0px;margin-top: -4px;">manquante{{ s }}</p>
                                                    <span class="subtext">TOTAL</span>
                                                </div>
                                            </div>
                                            <div class="indicator-line" style="margin-top: 10px;">
                                                <span class="indicator-sub-text">à échéance</span><span class="highlight-red"><strong>{{ indicators.missingAttestations2Weeks }}</strong></span>
                                            </div>
                                            <span class="indicator-sub-text">> 2 semaines</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-12">
                        <h4>Synthèse des formations :</h4>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th>Nombre de formations</th>
                                <td>{{ allProgrammes|length }}</td>
                            </tr>
                            <tr>
                                <th>Nombre de participations totales</th>
                                <td>{{ counts.totals.participations }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participations {{ category }}</th>
                                    <td>{{ count.participations }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th colspan="2">&nbsp;</th>
                            </tr>
                            <tr>
                                <th>Nombre de participants uniques totaux</th>
                                <td>{{ counts.totals.participants }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participants uniques {{ category }}</th>
                                    <td>{{ count.participants }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock body %}
