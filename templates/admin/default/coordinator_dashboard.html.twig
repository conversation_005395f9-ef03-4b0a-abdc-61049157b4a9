{% extends 'admin/base.html.twig' %}

{% block title %}Tableau de bord {{ year }} - {{ person.fullname }}{% endblock %}

{% block stylesheets %}
    <style>
        .box-widget {
            box-shadow: 0px 3px 6px #00000029;
            border-radius: 19px;
            padding: 30px;
        }
        .box-widget h4 {
            font-size: 19px;
            margin: 0 0 5px;
        }

        .progress-value {
            position: absolute;
            left: 10px;
            color: #fff;
            font-weight: bold;
        }

        .progress-wrapper {
            position: relative;
            width: 100%;
        }

        .objectif-value {
            white-space: nowrap;
            flex: 135px;
            flex-grow: 0;
            flex-shrink: 1;
            margin-left: 10px;
            font-size: 19px;
        }
        .objectif-value-text {
            text-align: left;
            font-weight: normal;
            font-size: 14px;
        }

        small {
            color: #888;
            font-size: 11px;
        }

        .progress-parent {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-top: 5px;
        }

        .progress,
        .progress > .progress-bar,
        .progress > .progress-bar-green {
            height: 36px;
            margin: 0;
            border-radius: 10px;
            font-size: 17px;
            display: flex;
            align-items: center;
        }

        .progress-bar-green {
            background-color: #59AC5F;
        }
        .progress-value {
            color: black;
        }
        .info-block {
            margin-top: 15px;
        }
    </style>
{% endblock %}

{% block body %}
    <p>
        <em>Consulter les tableaux de bords des années précédentes :</em>
    </p>
    <ul class="pagination">
        {% set maxYear = "now"|date('Y') + 1 %}
        {% for dateYear in first_year..maxYear %}
            <li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('admin_coordinator_dashboard', {id: person.id, year: dateYear}) }}">{{ dateYear }}</a></li>
        {% endfor %}
    </ul>
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <div class="callout callout-warning">
                        <p>* Le cumul des marges et les calculs des commissions se basent sur la totalité des formations de l'année.</p>
                    </div>
                    {% if not person.isCoordinatorLbi %}
                        {% if person.crIsSalarie() and person.loadObjectifOf(year) %}
                            <div class="col-sm-12 col-lg-4">
                                <div class="box box-widget">
                                    <h4 class="text-muted">Prévisionnel / réalisé :</h4>

                                    <!-- Chiffre d'affaires -->
                                    <div class="info-block">
                                        <div class="progress-parent">
                                            <div style="width: 100%">
                                                <strong>Chiffre d'affaires</strong>
                                            </div>
                                            <div class="objectif-value objectif-value-text">Objectif</div>
                                        </div>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.CAPercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.CARealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.CAObjectif|number_format(2, '.', ' ') }}€</div>
                                        </div>
                                    </div>

                                    <!-- Cumul de marges -->
                                    <div class="info-block">
                                        <strong>Cumul de marges</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.cumulMargePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.cumulMargeRealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.cumulMargeObjectif|number_format(2, '.', ' ') }}€<sup>**</sup></div>
                                        </div>
                                        <small>**Cumul Marges = CA Total – Coûts Experts – Coûts restauration</small>
                                    </div>

                                    <!-- Prime annuelle -->
                                    <div class="info-block">
                                        <strong>Prime annuelle</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.primeAnnuellePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.primeAnnuelleRealise|number_format(2, '.', ' ') }}€</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.primeAnnuelleObjectif|number_format(2, '.', ' ') }}€</div>
                                        </div>
                                    </div>

                                    <!-- Taux primes -->
                                    <div class="info-block">
                                        <strong>Taux primes</strong>
                                        <div class="progress-parent">
                                            <div class="progress-wrapper">
                                                <div class="progress">
                                                    <div class="progress-bar progress-bar-green" style="width: {{ CAObjectifRealiseDTO.tauxPrimePercentObjectif }}%;">
                                                        <span class="progress-value">{{ CAObjectifRealiseDTO.tauxPrimeRealise }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="objectif-value">{{ CAObjectifRealiseDTO.tauxPrimeObjectif }}%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="col-sm-12 col-lg-4">
                            <h4>Détails des marges :</h4>
                            <table class="table table-bordered table-responsive table-hover table-totaux">
                                <tr>
                                    <th>CA coordinateur total</th>
                                    <td>{{ person.allCoordinatorCaSecteurByCoordinator(year)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <th>Cumul des marges*</th>
                                    <td>{{ total.marges|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <th>Commissions taux de base*</th>
                                    <td>{{ total.commissionBase|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <th>Commission actualisée*</th>
                                    <td>{{ total.commissionActualisee|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <th>Commission exceptionnelle*</th>
                                    <td>{{ total.commissionExceptionnelle|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                {% if is_granted('ROLE_WEBMASTER') %}
                                    {% set totalHonorary = person.getAllCoordinatorsCommissionsComptabilisees(year) %}
                                    <tr>
                                        <th>Commissions comptabilisées</th>
                                        <td>{{ totalHonorary|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                    <tr>
                                        <th>Écart commissions</th>
                                        <td>{{ (total.commissionBase - totalHonorary)|number_format(2, '.', ' ') }} €</td>
                                    </tr>
                                {% endif %}
                                <tr>
                                    <td colspan="2">
                                        <a class="btn btn-eduprat" target="_blank" href="{{ asset('uploads/Tableau taux d\'intéressement.pdf') }}"><i class="fa fa-download"></i>    Télécharger le tableau d’intéressement</a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        {% endif %}
                    {% endif %}

                    <div class="col-lg-12">
                        <h4>Synthèse des formations :</h4>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th>Nombre de formations</th>
                                <td>{{ allProgrammes|length }}</td>
                            </tr>
                            <tr>
                                <th>Nombre de participations totales</th>
                                <td>{{ counts.totals.participations }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participations {{ category }}</th>
                                    <td>{{ count.participations }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th colspan="2">&nbsp;</th>
                            </tr>
                            <tr>
                                <th>Nombre de participants uniques totaux</th>
                                <td>{{ counts.totals.participants }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participants uniques {{ category }}</th>
                                    <td>{{ count.participants }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock body %}
