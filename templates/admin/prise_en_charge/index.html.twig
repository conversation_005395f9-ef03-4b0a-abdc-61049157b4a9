{% extends 'admin/base.html.twig' %}

{% block title %}Prise en charge{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-header"></div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.priseEnCharge.name"|trans }}</th>
                            <th>{{ 'admin.global.actions' | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for priseEnCharge in priseEnCharges %}
                            <tr>
                                <td>{{ priseEnCharge.name }}</td>
                                <td>
                                    <a href="{{ url('admin_prise_en_charge_edit', {'id': priseEnCharge.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                    <a href="{{ url('admin_prise_en_charge_delete', {'id': priseEnCharge.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
