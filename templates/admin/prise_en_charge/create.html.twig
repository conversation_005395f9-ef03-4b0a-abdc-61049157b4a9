{% extends 'admin/base.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'alienor_finance_mode_create' %}
        {{ 'admin.priseEnCharge.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_finance_mode_edit' %}
        {{ 'admin.priseEnCharge.titles.edit'|trans }}
    {% endif %}
{% endblock %}
{% block body %}
<div class="box box-info">
    <div class="box-body">
        {{ form_start(form) }}
            {{ form_row(form.name) }}
            <b>{{ 'admin.priseEnCharge.form.priseEnChargePicture'|trans }}</b>
            <div class="form-group vich-image">
                {{ form_row(form.priseEnChargePictureFile) }}
            </div>
            {{ form_row(form.priseEnChargePictureFrom) }}
        <button type="submit" class="btn btn-eduprat pull-right">{{ "admin.global.save"|trans }}</button>
        {{ form_widget(form._token) }}
        {{ form_end(form, {'render_rest': false}) }}
    </div>
</div>
{% endblock %}

{% block javascripts %}
    <script>
        $('#eduprat_domainbundle_prise_en_charge_priseEnChargePictureFile_file').change(function() {
            size = this.files[0].size;
            if (size > 2048000) {
                this.value = '';
                alert('Le poids de l\'image que vous souhaitez importer est supérieur à la taille autorisée de 2 Mo.\nMerci de l\'optimiser et réessayer.')
            }
        })
    </script>
{% endblock %}