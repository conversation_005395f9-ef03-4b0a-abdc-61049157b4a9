{% extends 'admin/base.html.twig' %}

{% block title %}Liste des experts{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ 'admin.tcs.expert.first_name'|trans }}</th>
                            <th>{{ 'admin.tcs.expert.last_name'|trans }}</th>
                            <th>{{ 'admin.tcs.expert.email'|trans }}</th>
                            <th>{{ 'admin.global.actions' | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                            {% for expert in experts %}
                                <tr>
                                    <td>{{ expert.firstName }}</td>
                                    <td>{{ expert.lastName }}</td>
                                    <td>{{ expert.email }}</td>
                                    <td>
                                        <a href="{{ path('app_eduprat_admin_bundle_controller_expert_edit', {'id': expert.id}) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                        {# <a href="{{ url('app_eduprat_admin_bundle_controller_expert_delete', {'id': expert.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a> #}
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="5">Aucun expert trouvé</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {# <a href="{{ path('app_eduprat_admin_bundle_controller_expert_new') }}">{{ 'admin.global.add_new'|trans }}</a> #}
{% endblock %}
