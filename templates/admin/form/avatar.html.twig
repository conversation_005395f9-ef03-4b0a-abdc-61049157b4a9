{% block vich_file_row -%}
    {% set force_error = true %}
    {{- block('form_row') }}
{%- endblock %}

{% block vich_file_widget %}
    <div class="vich-file">
        {{ form_widget(form.file) }}
        {% if form.delete is defined %}
        {{ form_row(form.delete) }}
        {% endif %}

        {% if download_uri is defined and download_uri %}
        <a href="{{ download_uri }}">{{ 'download'|trans({}, 'VichUploaderBundle') }}</a>
        {% endif %}
    </div>
{% endblock %}

{% block vich_image_row -%}
    {% set force_error = true %}
    {{- block('form_row') }}
{%- endblock %}

{% block vich_image_widget %}
    <div class="vich-avatar panel panel-eduprat">
            {{ form_widget(form.file) }}
            {% if form.delete is defined %}
                {{ form_row(form.delete) }}
            {% endif %}
            <br>
            {% if image_uri is defined and image_uri %}
                <p>Image sélectionnée :</p>
                <img src="{{ asset_helper is same as(true) ? asset(image_uri) : image_uri }}" alt="" />
            {% else %}
                <p>Image par défaut :</p>
                <img src="{{ asset('img/default-avatar.png') }}" alt="" />
            {% endif %}
    </div>
{% endblock %}
