{% block vich_file_row -%}
    {% set force_error = true %}
    {{- block('form_row') }}
{%- endblock %}

{% block vich_file_widget %}
    <div class="vich-file">
        {{ form_widget(form.file) }}
        {% if form.delete is defined %}
        {{ form_row(form.delete) }}
        {% endif %}

        {% if download_uri is defined and download_uri %}
        <a href="{{ download_uri }}">{{ 'download'|trans({}, 'VichUploaderBundle') }}</a>
        {% endif %}
    </div>
{% endblock %}

{% block vich_image_row -%}
    {% set force_error = true %}
    {{- block('form_row') }}
{%- endblock %}

{% block vich_image_widget %}
    <div class="vich-image">
        {{ form_widget(form.file) }}
        {% if form.delete is defined %}
        {{ form_row(form.delete) }}
        {% endif %}

        {%- if image_uri -%}
            <a href="{{ asset_helper is same as(true) ? asset(image_uri) : image_uri }}" download>
                <img style="{% if attr["max_width"] is defined %}max-width: {{ attr["max_width"] }}px;{% endif %}{% if attr["max_height"] is defined %}max-height: {{ attr["max_height"] }}px;{% endif %}" src="{{ asset_helper is same as(true) ? asset(image_uri) : image_uri }}" alt="" />
            </a>
        {%- endif -%}
        {%- if download_uri -%}
            <a href="{{ asset_helper is same as(true) ? asset(download_uri) : download_uri }}" download>
                {{ translation_domain is same as(false) ? download_label : download_label|trans({}, translation_domain) }}
            </a>
        {%- endif -%}
    </div>
{% endblock %}
