{% extends 'domain/base_mail.html.twig' %}

{% set f = participation.formation %}
{% set p = f.programme %}
{% set person = participation.participant.user %}

{% block body %}
    <div>Bonjour,</div>
    <br>
    <div>Faisant suite à votre participation à la formation  «&nbsp;{{p.title }}&nbsp;» {% if f.isPresentielle or f.isAudit %}du {{ f.startDate | date('d/m/Y') }}{% else %}qui s’est tenue du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }}{% endif %}, nous vous informons que les documents ci-dessous sont disponibles sur votre espace personnel Eduprat.</div>
    <br>
    <div>Veuillez cliquer sur les liens ci-dessous afin d’y accéder :</div>
    <ul>
        {% if not participation.financeSousMode.isHorsDpc %}<li><a class="btn btn-eduprat" href="{{ url('pdf_certificate_participation_pdf', {id: participation.id, token: participation.token}) }}">Votre attestation de participation {% if not participation.financeSousMode.isHorsDpc %}DPC{% endif %}</a></li>{% endif %}
        <li><a class="btn btn-eduprat" href="{{ url('pdf_certificate_participation_horary_pdf', {id: participation.id, token: participation.token}) }}">Votre attestation de participation horaire</a></li>
        <li>
            {% set lien = f.hasFormPost ? "pdf_restitution_audit_pdf" : "pdf_audit_restitution_groupe_individuelle_pdf" %}
            <a href="{{ url(lien, {id: participation.id, token: participation.token}) }}">
                {% if f.isTcs %}
                    Votre restitution liée aux réponses données aux TCS
                {% else %}
                    {% if f.isVignette %}
                        Votre scoring lié aux réponses données aux
                    {% else %}
                        Une restitution personnalisée de vos
                    {% endif %}
                    {% if f.isAudit and not f.isVFC %}
                        {% if f.audit.isPredefinedType %} cas cliniques
                        {% else %} audits
                        {% endif %}
                    {% elseif f.isVignette %}
                        vignettes cliniques
                    {% else %} questionnaires
                    {% endif %}
                    pré{% if not f.hasFormPost %}-{% else %} et post-{% endif %}session
                {% endif %}
            </a>
        </li>

        {% if f.topoFiles is not null %}
            {% for file in f.topoFiles %}
                <li>
                    {% set name = "Le diaporama" %}
                    {% if file.topoOriginalName %}
                        {% set name = name ~ " : " ~ file.topoOriginalName %}
                    {% endif %}
                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('eduprat_audit_formation_topo', {'id' : file.id, 'token' : file.token}) }}">{{ name }}</a>
                </li>
            {% endfor %}
        {% endif %}

        {% if f.programme.topoProgrammeFiles is not null %}
            {% for file in f.programme.topoProgrammeFiles %}
                <li>
                    {% set name = "Le diaporama" %}
                    {% if file.topoOriginalName %}
                        {% set name = name ~ " : " ~ file.topoOriginalName %}
                    {% endif %}
                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('eduprat_audit_formation_topo_programme', {'id' : file.id, 'token' : file.token}) }}">{{ name }}</a>
                </li>
            {% endfor %}
        {% endif %}

        {% if f.programme.toolProgrammeFiles is not null %}
            {% for file in f.programme.toolProgrammeFiles %}
                <li>
                    {% set name = "Les boîtes à outils" %}
                    {% if file.topoOriginalName %}
                        {% set name = name ~ " : " ~ file.topoOriginalName %}
                    {% endif %}
                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('eduprat_audit_formation_topo_tool_programme', {'id' : file.id, 'token' : file.token}) }}">{{ name }}</a>
                </li>
            {% endfor %}
        {% endif %}

        {% if f.programme.documentsPedagogiquesFiles is not null %}
            {% for file in f.programme.documentsPedagogiquesFiles %}
                <li>
                    {% set name = "Les recommandations" %}
                    {% if file.topoOriginalName %}
                        {% set name = name ~ " : " ~ file.topoOriginalName %}
                    {% endif %}
                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('eduprat_audit_formation_documents_pedagogiques', {'id' : file.id, 'token' : file.token}) }}">{{ name }}</a>
                </li>
            {% endfor %}
        {% endif %}
    </ul>
    {% if f.isElearning %}
        <div>
            Important : Le diaporama de la formation est accessible sur votre espace personnel Eduprat.
            <br>
            - Une fois connecté, veuillez cliquer sur « Plateforme Elearning » dans le menu à gauche.
            <br>
            - Puis sur « Mes formations e-learning » dans le menu en haut de page.
            <br>
            - Puis sur « Voir le contenu » de la formation
            <br>
            - Puis sur « Voir le détail de la formation »
            <br>
            - Et enfin sur « Documents de la formation »
        </div>
    {% endif %}
    <br>
    <div>Merci de bien vouloir remplir le questionnaire satisfaction, sur votre espace personnel Eduprat, si vous ne l’avez pas déjà fait.</div>
{% endblock %}
