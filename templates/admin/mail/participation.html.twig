{% extends 'domain/base_mail.html.twig' %}

{% set f = participation.formation %}
{% set p = f.programme %}
{% set person = participation.participant.user %}
{% set hours = f.startDate|date('H\\hi') == '00h00' ? null : f.startDate|date('H\\hi') %}

{% block body %}
    <div>Bonjour,</div>
    <br>
    {% if p.isElearningTwoUnity() %}
        {% set hours = f.getUnityByPosition(2).calculateStartDate()|date('H\\hi') == '00h00' ? null : f.getUnityByPosition(2).calculateStartDate()|date('H\\hi') %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra le
            {{ f.getUnityByPosition(2).calculateStartDate() | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %}</div>
    {% elseif f.isElearning %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }}.</div>
    {% elseif f.isPresentielle %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations le {{ f.startDate | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %} {% include 'domain/email/common/classe_virtuelle.html.twig' %}</div>
    {% elseif f.isAudit or f.isTcs %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }} avec une réunion le {{ f.startDate | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %} {% include 'domain/email/common/classe_virtuelle.html.twig' %}</div>
    {% endif %}
    {% if (f.isPresentielle or f.isAudit or f.isVfc or f.isTcs) and not p.classeVirtuelle and (not f.isElearning or p.isElearningTwoUnity()) %}
        {% include 'domain/email/common/address.html.twig' %}
    {% endif %}
    <br>
    {% if opened %}
        <div>Afin de {% if f.isElearning and not p.isElearningTwoUnity() %}démarrer votre parcours de formation e-learning{% else %}préparer cette formation{% endif %}, merci de vous connecter à votre espace personnel{% if not f.isElearning or p.isElearningTwoUnity() %} afin de démarrer votre parcours pré-session.{% else %} :{% endif %}</div>
        <br>
        <div>
            Veuillez cliquer  <b><a style="font-weight: bold; text-decoration: underline" href="{{ url('eduprat_audit_login') }}">ici</a></b> ou vous rendre sur le site <a href="https://extranet.eduprat.fr/login">https://extranet.eduprat.fr/login</a>.
            <br>
            Saisir votre identifiant (<b>{{ participation.participant.identifier }}</b>), votre mot de passe.
        </div>
        <br>
        {% if f.isAudit and not f.isVfc() %}
            {% if f.audit %}
                {% if f.audit.introduction %}
                    <div>{{ f.audit.introduction | raw }}</div>
                    <br>
                {% endif %}
            {% endif %}
        {% endif %}
    {% else %}
        {% if f.isPresentielle or p.isElearningTwoUnity() %}
            <div>Le parcours pré-session sera accessible le {{ f.formOpeningDate | date('d/m/Y') }}, un mail vous sera envoyé lors de son ouverture.</div>
            <br>
        {% else %}
            <div>Celle-ci n’a pas encore démarré, elle sera ouverte le {{ f.formOpeningDate | date('d/m/Y') }}, un mail vous sera envoyé lors de son ouverture.</div>
            <br>
            {% if f.isAudit %}
                {% if f.audit %}
                    {% if f.audit.introduction %}
                        <div>{{ f.audit.introduction | raw }}</div>
                        <br>
                    {% endif %}
               {% endif %}
            {% endif %}
        {% endif %}
    {% endif %}
{% endblock %}
{% block forInscription %}
    <br>
    <a href="https://www.eduprat.fr/cgv" target="blank"> Voir nos Conditions générales de vente </a>
{% endblock %}
