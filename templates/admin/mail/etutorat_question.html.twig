{% extends 'domain/base_mail.html.twig' %}

{% block body %}
    {% set participant = participation.participant %}
    {% set formation = participation.formation %}
    <div>Bonjour,</div>
    <br>
    <div>Nouvelle question d’un participant :</div>
    <br>
    <div>De : {{ participant.civility }} {{ participant.fullname }} – {{ participant.email }} – {{ participant.phone }}</div>
    <div>Formation : {{ formation.programme.title }}</div>
    <div>Référence : {{ formation.programme.reference }}</div>
    <div>Session : {{ formation.sessionNumber }}</div>
    <div>Date de la formation :
        {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
            Le {{ formation.startDate | date('d/m/Y') }}
        {% else %}
            Du {{ formation.startDate | date('d/m/Y') }} au {{ formation.endDate | date('d/m/Y') }}
        {% endif %}
    </div>
    {% if participant.coordinator %}
        <div>Coordinateur : {{ participant.coordinator.fullname }}</div>
    {% endif %}
    <div>E-tutorat : Etape {{ stepId }}</div>
    <br>
    <p>Question :</p>
    <p>{{ message|nl2br }}</p>
{% endblock %}