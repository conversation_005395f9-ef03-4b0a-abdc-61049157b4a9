{% extends 'domain/base_mail.html.twig' %}

{% set f = participation.formation %}
{% set p = f.programme %}
{% set person = participation.participant.user %}
{% set cp = participation.coordinator.person %}
{% set n1Value = f.isPluriAnnuelle ? "true" : "false" %}

{% block body %}
    <div><PERSON><PERSON><PERSON>,</div>
    <p>Merci de nous <b>envoyer votre attestation sur l’honneur</b> certifiant avoir
        suivi la totalité de la formation «&nbsp;{{ p.title}}&nbsp;» qui s'est tenue du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }}
        {%- if f.programme.format != "Elearning" -%}, avec une réunion
            {% if f.startDate|date('d/m/Y') == f.endDate|date('d/m/Y') %}
                le {{ f.startDate | date('d/m/Y') }}
            {%- else -%}
                du {{ f.startDate | date('d/m/Y') }} au {{ f.endDate | date('d/m/Y') }}
            {%- endif -%}{%- endif -%}.
    </p>
    <p>Vous trouverez le modèle pré complété en cliquant <a style="font-weight: bold;text-decoration: underline" target="_blank" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null", n1 : n1Value  }) }}">ici</a>, que nous vous remercions de <span style="color: #e50017">dater, signer et tamponner</span> afin que l’attestation soit valide.</p>
    <p><b>L’attestation est à envoyer à votre Coordinateur Régional : {{ cp.fullName }}</b><br>
        par mail : <a href="mailto:{{ cp.email }}">{{ cp.email }}</a> (préférable)
        {% if cp.phone %}
            <br>ou par SMS : {{ cp.phone }}
        {% endif %}
        </p>
    <div>Ou à charger sur votre espace personnel : <a style="text-decoration: underline" href="{{ url('eduprat_audit_login') }}">{{ url('eduprat_audit_login') }}</a>.</div>
    <div>• Onglet "Mes documents téléchargeables" de la session</div>
    <div>• Puis cliquez sur "Importer l’attestation complétée et signée"</div>
{% endblock %}
