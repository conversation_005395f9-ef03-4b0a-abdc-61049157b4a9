{% extends 'domain/base_mail.html.twig' %}

{% set f = participation.formation %}
{% set p = f.programme %}
{% set person = participation.participant.user %}
{% set hours = f.startDate|date('H\\hi') == '00h00' ? null : f.startDate|date('H\\hi') %}

{% block body %}
    <div>Bonjour,</div>
    <br>
    {% if p.isElearningTwoUnity() %}
        {% set hours = f.getUnityByPosition(2).calculateStartDate()|date('H\\hi') == '00h00' ? null : f.getUnityByPosition(2).calculateStartDate()|date('H\\hi') %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra le
            {{ f.getUnityByPosition(2).calculateStartDate() | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %}</div>
    {% elseif f.isElearning %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }}.</div>
    {% elseif f.isPresentielle %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations le {{ f.startDate | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %} {% include 'domain/email/common/classe_virtuelle.html.twig' %}</div>
    {% elseif f.isAudit or f.isTcs %}
        <div>Nous vous confirmons votre inscription à la formation «&nbsp;{{p.title }}&nbsp;» organisée par Eduprat Formations qui se tiendra du {{ f.openingDate | date('d/m/Y') }} au {{ f.closingDate | date('d/m/Y') }} avec une réunion le {{ f.startDate | date('d/m/Y') }}{% if hours %} à {{ hours }}{% endif %} {% include 'domain/email/common/classe_virtuelle.html.twig' %}</div>
    {% endif %}
    {% if (f.isPresentielle or f.isAudit or f.isVfc or f.isTcs) and not p.classeVirtuelle and (not f.isElearning or p.isElearningTwoUnity()) %}
        {% include 'domain/email/common/address.html.twig' %}
    {% endif %}
    <br>
    {% if opened %}
        <div>Comme il s’agit de votre première inscription à une formation Eduprat, merci de définir votre mot de passe en cliquant <a style="font-weight: bold; text-decoration: underline" href="{{ url('eduprat_password_reset', {'token': person.token, 'register': "true"}) }}">ici</a> ou vous rendre sur le site <a href="https://extranet.eduprat.fr/login">https://extranet.eduprat.fr/login</a>. Vous pourrez ensuite vous connecter à votre espace personnel avec votre identifiant Eduprat{% if participation.participant.identifier %} : <b>{{ participation.participant.identifier }}</b>{% endif %}.</div>
        <br>
        <div>Une fois connecté, vous pourrez démarrer {% if f.isElearning and not p.isElearningTwoUnity() %} votre parcours de formation e-learning.{% else %}le parcours pré-session qui constitue le premier temps de la formation.{% endif %}</div>
        <br>
        {% if f.isAudit and f.audit is not null and f.audit.introduction and not f.isVfc() %}
            <div>{{ f.audit.introduction | raw }}</div>
            <br>
        {% endif %}
    {% else %}
        {% if f.isPresentielle or p.isElearningTwoUnity() %}
            <div>Le parcours pré-session sera accessible le {{ f.formOpeningDate | date('d/m/Y') }}, un mail vous sera envoyé lors de son ouverture.</div>
        {% else %}
            <div>Celle-ci n’a pas encore démarré, elle sera ouverte le {{ f.formOpeningDate | date('d/m/Y') }}, un mail vous sera envoyé lors de son ouverture.</div>
            {% if f.isAudit and f.audit is not null and f.audit.introduction %}
                <br>
                <div>{{ f.audit.introduction | raw }}</div>
            {% endif %}
        {% endif %}
    {% endif %}
{% endblock %}
{% block forInscription %}
    <br>
    <a href="https://www.eduprat.fr/cgv" target="blank"> Voir nos Conditions générales de vente </a>
{% endblock %}
