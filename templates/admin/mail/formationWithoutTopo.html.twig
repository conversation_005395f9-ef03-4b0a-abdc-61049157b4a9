{% block body %}
    {% set hoursStart = formation.startDate|date('H\\hi') == '00h00' ? null : formation.startDate|date('H\\hi') %}
    {% set hoursEnd = formation.endDate|date('H\\hi') == '00h00' ? null : formation.endDate|date('H\\hi') %}
    <p>Bonjour,</p>
    <p>Merci d'insérer sur l’extranet le topo de la formation suivante :</p>
    <br>
    <br>
    <p>Titre : {{ formation.programme.title }}</p>
    <p>Date de réunion : 
        {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
            Le {{ formation.startDate | date('d/m/Y') }} {% if hoursStart %}de {{ hoursStart }} à {{ hoursEnd }}{% endif %}
        {% else %}
            Du {{ formation.startDate | date('d/m/Y') }}{% if hoursStart %} à {{ hoursStart }}{% endif %} au {{ formation.endDate | date('d/m/Y') }}{% if hoursEnd %} à {{ hoursEnd }}{% endif %}
        {% endif %}
    </p>
    <p>Référence : {{ formation.reference }}</p>
    <p>N° de session : {{ formation.sessionNumber }}</p>
    <br>
    <p>Pour rappel, le topo doit être sous format PDF et ne doit pas dépasser les 10Mo. Vous pouvez utiliser un outil de compression type ILovePDF : https://www.ilovepdf.com/fr/compresser_pdf.</p>
    <br>
    <p>Bonne journée,</p>
    <p>L'Equipe Eduprat.</p>    
{% endblock %}