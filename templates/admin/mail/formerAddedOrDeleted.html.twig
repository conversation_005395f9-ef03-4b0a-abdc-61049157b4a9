 {% block body %}
     {% set hoursStart = formation.startDate|date('H\\hi') == '00h00' ? null : formation.startDate|date('H\\hi') %}
     {% set hoursEnd = formation.endDate|date('H\\hi') == '00h00' ? null : formation.endDate|date('H\\hi') %}
     <div>Un formateur sans SIRET vient d'être {% if ajout %}ajouté à {% else %}supprimé de {% endif %} de la formation suivante :</div>
     <br>
     <br>
     <p>Référence : {{ formation.programme.reference }}</p>
     <p>N° de session : {{ formation.sessionNumber }}</p>
     <p>Date de réunion : 
          {% if formation.startDate|date('d/m/Y') == formation.endDate|date('d/m/Y') %}
              Le {{formation.startDate | date('d/m/Y') }} {% if hoursStart %}de {{ hoursStart }} à {{ hoursEnd }}{% endif %}
          {% else %}
              Du {{formation.startDate | date('d/m/Y') }}{% if hoursStart %} à {{ hoursStart }}{% endif %} au {{formation.endDate | date('d/m/Y') }}{% if hoursEnd %} à {{ hoursEnd }}{% endif %}
          {% endif %}
      </p>
     <p>Titre : {{ formation.programme.title }}</p>
     <p>Formateur : <b>{{ formateur.person.invertedFullname }}</b></p>
     <p>Coordinateurs : </p> 
     <ul>
         {% for coordinator in formation.coordinators %}
             <li>{{ coordinator.person.invertedFullname }}</li>
         {% endfor %}
     </ul>
 {% endblock %}