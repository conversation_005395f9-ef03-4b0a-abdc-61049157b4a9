{% extends 'domain/base_mail.html.twig' %}

{% block body %}
    {% set participant = participation.participant %}
    {% set formation = participation.formation %}
    {% set file = type == "topo" ? formation.firstTopoFile : null %}
    <div>Bonjour,</div>
    <br>
        {% if type == "prerestitution" %} 
            <div> Vous trouverez l'analyse de vos résultats pré-session en cliquant <a href="{{ url('pdf_audit_restitution_groupe_individuelle_pdf', {id: participation.id, token: participation.token}) }}"> ici </a>.</div>
        {% elseif type == "restitution" %}
            <div> Vous trouverez l'analyse de vos résultats post-session en cliquant <a href="{{ url('pdf_restitution_audit_pdf', {id: participation.id, token: participation.token}) }}"> ici </a>.</div>
        {% elseif type == "topo" %}
            <div> Votre {{type}} est disponible en téléchargement en cliquant <a href="{{  url('eduprat_audit_formation_topo', {'id' : file.id, 'token' : file.token}) }}"> ici </a>.</div>
        {% endif %}
{% endblock %}