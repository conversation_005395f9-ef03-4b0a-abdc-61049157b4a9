{% extends 'admin/evaluation/base-report.html.twig' %}

{% block box_title %}
    {{ 'evaluationGlobal.titles.coordinator_annual'|trans({"%civility%": person.civility, "%fullname%": person.fullname, "%year%": year}) }}
{% endblock %}

{% block box_header %}
    <tr>
        <th>{{ "evaluationGlobal.labels.coordinator" | trans }}</th>
        <td>{{ person.fullname }}</td>
    </tr>
    <tr>
        <th>{{ "evaluationGlobal.labels.year" | trans }}</th>
        <td>{{ year }}</td>
    </tr>
{% endblock %}

{% block box_questions %}
    <tr>
        <th>Accompagnement des participants</th>
    </tr>
    {% for question in reporting.participantsAccueil %}
        <tr>
            <td>{{ question.label|trans }}</td>
            <td>{{ question.count }}</td>
            <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
        </tr>
    {% endfor %}
    <tr>
        <th>Conformité et adaptation des locaux - Participants</th>
    </tr>
    {% for question in reporting.participantsConformite %}
        <tr>
            <td>{{ question.label|trans }}</td>
            <td>{{ question.count }}</td>
            <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
        </tr>
    {% endfor %}
    <tr>
        <th>Accompagnement des formateurs</th>
    </tr>
    {% for question in reporting.formersAccueil %}
        <tr>
            <td>{{ question.label|trans }}</td>
            <td>{{ question.count }}</td>
            <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
        </tr>
    {% endfor %}
    <tr>
        <th>Conformité et adaptation des locaux - Formateurs</th>
    </tr>
    {% for question in reporting.formersConformite %}
        <tr>
            <td>{{ question.label|trans }}</td>
            <td>{{ question.count }}</td>
            <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
        </tr>
    {% endfor %}
{% endblock %}