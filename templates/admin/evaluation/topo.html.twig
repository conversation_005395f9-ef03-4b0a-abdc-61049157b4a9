{% extends 'admin/base.html.twig' %}

{% block title %}{{ "admin.topo.list"|trans }}{% endblock %}

{% block body %}
    {% if search_form is defined %}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <h3 class="box-title">{{ "admin.formation.titles.search"|trans }}</h3>
            </div>
            <div class="box-body eduprat-search">
                {{ form_start(search_form, {'attr': {'class': 'form-inline'}}) }}
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            {% for key,field in search_form %}
                                {% if key != '_token' %}
                                    <div class="form-group mrl">
                                        {{ form_label(field) }}<br>
                                        {{ form_widget(field) }}
                                        {% if key == 'start' or key == 'end' %}
                                            <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                            <div class="pull-right">
                                {% if search_form|length > 2 %} <br>{% endif %}
                                <button class="btn btn-eduprat" type="submit">
                                    <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {{ form_end(search_form) }}
            </div>
        </div>
    {% endif %}
    <p style="text-align:center;">Donnés mises à jour le {{ "now"|date('d/m/Y') }} à 03h00 </p>
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                {% if programmes|length %}
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.topo.title"|trans }}</th>
                            <th><a href="{{ url('evaluation_global_topo_index', extraParams|merge({ page: page, sortBy: "number", order: extraParams.sortBy == "number" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.count"|trans }}
                                    {% if extraParams.sortBy == "number" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a></th>
                            <th>
                                <a href="{{ url('evaluation_global_topo_index', extraParams|merge({ page: page, sortBy: "participant_avg", order: extraParams.sortBy == "participant_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_participant"|trans }}
                                    {% if extraParams.sortBy == "participant_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                                </th>
                            <th>
                                <a href="{{ url('evaluation_global_topo_index', extraParams|merge({ page: page, sortBy: "former_avg", order: extraParams.sortBy == "former_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_former"|trans }}
                                    {% if extraParams.sortBy == "former_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                                </th>
                            <th>
                                <a href="{{ url('evaluation_global_topo_index', extraParams|merge({ page: page, sortBy: "total_avg", order: extraParams.sortBy == "total_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_avg"|trans }}
                                    {% if extraParams.sortBy == "total_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                                </th>
                            <th>{{ "admin.topo.report"|trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for programme in programmes %}
                            <tr>
                                <td>{{ programme.title }}</td>
                                <td>{{ programme.number }}</td>
                                <td>
                                    {% if programme.participant_count > 0 %}
                                        {{ programme.participant_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td>
                                    {% if programme.former_count > 0 %}
                                        {{ programme.former_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td>
                                    {% if programme.former_count > 0 and programme.participant_count > 0 %}
                                        {{ programme.total_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td class="prog-actions">
                                    {% if is_granted('ROLE_WEBMASTER') %}
                                        <a href="#" title="Liste des evaluations" class="mbn evalListLink btn btn-eduprat"><i class="fa fa-file-text-o"></i> <i class="fa fa-angle-right"></i></a>
                                    {% endif %}
                                </td>
                                <td>
                                    </td>
                            </tr>
                            <tr class="sessionsWrap">
                                <td colspan="6">
                                    <div class="evalList">
                                        <table class="table table-bordered table-responsive table-hover">
                                            {% for year in minYear..maxYear %}
                                                <tr>
                                                    <td>{{ year }}</td>
                                                    <td>
                                                        <a class="btn btn-eduprat" title="{{ "admin.topo.participant"|trans }}" href="{{ url('evaluation_global_topo', {year: year, title: programme.title, role: 'participant'}) }}">{{ "admin.topo.participant"|trans }}</a>
                                                    </td>
                                                    <td>
                                                        <a class="btn btn-eduprat" title="{{ "admin.topo.former"|trans }}" href="{{ url('evaluation_global_topo', {year: year, title: programme.title, role: 'formateur'}) }}">{{ "admin.topo.former"|trans }}</a>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                        <div class="callout callout-primary">
                            <p>
                                <span class="icon fa fa-info-circle"></span>
                                {{ 'admin.user.empty'|trans }}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    {% if npages > 1 %}
                        {% include '@AlienorApi/Includes/paginationBootstrap.html.twig' with {urlName : search_route, extraParams : extraParams } %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
