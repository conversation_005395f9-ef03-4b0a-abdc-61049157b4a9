{% extends 'admin/base.html.twig' %}

{% block title %}{{ "admin.topo.list"|trans }}{% endblock %}

{% block body %}
    {% if search_form is defined %}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <h3 class="box-title">Rechercher un utilisateur</h3>
            </div>
            <div class="box-body eduprat-search">
                {{ form_start(search_form, {'attr': {'class': 'form-inline'}}) }}
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            {% for key,field in search_form %}
                                {% if key != '_token' %}
                                    <div class="form-group mrl">
                                        {{ form_label(field) }}<br>
                                        {{ form_widget(field) }}
                                        {% if key == 'start' or key == 'end' %}
                                            <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                            <div class="pull-right">
                                {% if search_form|length > 2 %} <br>{% endif %}
                                <button class="btn btn-eduprat" type="submit">
                                    <span class="fa fa-search"></span> {{ "admin.global.search"|trans }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {{ form_end(search_form) }}
            </div>
        </div>
    {% endif %}
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                {% if users|length %}
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "p.lastname", order: extraParams.sortBy == "p.lastname" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "login.lastname"|trans }}
                                    {% if extraParams.sortBy == "p.lastname" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "p.roles", order: extraParams.sortBy == "p.roles" ? extraParams.order == "DESC" ? "ASC" : "DESC" : "ASC"})) }}">
                                    {{ "login.roles"|trans }}
                                    {% if extraParams.sortBy == "p.roles" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "top" : "sub" }};" class="fa fa-sort-{{ (extraParams.order == "DESC" ? "ASC" : "DESC")|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "number", order: extraParams.sortBy == "number" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.count"|trans }}
                                    {% if extraParams.sortBy == "number" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "participant_avg", order: extraParams.sortBy == "participant_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_participant"|trans }}
                                    {% if extraParams.sortBy == "participant_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "former_avg", order: extraParams.sortBy == "former_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_coordinator_former"|trans }}
                                    {% if extraParams.sortBy == "former_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="{{ url('evaluation_global_user_index', extraParams|merge({ page: page, sortBy: "total_avg", order: extraParams.sortBy == "total_avg" ? extraParams.order == "ASC" ? "DESC" : "ASC" : "DESC"})) }}">
                                    {{ "admin.topo.note_avg"|trans }}
                                    {% if extraParams.sortBy == "total_avg" %}
                                        <i style="vertical-align: {{ extraParams.order == "DESC" ? "sub" : "top" }};" class="fa fa-sort-{{ extraParams.order|lower }}"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>{{ "admin.global.actions"|trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for user in users %}
                            <tr>
                                <td>{{ user.entity.invertedFullname }}</td>
                                <td>
                                    {% if user.entity.roles is not empty %}
                                        {{ ('user.role.'~user.entity.roles[0])|trans }}
                                    {% endif %}
                                </td>
                                <td>
                                    {{ user.number }}
                                </td>
                                <td>
                                    {% if user.participant_avg > 0 %}
                                        {{ user.participant_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.former_avg > 0 %}
                                        {{ user.former_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.total_avg > 0 %}
                                        {{ user.total_avg|round(2) }}
                                    {% else %}
                                        NA
                                    {% endif %}
                                </td>
                                <td class="prog-actions">
                                    {% if user.entity.isFormer or user.entity.isCoordinator %}
                                        <a href="{{ url('admin_user_monitoring', {'id': user.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.user.monitor'|trans }}"><i class="fa fa-eye"></i></a>
                                    {% endif %}
                                    {% if user.entity.isCoordinator %}
                                        <a href="{{ url('admin_coordinator_table', {'id': user.id, 'year': "now"|date('Y') }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.user.coordinator_table'|trans }}"><i class="fa fa-dashboard"></i></a>
                                        <a href="{{ url('admin_coordinator_dashboard', {'id': user.id, 'year': "now"|date('Y') }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.user.coordinator_dashboard'|trans }}"><i class="fa fa-bar-chart-o"></i></a>
                                    {% endif %}
                                </td>
                                <td>
                                    </td>
                            </tr>

                            <tr class="sessionsWrap">
                                <td colspan="6">

                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                        <div class="callout callout-primary">
                            <p>
                                <span class="icon fa fa-info-circle"></span>
                                {{ 'admin.user.empty'|trans }}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    {% if npages > 1 %}
                        {% include '@AlienorApi/Includes/paginationBootstrap.html.twig' with {urlName : search_route, extraParams : extraParams } %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
