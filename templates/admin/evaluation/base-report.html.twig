{% extends 'admin/base.html.twig' %}

{% block title %}{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{% block box_title %}{% endblock %}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        {% if pdf is defined and is_granted('ROLE_WEBMASTER') %}
                            <tr>
                                <td colspan="2">
                                    <a class="btn btn-eduprat" target="_blank" title="{{ "evaluationGlobal.report.participant"|trans }}" href="{{ pdf }}"><i class="fa fa-download"></i> {{ "evaluationGlobal.report.download"|trans }}</a>
                                </td>
                            </tr>
                        {% endif %}
                        {% block box_header %}{% endblock %}
                    </table>
                    <br>
                    <h4 style="background-color: #109199;color:white;padding:3px;">Evaluation de la formation</h4>
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.user.evaluation.question" | trans }}</th>
                            <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% block box_questions %}
                            {% for question in reporting.reporting %}
                                {% if question.title is defined %}
                                    <th>{{ ("evaluation.global." ~ question.title ~ ".title")|trans }}</th>
                                {% endif %}
                                <tr>
                                    <td>{{ question.label|trans }}</td>
                                    <td>{{ question.count }}</td>
                                    <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
                                </tr>
                            {% endfor %}
                        {% endblock %}
                        </tbody>
                    </table>
                    {% if reporting.showRadar is not defined or reporting.showRadar %}
                        <br>
                        <div id="spiderweb"></div>
                    {% endif %}
                    {% if syntheseDatas is defined and role == "ROLE_PARTICIPANT" %}
                        <br>
                        <h4 style="background-color: #109199;color:white;padding:3px;">Fiche de synthèse <span style="font-size:14px" class="pull-right"> {{ syntheseDatas.answeredPoeple }} personnes ont répondu à ce questionnaire </span></h4>
                        <table class="table table-bordered table-hover">
                            <tbody>
                                {% if syntheseDatas.competences is defined and syntheseDatas.competences|length > 0 %}
                                    <tr>
                                        <th>{{ "admin.formation.synthese.competence" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.acquis" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.enAcquisition" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.nonAcquis" | trans }}</th>
                                    </tr>
                                    {% for competence,value in syntheseDatas.competences %}
                                        <tr>
                                            <td>{{ competence }}</td>
                                            <td>{{ value[2]|round(2) }}%</td>
                                            <td>{{ value[1]|round(2) }}%</td>
                                            <td>{{ value[0]|round(2) }}%</td>
                                        </tr>
                                    {% endfor %}
                                {% endif %}

                                {% if syntheseDatas.connaissances is defined and syntheseDatas.connaissances|length > 0 %}
                                    <tr>
                                        <th>{{ "admin.formation.synthese.connaissance" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.acquis" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.enAcquisition" | trans }}</th>
                                        <th>{{ "admin.formation.synthese.nonAcquis" | trans }}</th>
                                    </tr>
                                    {% for connaissance,value in syntheseDatas.connaissances %}
                                        <tr>
                                            <td>{{ connaissance }}</td>
                                            <td>{{ value[2]|round(2) }}%</td>
                                            <td>{{ value[1]|round(2) }}%</td> 
                                            <td>{{ value[0]|round(2) }}%</td>
                                        </tr>
                                    {% endfor %}
                                {% endif %}
                            </tbody>
                        </table>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {% if reporting.radarData %}
        <script src="https://code.highcharts.com/highcharts.js"></script>
        <script src="https://code.highcharts.com/highcharts-more.js"></script>
        <script src="https://code.highcharts.com/modules/exporting.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.6/d3.min.js" charset="utf-8"></script>
        <script src="{{ asset('js/radarChart.js') }}"></script>
        <script>

		    var radarData = JSON.parse('{{ reporting.radarData|json_encode|escape('js') }}');
		    radarData[0] = radarData[0].filter(function (r) {
			    return !r.hasOwnProperty("show") || r.show;
		    });
		    var margin = {top: 150, right: 100, bottom: 150, left: 100};
		    var width = 519;
		    var height = 350;
		    var maxValue = 100;
		    var color = d3.scale.ordinal().range(["#3c8dbc"]);
		    var radarChartOptions = {
			    w: width,
			    h: height,
			    margin: margin,
			    maxValue: maxValue,
			    levels: 4,
			    roundStrokes: true,
			    labelFactor: 1.15,
			    lineBackground: true,
			    dotRadius: 0,
			    wrapWidth: 150,
			    color: color,
			    legends: ["Moyenne des réponses"]
		    };

		    //Call function to draw the Radar chart
		    RadarChart("#spiderweb", radarData, radarChartOptions);

        </script>
    {% endif %}
{% endblock %}