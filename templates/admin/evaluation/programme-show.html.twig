{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'evaluationCoordinator.evalProgramme.title'|trans }}{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'evaluationCoordinator.evalProgramme.detail'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.programmeTitle" | trans }}</th>
                            <td>{{ programme.title }}</td>
                        </tr>
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.coordinator" | trans }}</th>
                            <td>
                                {% if programme.coordinators|length %}
                                    {% for coordinator in programme.coordinators %}
                                        {{ coordinator.person.firstname }} {{ coordinator.person.lastname }}
                                    {% endfor %}
                                {% else %}
                                    /
                                {% endif %}
                            </td>
                        </tr>
                        {# <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.city" | trans }}</th>
                            <td>{{ programme.city }}</td>
                        </tr> #}
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.formers" | trans }}</th>
                            <td>
                                {% if programme.formateurs|length %}
                                    {% for formateur in programme.formateurs %}
                                        {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                                    {% endfor %}
                                {% else %}
                                    /
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.durationParticipants" | trans }}</th>
                            <td>
                                {% for formation in programme.formations %}
                                    <div>{{ programme.reference }} : {{ programme.durationPresentielle + programme.durationNotPresentielle }}h / {{participants[formation.id]}} {{ "evaluationCoordinator.evalProgramme.participants" | trans }}</div>
                                {% endfor %}
                            </td>
                        </tr>
                        {# <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.date" | trans }}</th>
                            <td>
                                {% if programme.startDate|date('d/m/Y') == programme.endDate|date('d/m/Y') %}
                                    Le {{ programme.startDate | date('d/m/Y') }}
                                {% else %}
                                    Du {{ programme.startDate | date('d/m/Y') }} au {{ programme.endDate | date('d/m/Y') }}
                                {% endif %}
                            </td>
                        </tr> #}
                    </table>
                    <br>
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.user.evaluation.question" | trans }}</th>
                            <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% if evaluation|length %}
                            {% for i in 1..evaluation|length %}
                                <tr>
                                    <td>{{ ("evaluation.programme.question." ~ i ~".label") | trans }}</td>
                                    <td>
                                        {% if evaluation[i] is defined %}
                                            {{ evaluation[i].count }}
                                        {% else %}
                                            0
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if evaluation[i] is defined %}
                                            {{ evaluation[i].avg | round(2) }}
                                        {% else %}
                                            NA
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
