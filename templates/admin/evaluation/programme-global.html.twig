{% extends 'admin/evaluation/base-report.html.twig' %}

{% block box_title %}
    {% if formation is defined %}
        {{ 'evaluationGlobal.titles.session'|trans }} {{ ('evaluation.subtitle.session.' ~ role)|trans }}
    {% else %}
        {{ 'evaluationGlobal.titles.programme'|trans }} {{ ('evaluation.subtitle.' ~ role)|trans }}
    {% endif %}
{% endblock %}

{% block box_header %}
    {% if formation is defined %}
        <tr>
            <th>{{ "evaluationCoordinator.evalFormer.programmeTitle" | trans }}</th>
            <td>{{ formation.programme.title }}</td>
        </tr>
        <tr>
            <th>{{ "evaluationCoordinator.evalFormer.reference" | trans }}</th>
            <td>{{ formation.programme.reference }}</td>
        </tr>
        <tr>
            <th>{{ "evaluationCoordinator.evalFormer.sessionNumber" | trans }}</th>
            <td>{{ formation.sessionNumber }}</td>
        </tr>
        <tr>
            <th>{{ "evaluationGlobal.labels.date" | trans }}</th>
            <td>Le {{ formation.startDate|date('d/m/Y') }} à {{ formation.city }}</td>
        </tr>

    <tr>
        <th>{{ "evaluationGlobal.labels.coordinator" | trans }}{% if formation.coordinators|length > 1 %}s{% endif %}</th>
        <td>{% for coordinator in formation.coordinators %}{{ coordinator.person.fullname }}{% if not loop.last %}, {% endif %}{% endfor %}</td>
    </tr>
    <tr>
        <th>{{ "evaluationGlobal.labels.former" | trans }}{% if formation.formateurs|length > 1 %}s{% endif %}</th>
        <td>
            {% for former in formation.formateurs %}{{ former.person.fullname }}{% if not loop.last %}<br/>{% endif %}{% endfor %}
        </td>
    </tr>
    {% else %}
        <tr>
            <th>{{ "evaluationCoordinator.evalFormer.programmeTitle" | trans }}</th>
            <td>{{ programme.title }}</td>
        </tr>
        <tr>
            <th>{{ "evaluationCoordinator.evalFormer.reference" | trans }}</th>
            <td>{{ programme.reference }}</td>
        </tr>
    {% endif %}
{% endblock %}

{% block box_questions %}
    {% for question in reporting.reporting %}
        {% if question.title is defined %}
            {% if question.identifier == "formerRecommande" and formation is defined %}<tr><td></td></tr>{% endif %}
            {% if role == "ROLE_COORDINATOR" and ("evaluation.global." ~ question.title ~ ".title_coordinator")|trans != ("evaluation.global." ~ question.title ~ ".title_coordinator") %}
                <th>{{ ("evaluation.global." ~ question.title ~ ".title_coordinator")|trans }}</th>
            {% elseif formation is defined or question.identifier != "formerRecommande" %}
                <th>{{ ("evaluation.global." ~ question.title ~ ".title")|trans }}</th>
            {% endif %}
            {% if question.identifier == "formerRecommande" and formation is defined%}
                <th>Nombre de OUI</th>
                <th>Nombre de NON</th>
            {% endif %}
            {% if needHeaderRefresh is defined and needHeaderRefresh == true %}
                {% set needHeaderRefresh = false %}
                <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                <th>{{ "admin.user.evaluation.avg" | trans }}</th>
            {% endif %}
        {% endif %}
        {% if question.identifier == "formerRecommande" %}
            {% if formation is defined %}
                {% for former in formation.formateurs  %}
                    <tr>
                        <td>{{ former.person.fullname }}</td>
                        {% if question.formers[former.id] is defined %}
                            <td>{{ question.formers[former.id].sum }}</td>
                            <td>{{ question.formers[former.id].count - question.formers[former.id].sum }}</td>
                        {% else %}
                            <td>0</td>
                            <td>0</td>
                        {% endif %}
                    </tr>
                {% endfor %}
                {% set needHeaderRefresh = true %}
            {% endif %}
        {% elseif question.question == "satisfaction_2" %}
            <th></th>
            <th>Nombre de OUI</th>
            <th>Nombre de NON</th>
            <tr>
                <td>{{ question.label|trans }}</td>
                <td>{{ question.sum }}</td>
                <td>{{ question.count - question.sum }}</td>
            </tr>
            {% set needHeaderRefresh = true %}
        {% else %}
            <tr>
                <td>{{ question.label|trans }}</td>
                <td>{{ question.count }}</td>
                <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
            </tr>
        {% endif %}
    {% endfor %}
{% endblock %}
