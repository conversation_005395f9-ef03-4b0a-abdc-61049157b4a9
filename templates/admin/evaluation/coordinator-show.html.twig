{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'evaluationCoordinator.evalCoordinator.title'|trans }}{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'evaluationCoordinator.evalCoordinator.detail'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ "evaluationCoordinator.evalCoordinator.programmeTitle" | trans }}</th>
                            <td>{{ programme.title }}</td>
                        </tr>
                        <tr>
                            <th>{{ "evaluationCoordinator.evalCoordinator.coordinator" | trans }}</th>
                            <td>{{ coordinator.person.fullname }}</td>
                        </tr>
                         {# <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.city" | trans }}</th>
                            <td>{{ programme.city }}</td>
                        </tr> #}
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.formers" | trans }}</th>
                            <td>
                                {% if programme.formateurs|length %}
                                    {% for formateur in programme.formateurs %}
                                        {{ formateur.person.firstname }} {{ formateur.person.lastname }}
                                    {% endfor %}
                                {% else %}
                                    /
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.durationParticipants" | trans }}</th>
                            <td>
                                {% for formation in programme.formations %}
                                    <div>{{ programme.reference }} : {{ programme.durationPresentielle + programme.durationNotPresentielle }}h / {{participants[formation.id]}} {{ "evaluationCoordinator.evalProgramme.participants" | trans }}</div>
                                {% endfor %}
                            </td>
                        </tr>
                        {# <tr>
                            <th>{{ "evaluationCoordinator.evalProgramme.date" | trans }}</th>
                            <td>
                                {% if programme.startDate|date('d/m/Y') == programme.endDate|date('d/m/Y') %}
                                    Le {{ programme.startDate | date('d/m/Y') }}
                                {% else %}
                                    Du {{ programme.startDate | date('d/m/Y') }} au {{ programme.endDate | date('d/m/Y') }}
                                {% endif %}
                            </td>
                        </tr> #}
                    </table>
                    <br>
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.user.evaluation.question" | trans }}</th>
                            <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                            <th>{{ "evaluationCoordinator.coordinator.yesNo" | trans }}</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% if evaluation|length %}
                            {% for i in 1..evaluation|length %}
                                {% if ("evaluationCoordinator.coordinator.question." ~ i ~".label") != "comment" %}
                                    <tr>
                                        <td>{{ ("evaluationCoordinator.coordinator.question." ~ i ~".label") | trans }}</td>
                                        <td>
                                            {% if evaluation[i] is defined %}
                                                {{ evaluation[i].count }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if evaluation[i] is defined %}
                                                {{("evaluationCoordinator.coordinator.answer." ~ evaluation[i].avg | round(2) ~"") | trans}}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        </tbody>
                    </table>

                    <h4>{{ ("evaluationCoordinator.coordinator.question.5.label") | trans }}</h4>
                    <p>
                        <em>{{ commentaire }}</em>
                    </p>
                </div>

                        <table class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <th>{{ "admin.user.evaluation.question" | trans }}</th>
                                <th>{{ "admin.user.evaluation.answers" | trans }}</th>
                                <th>{{ "admin.user.evaluation.avg" | trans }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% if evaluationsFormers|length %}
                                {% for i in 1..evaluationsFormers|length %}
                                    <tr>
                                        <td>{{ ("evaluation.programme.question." ~ i ~".label") | trans }}</td>
                                        <td>
                                            {% if evaluationsFormers[i] is defined %}
                                                {{ evaluationsFormers[i].count }}
                                            {% else %}
                                                0
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if evaluationsFormers[i] is defined %}
                                                {{ evaluationsFormers[i].avg | round(2) }}
                                            {% else %}
                                                NA
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                            </tbody>
                        </table>

            </div>
        </div>
    </div>
{% endblock %}
