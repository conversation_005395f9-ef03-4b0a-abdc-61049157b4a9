{% extends 'admin/evaluation/base-report.html.twig' %}

{% block box_title %}
    {{ 'evaluationGlobal.titles.former_annual'|trans({"%civility%": person.civility, "%fullname%": person.fullname, "%year%": year}) }}
    </h2><h2 class="box-title">
    {{ 'evaluationGlobal.titles.former_annual_2'|trans() }}
{% endblock %}

{% block box_header %}
    <tr>
        <th>{{ "evaluationGlobal.labels.former" | trans }}</th>
        <td>{{ person.fullname }}</td>
    </tr>
    <tr>
        <th>{{ "evaluationGlobal.labels.year" | trans }}</th>
        <td>{{ year }}</td>
    </tr>
{% endblock %}

{% block box_questions %}
    {% for question in reporting.questions %}
        {% if question.title is defined %}
            <th>{{ ("evaluation.global." ~ question.title ~ ".title")|trans }}</th>
        {% endif %}
        <tr>
            <td>{{ question.label|trans }}</td>
            <td>{{ question.count }}</td>
            <td>{{ question.avg > 0 ? question.avg : "NA"}}</td>
        </tr>
    {% endfor %}
    <tr>
        <th>{{ "evaluation.global.formerRecommande.questions.5.label"|trans }}</th>
        <td>
            {% if reporting.expert[1] > reporting.expert[0] %}
                Oui
            {% elseif reporting.expert[1] == reporting.expert[0] %}
                Neutre
            {% else %}
                Non
            {% endif %}
        </td>
    </tr>
{% endblock %}