{% extends 'admin/base.html.twig' %}

{% form_theme form 'admin/form/fields.html.twig' %}

{% block title %}
    {% if app.request.get('_route') == 'admin_programme_create' %}
        {{ 'formation.titles.create'|trans }}
    {% elseif app.request.get('_route') == 'admin_programme_edit'%}
        {{ 'formation.titles.edit'|trans }}
    {% endif %}
{% endblock %}
{% set creation = app.request.get('_route') == 'admin_programme_create' ? true : false %}

{% block stylesheets %}
    <link href="{{ asset('css/multi-select.dist.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}

    {{ form_start(form) }}

    <div class="nav-tabs-custom">
        <div class="tab-content">
            <div class="tab-pane active" id="tab_administrative">
                <div class="box-body">
                    {% if not form.vars.valid %}
                        <div class="callout callout-danger">
                            {{ 'formation.error'|trans }}
                        </div>
                    {% endif %}
                    <div class="box box-info">
                        <div class="box-body">
                            {{ form_row(form.title) }}
                            {{ form_row(form.reference) }}
                            {{ form_row(form.year) }}
                            {{ form_row(form.andpcStatus) }}
                            {{ form_row(form.cout) }}
                            <div class="form-group vich-image">
                                {{ form_row(form.pictureFile) }}
                            </div>
                            {{ form_row(form.pictureFrom) }}
                            {{ form_row(form.certifying) }}
                            {{ form_row(form.category) }}
                            {{ form_row(form.sessionType) }}
                            {{ form_row(form.formType) }}
                            <div class="form-group {% if not form.presence.vars.valid %}has-error{% endif %}">
                                {{ form_label(form.presence) }}
                                <p style="margin-bottom: 2px; margin-top: -4px; font-size: 12px; color:red; font-style:italic">Attention de s'assurer ici à renseigner la bonne valeur</p>
                                {{ form_widget(form.presence) }}
                                {{ form_errors(form.presence) }}
                            </div>
                            {# Formulaire des unités #}
                            <div class="unities-forms form-group{% if form.unities.vars.errors|length > 0 %} has-error{% endif %}">
                                <label class="control-label">{{ 'admin.formation.unities.title'|trans }}</label>
                                {{ form_errors(form.unities) }}
                                <ul class="unities" data-prototype="{% apply escape %}{{ include('admin/programme/unity_prototype.html.twig', { 'unity': form.unities.vars.prototype, 'init_tinymce' : true }) }}{% endapply %}">
                                    {% for unity in form.unities %}
                                        {{ include('admin/programme/unity_prototype.html.twig', { 'unity': unity, 'loop' : loop }) }}
                                    {% endfor %}
                                </ul>
                            </div>
                            {{ form_widget(form.programmesAssocies) }}
                            {{ form_row(form.tags) }}
                            <div>
                                <button class="btn btn-eduprat add-tag" id="add-tag"><span class="fa fa-plus"></span> Ajouter en tant que nouveau Tag </button>
                                <span id="add-tag-error" class="text-danger mlm hidden"><i class="fa fa-warning"></i> Ce tag est déjà existant</span>
                            </div>
                        </div>
                    </div>
                    <div class="box box-info mtl">
                        <div class="box-body">
                            {{ form_row(form.categories) }}
                            {{ form_row(form.specialities) }}
                            {{ form_row(form.exercisesMode) }}
                            {{ form_row(form.prisesEnCharge) }}
                            {# <div class="form-group vich-image">
                                {{ form_row(form.priseEnChargePictureFile) }}
                            </div>
                            {{ form_row(form.priseEnChargePictureFrom) }} #}
                        </div>
                    </div>
                    <div class="box box-info">
                        <div class="box-body">
                            {{ form_row(form.resume) }}
                            {{ form_row(form.prerequis) }}
                            {{ form_row(form.objectives) }}
                             {# Formulaire des connaissances #}
                            <div class="connaissances-forms form-group{% if form.connaissances.vars.errors|length > 0 %} has-error{% endif %}">
                                <label class="control-label">{{ 'admin.formation.connaissances.title'|trans }}</label>
                                {{ form_errors(form.connaissances) }}
                                <ul class="connaissances" data-prototype="{% apply escape %}{{ include('admin/programme/connaissance_prototype.html.twig', { 'connaissance': form.connaissances.vars.prototype }) }}{% endapply %}">
                                    {% for connaissance in form.connaissances %}
                                        {{ include('admin/programme/connaissance_prototype.html.twig', { 'connaissance': connaissance, 'loop' : loop }) }}
                                    {% endfor %}
                                </ul>
                            </div>
                            {# Formulaire des compétences #}
                            <div class="form-group{% if form.competences.vars.errors|length > 0 %} has-error{% endif %}">
                                <label class="control-label">{{ 'admin.formation.competences.title'|trans }}</label>
                                {{ form_errors(form.competences) }}
                                <ul class="competences" data-prototype="{% apply escape %}{{ include('admin/programme/competence_prototype.html.twig', { 'competence': form.competences.vars.prototype }) }}{% endapply %}">
                                    {% for competence in form.competences %}
                                        {{ include('admin/programme/competence_prototype.html.twig', { 'competence': competence, 'loop' : loop }) }}
                                    {% endfor %}
                                </ul>
                            </div>
                            {{ form_row(form.nationalOrientation) }}
                            {{ form_row(form.additionalInfos) }}
                            <div class="form-group vich-image vich-image-infos">
                                {{ form_row(form.firstAdditionalInfosPictureFile) }}
                            </div>
                            {{ form_row(form.firstAdditionalInfosPictureFrom) }}
                            <div class="firstAdditionalInfosPictureFrom form-group vich-image hidden">
                                <img style="max-height: 80px;" src="" alt="">
                            </div>
                            {{ form_row(form.firstAdditionalInfosPictureLink) }}
                            <div class="form-group vich-image vich-image-infos">
                                {{ form_row(form.secondAdditionalInfosPictureFile) }}
                            </div>
                            {{ form_row(form.secondAdditionalInfosPictureFrom) }}
                            <div class="secondAdditionalInfosPictureFrom form-group vich-image hidden">
                               <img style="max-height: 80px;" src="" alt="">
                            </div>
                            {{ form_row(form.secondAdditionalInfosPictureLink) }}
                        </div>
                    </div>
                    <div class="box box-info">
                        <div class="box-body">
                            {{ form_row(form.durationNotPresentielle) }}
                            {{ form_row(form.durationPresentielle) }}
                        </div>
                    </div>
                    <div class="box box-solid box-primary">
                    <div class="box-header">
                        <h3 class="box-title">Actalians</h3>
                    </div>
                    <div class="box-body">
                        {{ form_row(form.durationNotPresentielleActalians) }}
                        {{ form_row(form.objectivesActalians) }}
                    </div>
            </div>
            <div class="box-body">
                {{ form_row(form.commentaireNotif) }}
            </div>
            <div id="box-body--video" class="box-body" style="display: none;">
                {{ form_row(form.linkVideoModule1) }}
            </div>
            {# <div class="form-group" data-type="file_list">
                <label class="control-label">Topos</label> (<i>Types de fichiers acceptés : PDF</i>)
                <br>
                <ul class="files" data-prototype="{{ form_widget(form.topoProgrammeFiles.vars.prototype)|e('html_attr') }}">
                    {% for topoFile in form.topoProgrammeFiles %}
                        <li class="files_list">
                            <label>{{ topoFile.vars.value.topoOriginalName }}</label>
                            {{ form_row(topoFile) }}
                        </li>
                    {% endfor %}
                </ul>
            </div> #}
            {{ form_row(form.tempTags) }}
            {{ form_widget(form._token) }}
        </div>
    </div>

    {{ form_row(form.save) }}

    {{ form_end(form, {'render_rest': false}) }}
    {% if fichiersTopo is defined %}
        <div class="box box-solid box-primary" id="filesBlock">
            <div class="box-header">
                <h3 class="box-title">Topos par défaut & Boite à outils</h3>
            </div>
            <div class="box-body">
                <div class="alert alert-warning">
                    Attention, les modifications du formulaire ci-dessus ne seront pas conservées à l'insertion d'un nouveau document, veuillez cliquer sur "Enregister" avant cela.
                </div>
                {% set errorDisplayed = false %}
                {% if fichiersTopo is not empty %}
                    {% for file in fichiersTopo %}
                        {% if file.topo is not null %}
                            {% if not errorDisplayed and forms_topo[file.id].vars.errors|length > 0 %}
                                {{ form_errors(forms_topo[file.id]) }}
                                {% set errorDisplayed = true %}
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                {% endif %}
                <ul class="uploadList">
                    <li>
                        {# Topo new #}
                        {{ "admin.formation.toposProgramme" | trans }}
                        {% if not errorDisplayed and forms_topo_new.vars.errors|length > 0 %}
                            {{ form_errors(forms_topo_new) }}
                            {% set errorDisplayed = true %}
                        {% endif %}
                        <ul>
                            {% if fichiersTopo is not empty %}
                                {% for file in fichiersTopo %}
                                    {% if file.topo is not null %}
                                        <li>
                                            {% set name = "admin.formation.topo"|trans %}
                                            {% if file.topoOriginalName %}
                                                {% set name = file.topoOriginalName %}
                                            {% endif %}
                                            <a class="link-topo" target="_blank" title="{{ name }}" href="{{ url('admin_topo_programme_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                            <div id="{{ "topoFile" ~ file.id }}" class="downloadFile downloadFile-depot downloadFileTopo">
                                                {% form_theme forms_topo[file.id] 'admin/form/no_errors.html.twig' %}
                                                {{ form_start(forms_topo[file.id],{'attr': {'class': 'formDownloadFile formDownloadFileTopos deletableFile'}}) }}
                                                {{ form_widget(forms_topo[file.id].topoFile) }}
                                                {% if forms_topo[file.id].submit is defined %}
                                                    {{ form_widget(forms_topo[file.id].submit) }}
                                                {% endif %}
                                                {{ form_widget(forms_topo[file.id]._token) }}
                                                {{ form_end(forms_topo[file.id], {render_rest: false}) }}
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                            <li>{{ "admin.formation.deposer"|trans }}
                                <div id="topoFile" class="downloadFile downloadFile-depot downloadFileTopo upload-topo">
                                    <i class="fa fa-download btn-download-file" id="{{ forms_topo_new.vars.name }}"></i>
                                    {% form_theme forms_topo_new 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(forms_topo_new,{'attr': {'class': 'formDownloadFile formDownloadFileTopos'}}) }}
                                    {{ form_widget(forms_topo_new.topoFile) }}
                                    {{ form_end(forms_topo_new) }}
                                </div>
                            </li>
                        </ul>
                    </li>
                    <br>
                    <li>
                        {# Toolbox new #}
                        {{ "admin.formation.tools" | trans }}
                        {% if not errorDisplayed and forms_topo_tool_new.vars.errors|length > 0 %}
                            {{ form_errors(forms_topo_tool_new) }}
                            {% set errorDisplayed = true %}
                        {% endif %}
                        <ul>
                            {% if fichiersTopoTool is not empty %}
                                {% for file in fichiersTopoTool %}
                                    {% if file.topo is not null %}
                                        <li>
                                            {% set name = "admin.formation.topo"|trans %}
                                            {% if file.topoOriginalName %}
                                                {% set name = file.topoOriginalName %}
                                            {% endif %}
                                            <a class="link-topo" target="_blank" title="{{ name }}" href="{{ url('admin_topo_tool_programme_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                            <div id="{{ "toolFile" ~ file.id }}" class="downloadFile downloadFile-depot downloadFileTopo">
                                                {% form_theme forms_topo_tool[file.id] 'admin/form/no_errors.html.twig' %}
                                                {{ form_start(forms_topo_tool[file.id],{'attr': {'class': 'formDownloadFile formDownloadFileTopos deletableFile'}}) }}
                                                {{ form_widget(forms_topo_tool[file.id].topoFile) }}
                                                {% if forms_topo_tool[file.id].submit is defined %}
                                                    {{ form_widget(forms_topo_tool[file.id].submit) }}
                                                {% endif %}
                                                {{ form_widget(forms_topo_tool[file.id]._token) }}
                                                {{ form_end(forms_topo_tool[file.id], {render_rest: false}) }}
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                            <li>{{ "admin.formation.deposer"|trans }}
                                <div id="toolFile" class="downloadFile downloadFile-depot downloadFileTopo upload-topo">
                                    <i class="fa fa-download btn-download-file" id="{{ forms_topo_tool_new.vars.name }}"></i>
                                    {% form_theme forms_topo_tool_new 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(forms_topo_tool_new,{'attr': {'class': 'formDownloadFile formDownloadFileTopos'}}) }}
                                    {{ form_widget(forms_topo_tool_new.topoFile) }}
                                    {{ form_end(forms_topo_tool_new) }}
                                </div>
                            </li>
                        </ul>
                    </li>
                    <br>
                    <li>
                        {# Documents pédagogiques new #}
                        {{ "admin.formation.documentsPedagogiques" | trans }}
                        {% if not errorDisplayed and forms_docs_pedagogique_new.vars.errors|length > 0 %}
                            {{ form_errors(forms_docs_pedagogique_new) }}
                            {% set errorDisplayed = true %}
                        {% endif %}
                        <ul>
                            {% if fichiersDocsPedagogiques is not empty %}
                                {% for file in fichiersDocsPedagogiques %}
                                    {% if file.topo is not null %}
                                        <li>
                                            {% set name = "admin.formation.topo"|trans %}
                                            {% if file.topoOriginalName %}
                                                {% set name = file.topoOriginalName %}
                                            {% endif %}
                                            <a class="link-topo" target="_blank" title="{{ name }}" href="{{ url('admin_document_pedagogique_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                            <div id="{{ "toolFile" ~ file.id }}" class="downloadFile downloadFile-depot downloadFileTopo">
                                                {% form_theme forms_docs_pedagogiques[file.id] 'admin/form/no_errors.html.twig' %}
                                                {{ form_start(forms_docs_pedagogiques[file.id],{'attr': {'class': 'formDownloadFile formDownloadFileTopos deletableFile'}}) }}
                                                {{ form_widget(forms_docs_pedagogiques[file.id].topoFile) }}
                                                {% if forms_docs_pedagogiques[file.id].submit is defined %}
                                                    {{ form_widget(forms_docs_pedagogiques[file.id].submit) }}
                                                {% endif %}
                                                {{ form_widget(forms_docs_pedagogiques[file.id]._token) }}
                                                {{ form_end(forms_docs_pedagogiques[file.id], {render_rest: false}) }}
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                            <li>{{ "admin.formation.deposer"|trans }}
                                <div id="toolFile" class="downloadFile downloadFile-depot downloadFileTopo upload-topo">
                                    <i class="fa fa-download btn-download-file" id="{{ forms_docs_pedagogique_new.vars.name }}"></i>
                                    {% form_theme forms_docs_pedagogique_new 'admin/form/no_errors.html.twig' %}
                                    {{ form_start(forms_docs_pedagogique_new,{'attr': {'class': 'formDownloadFile formDownloadFileTopos'}}) }}
                                    {{ form_widget(forms_docs_pedagogique_new.topoFile) }}
                                    {{ form_end(forms_docs_pedagogique_new) }}
                                </div>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    {% endif %}

{% endblock %}

{% block javascripts %}
    <script type="text/javascript" src="{{ asset('js/jquery.multi-select.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/jquery.quicksearch.js') }}"></script>
    <script>

        var creation = {{ creation ? "true" : "false" }};
        $(document).ready(function() {
             $('form[name="eduprat_domainbundle_programme"]').submit(function(e){
                var valid = true;
                if (creation && $('.unity-name').length == 1) {
                   if ( $("#eduprat_domainbundle_programme_unities_1_presence").val() == "E-Learning" &&
                        $('#eduprat_domainbundle_programme_formType').val() !== "predefined" &&
                        $('#eduprat_domainbundle_programme_formType').val() !== "survey" ){
                        valid = confirm("Attention, Vous êtes sur le point de créer une formation Elearning comprenant une seule unité, associée à un formulaire de type " + $('#eduprat_domainbundle_programme_formType').find('option:selected').text() + ".");
                   }
                }
                if (!valid) {
                    e.preventDefault();
                }
            });
        	$('#eduprat_domainbundle_programme_categories').multiSelect({});
        	$('#eduprat_domainbundle_programme_specialities').multiSelect({});
        	$('#eduprat_domainbundle_programme_exercisesMode').multiSelect({});
        	$('#eduprat_domainbundle_programme_prisesEnCharge').multiSelect({});
	        updateSpecialities();

            var multiselectInitialized = false;

        	function initMultiselect() {
        		if (!multiselectInitialized) {
			        $('#eduprat_domainbundle_programme_tags').multiSelect({
				        selectableHeader: "<input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
				        selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
				        afterInit: function(ms){
					        var that = this,
						        $selectableSearch = that.$selectableUl.prev(),
						        $selectionSearch = that.$selectionUl.prev(),
						        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
						        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

					        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
						        .on('keydown', function(e){
							        if (e.which === 40){
								        that.$selectableUl.focus();
								        return false;
							        }
						        });

					        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
						        .on('keydown', function(e){
							        if (e.which == 40){
								        that.$selectionUl.focus();
								        return false;
							        }
						        });
					        multiselectInitialized = true;
				        },
				        afterSelect: function(){
					        this.qs1.cache();
					        this.qs2.cache();
				        },
				        afterDeselect: function(){
					        this.qs1.cache();
					        this.qs2.cache();
				        }
			        });
                }
            }

	        $('#eduprat_domainbundle_programme_tags').multiSelect({
		        selectableHeader: "<input id='tag-text' type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
		        selectionHeader: "<input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'>",
		        afterInit: function(ms){
			        var that = this,
				        $selectableSearch = that.$selectableUl.prev(),
				        $selectionSearch = that.$selectionUl.prev(),
				        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
				        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

			        that.qs1 = $selectableSearch.quicksearch(selectableSearchString, {
                            removeDiacritics: true
                        })
				        .on('keydown', function(e){
					        if (e.which === 40){
						        that.$selectableUl.focus();
						        return false;
					        }
				        });

			        that.qs2 = $selectionSearch.quicksearch(selectionSearchString, {
                            removeDiacritics: true
                        })
				        .on('keydown', function(e){
					        if (e.which == 40){
						        that.$selectionUl.focus();
						        return false;
					        }
				        });
		        },
		        afterSelect: function(){
			        this.qs1.cache();
			        this.qs2.cache();
		        },
		        afterDeselect: function(){
			        this.qs1.cache();
			        this.qs2.cache();
		        }
	        });

            $('#add-tag').click(function(e) {
				e.preventDefault();
				e.stopPropagation();
				$("#add-tag-error").addClass("hidden");
                var tagBlock = $('#ms-eduprat_domainbundle_programme_tags');
                var tagSelection = tagBlock.find('.ms-selection');
                var tagSelectonList = tagSelection.find('.ms-list');

                let tag = $('#tag-text').val();
				var id = '#' + tag;

				let existingTags = tagBlock.find(".ms-selectable .ms-elem-selectable").map((i, v) => $(v).text().toLowerCase().normalize("NFD").replace(/\p{Diacritic}/gu, "")).get();

				if (existingTags.includes(tag.toLowerCase().normalize("NFD").replace(/\p{Diacritic}/gu, ""))) {
					$("#add-tag-error").removeClass("hidden");
				} else if (tag !== "" && $(id).length === 0) {
                    tagSelectonList.append('<li selected="selected" class="ms-elem-selection ms-selected new-tag" id='+tag+'><span class="new-tag">'+ tag +'</span></li>');
                    $('#eduprat_domainbundle_programme_tempTags').val($('#eduprat_domainbundle_programme_tempTags').val() + ";" + tag)

                    var newTagId = '#' + tag;
                    var newTag = tag;

                    $(newTagId).click(function(e) {
                        $('#eduprat_domainbundle_programme_tempTags').val($('#eduprat_domainbundle_programme_tempTags').val().replace(";" + newTag, ""))
                        $(this).remove();
                    })
                }
            })

            $('.new-tag').click(function(e) {
            })


	        $('#eduprat_domainbundle_programme_categories').change(updateSpecialities);

	        function updateSpecialities() {
		        var selected = $('#eduprat_domainbundle_programme_categories').find('option:selected').map(function (i, n) {
			        return $(n).text();
		        }).get();
		        $.each($('#ms-eduprat_domainbundle_programme_specialities').find('li.ms-optgroup-container'), function (index, value) {
			        var $li = $(value);
			        var value = $li.find('ul.ms-optgroup').find('.ms-optgroup-label').find('span').text();
			        if (selected.indexOf(value) === -1) {
				        $li.addClass('hidden');
				        $('#eduprat_domainbundle_programme_specialities').multiSelect('deselect', $('#eduprat_domainbundle_programme_specialities').find('optgroup[label="'+value+'"]').find('option').map(function (i, n) {
					        return $(n).attr('value');
				        }).get());
			        } else {
				        $li.removeClass('hidden');
			        }
		        });
	        }

            $('#eduprat_domainbundle_programme_sessionType').change(updateFormType);

	        function updateFormType(firstInit = false) {
                let $edupratDomainbundleProgrammeSessionType = $('#eduprat_domainbundle_programme_sessionType');
                var data = $edupratDomainbundleProgrammeSessionType.find('option:selected').first().data("form");
		        var selected = data ? data.split(",") : [];
                let $edupratDomainbundleProgrammeFormType = $('#eduprat_domainbundle_programme_formType');
                var $current = $edupratDomainbundleProgrammeFormType.find("option:selected");
                $.each($edupratDomainbundleProgrammeFormType.find('option'), function (index, value) {
                    var $li = $(value);
                    var value = $li.attr("value");
                    if (selected.length) {
                        if (selected.indexOf(value) === -1) {
                            $li.addClass('hidden');
                        } else {
                            $li.removeClass('hidden');
                        }
                    } else {
                        $li.removeClass('hidden');
                    }
                });

                if (!$current.hasClass("hidden")) {
                    $edupratDomainbundleProgrammeFormType.val($current.val());
                } else {
                    $edupratDomainbundleProgrammeFormType.val($edupratDomainbundleProgrammeFormType.find('option:not(.hidden)').first().attr("value"));
                }

                if (firstInit !== true) {
                    globalInitDescriptifHeureHorsConnexion();
                }
            }

	        updateFormType([true]);

            $('form.deletableFile').submit(function(e){
                var valid = confirm("Attention, vous êtes sur le point de supprimer un fichier commun à l'ensemble des sessions qui composent ce programme.");
                if (!valid) {
                    e.preventDefault();
                }
            });
        });
    </script>
    {{ tinymce_init() }}
    <script>
        var editor;
        var $connaissancesCollectionHolder;
        var $connaissancesSurchargeCollectionHolder;
        var $competencesCollectionHolder;
        var $competencesSurchargeCollectionHolder;
        var $objectivesCollectionHolder;
        var $objectivesSurchargeCollectionHolder;
        var $unitiesCollectionHolder;
        var $unitiesSurchargeCollectionHolder;

        var autocompleteUrl = "{{ url("admin_programme_autocomplete") }}";
        var checkDuplicateUrl = "{{ url("admin_programme_duplicate_check") }}";
        var isCopy = {% if copy is defined and copy %}true{% else %}false{% endif %};
        var isDuplicated = false;
        var isGuidedFormation = {% if isGuidedFormation is defined and isGuidedFormation %}true{% else %}false{% endif %};

        // connaissances
        var $addConnaissanceLink = $('<a href="#" class="add_connaissance_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter une connaissance</a>');
        var $newConnaissanceLinkLi = $('<div></div>').append($addConnaissanceLink);

        // competences
        var $addCompetenceLink = $('<a href="#" class="add_competence_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter une compétence</a>');
        var $newCompetenceLinkLi = $('<div></div>').append($addCompetenceLink);

        // objectives
        var $addObjectiveLink = $('<a href="#" class="add_competence_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter un objectif</a>');
        var $newObjectiveLinkLi = $('<div></div>').append($addObjectiveLink);

        var $addUnityLink = $('<a href="#" class="add_unity_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter une unité</a>');
        var $newUnityLinkLi = $('<div></div>').append($addUnityLink);

        jQuery(document).ready(function () {

            $('.btn-download-file').click(function () {
                $('form[name="'+$(this).attr('id')+'"]').find('input').click();
            });

            $(document).on('change', 'form.formDownloadFile input[type="file"]', function () {
                $(this).closest("form").submit();
            });

            // connaissances
            $connaissancesCollectionHolder = $('ul.connaissances');

	        $connaissancesCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLink($(this));
            });
            $connaissancesCollectionHolder.after($newConnaissanceLinkLi);
            $connaissancesCollectionHolder.data('index', $connaissancesCollectionHolder.find('> li.panel').length + 1);
            $addConnaissanceLink.on('click', function (e) {
                e.preventDefault();
                addItemFormConnaissance($connaissancesCollectionHolder);
            });

            // competences
            $competencesCollectionHolder = $('ul.competences');

	        $competencesCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLinkCompetence($(this));
            });
            $competencesCollectionHolder.after($newCompetenceLinkLi);
            $competencesCollectionHolder.data('index', $competencesCollectionHolder.find('> li.panel').length + 1);
            $addCompetenceLink.on('click', function (e) {
                e.preventDefault();
                addItemFormCompetence($competencesCollectionHolder);
            });

            // objectives
            $objectivesCollectionHolder = $('ul.objectives');

	        $objectivesCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLinkObjective($(this));
            });
            $objectivesCollectionHolder.after($newObjectiveLinkLi);
            $objectivesCollectionHolder.data('index', $objectivesCollectionHolder.find('> li.panel').length + 1);
            $addObjectiveLink.on('click', function (e) {
                e.preventDefault();
                addItemFormObjective($objectivesCollectionHolder);
            });

            // unities
            if (!isGuidedFormation) {
                $unitiesCollectionHolder = $('ul.unities');

                $unitiesCollectionHolder.find('> li').each(function () {
                    addItemFormDeleteLinkUnity($(this));
                });
                $unitiesCollectionHolder.after($newUnityLinkLi);
                $unitiesCollectionHolder.data('index', $unitiesCollectionHolder.find('> li.panel').length + 1);
                $addUnityLink.on('click', function (e) {
                    e.preventDefault();
                    addItemFormUnity($unitiesCollectionHolder);
                    updateUnityName();
                });

                if ($unitiesCollectionHolder.find('> li.panel').length == 3) {
                    $('.add_unity_link').addClass('hidden');
                }
            }

	        $("form input, form select").on("invalid", function() {
		        var id = $(this).closest('.tab-pane').attr('id');
		        $('.nav-tabs a[href="#' + id + '"]').tab('show');
	        });

            $('#eduprat_domainbundle_programme_pictureFile_file').change(function() {
                 size = this.files[0].size;
                 if (size > 2048000) {
                     this.value = '';
                     alert('Le poids de l\'image que vous souhaitez importer est supérieur à la taille autorisée de 2 Mo.\nMerci de l\'optimiser et réessayer.')
                 }
             })

            $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_file').change(function() {
                 size = this.files[0].size;
                 if (size > 2048000) {
                     this.value = '';
                     alert('Le poids de l\'image que vous souhaitez importer est supérieur à la taille autorisée de 2 Mo.\nMerci de l\'optimiser et réessayer.')
                 } else {
                    $("#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_delete").prop("checked", false);
                 }
             })

            $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_file').change(function() {
                 size = this.files[0].size;
                 if (size > 2048000) {
                     this.value = '';
                     alert('Le poids de l\'image que vous souhaitez importer est supérieur à la taille autorisée de 2 Mo.\nMerci de l\'optimiser et réessayer.')
                 } else {
                    $("#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_delete").prop("checked", false);
                 }
             })

            $('#eduprat_domainbundle_programme_presence').change(updateAdditionalInfos);

	        function updateAdditionalInfos() {
                var data = $('#eduprat_domainbundle_programme_presence').find('option:selected').first().data("infos");
                $(".vich-image-infos img").addClass("hidden");
                tinyMCE.get("eduprat_domainbundle_programme_additionalInfos").setContent(data.additionalInfos ? data.additionalInfos : "");
                $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureLink').val(data.firstAdditionalInfosPictureLink ? data.firstAdditionalInfosPictureLink : "");
                $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFrom').val(data.firstAdditionalInfosPictureFrom ? data.firstAdditionalInfosPictureFrom : "").change();
                $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureLink').val(data.secondAdditionalInfosPictureLink ? data.secondAdditionalInfosPictureLink : "");
                $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFrom').val(data.secondAdditionalInfosPictureFrom ? data.secondAdditionalInfosPictureFrom : "").change();
                 if (!data.hasOwnProperty("firstAdditionalInfosPictureFrom")) {
                	$("#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_delete").prop("checked", true);
                }
                if (!data.hasOwnProperty("secondAdditionalInfosPictureFrom")) {
                	$("#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_delete").prop("checked", true);
                }
	        }

	        $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_file').change(function(e) {
	        	$('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFrom').val("").change();
	        });


	        $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_file').change(function(e) {
	        	$('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFrom').val("").change();
	        });

	        $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFrom').change(function(e) {
	        	var img = $(this).val();
	        	var url = "/uploads/programme/picture/" + img;
	        	if (img !== "") {
                    $(".firstAdditionalInfosPictureFrom.vich-image").removeClass("hidden");
                    $(".firstAdditionalInfosPictureFrom.vich-image img").attr("src", url);
	        	} else {
                    $(".firstAdditionalInfosPictureFrom.vich-image").addClass("hidden");
                    $(".firstAdditionalInfosPictureFrom.vich-image img").removeAttr("src");
	        	}
	        });

	        $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFrom').change(function(e) {
	        	var img = $(this).val();
	        	var url = "/uploads/programme/picture/" + img;
	        	if (img !== "") {
                    $(".secondAdditionalInfosPictureFrom.vich-image").removeClass("hidden");
                    $(".secondAdditionalInfosPictureFrom.vich-image img").attr("src", url);
	        	} else {
                    $(".secondAdditionalInfosPictureFrom.vich-image").addClass("hidden");
                    $(".secondAdditionalInfosPictureFrom.vich-image img").removeAttr("src");
	        	}
	        });

	        if ($("#eduprat_domainbundle_programme_reference").val() === "") {
	            setTimeout(updateAdditionalInfos, 300);
	        }

        });

        function updateUnityName() {
            var count = 1;
            $('.unity-name').each(function () {
                $(this).html("Unité " + count);
                count ++;
            });
        }

        function updateDurations() {
            var totalTime = 0;
            $('.hours').each(function( index ) {
                totalTime += (+$(this).val())
            });
            $('.durationPresentielle').val(totalTime);

            var totalTime = 0;
            $('.elearning-hours').each(function( index ) {
                totalTime += (+$(this).val())
            });
            $('.durationNotPresentielle').val(totalTime);
        }

        function addItemFormConnaissance($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLink($newFormLi);
        }

         function addItemFormCompetence($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLinkCompetence($newFormLi);
        }

         function addItemFormObjective($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLinkObjective($newFormLi);
        }

        function addItemFormUnity($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);
            $collectionHolder.data('index', index + 1);
            var $newFormLi = $(newForm);
            $collectionHolder.append($newFormLi);
            //addItemFormDeleteLinkUnity($newFormLi);
            $unitiesCollectionHolder.find('> li').each(function () {
                if (!$(this).find('.btn-danger').length) {
                    addItemFormDeleteLinkUnity($(this));
                }
            });
            if ($unitiesCollectionHolder.find('> li.panel').length == 3) {
                $('.add_unity_link').addClass('hidden');
            }
            launchTinyMceUnity($newFormLi.find('.presence'));
        }

        function addItemFormDeleteLink($itemFormLi) {
	        var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer la connaissance</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $itemFormLi.remove();
            });
        }

        function addItemFormDeleteLinkCompetence($itemFormLi) {
	        var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer la compétence</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $itemFormLi.remove();
            });
        }

        function addItemFormDeleteLinkObjective($itemFormLi) {
	        var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer l\'objectif</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $itemFormLi.remove();
            });
        }


        function addItemFormDeleteLinkUnity($itemFormLi) {
	        var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer l\'unité</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $itemFormLi.remove();
                $('.add_unity_link').removeClass('hidden');
                updateUnityName();
                updateDurations();
                $('.presence').trigger('change');
            });
        }

        var $coordinatorsCollectionHolder;
        var $coordinatorsSurchargeCostCollectionHolder;
        var $coordinatorsSurchargeAvancesCollectionHolder;

        // coordinators
        var $addCoordinatorLink = $('<a href="#" class="add_coordinator_link btn btn-eduprat"><span class="glyphicon glyphicon-plus-sign"></span> Ajouter un coordinateur</a>');
        var $newCoordinatorLinkLi = $('<div></div>').append($addCoordinatorLink);

        var $firstInit = true;

        jQuery(document).ready(function () {
            // coordinators
            $coordinatorsCollectionHolder = $('ul.coordinators');
	        $coordinatorsSurchargeCostCollectionHolder = $('ul.coordinators-surcharge-cost');
	        $coordinatorsSurchargeAvancesCollectionHolder = $('ul.coordinators-surcharge-avances');
            $coordinatorsCollectionHolder.find('> li').each(function () {
                addItemFormDeleteLinkCoordinator($(this));
                moveSurchargeCoordinator($(this));
                var id = $(".form-group:first", $(this)).children().attr('id');
            });
            $coordinatorsCollectionHolder.after($newCoordinatorLinkLi);
            $coordinatorsCollectionHolder.data('index', $coordinatorsCollectionHolder.find('> li.panel').length + 1);
            $addCoordinatorLink.on('click', function (e) {
                e.preventDefault();
                addItemFormCoordinator($coordinatorsCollectionHolder);
            });

            if ($('.data-programme').length) {
	            $(document).on('change', '.update-honorary', function(e) {
		            previewCoordinatorCost($(this).closest('li[data-index]').data('index'));
	            });

	            $(document).on('change', '.update-cost', function(e) {
		            previewFormationCost($(this).closest('li[data-index]').data('index'));
	            });

	            $(document).on('change', '.update-all-honorary', function(e) {
		            previewAllCoordinatorCost();
		            previewAllFormationCost();
	            });

	            previewAllCoordinatorCost();
	            previewAllFormationCost();
            } else {
            	$('form[name=eduprat_domainbundle_programme]').submit(function(e){
                    if (isDuplicated && !confirm('Une formation avec le même titre et la même référence existe déjà, êtes vous sûr de vouloir enregistrer ? Attention la création de doublon peut entraîner des problèmes d\'analyse de certaines données')) {
                        e.preventDefault();
                        return false;
                    }
                })
            }

            if (isCopy) {
                if (!$('#eduprat_domainbundle_programme_pictureFile_file').val()) {
                    let picture = $('.vich-image > a').attr("href");
                    if (picture) {
                        $('#eduprat_domainbundle_programme_pictureFrom').val(picture.substring(picture.lastIndexOf('/') + 1));
                    }
                }
                if (!$('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_file').val()) {
                    let picture = $('.vich-image > a').attr("href");
                    if (picture) {
                        $('#eduprat_domainbundle_programme_firstAdditionalInfospictureFrom').val(picture.substring(picture.lastIndexOf('/') + 1));
                    }
                }
                if (!$('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_file').val()) {
                    let picture = $('.vich-image > a').attr("href");
                    if (picture) {
                        $('#eduprat_domainbundle_programme_secondAdditionalInfospictureFrom').val(picture.substring(picture.lastIndexOf('/') + 1));
                    }
                }
            }
        });


        function addItemFormCoordinator($collectionHolder) {
            var prototype = $collectionHolder.data('prototype');
            var index = $collectionHolder.data('index');
            var newForm = prototype.replace(/__name__/g, index);

            newForm = $(newForm);

            $collectionHolder.data('index', index + 1);
            var $newFormLi = newForm;
            $collectionHolder.append($newFormLi);
            addItemFormDeleteLinkCoordinator($newFormLi);
	        moveSurchargeCoordinator($newFormLi);
        }

        function moveSurchargeCoordinator($itemFormLi) {
        	$itemFormLi.find('.coordinator-surcharge-name').val($itemFormLi.find('select').find('option:selected').text());
	        $coordinatorsSurchargeCostCollectionHolder.append($itemFormLi.find('.coordinator-surcharge-cost'));
	        $coordinatorsSurchargeAvancesCollectionHolder.append($itemFormLi.find('.coordinator-surcharge-avances'));
        }

        function addItemFormDeleteLinkCoordinator($itemFormLi) {
        	var index = $itemFormLi.data('index');
            var $removeFormA = $('<a class="btn btn-danger" href="#" title="Supprimer"><i class="fa fa-trash-o"></i> Supprimer le coordinateur</a>');
            $itemFormLi.append($removeFormA);

            $removeFormA.on('click', function (e) {
                e.preventDefault();
	            $('.coordinator-surcharge-avances[data-index="' + index + '"]').remove();
	            $('.coordinator-surcharge-cost[data-index="' + index + '"]').remove();
                $itemFormLi.remove();
            });
        }

        $(document).on('change', '.connaissance-person-select', function(e) {
	        var index = $(this).closest('.panel').data('index');
	        $('.connaissance-surcharge[data-index="' + index + '"]').find('.update-all-honorary').val($(this).find('option:selected').data('price'));
	        $('.connaissance-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());

        });

        $(document).on('change', '.competence-person-select', function(e) {
	        var index = $(this).closest('.panel').data('index');
	        $('.competence-surcharge[data-index="' + index + '"]').find('.update-all-honorary').val($(this).find('option:selected').data('price'));
	        $('.competence-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());

        });

        $(document).on('change', '.objective-person-select', function(e) {
	        var index = $(this).closest('.panel').data('index');
	        $('.objective-surcharge[data-index="' + index + '"]').find('.update-all-honorary').val($(this).find('option:selected').data('price'));
	        $('.objective-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());

        });

        $(document).on('change', '.coordinator-input', function(e) {
        	var index = $(this).closest('.panel').data('index');
	        $('.coordinator-surcharge-name[data-index="' + index + '"]').val($(this).find('option:selected').text());
        });

        $('#eduprat_domainbundle_programme_title, #eduprat_domainbundle_programme_reference').on("keyup change", function(e) {
        	checkDuplicate($('#eduprat_domainbundle_programme_title').val(), $('#eduprat_domainbundle_programme_reference').val());
        });

        function initDescriptifHeureHorsConnexion(numUnity) {
            var $li = $('.unities li').eq(numUnity - 1);

            var auditType = $('#eduprat_domainbundle_programme_sessionType').val();
            var formType = $('#eduprat_domainbundle_programme_formType').val();
            var presenceUnity = $li.find('.presence').val();
            var $descriptifOfflineIdField = $li.find('.descriptifOffline');
            $tiny = tinymce.get($descriptifOfflineIdField.attr('id'));
            let content = ""
            if (auditType === 'formation_audit' && formType === 'audit' && presenceUnity === 'E-Learning') {
                if (numUnity === 1) {
                    content = '{{ "admin.programme.descriptifOffline.formation_audit_audit.unity1"|trans|raw }}';
                } else if (numUnity === 3) {
                    content = '{{ "admin.programme.descriptifOffline.formation_audit_audit.unity3"|trans|raw }}';
                }
            }
            if ($tiny) {
                $tiny.setContent(content);
            }
        }

        function globalInitDescriptifHeureHorsConnexion() {
            initDescriptifHeureHorsConnexion(1);
            initDescriptifHeureHorsConnexion(3);
        }

        document.getElementById('eduprat_domainbundle_programme_sessionType').addEventListener('change', function () {
            globalInitDescriptifHeureHorsConnexion();
        });
        document.getElementById('eduprat_domainbundle_programme_formType').addEventListener('change', function () {
            globalInitDescriptifHeureHorsConnexion();
        });

        let newTinyMce = [];
        $('.tinymce').each(function() {
            newTinyMce.push('#' + $(this).attr('id'));
        })
        function launchTinyMceUnity($this) {
            $this.closest('li').find('.tinymce').each(function() {
                newTinyMce.push('#' + $(this).attr('id'));
            });
            if (newTinyMce.length) {
                let options = stfalcon_tinymce_config;
                options.selector = newTinyMce;
                initTinyMCE(options);
            }
        }

        $(document).on('change', '.presence', function (e, firstInit = false) {
            e.preventDefault();
            var numUnity = $(this).closest('li').data('index');
            var $types = $(this).closest('li').find('[data-type]');
            $types.addClass('hidden');

            if ($(this).val() == "E-Learning") {
                $types.filter('[data-type=elearning-connected]').removeClass('hidden');
                $types.filter('[data-type=elearning-offline]').removeClass('hidden');
                // Pas minTime sur l'unité 2 quand elearning car déclarée au niveau leçon elearning
                if ($(this).parent().parent().find('.unity-name').text() != "Unité 2") {
                    $types.filter('[data-type=elearning-minTime]').removeClass('hidden')
                }
                var nbHoursId = "#" + $(this).attr("id").replace("presence", "nbHours");
                $(nbHoursId).val(0);
                $('.hours').trigger('change');
            } else {
                var nbHoursOfflineId = "#" + $(this).attr("id").replace("presence", "nbHoursOffline");
                var nbHoursConnectedId = "#" + $(this).attr("id").replace("presence", "nbHoursConnected");
                $(nbHoursOfflineId).val(0);
                $(nbHoursConnectedId).val(0);
                $('.elearning-hours').trigger('change');
                if ($(this).val() !== "") {
                    $types.filter('[data-type=nbDays]').removeClass('hidden');
                    $types.filter('[data-type=nbHours]').removeClass('hidden');
                }
            }
            var methodId = "#" + $(this).attr("id").replace("presence", "method");
            if ($(methodId).val() == "continue" && !firstInit) {
                var id = "#" + $(this).attr("id");
                var data = $(id).find('option:selected').first().data("infos");
                $(".vich-image-infos img").addClass("hidden");
                tinyMCE.get("eduprat_domainbundle_programme_additionalInfos").setContent(data.additionalInfos ? data.additionalInfos : "");
                $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureLink').val(data.firstAdditionalInfosPictureLink ? data.firstAdditionalInfosPictureLink : "");
                $('#eduprat_domainbundle_programme_firstAdditionalInfosPictureFrom').val(data.firstAdditionalInfosPictureFrom ? data.firstAdditionalInfosPictureFrom : "").change();
                $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureLink').val(data.secondAdditionalInfosPictureLink ? data.secondAdditionalInfosPictureLink : "");
                $('#eduprat_domainbundle_programme_secondAdditionalInfosPictureFrom').val(data.secondAdditionalInfosPictureFrom ? data.secondAdditionalInfosPictureFrom : "").change();
                 if (!data.hasOwnProperty("firstAdditionalInfosPictureFrom")) {
                	$("#eduprat_domainbundle_programme_firstAdditionalInfosPictureFile_delete").prop("checked", true);
                }
                if (!data.hasOwnProperty("secondAdditionalInfosPictureFrom")) {
                	$("#eduprat_domainbundle_programme_secondAdditionalInfosPictureFile_delete").prop("checked", true);
                }
            }
            if (!firstInit) {
                initDescriptifHeureHorsConnexion(numUnity);
            }
        });

        $('.presence').trigger('change', [true]);

        $(document).on('change', '.hours', function (e) {
            updateDurations();
        });

        $(document).on('change', '.elearning-hours', function (e) {
            updateDurations();
        });

        function getFormationCost() {
            var $costInputs = $('#collapseCost').find('input');
            var cost = 0;
            $.each($costInputs, function(key, input) {
            	cost += parseFloat($.isNumeric($(input).val()) ? $(input).val() : 0);
            });
            return cost;
        }

        function getCoordinatorAvances(index) {
	        var $costInputs = $('#collapse-coord-' + index).find('input');
	        var cost = 0;
	        $.each($costInputs, function(key, input) {
		        cost += $.isNumeric(parseFloat($(input).val())) ? parseFloat($(input).val()) : 0;
	        });
	        return cost;
        }

        function checkDuplicate(title, reference) {
	        jQuery.ajax({
		        url: checkDuplicateUrl,
		        type: 'POST',
		        data: {title: title, reference: reference},
		        success: function(data, textStatus, xhr) {
                    isDuplicated = data.duplicated;
		        },
		        error: function(data, textStatus, xhr) {
			        console.log(data);
		        },
	        });
        }

        let presence = document.getElementById('eduprat_domainbundle_programme_presence');
        let programmeAssocieEL = document.getElementById('eduprat_domainbundle_programme_programmesAssocies_programmeElearning');
        let programmeAssocieCV = document.getElementById('eduprat_domainbundle_programme_programmesAssocies_programmeClasseVirtuelle');
        let programmeAssocieSite = document.getElementById('eduprat_domainbundle_programme_programmesAssocies_programmeSurSite');
        let updateLabelProgrammeAssocies = function() {
            programmeAssocieEL.closest('.form-group').classList.remove('hide');
            programmeAssocieCV.closest('.form-group').classList.remove('hide');
            programmeAssocieSite.closest('.form-group').classList.remove('hide');
            if (presence.value == 'Sur site') {
                programmeAssocieSite.closest('.form-group').classList.add('hide');
            } else if (presence.value == 'Classe virtuelle') {
                programmeAssocieCV.closest('.form-group').classList.add('hide');
            } else if (presence.value == 'E-Learning') {
                programmeAssocieEL.closest('.form-group').classList.add('hide');
            }
        };
        presence.addEventListener('change', function(e) {
            programmeAssocieEL.value = "";
            programmeAssocieCV.value = "";
            programmeAssocieSite.value = "";
            updateLabelProgrammeAssocies();
        });
        updateLabelProgrammeAssocies();

        var toggleVideoField = function () {
            var champAValue = document.getElementById('eduprat_domainbundle_programme_sessionType').value;
            var champB = document.getElementById('box-body--video');

            var isVignette = champAValue === 'formation_vignette_audit' || champAValue === 'formation_vfc';
            champB.style.display = isVignette ? 'block' : 'none';
        }
        document.getElementById('eduprat_domainbundle_programme_sessionType').addEventListener('change', toggleVideoField);
        toggleVideoField();
    </script>
{% endblock %}
