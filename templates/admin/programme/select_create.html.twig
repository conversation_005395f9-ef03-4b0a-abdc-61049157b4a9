{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'formation.titles.create_select_type'|trans }}{% endblock %}

{% block stylesheets %}
    <style>
        .control-label {
            color: #109199;
        }

        input[type="radio"] {
            display: none;
        }

        #select_formation_create_formationType label,
        #select_formation_create_presence label {
            display: flex;
            gap: 8px;
            justify-content: center;
            align-items: center;
            padding: 16px 20px;
            border: 2px solid #9B9B9B;
            border-radius: 5px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.3s;
            box-sizing: border-box;
            text-align: center;
            color: #9B9B9B;
        }

        #select_formation_create_presence label {
            flex-direction: column;
            gap: 16px;
        }
        #select_formation_create_formationType label.active:not(.disabled),
        #select_formation_create_presence label:not(.disabled).active {
            color: #109199;
            border-color: #109199;
            border-width: 4px;
        }
        #select_formation_create_formationType label.disabled,
        #select_formation_create_presence label.disabled {
            opacity: 0.5;
        }

        #select_formation_create_formationType label.active:not(.disabled)::before,
        #select_formation_create_presence label.active:not(.disabled)::before {
            content: '';
            position: absolute;
            top: -7px;
            right: -7px;
            width: 20px;
            height: 20px;
            border: 2px solid #109199;
            background-color: #109199;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            font-weight: normal;
        }
        #select_formation_create_formationType label.active:not(.disabled)::after,
        #select_formation_create_presence label.active:not(.disabled)::after {
            content: '✔';
            color: white;
            position: absolute;
            top: -8px;
            right: -4px;
            font-size: 16px;
        }
        #select_formation_create_formationType .radio+.radio,
        #select_formation_create_presence .radio+.radio {
            margin: 10px 0;
        }
        .flex-container {
            display: flex;
        }
        .flex-column {
            flex-direction: column;
            width: 100%;
        }
        .pll {
            padding-top: 1.5em !important;
        }
    </style>
{% endblock %}

{% block body %}
<div class="nav-tabs-custom">
    <div class="tab-content">
        <div class="tab-pane active ptl pll">
            {{ form_start(selectFormationCreate) }}
            <div class="form-group flex-container">
                <div style="width: 32px; height: 32px; border-radius: 100px; border: 2px #109199 solid; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
                    <div style="color: #109199; font-size: 16px; font-family: Source Sans Pro; font-weight: 700;">1</div>
                </div>
                <div class="flex-container flex-column mll">
                    <div class="flex-container mts">
                        {{ form_label(selectFormationCreate.formationType) }}
                    </div>
                    {{ form_errors(selectFormationCreate.formationType) }}
                    <div id="select_formation_create_formationType" class="row">
                        {% for child in selectFormationCreate.formationType.children %}
                            <div class="radio col-sm-12 col-md-4">
                                <div class="radio">
                                    <label for="{{ child.vars.id }}" class="required active">
                                        <input type="radio" id="{{ child.vars.id }}" name="{{ child.parent.vars.full_name }}" required="required" value="{{ child.vars.value }}">
                                        <i class="fa fa-lg {% if child.vars.value == 'VFC' %}fa-user-md{% else %}fa-plus{% endif %}"></i>
                                        {{ child.vars.label|trans }}
                                    </label>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% do selectFormationCreate.formationType.setRendered %}
            <div class="form-group flex-container">
                <div style="width: 32px; height: 32px; border-radius: 100px; border: 2px #109199 solid; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
                    <div style="color: #109199; font-size: 16px; font-family: Source Sans Pro; font-weight: 700;">2</div>
                </div>
                <div class="flex-container flex-column mll">
                    <div class="flex-container flex-column">
                        <div class="flex-container mts">
                        {{ form_label(selectFormationCreate.presence) }}
                        </div>
                    </div>
                    {{ form_errors(selectFormationCreate.presence) }}
                    <div id="select_formation_create_presence" class="row">
                        {% for child in selectFormationCreate.presence.children %}
                            <div class="radio col-sm-12 col-md-4">
                                <div class="radio">
                                    <label for="{{ child.vars.id }}" class="required active">
                                        <input type="radio" id="{{ child.vars.id }}" name="{{ child.parent.vars.full_name }}" required="required" value="{{ child.vars.value }}">
                                        <i class="fa
                                            {% if child.vars.value == 'Sur site' %}
                                                fa-map-marker
                                            {% elseif child.vars.value == 'E-Learning' %}
                                                fa-laptop
                                            {% elseif child.vars.value == 'Classe virtuelle' %}
                                                fa-globe
                                            {% endif%} fa-4x"></i>
                                        {{ child.vars.value }}
                                    </label>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% do selectFormationCreate.presence.setRendered %}
                </div>
            </div>
            {{ form_end(selectFormationCreate) }}
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    <script type="text/javascript">
        var combinaisonValide = JSON.parse('{{ combinaisons|json_encode|raw }}');
        let presences = document.querySelectorAll('input[name="select_formation_create[presence]"]');
        let typeFormations = document.querySelectorAll('input[name="select_formation_create[formationType]"]');
        document.addEventListener('DOMContentLoaded', () => {
            const radioButtons = document.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', () => {
                    synchroLabelAndInput(radioButtons);
                });
            });
            typeFormations.forEach((typeFormation) => {
                typeFormation.addEventListener('change', () => {
                    function reinitPresence() {
                        presences.forEach(presence => {
                            presence.disabled = false;
                            presence.checked = false;
                        });
                    }
                    reinitPresence();
                    toggleAccessCombinaison();
                    synchroLabelAndInput(radioButtons);
                });
            });
            synchroLabelAndInput(radioButtons);
        });
        function synchroLabelAndInput(radioButtons) {
            radioButtons.forEach(btn => {
                const label = document.querySelector(`label[for="${btn.id}"]`);
                if (btn.checked) {
                    label.classList.add('active');
                } else {
                    label.classList.remove('active');
                }
                if (btn.disabled) {
                    label.classList.add('disabled');
                } else {
                    label.classList.remove('disabled');
                }
            });
        }
        function toggleAccessCombinaison() {
            let typeFormation = document.querySelector('input[name="select_formation_create[formationType]"]:checked').value;
            presences.forEach(presence => {
                presence.disabled = typeFormation ? !combinaisonValide[typeFormation].includes(presence.value) : false;
            });
        }
    </script>
{% endblock %}
