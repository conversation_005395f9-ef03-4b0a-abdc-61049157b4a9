{% extends 'admin/base.html.twig' %}

{% block title %}Foire aux questions{% endblock %}

{% block body %}
    <div class="box box-info">
        <div class="box-header"></div>
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ "admin.faq.position"|trans }}</th>
                            <th style="width: 50%">{{ "admin.faq.question"|trans }}</th>
                            <th style="width: 30%">{{ "admin.faq.answer"|trans }}</th>
                            <th>{{ 'admin.global.actions' | trans }}</th>
                        </tr>
                        </thead>
                        <tbody id="sortable">
                        {% for faq in faqs %}
                            <tr data-id="{{ faq.id}}">
                                <td class="text-center"><i class="handle fa fa-align-justify"></i></td>
                                <td style="width: 50%">{{ faq.question|length > 200 ? faq.question|slice(0, 200) ~ '...' : faq.question  }}</td>
                                <td style="width: 30%">{{ faq.answer|length > 200 ? faq.answer|slice(0, 200) ~ '...' : faq.answer  }}</td>
                                <td>
                                    <a href="{{ url('admin_faq_edit', {'id': faq.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.edit'|trans }}"><i class="glyphicon glyphicon-edit"></i></a>
                                    <a href="{{ url('admin_faq_delete', {'id': faq.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script
            src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"
            integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU="
            crossorigin="anonymous"></script>
    <script>
        $(document).ready(function() {
	        $("#sortable").sortable({
		        placeholder: "ui-state-highlight",
		        handle: ".handle",
		        helper: "clone",
		        tolerance: "pointer",
		        stop: function (event, ui) {

			        jQuery.ajax({
				        url: '{{ url("admin_faq_position") }}',
				        type: 'POST',
				        dataType: 'json',
				        data: {
				        	positions: getPositions()
                        },
				        success: function (data, textStatus, xhr) {
					        console.log(data);
				        },
				        error: function (xhr, textStatus, errorThrown) {

				        }
			        });

                }
	        });
	        $("#sortable").disableSelection();
        });

        function getPositions() {
	        return $("#sortable").find('tr').map(function() {
		        return $(this).data('id');
	        }).get();
        }
    </script>
{% endblock %}
