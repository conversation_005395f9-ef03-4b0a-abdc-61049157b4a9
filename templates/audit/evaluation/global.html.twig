{% extends 'audit/base.html.twig' %}

{% form_theme form 'audit/evaluation/question.html.twig' %}

{% block css %}
    <link rel="stylesheet" href="{{ asset('css/evaluation-style.css') }}">
{% endblock %}

{% block body %}
    {% include "audit/common/timeline.html.twig" %}

    <h1 class="text-center">{{ "evaluation.label"|trans }} {{ ("evaluation.titles." ~ role)|trans }}</h1>
    <h2 class="text-center">{{ "evaluation.title"|trans }} - {{ ("evaluation.titles." ~ role)|trans }}</h2>
    <hr>
    <div>{{ "evaluation.desc_1" | trans }}</div>
    <div>{{ "evaluation.desc_4" | trans }}</div>
    <br/>
    <div>{{ "evaluation.desc_5" | trans }}</div>
    <div>{{ "evaluation.desc_6" | trans }}</div>
    <br/>

    {% if former is not null %}
        <h4 class="evaluation-former">Questions concernant le formateur suivant : <b>{{ former.person.civility }} {{ former.person.fullname }}</b></h4>
    {% endif %}

    {{ form_start(form) }}
    {% for child in form %}
        {% if child.vars.title is defined and child.vars.title is not empty %}
            {% if person.isCoordinator and ("evaluation.global." ~ child.vars.title ~ ".title_coordinator")|trans != ("evaluation.global." ~ child.vars.title ~ ".title_coordinator") %}
                <h3>{{ ("evaluation.global." ~ child.vars.title ~ ".title_coordinator")|trans }}</h3>
            {% else %}
                <h3>{{ ("evaluation.global." ~ child.vars.title ~ ".title")|trans }}</h3>
            {% endif %}
            {% if person.isCoordinator and ("evaluation.global." ~ child.vars.title ~ ".label_coordinator")|trans != ("evaluation.global." ~ child.vars.title ~ ".label_coordinator") %}
                <h4>{{ ("evaluation.global." ~ child.vars.title ~ ".label_coordinator")|trans }}</h4>
            {% else %}
                <h4>{{ ("evaluation.global." ~ child.vars.title ~ ".label")|trans }}</h4>
            {% endif %}
        {% endif %}
        {{ form_row(child) }}
    {% endfor %}
    {{ form_rest(form) }}
    {% if page == manager.getNbPages(formation, role) %}
        <div class="form-group">
            <p>
                Le traitement des données recueillies permet d’assurer le suivi de votre parcours de formation.<br>
                Pour en savoir plus sur la gestion de vos données à caractère personnel et pour exercer vos droits, cliquez
                <a href="{{ url('eduprat_front_personal_data') }}">ici</a> ou contactez <a href="mailto:<EMAIL>"><EMAIL></a>.
            </p>
        </div>
    {% endif %}

    <div class="page-footer">
        {% if validationDisabled and isLastPage %}<div class="alert alert-danger alert-obligatoire module-notif">{% set pastTime =  moduleFromStep.minTime / 60 %} Ce module nécessite que vous passiez au minimum {{ pastTime }} minute{% if pastTime > 1%}s{% endif %} pour le valider. Nous vous invitons à le parcourir de nouveau afin d'atteindre le temps minimum.</div>{% endif %}
        <div class="text-right">
            <button {% if validationDisabled and isLastPage %} disabled {% endif %} type="submit" id="evaluation-submit" class="btn btn-eduprat btn-medium {% if validationDisabled and isLastPage %} validationDisabled disabled {% endif %}">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></button> 
        </div>
        {% if course %}
            {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
            {% if modulesLeftSentence %}
                <div class="text-right font-16">
                {{ modulesLeftSentence|raw }}
                </div>
            {% endif %}
        {% endif %}
    </div>
    {{ form_end(form) }}
    <div class="text-center">
        <ul class="pagination">
            {% if manager.getNbPages(formation, role) %}
            {% for pageId in 1..manager.getNbPages(formation, role)  %}
                {% if pageId <= page %}
                    <li {% if pageId == page %}class="active"{% endif %}>
                        <a href="{{ url('eduprat_evaluation_global', {person: person.id, formation: formation.id, page: pageId }) }}">{{ pageId }}</a>
                    </li>
                {% else %}
                    <li class="inactive">
                        <span>{{ pageId }}</span>
                    </li>
                {% endif %}
            {% endfor %}
            {% endif %}
        </ul>
    </div>
{% endblock %}

{% block javascripts %}
<script>
	jQuery(document).ready(function () {
		$(".question-radio-undefined input:radio").attr("checked", false);
		redirectIfInactive(120 * 60 * 1000)
	});
</script>
{% endblock %}