<script>
	jQuery(document).ready(function() {
		var form = $('.login-form');
		var submit_btn = $('button[type=submit]');
		var username = $('input[name=_username]');
		var password = $('input[name=_password]');
		var token = $('input[name=_csrf_token]');
		var error_block = $('#error-block');
		var captcha_container = $('#captcha-container');
		var hcaptcha_widget_id = null;

		if (form.length) {
			$(window).keydown(function(event){
				if(event.keyCode === 13) {
					submit_btn.click();
					event.preventDefault();
					return false;
				}
			});

			submit_btn.click(function(e) {
				e.preventDefault();
				var error = false;

				var user_group = username.closest('.form-group');
				user_group.removeClass('has-error');
				user_group.find('#username-error').remove();

				var pass_group = password.closest('.form-group');
				pass_group.removeClass('has-error');
				pass_group.find('#password-error').remove();

				// Nettoyer les erreurs de captcha
				captcha_container.removeClass('has-error');
				captcha_container.find('#captcha-error').remove();

				if ( $.trim(username.val()) === "" ) {
					user_group.addClass('has-error');
					user_group.append('<span id="username-error" class="help-block">Ce champ est obligatoire</span>');
					error = true;
				}

				if ( $.trim(password.val()) === "" ) {
					pass_group.addClass('has-error');
					pass_group.append('<span id="password-error" class="help-block">Ce champ est obligatoire</span>');
					error = true;
				}

				// Vérifier le captcha s'il est visible
				if (!captcha_container.hasClass('hide')) {
					var captcha_response = $('textarea[name="h-captcha-response"]').val();
					if (!captcha_response || captcha_response.trim() === "") {
						captcha_container.addClass('has-error');
						if (captcha_container.find('#captcha-error').length === 0) {
							captcha_container.append('<span id="captcha-error" class="help-block">Veuillez compléter le captcha</span>');
						}
						error = true;
					} else {
						captcha_container.removeClass('has-error');
						captcha_container.find('#captcha-error').remove();
					}
				}

				if (!error) {
					error_block.addClass('hide');
					var url = $('.login-form').attr('action');
					$.ajax({
						type        : 'POST',
						url         : url,
						data        : form.serialize(),
						dataType    : "json",
						success     : function(data, status, object) {
							token.val(data.token);
							error_block.removeClass('hide').addClass('callout-success').removeClass('callout-danger');
							error_block.find('p').text("Connexion en cours ...");
							window.location = data.redirect;
						},
						error: function(data, status, object){
							error_block.removeClass('hide');
							error_block.find('p').text(data.responseJSON.message);

							// Afficher le captcha si le compteur d'erreurs >= 3
							if (data.responseJSON && data.responseJSON.error_count_login_try >= 3) {
								showCaptcha();
							}

                            if (data.status === 429) {
                                form.remove();
                            }
						}

					});

				} else {
					error_block.removeClass('hide');
					error_block.find('p').text("Formulaire invalide");
				}
			});

			const togglePassword = document.querySelector('.togglePassword');
			const passwordInput = document.querySelector('#password');

			togglePassword.addEventListener('click', function (e) {
				// toggle the type attribute
				const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
				passwordInput.setAttribute('type', type);
				// toggle the eye slash icon
				this.classList.toggle('fa-eye-slash');
			});
        }

        // Fonction pour afficher le captcha
        function showCaptcha() {
            if (captcha_container.hasClass('hide')) {
                captcha_container.removeClass('hide');
            }

            // Charger le captcha seulement s'il n'est pas déjà chargé
            if (hcaptcha_widget_id === null) {
                hCaptchaLoader({ sentry: false }).then(function(hcaptcha) {
                    // hCaptcha API is ready
                    hcaptcha_widget_id = hcaptcha.render('captcha-container', {
                        sitekey: '{{ app.request.server.get('HCAPTCHA_SITE_KEY') }}',
                        // Additional options here
                    });
                });
            }
        }
        if (!captcha_container.hasClass('hide')) {
            showCaptcha();
        }
	});
</script>
