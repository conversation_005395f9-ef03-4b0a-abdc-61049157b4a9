.stacktable {
    width: 100%;
}

.st-head-row {
    padding-top: 1em;
    text-align: center;
}

.st-head-row.st-head-row-main {
    font-size: 16px;
}

.st-key {
    width: 49%;
    text-align: right;
    padding-right: 1%;
    font-weight: bold;
}

.st-val {
    width: 49%;
    padding-left: 1%;
}

.st-delimiter {
    height: 3px;
    background: #cc5001 !important;
    display: block;
    width: 200%;
}

.st-delimiter + .st-delimiter {
    height: 0;
}

/* RESPONSIVE EXAMPLE */

.stacktable.large-only {
    display: table;
}

.stacktable.small-only {
    display: none;
}

@media (max-width: 991px) {
    .stacktable.large-only {
        display: none;
    }

    .stacktable.small-only {
        display: table;
    }

    .table-responsive > .table.stacktable > thead > tr > th,
    .table-responsive > .table.stacktable > tbody > tr > th,
    .table-responsive > .table.stacktable > tfoot > tr > th,
    .table-responsive > .table.stacktable > thead > tr > td,
    .table-responsive > .table.stacktable > tbody > tr > td,
    .table-responsive > .table.stacktable > tfoot > tr > td {
        white-space: normal;
    }
}

table.dataTable tfoot th, table.dataTable tfoot td {
    font-weight: bold;
    padding: 8px 10px !important;
}

table.dataTable tfoot tr {
    background: #e0ad8d;
}