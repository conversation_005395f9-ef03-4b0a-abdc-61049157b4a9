html {
    background: #ecf0f5;
}

.align-middle {
    vertical-align: middle !important;
}

.wrapper {
    position: inherit;
}

.main-sidebar, .left-side {
    padding-top: 100px;
}

header .navbar-right {
    margin-right: 0;
}

.login-logo, .register-logo {
    padding-top: 20px;
}

.btn-block {
    padding-right: 0;
    padding-left: 0;
}

.btn.btn-selection {
    width: 100%;
    margin-bottom: 0px;
}

.btn.btn-eduprat {
    background-color: #109199;
    color: #fff;
    margin-bottom: 4px;
}

.btn.btn-icons-incexclusion {
    background-color: #109199;
    color: #fff;
    margin-bottom: 0px;
    padding: 4px 6px;
    font-size: 8px;
}
.btn-large {
    min-width: 200px;
}
.btn-eduprat.btn-eduprat--light {
    background-color: inherit;
    color: #109199;
}
.session-inclued {
    background-color: #dbffe9;
}

.btn.btn-flyer {
    background-color: #0979B0;
    color: #fff;
    margin-bottom: 4px;
}

.btn.btn-programme {
    background-color: #09B08D;
    color: #fff;
    margin-bottom: 4px;
}

.btn.btn-empty-selection {
    background-color: #808080;
    color: #fff;
    margin-bottom: 4px;
}

.btn-count-selection {
    background-color: #00A65A;
    color: #fff;
    margin-bottom: 4px;
    width: 50%;
    font-size: 20px;
    padding: 15px;
    font-weight: bold;
    cursor: inherit;
    height: 7vh;
}

.btn-disabled {
    cursor: not-allowed;
}

.btn.btn-eduprat.action-btn {
    margin-bottom: 0px;
}

.btn-download {
    margin-left: 25px;
}
.btn-download2 {
    margin-right: 25px;
}
.btn.btn-small {
    padding: 2px 6px;
    margin-bottom: 0;
}

.btn-icon {
    width: 34px;
    text-align: center;
    margin-bottom: 0 !important;
    padding: 6px;
}

.btn-icon-mini {
    width: 20px;
    font-size: 12px;
    padding: 1px;
}

.btn.btn-eduprat:not(.btn-icon) > i {
    margin-right: 8px;
    margin-left: 2px;
}

.btn.btn-eduprat:hover {
    background-color: #107d85;
    color: #fff;
}

.btn.btn-flyer:hover {
    background-color: #08648e;
    color: #fff;
}

.btn.btn-programme:hover {
    background-color: #0a997a;
    color: #fff;
}

.btn.btn-count-selection:hover {
    color: #fff;
}

.btn.btn-count-selection:active {
    box-shadow: inherit;
}

.btn.btn-empty-selection:hover {
    background-color: #606060;
    color: #fff;
}

.btn.btn-eduprat a {
    color: #fff;
}
.btn-red {
    background-color: #e5432e;
    color: white;
}

.eduprat-search {
    background-color: #bddef1
}

.pointer {
    cursor: pointer;
}

label + span.required {
    color: red;
}

.ms-container {
    width: 100% !important;
}

.eduprat-search .ms-container {
    width: 550px !important;
}

/*** AUDITS ***/

ul.questions, ul.patients, ul.activities, ul.bibliographies {
    list-style-type: none;
    padding-left: 16px;
}

ul.questions > li, ul.patients > li, ul.activities > li, ul.bibliographies > li {
    border-left: 3px solid #3c8dbc;
    padding-left: 16px;
    margin-bottom: 16px;
}

ul.choices {
    list-style-type: none;
    padding-left: 16px;
}

ul.choices > li {
    border-left: 2px solid #222d32;
    padding-left: 16px;
    margin-bottom: 16px;
}

ul.images, ul.patients-images {
    list-style-type: none;
    padding-left: 16px;
}

ul.images > li, ul.patients-images > li {
    border-left: 2px solid #222d32;
    padding-left: 16px;
    margin-bottom: 16px;
}

ul.images > li img, ul.patients-images > li img {
    margin-bottom: 16px;
    max-width: 100px;
    max-height: 100px;
}

.btn-danger a {
    color: #ffffff;
}
.prograssion-title {
    float: left;
    margin-right: 15px;
}
.progression-etape3 .checkbox {
    float: left;
    margin-right: 30px;
}
.progression-etape3 .checkbox+.checkbox {
    margin-top: 10px;
}
.progression-etape3 .checkbox-group {
    overflow: hidden;
}
.progression-etape3 .prograssion-title {
    margin-top: 10px;
}
.datepicker input {
    padding: inherit;
}
.box-eduprat {
    padding: 10px;
}
.box-eduprat .infos>div {
    margin-bottom: 10px;
}
.box-eduprat .infos .header {
    font-weight: bold;
}
.pagination {
    margin-top: 0;
}

.box-collapse-blue .box-header {
    background: #3d8dbc;
    border-bottom: 0;
}

.panel.box.box-primary.box-collapse-blue {
    border: 2px solid #3c8dbc;
}

.panel.panel-eduprat {
    border-left: 3px solid #0f9199;
    padding-left: 16px;
    margin-bottom: 16px;
}

.vich-avatar img {
    max-width: 100px;
    max-height: 100px;
    border: 1px solid #d2d6de;
}

.box-collapse-blue .box-title a, .box-collapse-blue .box-title {
    color: white;
}

.btn-default-eduprat {
    white-space: inherit;
    margin-right: 10px;
    margin-top: 10px;
    width: 115px;
    line-height: 60px;
    height: 77px;
}
.btn-default-eduprat span {
    display: inline-block;
    line-height:1.2;
    vertical-align:middle;
}
.btn-default-eduprat label {
    font-weight: normal;
}
.lastImportDate {
    margin-left: 115px;
}
.downloadFile {
    display: inline-block;
}
.downloadFile-depot {
    width: 15px;
}
.btn-row {
    padding: 0 0 20px 0;
}
ul.formateurs, ul.formations, ul.coordinators, ul.coordinators-surcharge, ul.formateurs-surcharge {
    list-style: none;
    margin: 0;
    padding: 0 0 0 16px;
}
ul.formateurs > li, ul.coordinators > li, ul.formateurs-surcharge > li, ul.coordinators-surcharge > li {
    border-left: 3px solid #3c8dbc;
    padding-left: 16px;
    margin-bottom: 16px;
}
/*ul.formateurs li > div {*/
    /*width: 86%;*/
    /*display: inline-block;*/
    /*margin-right: 5px;*/
/*}*/
ul.connaissances li > a.btn, ul.competences li > a.btn, ul.objectives li > a.btn, ul.unities li > a.btn  {
    vertical-align: top;
}
ul.connaissances, ul.competences, ul.objectives, ul.unities {
    list-style: none;
    margin: 0;
    padding: 0 0 0 16px;
}
ul.connaissances > li, ul.competences > li, ul.objectives > li, ul.unities:not(.unities--date) > li {
    border-left: 3px solid #3c8dbc;
    padding-left: 16px;
    margin-bottom: 16px;
}

.formation.input-group-addon {
    display: none;
    padding: 8px 14px;
    width: 19%;
    margin-left: -3px;
}
.formation.input-group-addon .glyphicon {
    position: inherit;
}

@media (min-width: 768px) {
    .formation.input-group-addon {
        display: inline-block;
    }
}
.table-bilan {
    margin-top: 10px;
}
.table.table-bilan>thead>tr>th,
.table.table-bilan>thead>tr>td {
    padding: 5px;
}
.table-hover.table-bilan>tbody>tr:hover {
    background-color: transparent;
}
.table-bordered>tbody>tr>td.sessionsTitle {
    font-weight: bold;
    border-top: 1px solid #3C8DBC;
}
.table-bordered>tbody>tr.sessionsWrap > td {
    padding: 0;
}
.table-bordered>tbody>tr.current+.sessionsWrap > td {
    padding: 10px 20px;
    background-color: #f5f5f5;
}
.table-bordered>tbody>tr.current {
    background-color: #f5f5f5;
}
.table.table-totaux tr th,
.table.table-totaux tr td {
    width: 50%;
    border: 1px solid #ffffff;
}
.table.table-totaux2 tr th,
.table.table-totaux2 tr td {
    width: 50%;
    border: 1px solid #ECF0F5;
}
.table.table-totaux3 tr th,
.table.table-totaux3 tr td {
    width: 50%;
    border: 1px solid #ffffff;
}
.table.table-totaux tbody {
    border-top: 1px solid #3C8DBC;
}
.table.table-totaux tr td,
.table.table-totaux2 tr td {
    text-align: center;
}
.table-totaux {
    width: 30%;
    border-top: 1px solid #3C8DBC;
    background-color: #f5f5f5;
}
.table-totaux2 {
    width: 30%;
    border-top: 1px solid #3C8DBC;
    background-color: #ffffff;
}
.table-totaux3 {
    border-top: 1px solid #3C8DBC;
    background-color: #f5f5f5;
}
.sessionsListLink i,
.formationsListLink i,
.evalListLink i,
.honoraryListLink i {
    transform: inherit;
}
.sessionsListLink.active i.fa-angle-right,
.formationsListLink.active i.fa-angle-right,
.evalListLink.active i.fa-angle-right,
.honoraryListLink.active i.fa-angle-right {
    transform: rotate(90deg);
}

.btn-andpc, .btn-download-file, .btn-download-file-attestation {
    cursor: pointer;
}

.downloadFile input[type='file']{
    position: absolute;
    margin-top: 3px;
    margin-left: 3px;
    height: 1px;
    width: 1px;
    z-index: -5;
}

.edit.label, .cancel-edit.label {
    background-color: #109199 !important;
}

.cancel-edit.label {
    margin-left: 1em;
}

.edit-text:not(:empty) {
    margin-left: 0.5em;
    font-size: 1em;
}
.row-participations .cout a {
    margin-right: 3px;
}
.vich-file label.col-sm-3 {
    width: 100%;
    float: none;
    padding: 0;
    position: inherit;
    min-height: inherit;
}
.vich-file .col-sm-9 {
    width: 100%;
    float: none;
    padding: 0;
    position: inherit;
    min-height: inherit;
}

.not-clotured {
    opacity: 0.7;
}

/** PAGE ERREUR **/

.error-page>.error-content>h3 {
    font-weight: 300;
    font-size: 25px;
    text-align: center;
}

.error-page>.headline {
    font-size: 100px;
    font-weight: 300;
    text-align: center;
    float: inherit;
}

.error-page>.error-content {
    display: block;
    margin-left: 0;
}

@media (max-width: 767px) {
    .main-header .navbar-right {
        float: right;
        margin: 0 20px;
    }
    .wrapper {
        overflow: visible;
    }
    .navbar-nav {
        margin: 5px -15px;
    }
}
.table>tbody>tr>td.prog-actions {
    padding-top: 4px;
    padding-left: 20px;
}
.table>tbody>tr>td.prog-actions a {
    margin-top: 4px;
}

.table>tbody>tr>td.actions a {
    margin-top: 4px;
}

.btn-billing-progression {
    margin: 35px 0 0 0;
}

.hide {
    display: none;
}

.downloadFileTopo {
    min-width: 160px;
    max-width: 250px;
    vertical-align: top;
}

.formDownloadFile {
    display: inline;
}

.formDownloadFile .checkbox {
    margin: 0 0 0 10px;
    display: inline;
}

.formDownloadFile .col-sm-9 {
    display: inline;
}

.formDownloadFile .form-group {
    display: inline;
}

.formDownloadFile .btn {
    padding: 0;
}

.fa-download.btn-download-file {
    margin-left: 10px;
}

.btn-attestation {
    width: 18px;
    margin-left: 0px !important;
}

.btn-attestation-circle {
    margin-left: 2px !important;
    font-size: 10px;
}

.link-topo {
    display: inline-block;
    margin: -0.25em -0.25em -0.25em 0;
    max-width: 24em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.upload-topo {
    width: 25px;
}

.participant-download-topo > div {
    overflow: hidden;
    text-overflow: ellipsis;
}

.participant-date-download {
    font-size: 0.85em;
    font-style: italic;
}

.login-box {
    width: 405px;
}

.togglePassword {
    right: 12px;
    cursor: pointer;
    position: absolute;
    top: 10px;
    font-size: 16px;
}

.download-csv {
    padding: 0 0 20px 0;
}

#eduprat_search_nbSession {
    width: 4em;
}

.edit_coordinator, .edit_exercise_mode {
    max-width: 8em;
}

a.link-disabled {
    color: inherit;
    text-decoration: line-through;
}

.handle {
    cursor: grab;
}

.handle.handle-icon:before {
    font-family: Fontawesome;
    content: "\f039";
}

.handle.handle-icon {
    margin-right: 10px;
}

.gu-mirror {
    list-style-type: none;
    background: #eaeaea;
}

span.question-index-title {
    font-size: 12px;
    color: #2c3b41;
    /* padding-left: 10px; */
}

.ui-sortable-placeholder {
    background: #ddd;
    height: 60px;
}

tr.ui-sortable-helper {
    max-height: 60px !important;
    overflow: hidden;
}

/** SPINNER **/

.spinner {
    padding: 36px;
    width: 70px;
    text-align: center;
}

.spinner > div {
    width: 18px;
    height: 18px;
    background-color: #109199;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinner .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
    0%, 80%, 100% { -webkit-transform: scale(0) }
    40% { -webkit-transform: scale(1.0) }
}

@keyframes sk-bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    } 40% {
          -webkit-transform: scale(1.0);
          transform: scale(1.0);
      }
}

.formation-finance-sous-mode-header {
    width: 100%;
    text-align: center;
    padding: 10px;
    border: 1px solid #f4f4f4;
    border-bottom: none;
    font-weight: bold;
    font-size: 15px;
    color: white;
    background: #109199;
}

.nav-tabs-custom>.nav-tabs>li>a {
    white-space: nowrap;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.link-actalians {
    color: #e05817;
}

a.link-actalians:hover, a.link-actalians:active, a.link-actalians:focus {
    color: #fd7f44;
}

.link-actalians:after {
    content: ' (actalians)';
    font-style: italic
}

.collaps-list {
    background-color: #c3c6c9;
    padding: 10px 10px 5px 10px;
}

.collaps-list-session {
    margin: 5px 5px 0px 5px;
}

.collaps-list-attestation {
    background-color: white;
    margin: 5px 5px 0px 5px;
}

.collaps-list-eval {
    margin: 0px 5px 5px 5px;
}

.table-eval {
    margin-bottom: 5px;
}

.table-session {
    margin-bottom: 10px;
}

.leftBordered {
    border-left: 3px solid #3C8DBC;
}

.form-group-key {
    width: 25%;
}

.nav-tabs-custom>.nav-tabs>li.actalians-tab>a {
    background-color: #ebf3f7;
}

.nav-tabs-custom>.nav-tabs>li.actalians-tab>a:hover {
    background-color: #ebf3f7;
}

.actalians-tab {
    background-color: #ebf3f7;
}

.nav-tabs-custom>.tab-content {
    padding: 0px 10px 10px 10px;
}

.tab-pane {
    padding-top: 10px;
}

.nav-tabs-custom>.nav-tabs>li.active {
    border-top-color: #3c8dbc;
}

.nav-tabs-custom>.nav-tabs>li.active>a {
    /*border-bottom: 2px solid white;*/
    /*margin-bottom: -2px;*/
    border-left-color: #3c8dbc;
    border-right-color: #3c8dbc;
    border-left-width: 3px;
    border-right-width: 3px;
    border-top-width: 0;
    /*border-radius: 3px;*/
}

.tab-content>.active {
    /*border-top: 2px solid #3c8dbc;*/
}

@media (max-width: 768px) {
    .form-group-key {
        width: auto;
    }
}

.skin-blue .sidebar-menu>li.acces-crm>a {
    background-color: #c14700;
}

.skin-blue .sidebar-menu>li.acces-crm>a:hover {
    background-color: #CC5000;
}

.mr-participant {
    margin-left: 1.5rem;
}

input#eduprat_participant_search_programmeTitle {
    width: 20em;
}

.department-grp > .form-group {
    width: 100%;
}

@media (max-width: 991px) {
    .navbar-custom-menu>.navbar-nav {
        float:right
    }

    .navbar-custom-menu>.navbar-nav>li {
        position: static
    }

    .navbar-custom-menu>.navbar-nav>li>.dropdown-menu {
        position: absolute;
        right: 5%;
        left: auto;
        border: 1px solid #ddd;
        background: #fff
    }
}

.add-tag {
    margin-left:0.5rem;
    margin-top:0.5rem;
    color: #109199;
    cursor: pointer;
}

input[type='color'] {
    width: 5em;
}

.connaissances-forms {
    margin-top: 1em;
}

.import-attestation {
    display: initial;
    margin-left: 2px;
    color: #3c8dbc;
}

.import-attestation:hover {
    color: #76b8df;
}

.edit-button {
    /* min-width: 180px; */
    padding: 8px;
    border-radius: 3px;
    width: 210px;
}

.edit-button-grey {
    background-color: #B0B0B0;;
}

.edit-button-red {
    background-color: #DE4C33;
}

.edit-button-green {
    background-color: #59a059;;
    color: white;
    font-weight: bold;
}

.edit-button a{
    font-weight: bold;
    color: white;
}

a .edit-button{
    font-weight: bold;
    color: white;
}

.after-edit-button {
    margin-left: 5px;
    color: black;
}

.parcours-module-state {
    margin-right: 8px;
    margin-top: 6px;
}

.pre-session-group {
    display:flex;
    margin-top: 6px;
    font-weight: bold;
}

.post-session-group {
    display:flex;
    margin-top: 6px;
    margin-left: 8px;
    font-weight: bold;
}

.separator {
    margin-left: 10px;
    border-right: 1px solid #d9d9d9;
}

.orBlock {
    margin-left: 2em;
    border: 2px solid #33d3df;
    padding: 1em;
    border-style: dotted;
    border-radius: 10px;
}

.orGroup {
    text-align:center;
    margin-top: 1em;
}

.finance-sous-mode-header-hors-dpc {
    background-color: #7C8485;
}

.info-session {
    font-size: 20px;
    margin-right: 0.5em;
    color: #c14700;
}

.info-programme {
    font-size: 20px;
    margin-right: 0.5em;
    color: #c14700;
}

.info-session-blue {
    color: #3c8dbc;
}

.lesson-block {
    display:flex;
    border: 1px solid #e7e7e7;
    padding:1em;
    border-radius: 6px;
    margin-top: 1em;
}

.depend {
    display: none;
}

.files_list {
    margin-bottom: 2em;
}

.doc-files {
    list-style-type:none;
}

.optional-module {
    font-size: 10px;
    font-style: italic;
}

#filesBlock .uploadList .alert {
    margin-bottom: 0;
}

.modal-plaquette {
    display: none;
    position: fixed;
    z-index: 3;
    left: 0em;
    top: 0;
    width: 100%;
    height: 100vh;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.modal-plaquette-fav,
.modal-plaquette-download{
    z-index: 4;
    overflow: auto;
}
.modal-plaquette-logo-select{
    z-index: 5;
    overflow: auto;
}
.modal-plaquette-download-content {
    height: 445px;
}

.modal-plaquette-logo-select-content {
    min-height: 100% !important;
    margin-top: 0 !important;
    margin-right: initial !important;
    margin-left: auto !important;
    width: 32% !important;
    border-radius: initial !important;
    padding: 0px !important;
}

.modal-plaquette-content {
    background-color: #fefefe;
    border: 1px solid #888;
}
@media (min-width: 768px) {
    .modal-plaquette-content {
        margin-left: auto;
        margin-right: auto;
        padding: 25px;
        width: 55%;
        margin-top: 10vh;
        border-radius: 20px;
    }

    .modal-plaquette-download-content,
    .modal-plaquette-content-fav {
        width: 54%;
        height: 25vh;
        margin-top: 30vh
    }
    .modal-plaquette-download-content {
        height: 485px !important;
    }
}

.modal-plaquette-table {
    display: block;
    overflow: auto;
    max-height: 30em;
}
.modal-plaquette .inclusionSession {
    display: none;
}

#open-logo-form {
    display:flex;
    align-items: center;
    gap:10px;
    margin-bottom: 14px
}

.close {
    color: black;
    float: right;
    font-size: 28px;
    font-weight: bold;
    margin-right: 0.2em;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.titleSelector {
    margin-top: 1em;
    font-weight: bold;
}

.modal-btn {
    width: 200px;
}

.logos-partenaire-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.logo-partenaire {
    text-align: center;
    align-content: center;
    width: 175px;
    padding: 5px;
    height: 95px;
    border: 1px solid black;
    font-weight: bold;
}
.logo-partenaire:hover {
    cursor: pointer;
}

.logo-partenaire img {
    width: 85px;
}

.mbs {
    margin-bottom: 1em !important;
}

.mb {
    margin-bottom: 2em !important;
}

.mbxl {
    margin-bottom: 4em !important;
}

.mrs {
    margin-right: 1em !important;
}
.mls {
    margin-left: 1em !important;
}

.favorites-block {
    border-left: 1px solid grey;
}

.sub-page-plaquette {
    border-top: 3px solid #00c0ef;
    margin: 2em;
    padding-top: 2em;
}

.position-relative {
    position: relative;
}
.inline-block {
    display: inline-block;
}

.tag {
    /*border-radius: 5px;*/
    padding: 5px;
    color: black;
    margin: 2px 5px 2px 0;
    display: inline-block;
    border: 1px solid;
    border-radius: 5px;
}

.tag--color0 {
    border-color: blue;
}
.tag--color1 {
    border-color: red;
}
.tag--color2 {
    border-color: green;
}

#filtersRappel {
    max-height: 75px;
    overflow: auto;
}

.already-include {
    margin-top: 6px;
    width: 10px;
    height: 10px;
    border-radius: 40px;
    background-color: #109199;
}

.small-version {
    margin-left: 4px;
    margin-top: 5px;
    width: 8px;
    height: 8px;
}

.already-include-legend {
    margin-left: 4px;
    font-style: italic;
    font-size: 12px;
}

.spanNbSelectionSession--blue {
    color: #109199;
    font-weight: bold;
}

.inputs .form-control {
    margin-bottom: 1.5rem;
}

.flex {
    display: flex;
}
.flex-right {
    display: flex;
    justify-content: flex-end;
    width: 100%;
}

.js-elemOpenner .fa-angle-right, .fa-angle-up {
    transition-duration: 300ms;
    transition-property: transform;
}
.js-elemOpenner.active .fa-angle-right{
    transform: rotate(90deg);
}
.js-elemOpenner.active .fa-angle-up {
    transform: rotate(180deg);
}
.js-elemOpenner {
    cursor: pointer;
}
.js-opennable {
    width: 100%;
}


.header-nathan {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
}
.header-nathan .titre {

}
.header-nathan .titre h1 {
    color: #333;
    font-family: "Source Sans Pro";
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 0;
}
.align-horizontal {
    display: flex;
    flex-direction: row;
    gap: 30px;
 }
.legend {
    color: #848484;
    font-family: "Source Sans Pro";
}

.block-nathan {
    display: flex;
    padding: 32px;
    flex-direction: column;
    align-items: flex-start;
    gap: 32px;
    border-radius: 3px;
    background: #FFFFFF;
}
.block-nathan+.block-nathan {
    margin-top: 1.5rem;
}

.block-nathan .abime {
    display: flex;
    padding-left: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-left: 3px solid #109199;
    width: 100%;
}

.block-nathan .abime header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
}
.block-nathan .abime header h2 {
    color: #109199;
    font-family: "Source Sans Pro";
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 0;
}

.parent-block-nathan-blue {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-top: 32px;
    margin-bottom: 32px;
}
.block-nathan-blue {
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    background: rgba(16, 145, 153, 0.12);
    width: 100%;
}
.block-nathan-blue header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    width: 100%;
}
.block-nathan-blue header h3 {
    color: #109199;
    font-family: "Source Sans Pro";
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 0;
}
.block-nathan-blue .btn-icon {
    background-color: inherit;
    color: #109199;
    border-color: #109199;
}
.block-nathan-blue .btn-icon.btn-danger {
    border-color: inherit;
    color: #dd4b39;
}
.block-nathan-blue .btn-icon.btn-danger:hover {
    background-color: #dd4b39;
    color: #FFF;
}
/** fiche action **/

.table-action {
    width: 100%;
}
.table-action th {
    font-size: 17px;
    background: #747D8A;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 16px;
    border-right: 3px solid #F2F2F2;
}
.table-action td {
    background: white;
    color: black;
    text-align: center;
    padding: 16px;
    border-right: 3px solid #F2F2F2;
    border-bottom: 5px solid #F2F2F2;
}
.table-action td.reponse {
    text-align: left;
}
.table-action td.field-disabled {
    background: #E4E4E4;
    font-style: italic;
}
.table-action .add-action-row td {
    background: #E4E4E4;
    color: #707070;
    text-transform: uppercase;
    font-weight: bold;
    cursor: pointer;
}
.table-action .add-action-row td a {
    color: #707070;
}

.tcs_grp {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 2em;
    font-size: 20px
}

.tcs_grp_complete {
    color: #109199;
}

.tcs_grp_uncomplete {
    color: #dd4b39;
}

.tcs_grp_disabled {
    color: grey;
}

.tcs_grp_disabled:hover {
    color: grey;
}

.tcs_group_selected {
    font-weight: bold;
    font-size: 30px;
}
