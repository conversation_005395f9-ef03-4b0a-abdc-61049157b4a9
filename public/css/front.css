header {
    margin-bottom: 2em;
}

.bg-form header {
    margin-bottom: 0;
}

header .alert {
    margin-top: 2em;
}

h3 {
    color: #333;
    border-bottom: 1px solid #dadada;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
h4 {
    font-size: 16px;
}

.eduprat {
    color: #109199;
}

.box.box-eduprat {
    border-top-color: #109199;
}

.btn.btn-eduprat {
    background-color: #109199;
    color: #fff;
    padding: 2px 6px;
    margin-bottom: 4px;
}

.btn.btn-eduprat:hover {
    background-color: #107d85;
    color: #fff;
}

.btn.btn-secondaire {
    background-color: white;
    font-weight: bold;
    color: #109199;
    box-shadow: inset 0 0 0 3px #109199;
}

.btn.btn-secondaire:hover {
    background-color: white;
    color: #109199;
}

.btn.btn-big {
    padding: 16px 48px;
    font-size: 16px;
    border-radius: 12px;
}

.btn.btn-medium {
    padding: 16px 24px;
    font-size: 16px;
    border-radius: 12px;
}

.btn.btn-medium+.btn.btn-medium {
    margin-left: 24px;
}

#form-container {
    background-color: #fff;
    padding: 2em;
    margin: 2em auto;
    min-height: 660px;
}

label.control-label.required {
    display: inline;
}

form .help-block {
    color: #b0413e;
    font-weight: bold;
}

@media screen and (min-width: 768px) {
    #body {
        padding-left: 260px;
    }
    #logo {
        float: left;
    }
}

#form {
    padding: 1em;
}

#logo img {
    width: 100px;
}

#contact {
    margin-top: 2em;
}

body.form-page {
    background: #f3f3f3;
}

.question-range {
    margin-top: 1em;
}

.question-radio, .question-range {
    display: flex;
    justify-content: space-between;
    padding: 0 2em;
    max-width: 500px;
}

.question-radio label {
    display: flex;
}

.question {
    margin-bottom: 1em;
    padding-bottom: 2em;
    border-bottom: 1px solid #ccc;
}

.cell-links a, .cell-links > span {
    margin-bottom: 4px;
}
span.btn-success,
span.btn-danger {
    cursor:auto;
}

#legend ul {
    margin-top: 1em;
    list-style-type: none;
    padding-left: 0;
}

#legend ul li {
    margin-bottom: 1em;
    display: inline-block;
}

.audit-question, .survey-question {
    margin-bottom: 1em;
}
.pagination > li.inactive > span {
    color: #666;
    background-color: #eee;
}

#validTimeError {
    color: red;
}

#evaluation_coordinator_5 {
    display: block;
    width: 80%;
    height: 150px;
}

.blueimp-gallery {
    background: rgb(37, 44, 48) !important;
}

.blueimp-gallery-carousel {
    padding-bottom: 41.25% !important;
}

.blueimp-gallery-thumbnails a {
    display: inline-block;
}

.blueimp-gallery-thumbnails {
    margin-bottom: 10px;
    border: 1px solid #1e1e1e40;
    padding: 10px;
}

.blueimp-gallery-thumbnails a + a {
    margin-left: 10px;
}

.label-image {
    max-width: 100px;
    max-height: 100px;
}

#redirect {
    margin: 100px 0;
}

.text-big {
    font-size: 18px;
    color: #666;
}

/* ---------------------------------------------------
    SIDEBAR STYLE
----------------------------------------------------- */

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #1e282c;
    color: #fff;
    transition: all 0.3s;
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    height: 250px;
    padding: 20px;
    background: #109199;
    color: #fff;
    display:flex;
    justify-content:center;
    align-items:center;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
}

#sidebar ul.components {
    padding: 20px 0;
}

#sidebar ul p {
    color: #fff;
    padding: 10px;
}

#sidebar ul li a {
    padding: 10px 10px 10px 30px;
    font-size: 1em;
    display: block;
    color: #8aa4af;
}
#sidebar .sidebar-tcs ul li a {
    font-size: 16px;
}

#sidebar ul li.active a {
    color: #fff;
}

#sidebar ul li li a {
    padding: 10px 10px 10px 60px;
    font-size: 1em;
    display: block;
    color: #8aa4af;
    &.concat {
        padding: 0 0 0 40px;
    }
}

#sidebar ul li a i {
    margin-right: 10px;
    font-size: 18px;
}

#sidebar ul li a:hover {
    background: #323d43;
}

#sidebar ul li.active>a,
a[aria-expanded="true"] {
    /*color: #fff;*/
}

a[data-toggle="collapse"] {
    position: relative;
}

.dropdown-toggle::after {
    display: block;
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

ul ul a:not(.no-bg) {
    font-size: 0.9em !important;
    /* padding-left: 30px !important; */
    /* background: #2c3b41; */
}
.sidebar-tcs {
    ul ul a:not(.no-bg) {
        font-size: 16px !important;
    }
}
ul.CTAs {
    padding: 20px;
}

ul.CTAs a {
    text-align: center;
    font-size: 0.9em !important;
    display: block;
    border-radius: 5px;
    margin-bottom: 5px;
}

a.download {
    background: #fff;
    color: #7386D5;
}

a.article,
a.article:hover {
    background: #6d7fcc !important;
    color: #fff !important;
}

.sidebar-header-title {
    font-size: 20px;
    flex: 1 0 100%;
}

.sidebar-header-subtitle {
    font-size: 14px;
    flex: 1 0 100%;
}



/* ---------------------------------------------------
    CONTENT STYLE
----------------------------------------------------- */

#content {
    width: 100%;
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s;
}

/* ---------------------------------------------------
    MEDIAQUERIES
----------------------------------------------------- */
@media (min-width: 768px) {
    .sidebar-home, .sidebar-header-mobile {
        display: none;
    }
}
@media (max-width: 767px) {
    .menu, .sidebar-header-desktop {
        display: none !important;
    }

    .logo-eduprat {
        text-align: center;
        margin-left: -0px !important;
    }
}

@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
        position: absolute;
        z-index: 999;
        height: 100%;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #sidebarCollapse span {
        display: none;
    }
    .sidebar-toggle {
        position: absolute;
        top: 0;
        left: 0;
        background: #111111;
        width: 50px;
        height: 50px;
        border: 0;
        font-size: 25px;
        cursor: pointer;
        z-index: 999;
        text-align: center;
        vertical-align: middle;
        color: white;
    }
}

@media (min-width: 768px) {
    .sidebar-toggle {
        display: none;
    }
}

/** FORMATIONS **/

.formation {
    margin-bottom: 20px;
    -webkit-box-shadow: 5px 5px 13px -6px rgba(179,179,179,0.5);
    -moz-box-shadow: 5px 5px 13px -6px rgba(179,179,179,0.5);
    box-shadow: 5px 5px 13px -6px rgba(179,179,179,0.5);
}

.formation-header {
    height: 40px;
    color: #fff;
    padding: 10px 0;
    background: #97c497;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}


.formation-header-right {
    text-align: right;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: bold;
    padding-right: 15px;
}

.formation-header-left {
    float: left;
    padding-left: 15px;
}

.formation-header-left a {
    color: white;
}

.formation-header-left i {
    font-size: 20px;
    margin-right: 5px;
}

.formation-header-future {
    background: #97c497;
}

.formation-header-opened {
    background: #59a059;
}

.formation-header-closed {
    background: #03868b;
}

.formation-body {
    padding: 15px 20px;
    background-color: #fff;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.formation-title {
    font-size: 20px;
    font-weight: bold;
}

.formation-details {
    font-size: 12px;
}

.formation-warning {
    color: #a94442;
    margin: 10px 0;
}

/** TIMELINE **/

.step-title {
    font-size: 26px;
    margin-top: 6px;
}

.formation-timeline-inline {
    list-style-type: none;
    display: flex;
    /*align-items: center;*/
    justify-content: center;
    min-height: 90px;
    padding-top: 10px;
    margin-left: 0;
    padding-left: 127px;
    overflow: inherit;
}

.formation-timeline-inline .li{
    transition: all 200ms ease-in;
    flex: 1 25%;
    position: relative;
    max-width: 150px;
}

.formation-timeline {
    list-style-type: none;
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    ul {
        list-style-type: none;
        padding-left: 0;
        .li {
            margin-bottom: 0;
        }
    }
}


.formation-timeline-sidebar {
    width: 100%;
}

.formation-timeline .li {
    transition: all 200ms ease-in;
    flex: 1 100%;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.step-header {
    background-color: #E3E0E0;
    height: 55px;
    border-radius: 30px;
    color: black;
    display: flex;
    align-items: center;
    flex: 1 100%;
    cursor: pointer;
}

li.complete .step-header {
    background-color: #CDE2CD;
}

li.incomplete .step-header {
    background-color: #F1C8CC;
}

li.bg-eduprat .step-header {
    background-color: #109199;
    color: white;
}

li.bg-eduprat.unavailable .step-header {
    background-color: #E3E0E0;
    color: black;
}

.step-picto, .lesson-picto, .clock-picto, .pensil-picto {
    position: relative;
    transition: all 200ms ease-in;
}

.step-picto:before {
    content: "";
    display: block;
    width: 30px;
    height: 30px;
    padding-top: 10px;
    margin: 16px;
    text-align: center;
    background-color: white;
    border-radius: 30px;
    border: 1px solid #ddd;
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: white;
}


.li.complete .step-picto:before {
    background-color: #59a059;
    border: none;
    transition: all 200ms ease-in;
    content: "\f00c";
}


.li.incomplete .step-picto:before {
    background-color: #d34d5a;
    border: none;
    transition: all 200ms ease-in;
    content: "\f067";
}


.li.bg-eduprat .step-picto:before {
    background-color: white;
    color: #333;
    border: none;
    transition: all 200ms ease-in;
    content: "\f0c5";
}

.step-desc {
    flex-grow: 1;
}

.step-number {
    font-size: 18px;
}

.step-time {

}

.step-action {
    position: relative;
    transition: all 200ms ease-in;
    cursor: pointer;
}

.step-action:before {
    width: 25px;
    height: 25px;
    padding: 10px;
    margin: 10px;
    text-align: center;
    padding-top: 7px;
    font: normal normal normal 22px/1 FontAwesome;
    color: black;
    transition: all 200ms ease-in;
    content: "\f0d8";
}

.bg-eduprat:not(.unavailable) .step-action:before {
    color: white;
}

.step-modules {
    display: flex;
    justify-content: space-around;
    padding: 0 30px;
    flex: 1 100%;
    flex-wrap: nowrap;
    row-gap: 5px;
}

.step-modules-wrap {
    flex-wrap: wrap;
}

.step-module {
    display: flex;
    flex-grow: 1;
    background: #F5F5F5;
    margin: 0 2px;
    align-items: center;
    justify-content: center;
    padding: 8px 5px;
    position: relative;
}

.step-module.complete {
    color: #59a059;
}

.step-module.incomplete {
    color: #d34d5a;
    border-bottom: 5px solid #d34d5a;
}

.step-module.unavailable {
    color: #afafaf;
    cursor: default;
}

.unavailable .status-text {
    font-style: italic;
}

a.step-module[href="#!"] {
    cursor: default;
}

.module-icon {
    margin-right: 16px;
}

.step-module.complete .module-icon svg {
    fill: #59a059;
}

.step-module.incomplete .module-icon svg {
    fill: #d34d5a;
}

.step-module.unavailable .module-icon svg {
    fill: #afafaf;
}

span.module-clear {
    position: absolute;
    right: 6px;
    top: 6px;
}

@media (max-width: 1400px) {
    .step-modules {
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .step-module {
        width: 100%;
        flex: 1 100%;
        justify-content: flex-start;
    }

    .formation-timeline-inline {
        flex-wrap: wrap;
        padding-left: 0;
    }

    .formation-timeline-inline .li {
        flex: 1 100%;
        max-width: inherit;
        min-height: 36px;
    }
}

.timestamp {
    margin-bottom: 20px;
    /*padding: 0px 80px 0px 0px;*/
    /*display: flex;*/
    /*flex-direction: column;*/
    /*align-items: center;*/
    /*font-weight: 100;*/
    font-size: 12px;
}

.status {
    border-top: 1px solid #D6DCE0;
    position: relative;
    transition: all 200ms ease-in;
}

.status-sidebar {
    border-top: none;
    margin-bottom: 1em;
}

.status h4 {
    font-weight: 600;
    margin-bottom: 0;
}
.status:before {
    content: "";
    width: 25px;
    height: 25px;
    text-align: center;
    padding-top: 7px;
    background-color: white;
    border-radius: 25px;
    border: 1px solid #ddd;
    position: absolute;
    top: -15px;
    /* left: 42%; */
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: white;
}

.status-sidebar:before {
    top: 0px;
}


.li.incomplete .li.complete .status:before,
.li.complete .status:before {
    background-color: #59a059;
    border: none;
    transition: all 200ms ease-in;
    content: "\f00c";
}


.li.incomplete .status:before {
    background-color: #d34d5a;
    border: none;
    transition: all 200ms ease-in;
    content: "\f067";
}

.unavailable {
    color: #AAA;
}

.underline {
    text-decoration: underline;
}

a.underline:hover {
    text-decoration: underline;
}

.formation-timeline li a {
    color: inherit;
}

li.li:last-child .status {
    border-color: transparent;
}

@media (min-device-width: 0px) and (max-device-width: 768px) {
    .timeline {
        list-style-type: none;
        display: block;
    }

    .li {
        transition: all 200ms ease-in;
        display: flex;
        width: inherit;
    }
}


@media (min-device-width: 768px) and (max-device-width: 992px) {
    .formation-downloads {
        margin-top: 30px;
    }
}

@media (max-width: 768px) {
    .formation-timeline {
        flex-wrap: wrap;
        padding: 40px 0 0 0;
    }

    .step-time {
        display: none;
    }

    .li {
        flex: 1 100%;
        /* height: 90px; */
    }

    .status {
        height: 100%;
        border-top: none;
    }

    .status-text {
        position: relative;
        top: -15px;
    }

    .status-text-sidebar {
        top: 2px;
        left: 3em;
    }

    .status h4 {
        margin-top: 0;
    }

    .li .status {
        padding-left: 30px;
        margin: 0 auto;
    }

    .li.next-complete .status {
        border-left: 2px solid #59a059;
    }

    .li.next-incomplete .status {
        border-left: 1px solid #d34d5a;
    }

    .status:before {
        left: -13px;
        transition: all 200ms ease-in;
    }

    .status-sidebar:before {
        left: initial;
    }

    .formation-downloads {
        padding-left: 15px;
    }

    #formation-new {
        text-align: center !important;
        margin-top: 20px;
    }

}

@media (min-width: 768px) {

    .status-text {
        text-align: center;
        width: 100%;
        margin-top: 10px;
    }

    .status-text-sidebar {
        margin-top: 0px;
    }

    .formation-timeline-inline .status-text {
        position: absolute;
        left: calc(-50% + 12px);
        font-size: 17px;
        margin-top: 20px;
        color: #666;
    }

    .li.next-complete .status {
        border-top: 2px solid #59a059;
    }

    .li.next-incomplete .status {
        border-top: 1px solid #d34d5a;
    }

    .status {
        /*padding: 0px 40px;*/
        /*display: flex;*/
        /*justify-content: center;*/
        /*min-width: 200px;*/
    }

}

.formation-downloads {
    background: whitesmoke;
    margin: 0 30px;
    padding: 16px 0;
}

.formation-downloads > ul {
    list-style-type: none;
    padding-left: 15px;
}

.formation-downloads > ul li {
    margin-bottom: 5px;
}

.formation-downloads > ul li a {
    color: black;
    border-bottom: 1px solid black;
    font-size: 16px !important;
}

.formation-downloads > ul li a:before {
    content: "\f019";
    font: normal normal normal 18px/1 FontAwesome;
    margin-right: 5px;
}

.formation-downloads > ul li a.link-disabled {
    color: #AAA;
    border-bottom: 1px solid #AAA;
    cursor: not-allowed;
}

.timeline-disabled {
    cursor: default;
    pointer-events: none;
}

.timeline-disabled .timestamp {
    display: none;
}

#formation-tools {
    margin-bottom: 30px;
    font-size: 14px;
}

#formation-tools a {
    color: black;
    text-decoration: underline;
    background: #f3f3f3;
    padding: 5px 10px;
    border-radius: 10px;
}

#formation-sort {
    background: #f3f3f3;
    padding: 5px 10px;
    border-radius: 10px;
}

#formation-sort-order {
    margin-left: 5px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }

    #formation-sort {
        text-align: center;
    }
}

@media (min-width: 768px) {
    #formation-sort {
        float: left;
    }
}

/** PROFIL **/

#profil-participant {
    font-weight: bold;
    font-size: 26px;
    margin-bottom: 0;
}

.profile-header-city {
    font-size: 14px;
    margin-left: 10px;
    text-transform: uppercase;
    font-weight: normal;
}

.profile-header-city i {
    margin-right: 5px;
    font-size: 16px;
}

#profil-participant-category {
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 40px;
}

.table-title {
    font-size: 18px;
    text-transform: uppercase;
    color: #bbbbbb;
}

.table-profile {
    margin: 10px;
    margin-bottom: 30px;
}

.table-profile tr {
    /*border-bottom: 1px solid #dadada;*/
}

.table-profile tr td {
    width: 33%;
    border-bottom: 1px solid #dadada;
}

.table-profile tr td:first-child {
    font-weight: bold;
}

.profile-btn-container {
    display: inline;
}

.form-error {
    color: #d34d5a;
}

[data-field] td {
    vertical-align: middle !important;
}

.profile-edit-field-btn, .profile-edit-field-btn:focus, .profile-edit-field-btn:active, .profile-edit-field-btn:hover  {
    color: white;
    background: #109199;
    padding: 2px 6px;
    border-radius: 5px;
    margin-right: 5px;
    white-space: nowrap;
}

.cancel-edit-price, .cancel-edit-price:focus, .cancel-edit-price:active, .cancel-edit-price:hover {
    color: white;
    background: #d34d5a;
    padding: 2px 6px;
    border-radius: 5px;
    white-space: nowrap;
}

.cancel-edit-price i {
    margin-right: 5px;
}

.profile-btn-container {
    display: inline-block;
}

.profile-btn-container:last-child {
    margin-top: 5px;
}


@media (max-width: 768px) {
    .table-profile {
        margin-left: 0;
        margin-right: 0;
        table-layout: fixed;
    }

    .table-profile tr td {
        width: 33%;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .profile-btn-container {
        display: block;
    }

}

/** ALERT **/

#content .alert.alert-error {
    background-color: #d34d5a !important;
    border-radius: 10px;
}

.alert.alert-warning {
    background-color: #FFFAF3 !important;
    color: #957035!important;
    border-color: #957035 !important;
    border-radius: 5px;
    font-size: 18px;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin-bottom: 16px;
}

.alert.alert-warning i {
    font-size: 36px;
}

.alert.alert-info {
    background-color: #2A6887 !important;
    border-color: #1b485f;
}

.alert.alert-obligatoire {
    background-color: #d34d5a !important;
    border-color: #aa3c47;
}

/** CONTACT **/

#contact {
    font-size: 16px;
}

#contact-title {
    text-align: center;
    margin: 30px;
    font-size: 16px;
}

.contact-img {
    margin: 0 auto 30px auto;
}

.blue-eduprat {
    color: #109199;
}

.bold {
    font-weight: bold;
}

#faq {
    margin-top: 40px;
}

.faq {
    margin-top: 30px;
}

.faq-question {
    color: #aaa;
    text-transform: uppercase;
    font-size: 18px;
    font-weight: bold;
}

.faq-answer {
    margin-bottom: 30px;
}

/** SHEPERD **/

.hero-scroll {
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;
}

.hero-outer {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: table;
    height: 100%;
    padding: 20px 0;
    width: 100%;
}

.hero-outer .hero-inner {
    margin: 0 auto 1em;
    text-align: center;
    width: 540px;
}

@media (max-width: 600px) {
    .hero-outer .hero-inner {
        width: 340px;
    }
}

@media (max-width: 360px) {
    .hero-outer .hero-inner {
        width: 200px;
    }
}

.hero-outer .hero-inner h1,
.hero-outer .hero-inner h2,
.hero-outer .hero-inner h3,
.hero-outer .hero-inner p {
    color: #ffffff;
    font-weight: normal;
    line-height: 1;
    margin: 0 0 20px;
}

.hero-outer .hero-inner h3 {
    font-size: 1.3em;
    padding-top: 13px;
}

.hero-outer .hero-inner h1 {
    padding-top: 10px;
}

.hero-outer .hero-inner > * {
    opacity: 0.3;
    pointer-events: none;
    -webkit-transition: opacity 0.4s;
    transition: opacity 0.4s;
}

body:not(.shepherd-active) .hero-outer .hero-inner > * {
    opacity: 1;
    pointer-events: auto;
}

.hero-outer .hero-inner .shepherd-target.shepherd-enabled {
    opacity: 1;
    pointer-events: auto;
}

.hero-outer .hero-followup {
    padding-top: 20px;
}

.shepherd-element, .shepherd-element:after, .shepherd-element:before,
.shepherd-element *,
.shepherd-element *:after,
.shepherd-element *:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.shepherd-element {
    -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    max-height: 100%;
    max-width: 100%;
    width: 420px;
    z-index: 9999;
}

.shepherd-element .popper__arrow {
    border: 16px solid transparent;
    content: '';
    display: block;
    height: 0;
    pointer-events: none;
    position: absolute;
    width: 0;
}

.shepherd-element[x-placement^='top'] {
    margin-bottom: 16px;
}

.shepherd-element[x-placement^='top'] .popper__arrow {
    border-bottom: 0;
    border-top-color: #ffffff;
    bottom: -16px;
    left: calc(50% - 16px);
}

.shepherd-element[x-placement^='bottom'] {
    margin-top: 16px;
}

.shepherd-element[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: #eeeeee;
    border-top: 0;
    left: calc(50% - 16px);
    top: -16px;
}

.shepherd-element[x-placement^='left'] {
    margin-right: 16px;
}

.shepherd-element[x-placement^='left'] .popper__arrow {
    border-left-color: #ffffff;
    border-right: 0;
    margin-top: -16px;
    right: -16px;
    top: calc(50% - 16px);
}

.shepherd-element[x-placement^='right'] {
    margin-left: 16px;
}

.shepherd-element[x-placement^='right'] .popper__arrow {
    border-left: 0;
    border-right-color: #ffffff;
    left: -16px;
    top: calc(50% - 16px);
}

.shepherd-element.shepherd-has-title .shepherd-content header {
    padding: 1em;
}

.shepherd-element .shepherd-content {
    background: #ffffff;
    font-size: inherit;
    padding: 20px;
    border-radius: 15px;
}

.shepherd-element .shepherd-content header {
    padding: 0.75em 0.75em 0;
}

.shepherd-element .shepherd-content header:after {
    clear: both;
    content: '';
    display: table;
}

.shepherd-element .shepherd-content header .shepherd-title,
.shepherd-element .shepherd-content header .shepherd-cancel-link {
    font-weight: normal;
    margin: 0;
    padding: 0;
    position: relative;
    vertical-align: middle;
}

.shepherd-element .shepherd-content header .shepherd-title {
    border-bottom: 0;
    font-size: 32px;
    font-weight: bold;
}

.shepherd-element .shepherd-content header .shepherd-cancel-link {
    color: rgba(179, 179, 179, 0.75);
    font-size: 2em;
    margin-left: auto;
    text-decoration: none;
    -webkit-transition: color 0.5s ease;
    transition: color 0.5s ease;
}

.shepherd-element .shepherd-content header .shepherd-cancel-link:before {
    content: '\D7';
}

.shepherd-element .shepherd-content header .shepherd-cancel-link:hover {
    color: rgba(0, 0, 0, 0.75);
}

.shepherd-element .shepherd-content .shepherd-text {
    color: rgba(0, 0, 0, 0.75);
    line-height: 1.3em;
    padding: 0.75em;
}

.shepherd-element .shepherd-content .shepherd-text a,
.shepherd-element .shepherd-content .shepherd-text a:visited,
.shepherd-element .shepherd-content .shepherd-text a:active {
    border-bottom: 1px dotted;
    border-bottom-color: rgba(0, 0, 0, 0.75);
    color: rgba(0, 0, 0, 0.75);
    text-decoration: none;
}

.shepherd-element .shepherd-content .shepherd-text a:hover,
.shepherd-element .shepherd-content .shepherd-text a:visited:hover,
.shepherd-element .shepherd-content .shepherd-text a:active:hover {
    border-bottom-style: solid;
}

.shepherd-element .shepherd-content .shepherd-text p {
    margin-top: 0;
}

.shepherd-element .shepherd-content .shepherd-text p:last-child {
    margin-bottom: 0;
}

.shepherd-element .shepherd-content footer {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.shepherd-element .shepherd-content footer .shepherd-buttons {
    list-style: none;
    margin: 0;
    padding: 0;
    text-align: right;
}

.shepherd-element .shepherd-content footer .shepherd-buttons li {
    display: inline;
    margin: 0 0.5em 0 0;
}

.shepherd-element .shepherd-content footer .shepherd-buttons li:last-child {
    margin-right: 0;
}

.shepherd-element .shepherd-content footer .shepherd-buttons li .shepherd-button {
    display: inline-block;
    *display: inline;
    vertical-align: middle;
    *vertical-align: auto;
    *zoom: 1;
    border: 0;
    border-radius: 3px;
    color: #109199;
    cursor: pointer;
    font-family: inherit;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0.1em;
    line-height: 1em;
    text-transform: uppercase;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    margin-left: 20px;
}

.shepherd-element .shepherd-content footer .shepherd-buttons li .shepherd-button:hover {
}

.shepherd-element .shepherd-content footer .shepherd-buttons li .shepherd-button.shepherd-button-secondary {
    background: #e5e5e5;
    color: rgba(0, 0, 0, 0.75);
}

.shepherd-element .shepherd-content footer .shepherd-buttons li .shepherd-button.shepherd-button-secondary:hover {
    background: #cbcbcb;
    color: rgba(0, 0, 0, 0.75);
}

.shepherd-welcome .popper__arrow {
    border: 16px solid transparent;
    content: '';
    display: block;
    height: 0;
    position: absolute;
    width: 0;
}

.shepherd-welcome[x-placement^='top'] {
    margin-bottom: 16px;
}

.shepherd-welcome[x-placement^='top'] .popper__arrow {
    border-bottom: 0;
    border-top-color: #ffffff;
    bottom: -16px;
    left: calc(50% - 16px);
}

.shepherd-welcome[x-placement^='bottom'] {
    margin-top: 16px;
}

.shepherd-welcome[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: #ffffff;
    border-top: 0;
    left: calc(50% - 16px);
    top: -16px;
}

.shepherd-welcome[x-placement^='left'] {
    margin-right: 16px;
}

.shepherd-welcome[x-placement^='left'] .popper__arrow {
    border-left-color: #ffffff;
    border-right: 0;
    margin-top: -16px;
    right: -16px;
    top: calc(50% - 16px);
}

.shepherd-welcome[x-placement^='right'] {
    margin-left: 16px;
}

.shepherd-welcome[x-placement^='right'] .popper__arrow {
    border-left: 0;
    border-right-color: #ffffff;
    left: -16px;
    top: calc(50% - 16px);
}

.shepherd-welcome:not(.shepherd-has-title) .shepherd-content header,
.shepherd-welcome:not(.shepherd-has-title) .shepherd-content .shepherd-header {
    padding-bottom: 0;
}

.shepherd-welcome .shepherd-content header,
.shepherd-welcome .shepherd-content .shepherd-header {
    background: #ffffff;
}

.shepherd-welcome .shepherd-content .shepherd-text {
    color: #336457;
}

body::after {
    opacity: 0;
}

body.shepherd-active::after {
    content: "";
    display: block;
    background-color: #000;
    opacity: 0.6;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1000000; }

.shepherd-element, .shepherd-target.shepherd-enabled {
    z-index: 10000000;
    position: relative;
}

.alert-message i {
    margin-right: 10px;
    font-size: 18px;
    display: inline-block;
    vertical-align: middle;
}

.alert-remove i {
    font-size: 18px;
    cursor: pointer;
}

.step-tour-big {
    width: 750px;
    text-align: center;
}

.step-tour-big img {
    width: 110px;
    margin-bottom: 30px;
}

.step-tour-big.shepherd-element .shepherd-content header {
    margin-bottom: 0;
}

.step-tour-big.shepherd-element .shepherd-content {
    padding: 30px;
    padding-top: 0;
}

.step-tour-big.shepherd-element .shepherd-content .shepherd-text {
    margin-bottom: 20px;
}

.step-tour-gdpr {
    position: absolute !important;
    width: 600px;
}

.step-tour-notif {
    position: absolute !important;
    width: 600px;
}

.tour-big {
    font-weight: bold;
    font-size: 30px;
    line-height: 36px;
}

.step-tour-gdpr h4 {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    line-height: 36px;
}

.step-tour-gdpr ul {
    margin-bottom: 30px;
}

.step-tour-gdpr.shepherd-element .shepherd-content header {
    padding: 0;
    margin: 0;
}

.step-tour-gdpr input[type=radio] {
    margin-right: 15px;
    margin-bottom: 15px;
}

.step-tour-notif h4 {
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    line-height: 36px;
}

.step-tour-notif ul {
    margin-bottom: 30px;
}

.step-tour-notif.shepherd-element .shepherd-content header {
    padding: 0;
    margin: 0;
}

.step-tour-notif input[type=radio] {
    margin-right: 15px;
    margin-bottom: 15px;
}

.btn-timeline-red {
    background: #ca4b5d;
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
    text-align: center;
}

.btn-download-file, .btn-download-file-attestation {
    cursor: pointer;
}

.downloadFile-depot {
    margin-left: 10px;
    /* width: 15px; */
}

.formDownloadFileParticipantAction {
    display: inline;
}

.formDownloadFileParticipantAction .checkbox {
    margin: 0 0 0 10px;
    display: inline;
}

.formDownloadFileParticipantAction .col-sm-9 {
    display: inline;
}

.formDownloadFileParticipantAction .form-group {
    display: inline;
}

.formDownloadFileParticipantAction .btn {
    padding: 0;
}

.formDownloadFileParticipantAction input {
    display: none;
}

.btn-timeline-red:before {
    color: white;
    content: "\f067";
    font: normal normal normal 12px/1 FontAwesome;
    text-align: center;
    vertical-align: middle;
}

.iradio_line-custom {
    text-align: center;
    padding: 5px 15px;
}

.iradio_line-custom .icheck_line-icon {
    display: none;
}

.iradio_line-custom.icheckbox_line-blue.checked, .iradio_line-blue {
    background: rgba(36, 137, 197, 0.5);
}

.iradio_line-custom.icheckbox_line-red.checked, .iradio_line-red {
    background: rgba(229, 108, 105, 0.5);
}

@media (min-width: 768px) {
    .step-tour-gdpr.shepherd-element .shepherd-content {
        padding: 30px;
        background-size: contain;
        background-position: top left;
        background-repeat: no-repeat;
    }

    .step-tour-gdpr.shepherd-element .shepherd-content .shepherd-text {
        margin-bottom: 20px;
    }

    .step-tour-notif.shepherd-element .shepherd-content {
        padding: 30px;
        background-size: contain;
        background-position: top left;
        background-repeat: no-repeat;
    }

    .step-tour-notif.shepherd-element .shepherd-content .shepherd-text {
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .shepherd-element .shepherd-content footer .shepherd-buttons li {
        display: block;
        margin-right: 0;
        margin-top: 1em;
    }

    .tour-big {
        font-size: 26px;
        line-height: 26px;
    }

    .shepherd-element .shepherd-content header .shepherd-title {
        font-size: 26px;
    }


    .step-tour-big .text-center img {
        display: none;
    }
}

.step-tour-inactive header {
    display: none;
}

/* nav custom */

.nav-tabs-front .nav-tabs {
    border-bottom: none;
}

.nav-tabs-front li>a, .nav-tabs-front li>a:hover, .nav-tabs-front li>a:focus {
    color: #888;
    font-size: 16px;
}

.nav-tabs-front li>a:hover {
    border: none;
    border-bottom: 5px solid #109199;
    background: none;
}

.nav-tabs-front li.active>a, .nav-tabs-front li.active>a:hover, .nav-tabs-front li.active>a:focus {
    border: 1px solid rgba(16, 145, 153, 0.5);
    border-bottom: 5px solid #109199;
    background: none;
    color: #109199;
    font-weight: bold;
    font-size: 16px;
}

.tab-content {
    padding: 32px;
}

.tab-content.tab-eduprat {
    border: 1px solid rgba(16, 145, 153, 0.5);
}

.page-footer {
    margin: 16px 0;
}

/** Etutorat **/

.etutorat-row {
    background: #FFFFFF;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0 16px 16px;
}

.etutorat-row-answers {
    background: #FFFFFF;
    margin-bottom: 2px;
    display: flex;
}

.etutorat-row-answers .radio-inline input[type=radio] {
    position: inherit;
    transform: scale(1.3);
    width: 80px;
    margin-left: 0;
    display: block;
    margin-right: 0;
    padding-left: 0;
}


.etutorat-row-answers-synthese .radio-inline input[type=radio] {
    transform: scale(1.5);
    width: 100px;
}

.etutorat-row-answers .radio-inline, .etutorat-row-answers .radio-inline+.radio-inline {
    margin-left: 0;
    padding-left: 0;
    font-size: 0;
}

.etutorat-row-answers div {

}

.etutorat-rows {
    margin-bottom: 32px;
}

.etutorat-rows-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    color: #616161;
    font-size: 18px;
}

.etutorat-rows-title > div {
    display: flex;
}

.etutorat-rows-title > div > span {
    width: 80px;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
}

.etutorat-rows-title-synthese {
    margin-bottom: 0;
    align-items: flex-end;
}

.etutorat-rows-title-synthese > div > span {
    background: #747D8A;
    color: white;
    width: 100px;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    border-right: 2px solid #F2F2F2;
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

ul.images, ul.files {
    list-style-type: none;
    padding-left: 16px;
}

ul.images > li, ul.files > li {
    border-left: 2px solid #666666;
    padding-left: 16px;
    margin-bottom: 16px;
}

ul.images > li img, ul.files > li img {
    margin-bottom: 16px;
    max-width: 100px;
    max-height: 100px;
}

ul.images input, ul.files input {
    margin-bottom: 0;
}

ul.images .form-group, ul.files .form-group {
    margin-bottom: 15px;
}

.btn-add-image, .btn-add-file {
    background-color: #109199;
    color: #fff;
    padding: 6px 12px;
    margin-bottom: 16px;
}

textarea {
    border: 1px solid #999 !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    min-width: 100%;
}

.control-label {
    font-size: 16px;
    color: #666;
    font-weight: bold;
}



@media (max-width: 1200px) {

    .etutorat-notes {
        display: none !important;
    }

    .etutorat-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .etutorat-row-answers .radio-inline, .etutorat-row-answers .radio-inline+.radio-inline {
        margin-left: 0;
        padding-left: 0;
        margin-right: 16px;
        font-size: inherit;
    }

    .etutorat-row-answers .radio-inline input[type=radio] {
        display: inline-block;
        width: inherit;
        margin-right: 5px;
        margin-top: 0;
    }
}

/** fiche action **/

.table-action {
    width: 100%;
}
.table-action th {
    font-size: 17px;
    background: #747D8A;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 16px;
    border-right: 3px solid #F2F2F2;
}
.table-action td {
    background: white;
    color: black;
    text-align: center;
    padding: 16px;
    border-right: 3px solid #F2F2F2;
    border-bottom: 5px solid #F2F2F2;
}
.table-action td.field-disabled {
    background: #E4E4E4;
    font-style: italic;
}
.table-action .add-action-row td {
    background: #E4E4E4;
    color: #707070;
    text-transform: uppercase;
    font-weight: bold;
    cursor: pointer;
}
.table-action .add-action-row td a {
    color: #707070;
}

a.link-sub-btn {
    color: #333333 !important;
    font-style: italic;
    font-size: 16px;
}

/*** Finalisation formation ***/

#formation-validation {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

#formation-validation > div {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 36px;
    max-width: 400px;
}

.tippy-content {
    text-align: center;
}

@media (max-width: 960px) {

    .rwd-table {
        margin: 1em 0;
        min-width: 300px;
    }

    .rwd-table th {
        display: none;
    }

    #content-tcs .rwd-table th {
        display: block;
    }

    .rwd-table--hide-mobile {
        display: none;
    }

    .rwd-table td {
        display: block;
    }

    .rwd-table td:first-child {
        padding-top: .5em;
    }

    .rwd-table td:last-child {
        padding-bottom: .5em;
    }

    .rwd-table td[data-th]:not(.text-bold):before {
        content: attr(data-th) ": ";
        font-weight: bold;
        width: 100%;
        display: inline-block;
    }

    .rwd-table th, .rwd-table td {
        text-align: left;
    }

    .rwd-table tbody tr {
        background-color: #f1f1f1;
    }
    .rwd-table .action-row {
        border-bottom: 3px solid #AAA;
    }

    .rwd-table {
        border-radius: .4em;
        overflow: hidden;
    }

    .rwd-table th, .rwd-table td {
        margin: .5em 1em;
    }
}

.btn-attestation-circle {
    margin-left: 2px !important;
    font-size: 10px;
}

.attestation-honneur {
    font-size: 16px;
}

.downloadFile {
    display: inline-block;
}
.downloadFile input[type='file']{
    position: absolute;
    margin-top: 3px;
    margin-left: 3px;
    height: 1px;
    width: 1px;
    z-index: -5;
}

.fileList-title {
    margin-left: 6px;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 4px;
}

.end-session-blocs {
    height: 600px;
    background-color: #E0E0E0;
    width: 27%;
    margin: 2%;
}

.end-steps {
    font-size: 40px;
    font-weight: bold;
    margin-left: 12px;
}

.blue-text {
    color: #109199;
    font-weight: bold;
}

.black-text {
    color: black;
}

.link-text {
    color: #2199e8;
    font-weight:bold;
    text-decoration: underline;
}

.end-send-attestation > .form-group > input {
    text-align: center;
}

.end-send-attestation > .form-group {
    margin-bottom: 0px;
}

.picture-bloc {
    width: 75px;
    height: 75px;
    background-color: #107d85;
    align-self: center;
    border-radius: 100%;
    margin: auto;
    margin-top: -38px;
    background-repeat: no-repeat;
    background-position: center;
}

.picture-bloc-importer {
    background-image: url('../img/importer2.svg');
}

.picture-bloc-documentValide {
    background-image: url('../img/documentValide.svg');
}

.picture-bloc-telecharger {
    background-image: url('../img/telecharger.svg');
}

.module-notif {
    font-weight: bold;
    font-size: 18px;
}

.etutorat-row-answers > label {
    margin-left: 0;
    padding-left: 0;
    font-size: 0;
}

.etutorat-row-answers.etutorat-row-answers--showInResponsive {
    flex-direction: column;
}
.etutorat-row-answers.etutorat-row-answers--showInResponsive > label {
    font-size: 1rem;
    margin-right: 3rem;
}
.etutorat-row-answers.etutorat-row-answers--showInResponsive .type-note {
    display: inline-block;
    width: auto;
}
@media (width > 900px) {
    .etutorat-row-answers.etutorat-row-answers--showInResponsive {
        flex-direction: row;
    }
}
@media (width > 1200px) {
    .etutorat-row-answers.etutorat-row-answers--showInResponsive > label {
        font-size: 0;
        margin-right: inherit;
    }
    .etutorat-row-answers.etutorat-row-answers--showInResponsive .type-note {
        display: block;
        width: 80px;
    }
}
.type-note {
    position: inherit;
    transform: scale(1.3);
    width: 80px;
    margin-left: 0;
    display: block;
    margin-right: 0 !important;
    padding-left: 0;
}

.etutorat-row-answers-synthese > label > .type-note {
    margin-right: 15px !important;
}

.alert {
    font-size: 16px;
}

.lesson-picto:before {
    content: "";
    display: block;
    width: 20px;
    height: 20px;
    padding-top: 5px;
    margin-left: 5px;
    text-align: center;
    background-color: white;
    border-radius: 30px;
    border: 1px solid #ddd;
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: white;
}

.completed .lesson-picto:before {
    background-color: #59a059;
    border: none;
    transition: all 200ms ease-in;
    content: "\f00c";
}

.incompleted .lesson-picto::before {
    background-color: #d34d5a;
    border: none;
    transition: all 200ms ease-in;
    content: "\f067";
}

.unavailabled .lesson-picto::before {
    background-color: white;
    border: none;
    transition: all 200ms ease-in;
    content: "\f067";
}

.clock-picto:before {
    content: "";
    width: 20px;
    height: 20px;
    padding-top: 5px;
    margin-right: 5px;
    text-align: center;
    background-color: transparent;
    border-radius: 30px;
    border: 1px solid #ddd;
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: #59a059;
    border: none;
    transition: all 200ms ease-in;
    content: "\f017";
}

.pensil-picto:before {
    content: "";
    width: 20px;
    height: 20px;
    padding-top: 5px;
    margin-right: 5px;
    text-align: center;
    background-color: transparent;
    border-radius: 30px;
    border: 1px solid #ddd;
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: #59a059;
    border: none;
    transition: all 200ms ease-in;
    content: "\f040";
}

.remake-lesson:before {
    content: "";
    width: 20px;
    height: 20px;
    padding-top: 5px;
    margin-right: 5px;
    text-align: center;
    background-color: transparent;
    border-radius: 30px;
    border: 1px solid #ddd;
    transition: all 200ms ease-in;
    font: normal normal normal 12px/1 FontAwesome;
    color: white;
    border: none;
    transition: all 200ms ease-in;
    content: "\f01e ";
    float:right;
}

.remake-lesson {
    background-color: #59a059;
    color: white;
}

.completed {
    background-color: #CDE2CD;
}

.lesson-block {
    padding: 10px;
    margin-bottom: 2px;
    display: flex;
}

.lesson-action-block {
    color:white;
    margin-left: auto;
    margin-top: 4px;
}

.lesson-label {
    max-width: 80%;
}

@media (max-width: 1024px) {
    .lesson-block {
        display: block;
    }

    .lesson-action-block {
        margin-top: 1em;
        margin-left: 1em;
        margin-bottom: 1em;
    }

    .lesson-label {
        max-width: 80%;
    }

    .lesson-picto {
        text-align: center;
    }

    .lesson-picto:before {
        margin-left: 1em;
        margin-bottom: 4px;
    }
}

.lesson-action {
    text-align:right;
    border-radius: 6px;
    padding: 8px;
    font-weight: bold;
}

.lesson-block-green {
    background-color: #CDE2CD;
}

.lesson-block-red {
    background-color: #F1C8CC;
}

.lesson-block-grey {
    background-color: #F5F5F5;
}

.lesson-block-green .clock-picto:before, .lesson-block-green .pensil-picto:before, .lesson-block-green{
    color: #59a059;
}

.lesson-block-red .clock-picto:before, .lesson-block-red .pensil-picto:before, .lesson-block-red{
    color: #d34d5a;
}

.lesson-block-grey .clock-picto:before, .lesson-block-grey .pensil-picto:before, .lesson-block-grey{
    color: #afafaf;
}

.lesson-block-green .lesson-picto:before, .lesson-block-green .lesson-action {
    background-color: #59a059;
}

.lesson-block-red .lesson-picto:before, .lesson-block-red .lesson-action {
    background-color: #d34d5a;
}

.lesson-block-grey .lesson-picto:before, .lesson-block-grey .lesson-action {
    background-color: #afafaf;
}

.lesson-block-grey .lesson-picto:before {
    background-color: white;
}

.action-icon {
    margin-right: 0.5em;
}

.activity-content {
    text-align: center;
}

.activity-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}

.activity-text {
    all: revert;
    all: unset;
}

.activity-text ul {
    list-style-position: inside !important;
}

.activity-picture, .activity-pdf, .activity-video, .activity-prezi, .activity-powerpoint {
    text-align: center;
    margin: 4em;
}



.biblio-title {
    font-size: 16px;
    text-decoration: underline;
    font-weight: bold;
    margin-top: 1em;
}

.biblio-label {
    font-size: 18px;
    margin-top: 2em;
}

.activity-bibliographies {
    margin-left: 1em;
}

.activity-bibliographies > ul {
    list-style-type: none;
}

.sidebar-elearning {
    background-color: #109199 !important;
}

.sidebar-elearning > ul {
    margin-left: initial;
}

.sidebar-header-desktop {
    background: #1e282c;
}

.sidebar-header-mobile {
    background: #0f4975;
}

.sidebar-header-title-elearning {
    margin: 10px 0px;
    padding: 20px;
    font-size: 20px;
    flex: 1 0 100%;
}

.sidebar-elearning-progress {
    margin: 10px 0px;
    padding: 20px;
    flex: 1 0 100%;
}

.sidebar-lesson-title {
    font-size: 18px;
    text-align: center;
    font-weight: bold;
}

.sidebar-activity-list {
    margin-top: 2em;
}

.activity-item {
    display: flex;
}

.activity-item-active {
    font-weight: bold;
}

.sidebar-activity-completed {
    min-width: 20%;
    font-size:20px;
}

.sidebar-elearning-duration {
    background-color: #E2E2E2;
    padding: 20px;
    color: black;
    text-align: center;
    font-weight: bold;
}

.current-duration {
    font-size: 18px;
}

.activity-files {
    font-size: 18px;
    margin-bottom: 5em;
}

.activity-files ul {
    margin-left: 2em;
}

.activity-files ul, .activity-text ol {
    list-style-position: inside !important;
}

.activity-files-link {
    font-size: 16px;
    color: #5a5a5a;
    text-decoration: underline;
}

.activity-files-link:hover {
    color: #2a2a2a;
    text-decoration: underline;
}

.expected-answers-separator {
    width: 80%;
    border: 2px solid #a3a3a3;
    border-radius: 10px;
}

.expected-answers-list ul{
    font-weight:bold;
    list-style: none;
    }
    .expected-answers-list ul li::before {
    content: "\2022";
    color: #109199;
    display: inline-block;
    width: 1em;
    margin-left: -1.5em;
    font-size:25px;
}

.expected-answers-block {
    display: none;
}

.sidebar-activity-link {
    color: white !important;
}

.sidebar-activity-link:hover {
    color: white;
    font-weight: bold;
    background-color: transparent !important;
}

.activity-quizz-content .checkbox {
    display: flex;
}

.good-answer {
    font-size: 18px;
    margin-right: 1em;
    color:#59a059;
}

.wrong-answer {
    font-size: 20px;
    margin-right: 1em;
    color:#d34d5a;
}

.mlquiz {
    margin-left: 2.5em;
}

.elearning-picture {
    max-width: 35%;
    cursor: zoom-in;
}

.elearning-picture-big {
    min-width: 100%;
    cursor: zoom-out !important;
}

.tcs-picture, .slide-content {
    cursor: zoom-in;
}

.tcs-picture-big, .slide-content-big {
    scale: 1.3;
    cursor: zoom-out !important;
}

.elearning-pictureQuestion {
    max-width: 50%;
    cursor: zoom-in;
}

.last-consult {
    text-align: right;
    font-size:10px;
    margin-top:1em;
    font-weight: 600;
}

.menu {
    background-color: #2B6684;
    color: white;
    display: flex;
    width: 100%;
    padding: 0.5em 1em;
    font-weight: 600;
}

.menu-right {
    text-align: right;
    display: flex;
    margin-left: auto;
    font-size: 16px;
}

.menu-right-element {
    margin: 0em 1em;
}

.menu-link, .menu-link:hover, .menu-link:visited {
    color: white;
    font-size: 16px;
}

.menu-logout {
    border: 2px solid white;
    border-radius: 8px;
    padding: 0.4em 1.2em;
}

.icon-profile {
    margin-right: 0.5em;
    padding: 0.8em;
    border-radius: 22px;
    border: 1px solid #BDBFC7;
    background-color: #BDBFC7;
    color: #818288;
}

.menu-deroulant{
    width: 100%;
    margin: 0 auto;
    position: sticky;
    top: 0px;
}

.menu-deroulant ul{
    list-style-type: none;
}

.menu-deroulant ul li{
    float: left;
    width: 25%;
    text-align: center;
    position: relative;
}

.menu-deroulant ul::after{
    content: "";
    display: table;
    clear: both;
}

.menu-deroulant a, .menu-deroulant a:hover{
    color: white;
}

.sous{
    display: none;
    box-shadow: 0px 1px 2px #CCC;
    background-color: #2B6684;
    position: absolute;
    width: 150%;
    z-index: 10000;
}

.menu-deroulant > ul li:hover .sous{
    display: block;
}
.sous li{
    float: none;
    width: 100%;
    text-align: left;
}
.sous a{
    padding: 10px;
    border-bottom: none;
}
.sous a:hover{
    border-bottom: none;
    background-color: RGBa(200,200,200,0.1);
}


.navbar-nav>.user-menu>.dropdown-menu>.user-footer-menu {
    background-color: #2B6684;
}

.dropdown-menu-eduprat {
    border-color: #2B6684;
    width: auto !important;
    border: unset;
    min-width: 160px;
    font-size: 16px;
    margin-top: 18px !important;
}

.navbar-nav>li>a {
    padding-top: 0px;
    padding-bottom: 0px;
}

.user-menu>a {
    color: white;
}

.user-menu>a:hover, .user-menu>a:active, .user-menu>a:focus {
    color: white;
    background: #2B6684;
}

.navbar-nav>.user-menu>.dropdown-menu>.user-footer-menu {
    padding: 0px;
}

.navbar-nav>.user-menu>.dropdown-menu>.user-footer .btn-blue {
    color: white;
    background-color: #2B6684;
    /* border-color: #2B6684; */
}

.formations-link {
    background-color: #2B6684 !important;
}

.formations-link:hover, .formations-link:focus {
    color: white !important;
}

.ps-name-mobile {
    padding: 1em;
    font-weight: 600;
    text-align: right;
}

.links-block-mobile {
    text-align: center;
    padding-top: 1em;
    padding-bottom: 2em;
}

.links-block-mobile>a {
    line-height: 2em;
    font-size: 16px;
    color: white;
}

.menu-logout-mobile {
    color: #109199;
    font-size: 14px;
    border: 2px solid white;
    padding: 0.4em 1em;
    background-color: white;
    border-radius: 10px;
    font-weight: 600;
    margin-left: 5em;
}

.user-icon-menu {
    display: inline-block !important;
}

header > .row {
    margin-left: -15px !important;
}

.sidebar-no-link:hover {
    font-weight: normal;
    cursor: text;
}

.menu-right > .navbar-nav {
    overflow: unset;
}

.btn-menu-year {
    border-color: transparent;
    border-bottom-color: #ddd;
}

.navbar-nav>.user-menu>.dropdown-menu {
    padding: unset;
}

.step-current-time {
    margin-left: auto;
}
tr.eduprat-color,
th.eduprat-color {
    background-color: #109199;
    color: white;
}
.synthese-educative {
    border: 1px solid #109099;
    .synthese-educative-header {
        padding: 1em;
        background-color: #109099;
        color: white;
    }
    .synthese-educative-content {
        padding: 2em;
    }
}

#content-tcs {
    margin: 0 1.5rem;
    font-size: 20px;
    .table-action td {
        border-bottom: none;
        padding: 0;
        background-color: transparent;
        font-size: 20px;
        &.reponse {
            text-align: left;
            label {
                font-weight: normal;
                margin-left: 0.5rem;
                display: inline;
            }
        }
    }
    input[type=checkbox], input[type=radio] {
        margin: 8px 0 0 -20px;
    }
    .tcs-bilan {
        input[type=checkbox], input[type=radio] {
            margin: 8px 0 0 -5px;
        }
    }
    .tcs-only-mobile {
        display: inline-block;
        font-weight: bold;
    }
    .rwd-table th,.rwd-table td {
        margin: 0;
    }
    .rwd-table tbody tr{
        background-color: transparent;
    }
    td .question-responsive {
        font-weight: bold;
        text-decoration: underline;
        margin-top: 2rem;
    }
    .table-action th {
        font-size: 20px;
    }
}

@media (min-width: 960px) {
    .tcs-only-mobile, #content-tcs td .question-responsive{
        display: none !important;
    }
    #content-tcs .table-action td {
        background: white;
        padding: 16px;
        border-right: 3px solid #F2F2F2;
        border-bottom: 5px solid #F2F2F2;
    }
}
.tcs-results {
    display: flex;
    flex-direction: column;
    background-color: #e8e8e8;
    padding: 2rem;
    margin: 2rem 0 0;
    .tcs-result-items {
        display: flex;
        flex-direction: row;
    }
    .tcs-result-item {
        display: flex;
        flex-direction: row;
        flex-basis: 150px;
        &.tcs-result-item--restitution {
            display: flex;
            align-items: flex-start;
            flex: 1 0 1px;
        }
        &.tcs-result-item-experts {
            display: none;
        }
        &+ .tcs-result-item {
            display: none;
            flex-basis: max-content;
            &.tcs-result-item--restitution {
                display: flex;
                flex-basis: 1px;
            }
        }
        &.reponse {
            display: none;
            &.votre-reponse {
                display: flex;
                align-items: start;
                .votre-reponse-text {
                    display: flex;
                    flex-direction: column;
                    margin-left: 1rem;
                }
            }
            &.tcs-result-item--restitution {
                display: flex;
            }
            input, label {
                margin: 0;
                font-weight: normal;
            }
            label.marge-left {
                margin: 0 0 0 1rem;;
            }
            input[type=radio] {
                margin-top: 9px;
            }
        }
    }
    .votre-reponse input[type=radio] {
        accent-color: #109199;
        margin-top: 4px;
    }
    .votre-reponse input[type=radio][disabled] {
        accent-color: #109199;
    }
    .answer-bilan-experts-responsive {
        color: #109199;
        font-style: italic;
        font-weight: bold;
        display: block;
    }
}

.tcs-results-analyse {
    background-color: transparent;
    padding: 0rem;
    margin: 0rem 0 0;
}
.tcs-rendu-question {
    ul, ol {
        padding-left: 20px;
    }
}



.tcs-desktop {
    display: none;
}

@media (min-width: 960px) {
    .tcs-results {
        padding: 2rem 5rem;
        .tcs-result-items {
            margin-left: 5rem;
        }
        .tcs-result-items-axes {
            margin-left: 0rem;
        }
        .tcs-result-item {
            &+ .tcs-result-item {
                display: flex;
                margin-left: 5rem;
            }
            &.tcs-result-item-experts {
                display: flex;
            }
            &.reponse {
                display: flex;
                align-items: start;
                padding-left: 3.5rem;
                flex: 1;
                &.votre-reponse {
                    flex: 9;
                }
            }
        }
    }
    .tcs-results-analyse {
        padding: 0rem;
        .tcs-result-item {
            &.reponse {
                padding-left: 0rem;
                &.votre-reponse {
                    flex: 7;
                }
            }
        }
    }
    .tcs-result-item--restitution.tcs-votre-hypothese {
        flex: 7 !important;
    }
    .answer-bilan-experts-responsive {
        display: none;
    }
    .tcs-mobile {
        display: none;
    }
    .tcs-desktop {
        display: block;
    }
}
.tcs-answers-experts {
    margin: 0;
    i {
        color: #7e7e7e;
    }
    .tcs-answers-experts-items {
        margin: 0;
        padding: 1rem 0;
        .tcs-answers-experts-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 5px;
            padding: 0 1.5rem;
            br.hide-desktop {
                display: none;
            }
        }
    }
    .justification {
        margin-left: 10px;
        font-weight: bold;
    }
    .justification-detail {
        margin-left: 10px;
    }
    .justification-panel {
        padding: 1rem 1.5rem 0;
        margin: 0;
    }
    .justification-panel-axes {
        padding: 0rem;
    }
    .expert-ayant-repondu {
        background-color: #d9e7e8;
        padding: 2rem 1.5rem;
        margin: 0;
    }
}
@media (min-width: 960px) {
    .tcs-answers-experts {
        margin: 0 4rem;
        .tcs-answers-experts-items {
            margin: 1.5rem 4rem;
            padding: 0;
            background-color: transparent;
            .tcs-answers-experts-item {

            }
        }
        .tcs-answers-experts-items-axes {
            margin: 1.5rem 0rem;
        }
        .expert-ayant-repondu {
            background-color: transparent;
            margin: 0 0 10px;
            padding: 0;
        }
    }
    .tcs-answers-experts-axes {
        margin: 0 0rem;
    }
}
.text-cancelcase {
    text-transform: none !important;
}

.red {
    color: red;
}

.font-16 {
    font-size: 16px;
}

.next-icon {
    font-size: 14px;
    margin-left: 2px;
}

.modal-tcs {
    display: none;
    position: fixed;
    z-index: 3;
    left: 0em;
    top: 0;
    width: 100%;
    height: 100vh;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.modal-tcs-content {
    background-color: #fefefe;
    border: 1px solid #888;
    cursor: zoom-out !important;
}

@media (min-width: 768px) {
    .modal-tcs-content {
        margin-left: auto;
        margin-right: auto;
        padding: 25px;
        width: 60%;
        margin-top: 10vh;
        border-radius: 20px;
    }
}

.close {
    color: black;
    float: right;
    font-size: 28px;
    font-weight: bold;
    margin-right: 0.2em;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}
