{"name": "blueimp-gallery", "version": "2.33.0", "title": "blueimp Gallery", "description": "blueimp Gallery is a touch-enabled, responsive and customizable image and video gallery, carousel and lightbox, optimized for both mobile and desktop web browsers. It features swipe, mouse and keyboard navigation, transition effects, slideshow functionality, fullscreen support and on-demand content loading and can be extended to display additional content types.", "keywords": ["image", "video", "gallery", "carousel", "lightbox", "mobile", "desktop", "touch", "responsive", "swipe", "mouse", "keyboard", "navigation", "transition", "effects", "slideshow", "fullscreen"], "homepage": "https://github.com/blueimp/Gallery", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "repository": {"type": "git", "url": "git://github.com/blueimp/Gallery.git"}, "license": "MIT", "devDependencies": {"clean-css-cli": "^4.1.6", "eslint": "^4.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "prettier-eslint-cli": "^4.2.1", "uglify-js": "^3.0.28"}, "scripts": {"format": "prettier-eslint --no-semi --single-quote --write **/*.js", "test": "eslint .", "build:js": "cd js && uglifyjs blueimp-helper.js blueimp-gallery.js blueimp-gallery-fullscreen.js blueimp-gallery-indicator.js blueimp-gallery-video.js blueimp-gallery-vimeo.js blueimp-gallery-youtube.js -c -m -o blueimp-gallery.min.js --source-map url=blueimp-gallery.min.js.map", "build:jquery": "cd js && uglifyjs blueimp-gallery.js blueimp-gallery-fullscreen.js blueimp-gallery-indicator.js blueimp-gallery-video.js blueimp-gallery-vimeo.js blueimp-gallery-youtube.js jquery.blueimp-gallery.js -c -m -o jquery.blueimp-gallery.min.js --source-map url=jquery.blueimp-gallery.min.js.map", "build:css": "cd css && cleancss -c ie7 --source-map -o blueimp-gallery.min.css blueimp-gallery.css blueimp-gallery-indicator.css blueimp-gallery-video.css", "build": "npm run build:js && npm run build:jquery && npm run build:css", "preversion": "npm test", "version": "npm run build && git add -A js css", "postversion": "git push --tags origin master master:gh-pages && npm publish"}, "main": "js/blueimp-gallery.js"}