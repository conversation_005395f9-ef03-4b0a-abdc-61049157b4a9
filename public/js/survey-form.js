var $collectionHolder;

var $addQuestionLink = $('<a href="#" class="add_question_link btn btn-eduprat">Ajouter une question</a>');
var $addChoiceLink = $('<a href="#" class="add_choice_link btn btn-eduprat">Ajouter une case à cocher</a>');
var $addImageLink = $('<a href="#" class="add_choice_link btn btn-eduprat">Ajouter une image</a>');
var $newLinkLi = $('<div></div>').append($addQuestionLink);

function addQuestionFormDeleteLink($questionFormLi) {
	var $removeFormA = $('<a class="btn btn-danger" href="#">Supprimer la question</a>');
	var $handle = $("<span class='handle handle-icon'></span>")
	$questionFormLi.append($handle, $removeFormA);

	if (typeof patientId !== "undefined" && $questionFormLi.data('id')) {
		var text = 'Déplacer vers ';
		if (auditType === "predefined") {
			text  += 'un autre patient';
		} else {
			text += 'une autre vignette clinique';
		}
		var $patient = $('<a class="btn btn-primary btn-move-patient mls" data-question="'+$questionFormLi.data('id')+'" href="#">' + text + '</a>');
		$questionFormLi.append($patient);
	}

	$removeFormA.on('click', function (e) {
		e.preventDefault();
		var valid = confirm('Êtes-vous sûr de vouloir supprimer la question ?');
		if (valid) {
			$questionFormLi.remove();
			updatePositions();
		}
	});
}

function addChoiceFormAddLink($collectionHolder) {
	var $newLink = $addChoiceLink.clone();
	$collectionHolder.after($newLink);
	$newLink.click(function (e) {
		e.preventDefault();
		addChoiceForm($collectionHolder);
	});
}

function addChoiceForm($collectionHolder) {
	var prototype = $collectionHolder.data('prototype');
	var index = $collectionHolder.closest('li').data('index');
	var indexChoice = $collectionHolder.data('index');
	var newForm = prototype.replace(/__name__/g, index);
	newForm = newForm.replace(/__choice__/g, indexChoice);
	$collectionHolder.data('index', indexChoice + 1);
	var $newFormLi = $('<li></li>').data('index', indexChoice).append(newForm);
	$collectionHolder.append($newFormLi);
	addChoiceFormDeleteLink($newFormLi);
}

function addChoiceFormDeleteLink($choiceFormLi) {
	var $removeFormA = $('<a class="btn btn-danger" href="#">Supprimer la case</a>');
	$choiceFormLi.append($removeFormA);

	$removeFormA.on('click', function (e) {
		e.preventDefault();
		var valid = confirm('Êtes-vous sûr de vouloir supprimer la case à cocher ?');
		if (valid) {
			$choiceFormLi.remove();
		}
	});
}

function addImageFormAddLink($collectionHolder) {
	var $newLink = $addImageLink.clone();
	$collectionHolder.after($newLink);
	$newLink.click(function (e) {
		e.preventDefault();
		addImageForm($collectionHolder);
	});
}

function addImageForm($collectionHolder) {
	var prototype = $collectionHolder.data('prototype');
	var index = $collectionHolder.closest('li').data('index');
	var indexImage = $collectionHolder.data('index');
	var newForm = prototype.replace(/__name__/g, index);
	newForm = newForm.replace(/__image__/g, indexImage);
	$collectionHolder.data('index', indexImage + 1);
	var $newFormLi = $('<li></li>').data('index', indexImage).append(newForm);
	$input = $newFormLi.find('input');
	$input.change(function() {
		size = this.files[0].size;
		if (size > 2048000) {
			this.value = '';
			alert('Le poids de l\'image que vous souhaitez importer est supérieur à la taille autorisée de 2 Mo.\nMerci de l\'optimiser et réessayer.')
		}
	})
	$input.addClass('file-upload');
	$collectionHolder.append($newFormLi);
	addImageFormDeleteLink($newFormLi);
}

function addImageFormDeleteLink($imageFormLi) {
	var $removeFormA = $('<a class="btn btn-danger" href="#">Supprimer l\'image</a>');
	$imageFormLi.append($removeFormA);

	$removeFormA.on('click', function (e) {
		e.preventDefault();
		var valid = confirm('Êtes-vous sûr de vouloir supprimer l\'image ?');
		if (valid) {
			$imageFormLi.remove();
		}
	});
}

function updatePositions() {
	$.each($('ul.questions > li.panel'), function(index, node) {
		$(node).find('.question-position').val(index + 1);
		var title = $(node).find('.question-label').val();
		var patient = typeof patientId === "undefined" ? "" : (patientId + "-");
		$(node).find('.question-index').html(patient + (index + 1) + " : <span class='question-index-title'>" + title + "</span>");
	});
}

$(document).ready(function(){

	$(document).on('change', '.survey_question_type', function (e) {
		e.preventDefault();
		if ($.trim($(this).val()) === "") {
			return false;
		}
		var $types = $(this).closest('li').find('[data-type]');
		$types.addClass('hidden');
		$types.filter('[data-type=' + $(this).val() + ']').removeClass('hidden');
		if ($(this).val() === "radio") {
			$(this).closest('li').find('.radio-answer').attr('required', 'required');
		} else {
			$(this).closest('li').find('.radio-answer').removeAttr('required');
		}
	});

	$(document).on('change', '.radio-answer', function (e) {
		var id = $(this).closest('.box-body').find('.textarea-answer').attr('id');
		tinymce.get(id).setContent($(this).val());
	});

	$('.survey_question_type').trigger('change');
	$('.format').trigger('change');

	$("[type=submit]").click(function (event) {
		$.each($("form.form-collapse-validate").find("input, select, textarea"), function () {
			if ($(this).context.attributes["required"] !== undefined) {
				var invalid = false;
				if ($(this).attr("type") === "radio") {
					invalid = $('[name="' + $(this).attr("name") + '"]:checked').length === 0;
				} else {
					invalid = $(this).val() === "";
				}
				if (invalid && !$($(this).closest(".panel").find('a[data-toggle]').attr("href")).hasClass("in")) {
					$.support.transition = false;
					$(this).closest(".panel").find('a[data-toggle]').trigger("click");
					$.support.transition = true;
				}
			}
		});
	});

});