/////////////////////////////////////////////////////////
/////////////// The Radar Chart Function ////////////////
/////////////// Written by <PERSON><PERSON><PERSON> ////////////////
////////////////// VisualCinnamon.com ///////////////////
/////////// Inspired by the code of alangrafu ///////////
/////////////////////////////////////////////////////////

function RadarChart(id, data, options) {
	var cfg = {
		w: 600,				//Width of the circle
		h: 600,				//Height of the circle
		margin: {top: 20, right: 20, bottom: 20, left: 20}, //The margins of the SVG
		levels: 3,				//How many levels or inner circles should there be drawn
		maxValue: 0, 			//What is the value that the biggest circle will represent
		labelFactor: 1.25, 	//How much farther than the radius of the outer circle should the labels be placed
		wrapWidth: 60, 		//The number of pixels after which a label needs to be given a new line
		opacityArea: 0.6, 	//The opacity of the area of the blob
		dotRadius: 4, 			//The size of the colored circles of each blog
		opacityCircles: 0, 	//The opacity of the circles of each blob
		strokeWidth: 3, 		//The width of the stroke around each blob
		roundStrokes: false,	//If true the area and stroke will follow a round path (cardinal-closed),
		fillAreas: true,
		lineBackground: false,
		legends: [],
		color: d3.scale.category10(),	//Color function
		gradients: [["#ffeb3b", "#ff5722"]],
		actalians: false,
		rotationFactor: 0
	};

	//Put all of the options into a variable called cfg
	if('undefined' !== typeof options){
		for(var i in options){
			if('undefined' !== typeof options[i]){ cfg[i] = options[i]; }
		}//for i
	}//if

	//If the supplied maxValue is smaller than the actual one, replace by the max in the data
	var maxValue = Math.max(cfg.maxValue, d3.max(data, function(i){return d3.max(i.map(function(o){return o.value;}))}));
	maxValue = $.isNumeric(maxValue) ? maxValue : 0;

	var allAxis = (data[0].map(function(i, j){return i.axis})),	//Names of each axis
		total = allAxis.length,					//The number of different axes
		radius = Math.min(cfg.w/2, cfg.h/2), 	//Radius of the outermost circle
		Format = d3.format(''),			 	//Percentage formatting
		angleSlice = Math.PI * 2 / Math.max(1, total);		//The width in radians of each "slice"

	//Scale for the radius
	var rScale = d3.scale.pow()
		.exponent(1.5)
		.range([0, radius])
		.domain([0, maxValue]);

	//The radial line function
	var radarLine = d3.svg.line.radial()
		.interpolate("linear-closed")
		.radius(function(d) { return rScale(d.value); })
		.angle(function(d,i) {	return (i+cfg.rotationFactor)*angleSlice; });

	//Scale for the radius
	var rScale2 = d3.scale.pow()
		.exponent(1.5)
		.range([0, radius * 1])
		.domain([0, maxValue]);

	//The radial line function
	var radarLine2 = d3.svg.line.radial()
		.interpolate("linear-closed")
		.radius(function(d) { return rScale2(d.value); })
		.angle(function(d,i) {	return (i+cfg.rotationFactor)*angleSlice; });

	/////////////////////////////////////////////////////////
	//////////// Create the container SVG and g /////////////
	/////////////////////////////////////////////////////////

	//Remove whatever chart with the same id/class was present before
	d3.select(id).select("svg").remove();

	//Initiate the radar chart SVG
	var svg = d3.select(id).append("svg")
		.attr("width",  cfg.w + cfg.margin.left + cfg.margin.right)
		.attr("height", cfg.h + cfg.margin.top + cfg.margin.bottom)
		.attr("class", "radar"+id);

	if (cfg.actalians) {

		svg.append("rect")
			.attr("width", (cfg.w + cfg.margin.left + cfg.margin.right )/ 2)
			.attr("height", cfg.h + cfg.margin.top + cfg.margin.bottom)
			.attr('fill', '#f0f2f3')
		;

		svg.append("rect")
			.attr("width", (cfg.w + cfg.margin.left + cfg.margin.right) / 2)
			.attr("height", cfg.h + cfg.margin.top + cfg.margin.bottom)
			.attr("transform", "translate(" + ((cfg.w + cfg.margin.left + cfg.margin.right) / 2 ) + ",0)")
			.attr('fill', '#f7f8f9')
		;

		svg.append("line")
			.attr("x1", (cfg.w + cfg.margin.left + cfg.margin.right) / 2)
			.attr("y1", 0)
			.attr("x2", (cfg.w + cfg.margin.left + cfg.margin.right) / 2)
			.attr("y2", cfg.h + cfg.margin.top + cfg.margin.bottom)
			.attr('stroke-dasharray', "3, 3")
			.attr('stroke', '#aaa')
			.attr('fill', 'transparent')
		;

		svg.append("text")
			.attr("x", (cfg.w + cfg.margin.left + cfg.margin.right) / 2 - 10)
			.attr("y", cfg.h + cfg.margin.top + cfg.margin.bottom - 10)
			.attr('text-anchor', 'end')
			.attr('fill', '#005472')
			.attr('font-size', '18px')
			.text('CONNAISSANCES')
		;

		svg.append("text")
			.attr("x", (cfg.w + cfg.margin.left + cfg.margin.right) / 2 + 10)
			.attr("y", cfg.h + cfg.margin.top + cfg.margin.bottom - 10)
			.attr('text-anchor', 'start')
			.attr('fill', '#005472')
			.attr('font-size', '18px')
			.text('COMPÉTENCES')
		;

	}

	//Append a g element
	var g = svg.append("g")
		.attr("transform", "translate(" + (cfg.w/2 + cfg.margin.left) + "," + (cfg.h/2 + cfg.margin.top) + ")");

	/////////////////////////////////////////////////////////
	////////// Glow filter for some extra pizzazz ///////////
	/////////////////////////////////////////////////////////

	//Filter for the outside glow
	var filter = g.append('defs').append('filter').attr('id','glow'),
		feGaussianBlur = filter.append('feGaussianBlur').attr('stdDeviation','2.5').attr('result','coloredBlur'),
		feMerge = filter.append('feMerge'),
		feMergeNode_1 = feMerge.append('feMergeNode').attr('in','coloredBlur'),
		feMergeNode_2 = feMerge.append('feMergeNode').attr('in','SourceGraphic');


	cfg.gradients.reverse().forEach(function(colors, index) {
		//Filter for the outside glow
		var gradient = g.append('defs').append('linearGradient').attr('id','gradient-' + index)
				.attr('x1', '0%')
				.attr('x2', '100%')
				.attr('y1', '0%')
				.attr('y2', '0%')
			,
		color1 = gradient.append('stop').attr('offset','0%').attr('stop-color', function(d,i) {
			return colors[0];
		}).attr('stop-opacity','0.8'),
		color3 = gradient.append('stop').attr('offset','100%').attr('stop-color', function(d,i) {
			return colors[1];
		}).attr('stop-opacity','0.8');
	});


	/////////////////////////////////////////////////////////
	/////////////// Draw the Circular grid //////////////////
	/////////////////////////////////////////////////////////

	//Wrapper for the grid & axes
	var axisGrid = g.append("g").attr("class", "axisWrapper");
	//Draw the background circles
	if (cfg.lineBackground) {
		axisGrid.selectAll(".levels")
			.data(d3.range(1,(cfg.levels+1)).reverse())
			.enter()
			.append("path")
			.attr("class", "gridCircle")
			.attr("d", function(d, i){
				var points = d3.range(0, total).map(function(){
					return {value: Math.round(maxValue * d/cfg.levels)};
				});
				return radarLine(points);
			})
			.style("fill", "#FFFFFF")
			.style("stroke", "#CCCCCC")
			.style("fill-opacity", cfg.opacityCircles)
			.style("stroke-dasharray", "3, 3")
		;
	} else {
		axisGrid.selectAll(".levels")
			.data(d3.range(1,(cfg.levels+1)).reverse())
			.enter()
			.append("circle")
			.attr("class", "gridCircle")
			.attr("r", function(d, i){ return rScale(Math.round(maxValue * d/cfg.levels)); })
			.style("fill", "#FFFFFF")
			.style("stroke", "#CCCCCC")
			.style("fill-opacity", cfg.opacityCircles)
			.style("stroke-dasharray", "3, 3")
		;
	}

	//Text indicating at what % each level is
	axisGrid.selectAll(".axisLabel")
		.data(d3.range(1,(cfg.levels+1)).reverse())
		.enter().append("text")
		.attr("class", "axisLabel")
		.attr('text-anchor', 'middle')
		.attr("x", 13)
		.attr("y", function(d){return - rScale(Math.round(maxValue * d/cfg.levels)) - 7;})
		// .attr("y", function(d, i){ return rScale(maxValue * d/cfg.levels) * Math.sin(angleSlice* 0.5 - Math.PI/2) - 10; })
		.attr("dy", "0.4em")
		.style("font-size", "10px")
		.attr("fill", "#AAAAAA")
		.text(function(d,i) {
			var percent = Format(Math.round(maxValue * d/cfg.levels));
			if (percent > 0) {
				return percent + "%"
			}
			return "";
		});

	/////////////////////////////////////////////////////////
	//////////////////// Draw the axes //////////////////////
	/////////////////////////////////////////////////////////

	//Create the straight lines radiating outward from the center
	var axis = axisGrid.selectAll(".axis")
		.data(allAxis)
		.enter()
		.append("g")
		.attr("class", "axis");
	//Append the lines
	axis.append("line")
		.attr("x1", 0)
		.attr("y1", 0)
		.attr("x2", function(d, i){ return rScale(maxValue) * Math.cos(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.attr("y2", function(d, i){ return rScale(maxValue) * Math.sin(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.attr("class", "line")
		.style("stroke", "#CCCCCC")
		.style("fill-opacity", 0)
		.style("stroke-width", "0.5px");

	//Append the labels at each axis
	axis.append("text")
		.attr("class", "legend bold")
		.style('fill', "#AAAAAA")
		.style("font-size", "14px")
		.attr("dy", "0.35em")
		.attr("x", function(d, i){ return rScale(maxValue * cfg.labelFactor) * Math.cos(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.attr("y", function(d, i){ return (rScale(maxValue * cfg.labelFactor) * Math.sin(angleSlice*(i+cfg.rotationFactor) - Math.PI/2)) - 7 ; })
		.attr("text-anchor", function(d, i) {
			var x = (rScale(maxValue * cfg.labelFactor) * Math.cos(angleSlice*(i+cfg.rotationFactor) - Math.PI/2));
			if (x > -10 && x <10) {
				return "middle";
			}
			return x > 0 ? 'start' : 'end';
		})
		.text(function(d){return d})
		.call(wrap, cfg.wrapWidth);

	/////////////////////////////////////////////////////////
	///////////// Draw the radar chart blobs ////////////////
	/////////////////////////////////////////////////////////

	if(cfg.roundStrokes) {
		radarLine2.interpolate("linear-closed");
	}

	//Create a wrapper for the blobs
	var blobWrapper = g.selectAll(".radarWrapper")
		.data(data)
		.enter().append("g")
		.attr("class", "radarWrapper")
		.attr("data-index", function(d, i) {
			return data.length - 1 - i;
		});

	//Append the backgrounds
	if(cfg.fillAreas) {
		blobWrapper
			.append("path")
			.attr("class", "radarArea")
			.attr("d", function (d, i) {
				return radarLine2(d);
			})
			.style("fill", function (d, i) {
				return "url(#gradient-" + i + ")";
				// return cfg.color(i);
			})
			.style("fill-opacity", 0.1)
			.on('mouseover', function (d, i) {
				//Dim all blobs
				d3.selectAll(".radarArea")
					.transition().duration(200)
					.style("fill-opacity", 0.1);
				//Bring back the hovered over blob
				d3.select(this)
					.transition().duration(200)
					.style("fill-opacity", 0.7);
			})
			.on('mouseout', function () {
				//Bring back all blobs
				d3.selectAll(".radarArea")
					.transition().duration(200)
					.style("fill-opacity", cfg.opacityArea);
			});
	}

	//Create the outlines
	blobWrapper.append("path")
		.attr("class", "radarStroke")
		.attr("d", function(d,i) { return radarLine2(d); })
		.style("stroke-width", cfg.strokeWidth + "px")
		.style("stroke", function(d, i) { return cfg.color(i); })
		.style("fill", function(d, i) {
			return cfg.color(i);
		})
		.style("fill-opacity", 0.1)
		// .style("filter" , "url(#glow)")
	;

	//Append the circles
	blobWrapper.selectAll(".radarCircle")
		.data(function(d,i) { return d; })
		.enter().append("circle")
		.attr("class", "radarCircle")
		.attr("r", cfg.dotRadius)
		.attr("cx", function(d,i){ return rScale(d.value) * Math.cos(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.attr("cy", function(d,i){ return rScale(d.value) * Math.sin(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.style("fill", function(d,i,j) { return cfg.color(j); })
		.style("fill-opacity", 0.8);

	/////////////////////////////////////////////////////////
	//////// Append invisible circles for tooltip ///////////
	/////////////////////////////////////////////////////////

	//Wrapper for the invisible circles on top
	var blobCircleWrapper = g.selectAll(".radarCircleWrapper")
		.data(data)
		.enter().append("g")
		.attr("class", "radarCircleWrapper");

	//Append a set of invisible circles on top for the mouseover pop-up
	blobCircleWrapper.selectAll(".radarInvisibleCircle")
		.data(function(d, i) { return d; })
		.enter().append("circle")
		.attr("class", "radarInvisibleCircle")
		.attr("r", cfg.dotRadius*1.5)
		.attr("cx", function(d,i){ return rScale(d.value) * Math.cos(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.attr("cy", function(d,i){ return rScale(d.value) * Math.sin(angleSlice*(i+cfg.rotationFactor) - Math.PI/2); })
		.style("fill", "none")
		.style("pointer-events", "all")
		.on("mouseover", function(d,i) {
			var newX =  parseFloat(d3.select(this).attr('cx')) - 10;
			var newY =  parseFloat(d3.select(this).attr('cy')) - 10;

			tooltip
				.attr('x', newX)
				.attr('y', newY)
				.text(Format(d.value))
				.transition().duration(200)
				.style('opacity', 1);
		})
		.on("mouseout", function(){
			tooltip.transition().duration(200)
				.style("opacity", 0);
		});

	var lastNode = d3.selectAll('.radarCircleWrapper')[0][d3.selectAll('.radarCircleWrapper').length - 1];
	// lastNode.parentNode.insertBefore(d3.select('.axisWrapper').node(), lastNode.nextSibling);

	//Set up the small tooltip for when you hover over a circle
	var tooltip = g.append("text")
		.attr("class", "tooltip")
		.style("opacity", 0);

	if (cfg.legends.length) {
		var legend = g.append("g")
			.attr("class", "legend")
			.attr('fill', '#333333');

		var legends = legend.selectAll(".legendItem")
			.data(cfg.legends)
			.enter();

		legends.append("text")
			.attr("class", "legendItem")

			.attr('x', 12)
			.attr('y', function (d, i) {
				return -10 + (i * 15);
			})
			.text(function (d, i) {
				return d
			});

		legends.append('circle')
			.attr('r', 4)
			.attr('cy', function (d, i) {
				return -14 + i * 15;
			})
			.style("fill", function(d, i) {
				return "url(#gradient-" + (cfg.gradients.length - 1 - i) + ")";
			});

		// legends.append('line')
		// 	.attr('x1', -8)
		// 	.attr('x2', 8)
		// 	.attr('y1', function (d, i) {
		// 		return -4 + i * 15;
		// 	})
		// 	.attr('y2', function (d, i) {
		// 		return -4 + i * 15;
		// 	})
		// 	.style("stroke", function(d, i) {
		// 		return cfg.color(i);
		// 	})
		// 	.style("stroke-width", 2);

		var x = - cfg.w / 2 - cfg.margin.left + 15;
		var y = (-(cfg.h/2 + cfg.margin.top )) + 20;

		legend.attr("transform", 'translate(' + x + ', ' + y + ')');
	}

	// Reorder by areas size

	var orderedNodes = blobWrapper[0].sort(function(a,b) {
		var aPoints = Array.prototype.slice.call(a.getElementsByTagName("circle"), 0).map(function(n) {return {x:n.cx.baseVal.value, y:n.cy.baseVal.value}});
		var bPoints = Array.prototype.slice.call(b.getElementsByTagName("circle"), 0).map(function(n) {return {x:n.cx.baseVal.value, y:n.cy.baseVal.value}});
		var areaA = calcPolygonArea(aPoints);
		var areaB = calcPolygonArea(bPoints);
		if (areaA > areaB) return -1;
		if (areaA < areaB) return 1;
		return -1;
	});

	var sames = {};

	orderedNodes.forEach(function(n) {
		var circles = Array.prototype.slice.call(n.getElementsByTagName("circle"), 0);
		var values = circles.map(function(c) { return c.__data__.value});
		var index = parseInt(n.dataset.index);
		var eq = orderedNodes.forEach(function(o) {
			var i = parseInt(o.dataset.index);
			if (i === index) {
				return;
			};


			nodeCircles = Array.prototype.slice.call(o.getElementsByTagName("circle"), 0);
			nodeValues = nodeCircles.map(function(c) { return c.__data__.value});
			var same = true;

			values.forEach(function(value, key) {
				nodeValue = nodeValues[key];
				var diff = Math.abs(value - nodeValue);
				if (diff > 3) {
					same = false;
				}
			});

			if (same && (cfg.scoreGroup === cfg.scoreParticipant)) {
				var labelIndex = index > i ? index + "" + i : i + "" + index;
				sames[labelIndex] = cfg.legends[i] + " est identique à " + cfg.legends[index];
			}

		});
	});

	var samesValues = Object.keys(sames).map(function(key){return sames[key]});

	samesValues.forEach(function(s, i) {
		var h = 20 + (i * 15);
		var style = "margin-top: "+h+"px;";
		$(id).before($('<div style="margin-left: 10px;' + style + '"><i class="fa fa-info-circle"></i> ' + s + '</div>'));
	});

	orderedNodes.forEach(function(n, index) {
		g[0][0].append(n);
	});

	/////////////////////////////////////////////////////////
	/////////////////// Helper Function /////////////////////
	/////////////////////////////////////////////////////////

	//Taken from http://bl.ocks.org/mbostock/7555321
	//Wraps SVG text
	function wrap(text, width) {
		text.each(function() {
			var text = d3.select(this),
				words = text.text().split(/\s+/).reverse(),
				word,
				line = [],
				lineNumber = 0,
				lineHeight = 1.4, // ems
				y = text.attr("y"),
				x = text.attr("x"),
				dy = parseFloat(text.attr("dy")),
				tspan = text.text(null).append("tspan").attr("x", x).attr("y", y).attr("dy", dy + "em");

			while (word = words.pop()) {
				line.push(word);
				tspan.text(line.join(" "));
				if (tspan.node().getComputedTextLength() > width) {
					line.pop();
					tspan.text(line.join(" "));
					line = [word];
					tspan = text.append("tspan").attr("x", x).attr("y", y).attr("dy", ++lineNumber * lineHeight + dy + "em").text(word);
				}
			}

			// var y2 = parseFloat($(this).siblings('line').attr('y2'));
			// var box = this.getBoundingClientRect();
			// if (y2 > 0) {
			// 	$(this).find('tspan').each(function() {
			// 		d3.select(this).attr('y', parseFloat(d3.select(this).attr('y')) - (box.height / 2));
			// 	});
			// }  else if (y2 < 0) {
			// 	$(this).find('tspan').each(function() {
			// 		d3.select(this).attr('y', parseFloat(d3.select(this).attr('y')) + (box.height / 2));
			// 	});
			// }
		});
	}//wrap

	function calcPolygonArea(vertices) {
		var total = 0;

		for (var i = 0, l = vertices.length; i < l; i++) {
			var addX = vertices[i].x;
			var addY = vertices[i == vertices.length - 1 ? 0 : i + 1].y;
			var subX = vertices[i == vertices.length - 1 ? 0 : i + 1].x;
			var subY = vertices[i].y;

			total += (addX * addY * 0.5);
			total -= (subX * subY * 0.5);
		}

		return Math.abs(total);
	}

}//RadarChart