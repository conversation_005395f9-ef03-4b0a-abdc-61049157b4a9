/**
 * Estonian translation for bootstrap-datepicker
 * <PERSON><PERSON> <https://github.com/anroots>
 * Fixes by <PERSON><PERSON><PERSON> <<https://github.com/ragulka>
 */
;(function($){
	$.fn.datepicker.dates['et'] = {
		days: ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"],
		daysShort: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
		daysMin: ["P", "E", "T", "K", "N", "R", "L", "P"],
		months: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aprill", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember"],
		monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sept", "Ok<PERSON>", "<PERSON>", "<PERSON><PERSON>"],
		today: "<PERSON><PERSON>na",
		clear: "<PERSON><PERSON>hjenda",
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(jQuery));
