SUDO=sudo -u www-data
CONSOLE=php bin/console
COMPOSER=php -d "apc.enable_cli=0" ../composer.phar
PHPUNIT=php bin/phpunit
PHPCODEPTION=php vendor/bin/codecept
RECTOR=vendor/bin/rector

install: git-subup composer-install cache-clear assets db-update-force config cache-clear

update: git-update git-subup composer-install cache-clear composer-optimize db-update

### GIT ###
git-subup:
	$(SUDO) git submodule update --init

git-update:
	$(SUDO) git fetch origin
	$(SUDO) git stash
	$(SUDO) git pull
	$(SUDO) git stash pop || :

### COMPOSER ###
composer-install:
	$(SUDO) $(COMPOSER) install

composer-update:
	$(SUDO) $(COMPOSER) update

composer-autoload:
	$(SUDO) $(COMPOSER) dumpautoload

composer-optimize:
	$(SUDO) $(COMPOSER) dumpautoload --optimize


composer-script-update:
	$(SUDO) $(COMPOSER) run-script post-install-cmd

### CONSOLE ###
cache-clear:
	$(SUDO) $(CONSOLE) cache:clear
	$(SUDO) $(CONSOLE) cache:warmup

assets:
	$(SUDO) $(CONSOLE) assets:install --symlink

config:
	$(SUDO) $(CONSOLE) eduprat:init_config

db-update:
	$(SUDO) $(CONSOLE) doctrine:schema:update --dump-sql

db-update-force:
	$(SUDO) $(CONSOLE) doctrine:schema:update --force

### TESTS ###
test: test-unit test-functional

test-unit:
	$(SUDO) $(PHPCODEPTION) run tests/Unit/

test-functional:
	$(SUDO) $(PHPUNIT) tests/functional/

rector:
	$(SUDO) $(RECTOR) process src

rector-dry-run:
	$(SUDO) $(RECTOR) process src --dry-run
