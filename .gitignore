###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###
###> custom ###
.data_elastic
.data_mysql
.idea
/uploads/*
/app/config/dashboard.json
/var/.Halite.key
###< custom ###

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

/public/uploads/elearning/powerpoint/*
/public/uploads/elearning/pictureQuestion/*
/public/uploads/elearning/picture/*
/public/uploads/priseEnCharge/*
/public/uploads/surveyChoicePicture/*
/public/uploads/questionTCSPicture/*
/public/uploads/logoPartenaires/*
###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###
