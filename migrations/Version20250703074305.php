<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250703074305 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE logo_partenaire ADD person_uploader_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE logo_partenaire ADD CONSTRAINT FK_192419A77B8CD3FF FOREIGN KEY (person_uploader_id) REFERENCES person (id)');
        $this->addSql('CREATE INDEX IDX_192419A77B8CD3FF ON logo_partenaire (person_uploader_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE logo_partenaire DROP FOREIGN KEY FK_192419A77B8CD3FF');
        $this->addSql('DROP INDEX IDX_192419A77B8CD3FF ON logo_partenaire');
        $this->addSql('ALTER TABLE logo_partenaire DROP person_uploader_id');
    }
}
