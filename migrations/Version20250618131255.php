<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250618131255 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE person ADD coordinator_binome_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE person ADD CONSTRAINT FK_34DCD17611D57FE3 FOREIGN KEY (coordinator_binome_id) REFERENCES person (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_34DCD17611D57FE3 ON person (coordinator_binome_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE person DROP FOREIGN KEY FK_34DCD17611D57FE3');
        $this->addSql('DROP INDEX UNIQ_34DCD17611D57FE3 ON person');
        $this->addSql('ALTER TABLE person DROP coordinator_binome_id');
    }
}
