<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250626081632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE logo_partenaire (id INT AUTO_INCREMENT NOT NULL, createdAt DATETIME NOT NULL, logoName VARCHAR(255) NOT NULL, filename VARCHAR(255) NOT NULL, extension VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE logo_partenaire_formation (logo_partenaire_id INT NOT NULL, formation_id INT NOT NULL, INDEX IDX_C941D0911D75C305 (logo_partenaire_id), INDEX IDX_C941D0915200282E (formation_id), PRIMARY KEY(logo_partenaire_id, formation_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE logo_partenaire_formation ADD CONSTRAINT FK_C941D0911D75C305 FOREIGN KEY (logo_partenaire_id) REFERENCES logo_partenaire (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE logo_partenaire_formation ADD CONSTRAINT FK_C941D0915200282E FOREIGN KEY (formation_id) REFERENCES formation (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE logo_partenaire_formation DROP FOREIGN KEY FK_C941D0911D75C305');
        $this->addSql('ALTER TABLE logo_partenaire_formation DROP FOREIGN KEY FK_C941D0915200282E');
        $this->addSql('DROP TABLE logo_partenaire_formation');
        $this->addSql('DROP TABLE logo_partenaire');
    }
}
