<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250626090011 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE downloaded_plaquette_file ADD logoPartenaire INT DEFAULT NULL');
        $this->addSql('ALTER TABLE downloaded_plaquette_file ADD CONSTRAINT FK_B70256705FDE4704 FOREIGN KEY (logoPartenaire) REFERENCES logo_partenaire (id)');
        $this->addSql('CREATE INDEX IDX_B70256705FDE4704 ON downloaded_plaquette_file (logoPartenaire)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE downloaded_plaquette_file DROP FOREIGN KEY FK_B70256705FDE4704');
        $this->addSql('DROP INDEX IDX_B70256705FDE4704 ON downloaded_plaquette_file');
        $this->addSql('ALTER TABLE downloaded_plaquette_file DROP logoPartenaire');
    }
}
