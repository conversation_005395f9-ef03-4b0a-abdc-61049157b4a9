<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250703094003 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE logo_partenaire_usage (id INT AUTO_INCREMENT NOT NULL, coordinator INT NOT NULL, logo_partenaire INT NOT NULL, formation INT NOT NULL, titre VARCHAR(255) NOT NULL, createdAt DATETIME NOT NULL, INDEX IDX_F9DC6DFE7877946 (coordinator), INDEX IDX_F9DC6DF1D75C305 (logo_partenaire), INDEX IDX_F9DC6DF5200282E (formation), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE logo_partenaire_usage ADD CONSTRAINT FK_F9DC6DFE7877946 FOREIGN KEY (coordinator) REFERENCES person (id)');
        $this->addSql('ALTER TABLE logo_partenaire_usage ADD CONSTRAINT FK_F9DC6DF1D75C305 FOREIGN KEY (logo_partenaire) REFERENCES logo_partenaire (id)');
        $this->addSql('ALTER TABLE logo_partenaire_usage ADD CONSTRAINT FK_F9DC6DF5200282E FOREIGN KEY (formation) REFERENCES formation (id)');
        $this->addSql('ALTER TABLE logo_partenaire_formation DROP FOREIGN KEY FK_C941D0911D75C305');
        $this->addSql('ALTER TABLE logo_partenaire_formation DROP FOREIGN KEY FK_C941D0915200282E');
        $this->addSql('DROP TABLE logo_partenaire_formation');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE logo_partenaire_formation (logo_partenaire INT NOT NULL, formation INT NOT NULL, INDEX IDX_C941D0911D75C305 (logo_partenaire), INDEX IDX_C941D0915200282E (formation), PRIMARY KEY(logo_partenaire, formation)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE logo_partenaire_formation ADD CONSTRAINT FK_C941D0911D75C305 FOREIGN KEY (logo_partenaire) REFERENCES logo_partenaire (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE logo_partenaire_formation ADD CONSTRAINT FK_C941D0915200282E FOREIGN KEY (formation) REFERENCES formation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE logo_partenaire_usage DROP FOREIGN KEY FK_F9DC6DFE7877946');
        $this->addSql('ALTER TABLE logo_partenaire_usage DROP FOREIGN KEY FK_F9DC6DF1D75C305');
        $this->addSql('ALTER TABLE logo_partenaire_usage DROP FOREIGN KEY FK_F9DC6DF5200282E');
        $this->addSql('DROP TABLE logo_partenaire_usage');
    }
}
