<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250722074337 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'création des coordinator_person à partir des person role ROLE_COORDINATOR';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE coordinator_person (id INT NOT NULL, PRIMARY KEY (id)) DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci ENGINE = InnoDB;');
        $this->addSql('CREATE TABLE objectifs_salarie (id INT AUTO_INCREMENT NOT NULL, person_id INT DEFAULT NULL, year INT NOT NULL, objectif_ca DOUBLE PRECISION NOT NULL, taux_cumul_marge DOUBLE PRECISION NOT NULL, INDEX IDX_D6F9CFBC217BBB47 (person_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8 COLLATE `utf8_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE coordinator_person ADD CONSTRAINT FK_48E00DBFBF396750 FOREIGN KEY (id) REFERENCES person (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE objectifs_salarie ADD CONSTRAINT FK_D6F9CFBC217BBB47 FOREIGN KEY (person_id) REFERENCES coordinator_person (id)');
        $this->addSql('ALTER TABLE person ADD typePerson VARCHAR(255)');
        $this->connection->commit();
        $this->addSql("UPDATE `person` SET `typePerson` = 'person'");
        $this->addSql("UPDATE `person` SET `typePerson` = 'coordinator' WHERE `roles` LIKE '%ROLE_COORDINATOR%'");
        $this->addSql('ALTER TABLE person MODIFY typePerson VARCHAR(255) NOT NULL;');
        $this->addSql('INSERT INTO coordinator_person (id) 
                            SELECT id FROM person 
                            WHERE roles LIKE \'%ROLE_COORDINATOR%\' AND id NOT IN (SELECT id FROM coordinator_person);');

//        $this->addSql('INSERT INTO coordinator_person (id, crStatus, crStatusDate, crIdentifier)
//                            SELECT id, crStatus, crStatusDate, crIdentifier FROM person
//                            WHERE roles LIKE \'%ROLE_COORDINATOR%\' AND id NOT IN (SELECT id FROM coordinator_person);');

        $this->addSql('CREATE UNIQUE INDEX UNIQ_D6F9CFBC217BBB47BB827337 ON objectifs_salarie (person_id, year)');

//        // Récupération des données de person
//        $this->addSql('ALTER TABLE person DROP crStatus, DROP crStatusDate, DROP crIdentifier');
    }

    public function down(Schema $schema): void
    {
        // Supprimer l'index unique
        $this->addSql('DROP INDEX UNIQ_D6F9CFBC217BBB47BB827337 ON objectifs_salarie');

        // Supprimer les contraintes de clé étrangère
        $this->addSql('ALTER TABLE objectifs_salarie DROP FOREIGN KEY FK_D6F9CFBC217BBB47');
        $this->addSql('ALTER TABLE coordinator_person DROP FOREIGN KEY FK_48E00DBFBF396750');

        // Supprimer la table objectifs_salarie
        $this->addSql('DROP TABLE objectifs_salarie');

//        // ➕ Recréer les colonnes supprimées dans person
//        $this->addSql('ALTER TABLE person ADD crStatus VARCHAR(255) DEFAULT NULL');
//        $this->addSql('ALTER TABLE person ADD crStatusDate DATE DEFAULT NULL');
//        $this->addSql('ALTER TABLE person ADD crIdentifier VARCHAR(255) DEFAULT NULL');
//
//        // 🔁 Restaurer les données depuis coordinator_person vers person
//        $this->addSql('UPDATE person p
//        INNER JOIN coordinator_person cp ON p.id = cp.id
//        SET
//            p.crStatus = cp.crStatus,
//            p.crStatusDate = cp.crStatusDate,
//            p.crIdentifier = cp.crIdentifier
//        ');

        // Supprimer la table coordinator_person
        $this->addSql('DROP TABLE coordinator_person');

        // Supprimer la colonne typePerson
        $this->addSql('ALTER TABLE person DROP typePerson');
    }
}
