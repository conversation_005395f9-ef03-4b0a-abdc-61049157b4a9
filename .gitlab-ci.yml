# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

#variables:
#    GIT_SUBMODULE_STRATEGY: recursive
#    GIT_SUBMODULE_FORCE_HTTPS: "true"


image: gitlab.alienor.net:5050/dev-projets-alienor/eduprat:dev83-sf64

default:
    tags:
        - anetdev

stages:          # List of stages for jobs, and their order of execution
    - install
    - test
    - testE2E

cache:
    paths:
        - vendor/
    key:
        files:
            - composer.lock

install-dependances:
    stage: install
    script:
        - sudo -u www-data composer show
        - sudo -u www-data composer install
    artifacts:
        paths:
            - "vendor"
        expire_in: 10 minutes

test-sf-version:   # This job runs in the test stage.
    stage: test    # It only starts when the job in the build stage completes successfully.
    script:
        - echo "vérification version sf..."
        - sudo -u www-data php bin/console
        - echo "Fin vérification version sf"
    dependencies:
        - install-dependances

unit-test-job:   # This job runs in the test stage.
    stage: test    # It only starts when the job in the build stage completes successfully.
    variables:
        SYMFONY_DEPRECATIONS_HELPER: disabled
        XDEBUG_MODE: coverage
    script:
      - sudo -u www-data php bin/console
      - ls /builds/dev-projets-alienor/eduprat/vendor/symfony/cache/Adapter/
      - echo "Lancement des tests unitaires..."
      - sudo -u www-data php vendor/bin/codecept run tests/Unit
      - echo "Fin des tests unitaires"
    dependencies:
      - install-dependances

fonctional-test-job:   # This job runs in the test stage.
    stage: test    # It only starts when the job in the build stage completes successfully.
    variables:
        SYMFONY_DEPRECATIONS_HELPER: disabled
        DATABASE_URL: 'mysql://userdb:passdb@mariadb:3306/anetdb-test?serverVersion=mariadb-10.11.3&charset=utf8'
        XDEBUG_MODE: coverage
    services:
        -
         name: 'mariadb:10.11.3'
         alias: mariadb

         command:
            - '--default-authentication-plugin=mysql_native_password'
            - '--lower_case_table_names=1'
         variables:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_DATABASE: anetdb-test
            MYSQL_USER: userdb
            MYSQL_PASSWORD: passdb
    script:
        - echo "(Re)Création de la base de données..."
        - sudo -u www-data php bin/console
        - ls /builds/dev-projets-alienor/eduprat/vendor/symfony/cache/Adapter/
        - sudo -u www-data php bin/console doctrine:database:drop --force --if-exists --env=test
        - sudo -u www-data php bin/console doctrine:database:create --if-not-exists --env=test
        - sudo -u www-data php bin/console doctrine:schema:update --no-interaction --force --env=test
        - echo "Lancement des tests fonctionnels..."
        - sudo -u www-data php vendor/bin/phpunit tests/functional
        - echo "Fin des tests fonctionnels"
    dependencies:
      - install-dependances

#dataForPlaywright:
#    stage: testE2E
#    script:
#        - sudo -u www-data php bin/console doctrine:fixtures:load --env=test
#
#playwright:
#    stage: testE2E
#    image: mcr.microsoft.com/playwright:v1.40.0-jammy
#    script:
#        - npm ci
#        - npx playwright install --with-deps
#        - npx playwright test
