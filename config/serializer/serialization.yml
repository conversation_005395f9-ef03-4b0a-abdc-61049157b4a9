Eduprat\DomainBundle\Entity\Formation:
    attributes:
        id:
            groups: ['elastic', 'api', 'apiV2']
        sessionNumber:
            groups: ['api', 'elastic', 'elastic_evaluation', 'apiV2']
        createdAt:
            groups: ['elastic']
        updatedAt:
            groups: ['apiV2']
            serialized_name: "majSession"
        openingDate:
            groups: ['elastic', 'api', 'elastic_evaluation', 'apiV2']
        closingDate:
            groups: ['elastic', 'api', 'elastic_evaluation', 'apiV2']
        formOpeningDateApi:
            serialized_name: "formOpeningDate"
            groups: ['api']
        formClosingDateApi:
            serialized_name: "formClosingDate"
            groups: ['api']
        startDate:
            groups: ['elastic', 'api', 'elastic_evaluation']
        endDate:
            groups: ['elastic', 'api', 'apiV2']
        closed:
            groups: ['elastic']
        closedAndpc:
            groups: ['elastic']
        # ABSTRACT
#        category:
#            groups: ['elastic']
        caTotal:
            groups: ['elastic']
        participantCount:
            groups: ['elastic']
        group:
            groups: ['elastic']
        discr:
            groups: ['elastic']
#        isMedecin:
#            groups: ['elastic']
        coordinatorName:
            groups: ['elastic']
        coordinatorLbi:
            groups: ['elastic']
        supervisorName:
            groups: ['elastic']
#        lowercaseTitle:
#            groups: ['elastic']
        status:
            groups: ['elastic']
        statusApi:
            serialized_name: "status"
            groups: ['api']
        accessibilityStatus:
            groups: ['api']
        statusLabel:
            groups: ['elastic']
#        elasticParticipations:
#            groups: ['elastic']
#            serialized_name: "participants"
        coordinators:
            groups: ['elastic']
        subject:
            groups: ['elastic', 'elastic_evaluation']
        presentiel:
            groups: ['api']
        expert:
            groups: ['api_mobile', 'elastic']
        apiType:
            serialized_name: "type"
            groups: ['api']
        subType:
            groups: ['api']
        formType:
            groups: ['elastic']
        apiVariety:
            serialized_name: "variety"
            groups: ['api']
        variety:
            groups: ['elastic', 'elastic_evaluation']
        dpc:
            groups: ['api']
        formateursPersons:
            serialized_name: "formers"
            groups: ['api', 'apiV2']
        coordinatorsPerson:
            serialized_name: "coordinators"
            groups: ['api_mobile', 'apiV2']
        address:
            groups: ['api', 'apiV2']
        address2:
            groups: ['api', 'apiV2']
        city:
            groups: ['api', 'apiV2']
        zipCode:
            groups: ['api', 'apiV2']
        departement:
            groups: ['apiV2']
        region:
            groups: ['api', 'apiV2']
        localization:
            groups: ['api', 'elastic']
        startDateMonth:
            groups: ['elastic']
        startDateYear:
            groups: ['elastic']
        closingDateMonth:
            groups: ['elastic']
        closingDateYear:
            groups: ['elastic']
        isPluriannuelle:
            groups: ['elastic']
        searchStartDate:
            groups: ['elastic']
        searchEndDate:
            groups: ['elastic']
        zoomLink:
            groups: ['api']
        zoomId:
            groups: ['api']
        accessibilite:
            groups: ['api']
        outil:
            groups: ['api']
    subAttributes:
        programme:
            prefix: ""
            attributes:
                id:
                    groups: ['elastic', 'elastic_evaluation']
                    serialized_name: "programmeId"
                title:
                    groups: ['elastic', 'api', 'elastic_evaluation']
                reference:
                    groups: ['elastic', 'api', 'elastic_evaluation']
                cout:
                    groups: ['api']
                categories:
                    groups: ['elastic', 'api', 'elastic_evaluation']
                specialities:
                    groups: ['elastic', 'api', 'elastic_evaluation']
                resume:
                    groups: ['api']
                prerequis:
                    groups: ['api']
                objectives:
                    groups: ['api']
                certifying:
                    groups: ['api_elearning']
                picture:
                    groups: ['api']
                durationPresentielle:
                    groups: ['api']
                durationNotPresentielleTotal:
                    serialized_name: "durationNotPresentielle"
                    groups: ['api']
                fullTitle:
                    groups: ['elastic', 'elastic_evaluation']
                exercisesMode:
                    groups: ['elastic_evaluation']
                presence:
                    groups: ['api', 'elastic', 'elastic_evaluation']
            subAttributes:
                tags:
                    prefix: ""
                    attributes:
                        name:
                            groups: ['api']
                            serialized_name: "tags"
                            type: "map"
                connaissances:
                    prefix: ""
                    attributes:
                        name:
                            groups: ['api']
                            serialized_name: "connaissances"
                            type: "map"
                competences:
                    prefix: ""
                    attributes:
                        name:
                            groups: ['api']
                            serialized_name: "competences"
                            type: "map"
        financeModes:
            prefix: ""
            attributes:
                name:
                    groups: ['elastic', 'apiV2']
                    serialized_name: "financeModes"
                    type: "map"
        financeSousModes:
            prefix: ""
            attributes:
                identifiant:
                    groups: ['elastic', 'apiV2']
                    serialized_name: "financeSousModes"
                    type: "map"

Eduprat\DomainBundle\Entity\FormationAudit:
    attributes:
        audit:
            groups: ['api']
        subject:
            serialized_name: "subject"
            groups: ['elastic']
        region:
            groups: ['api']
        discr:
            groups: ['elastic']

Eduprat\DomainBundle\Entity\AuditCategory:
    attributes:
        id:
            groups: ['api', 'apiV2']
        name:
            groups: ['api', 'apiV2']

Eduprat\DomainBundle\Entity\FormationPresentielle:
    attributes:
        questionnaire:
            serialized_name: "survey"
            groups: ['api']
        subject:
            serialized_name: "subject"
            groups: ['elastic']
        region:
            groups: [ 'api' ]
        discr:
            groups: [ 'elastic' ]

Eduprat\DomainBundle\Entity\Audit:
    attributes:
        id:
            groups: ['api']
        label:
            groups: ['api']
    subAttributes:
        category:
            prefix: ""
            attributes:
                name:
                    serialized_name: "category"
                    groups: ['api']

Eduprat\DomainBundle\Entity\Survey:
    attributes:
        label:
            groups: ['api']
        minScore:
            groups: ['api']
        estimatedTime:
            groups: ['api']
        nbQuestions:
            groups: ['api_elearning']
    subAttributes:
        category:
            prefix: ""
            attributes:
                name:
                    serialized_name: "category"
                    groups: ['api']

Eduprat\DomainBundle\Entity\AuditQuestion:
    attributes:
        id:
            groups: ['api']
        label:
            groups: ['api']
        required:
            groups: ['api']
        position:
            groups: ['api']
        images:
            groups: ['api']
        categoryQuestion:
            serialized_name: 'category'
            groups: ['api']

Eduprat\DomainBundle\Entity\SurveyQuestion:
    attributes:
        id:
            groups: ['api']
        label:
            groups: ['api']
        type:
            groups: ['api']
        required:
            groups: ['api']
        position:
            groups: ['api']
        images:
            groups: ['api']
        categoryQuestion:
            serialized_name: 'category'
            groups: ['api']

Eduprat\DomainBundle\Entity\EvaluationGlobalQuestion:
    attributes:
        index:
            serialized_name: id
            groups: ['api']
        group:
            groups: ['api']
        label:
            groups: ['api']
        type:
            groups: ['api']
        minLabel:
            groups: ['api']
        maxLabel:
            groups: ['api']
        required:
            groups: ['api']
    subAttributes:
        former:
            prefix: ""
            attributes:
                id:
                    serialized_name: "formerId"
                    groups: ['api']
            subAttributes:
                person:
                    prefix: ""
                    attributes:
                        firstname:
                            serialized_name: "formerFirstname"
                            groups: ['api']
                        lastname:
                            serialized_name: "formerLastname"
                            groups: ['api']

Eduprat\DomainBundle\Entity\Former:
    attributes:
        id:
            groups: ['api']
    subAttributes:
        person:
            prefix: ""
            attributes:
                firstname:
                    groups: ['api']
                lastname:
                    groups: ['api']

Eduprat\DomainBundle\Entity\Image:
    attributes:
        id:
            groups: ['api']

Eduprat\DomainBundle\Entity\SurveyQuestionCategory:
    attributes:
        family:
            groups: ['api']
        name:
            groups: ['api']

Eduprat\DomainBundle\Entity\AuditQuestionCategory:
    attributes:
        name:
            groups: ['api']

Eduprat\DomainBundle\Entity\Programme:
    attributes:
        updatedAt:
            groups: ['apiV2']
            serialized_name: "majFormation"
        title:
            groups: ['apiV2']
        reference:
            groups: ['apiV2']
        cout:
            groups: ['apiV2']
        category:
            groups: ['apiV2']
            serialized_name: "thematique"
        sessionType:
            groups: ['apiV2']
        formType:
            groups: ['apiV2']
        format:
            groups: ['apiV2']
            serialized_name: "sessionFormat"
        presence:
            groups: ['apiV2']
        resume:
            groups: ['apiV2']
        prerequis:
            groups: ['apiV2']
        additionalInfos:
            groups: ['apiV2']
        durationNotPresentielle:
            groups: ['apiV2']
        durationPresentielle:
            groups: ['apiV2']
        durationTotal:
            groups: ['apiV2']
        specialities:
            groups: ['apiV2']
        formations:
            serialized_name: "sessions"
            groups: ['apiV2']
        categories:
            serialized_name: "professions"
            groups: ['apiV2']
        epp:
            groups: ['apiV2']
            serialized_name: "EPP"
    subAttributes:
        tags:
            prefix: ""
            attributes:
                name:
                    groups: ['apiV2']
                    serialized_name: "tags"
                    type: "map"
        connaissances:
            prefix: ""
            attributes:
                name:
                    groups: ['apiV2']
                    serialized_name: "connaissances"
                    type: "map"
        competences:
            prefix: ""
            attributes:
                name:
                    groups: ['apiV2']
                    serialized_name: "competences"
                    type: "map"
#        formAssocie:
#            groups: ['apiV2']


Eduprat\DomainBundle\Entity\PriseEnCharge:
    attributes:
        name:
            groups: [ 'apiV2' ]
        priseEnChargePicture:
            groups: [ 'apiV2' ]
            serialized_name: "logo"

Eduprat\DomainBundle\Entity\Connaissance:
    attributes:
        name:
            groups: [ 'apiV2' ]
Eduprat\DomainBundle\Entity\Competence:
    attributes:
        name:
            groups: [ 'apiV2' ]
Eduprat\DomainBundle\Entity\Tag:
    attributes:
        name:
            groups: [ 'apiV2' ]
#        price:
#            groups: [ 'apiV2' ]


#        note:
#            groups: ['apiV2']
#            serialized_name: "note"
#        votants:
#            groups: ['apiV2']
#        epp:
#            groups: ['apiV2']

        # lowercaseTitle:
        #     groups: ['elastic']
        # coordinatorHonorary:
        #     groups: ['elastic']
        # startDate:
        #     groups: ['elastic']
        # endDate:
        #     groups: ['elastic']
        # restaurationHonorary:
        #     groups: ['elastic']
        # formersHonorary:
        #     groups: ['elastic']
        # coordinators:
        #     groups: ['elastic']
        # # ABSTRACT
        # caTotal:
        #     groups: ['elastic']
        # coordinatorName:
        #     groups: ['elastic']
        # coordinatorLbi:
        #     groups: ['elastic']
        # supervisorName:
        #     groups: ['elastic']
        # cost:
        #     groups: ['elastic']
        # participantCount:
        #     groups: ['elastic']
        # closed:
        #     groups: ['elastic']
        # closedAndpc:
        #     groups: ['elastic']
        # group:
        #     groups: ['elastic']
        # typeLabel:
        #     groups: ['elastic']
        # discr:
        #     groups: ['elastic']
        # formationCost:
        #     groups: ['elastic']
        # avancesCost:
        #     groups: ['elastic']
        # physicalFormationCost:
        #     groups: ['elastic']
        # localization:
        #     groups: ['elastic']

Eduprat\DomainBundle\Entity\Participant:
    attributes:
        id:
            groups: ['elastic', 'api']
        category:
            groups: ['elastic', 'api']
        speciality:
            groups: ['elastic', 'api']
        age:
            groups: ['elastic']
        lastname:
            groups: ['elastic', 'api']
        firstname:
            groups: ['elastic', 'api']
        address:
            groups: ['elastic', 'api']
        zipCode:
            groups: ['elastic', 'api']
        city:
            groups: ['elastic', 'api']
        phone:
            groups: ['elastic', 'api']
        civility:
            groups: ['elastic', 'api']
        email:
            groups: ['elastic', 'api']
        rpps:
            groups: ['elastic', 'api']
        adeli:
            groups: ['elastic', 'api']
        # birthdate:
        #     groups: ['api']
        birthName:
            groups: ['api']
        notifInscription:
            groups: ['api']
        notifSurveyOpen:
            groups: ['api']
        notifRemindSurvey:
            groups: ['api']
        notifRemindSession:
            groups: ['api']
        notifNewSession:
            groups: ['api']
        notifSessionChange:
            groups: ['api']
    subAttributes:
        user:
            attributes:
                id:
                    serialized_name: "id"
                    groups: ['api']


Eduprat\DomainBundle\Entity\Participation:
    attributes:
        id:
            groups: ['api', 'elastic']
        formation:
            groups: ['api']
        exerciseMode:
            groups: ['elastic']
        totalTime:
            groups: ['api']
        price:
            groups: ['elastic']
        priceYearN1:
            groups: ['elastic']
            serialized_name: "priceN1"
        nbHour:
            groups: ['elastic']
    subAttributes:
        formation:
            prefix: ""
            attributes:
                durationTotalN:
                    groups: ['elastic']
                durationPresentielleN:
                    groups: ['elastic']
                durationNotPresentielleN:
                    groups: ['elastic']
                durationTotalN1:
                    groups: [ 'elastic' ]
                durationPresentielleN1:
                    groups: [ 'elastic' ]
                durationNotPresentielleN1:
                    groups: [ 'elastic' ]

        participant:
            prefix: ""
            attributes:
                id:
                    groups: ['elastic']
                    serialized_name: "id_participant"
                category:
                    groups: ['elastic']
                speciality:
                    groups: ['elastic']
                uga:
                    groups: ['elastic']
#                age:
#                    groups: ['elastic']
#                lastname:
#                    groups: ['elastic']
#                firstname:
#                    groups: ['elastic']
#                address:
#                    groups: ['elastic']
#                zipCode:
#                    groups: ['elastic']
#                city:
#                    groups: ['elastic']
#                phone:
#                    groups: ['elastic']
#                civility:
#                    groups: ['elastic']
#                email:
#                    groups: ['elastic']
#                rpps:
#                    groups: ['elastic']
#                adeli:
#                    groups: ['elastic']
        associatedCoordinator:
            prefix: ""
            subAttributes:
                person:
                    prefix: ""
                    attributes:
                        invertedFullname:
                            serialized_name: "coordinatorName"
#                        coordinatorLbi: ~
                    subAttributes:
                        supervisor:
                            attributes:
                                invertedFullname:
                                    serialized_name: "supervisorName"
        financeSousMode:
            prefix: ""
            attributes:
                identifiant:
                    groups: [ 'elastic' ]
                    serialized_name: "financeSousMode"
            subAttributes:
                financeMode:
                    prefix: ""
                    attributes:
                        name:
                            groups: [ 'elastic' ]
                            serialized_name: "financeMode"

Eduprat\DomainBundle\Entity\Coordinator:
    attributes:
        id:
            groups: ['elastic', 'apiV2']
#        honorary:
#            groups: ['elastic']
#        restaurationHonorary:
#            groups: ['elastic']
#        formerHonorary:
#            groups: ['elastic']
#        cost:
#            groups: ['elastic']
        caTotal:
            groups: ['elastic']
        participations:
            groups: ['elastic']
            serialized_name: "participants"
#        participantCount:
#            groups: ['elastic']
#        costTimbres:
#            groups: ['elastic']
#        costRepas:
#            groups: ['elastic']
#        costSalle:
#            groups: ['elastic']
#        costDivers:
#            groups: ['elastic']
#        marges:
#            groups: ['elastic']
#        formationCost:
#            groups: ['elastic']
#        avancesCost:
#            groups: ['elastic']
#        physicalFormationCost:
#            groups: ['elastic']
#        commissionTheorique:
#            groups: ['elastic']
#        surchargedCostFormers:
#            groups: ['elastic']
#        surchargedCostBadges:
#            groups: ['elastic']
#        surchargedCostKilometres:
#            groups: ['elastic']
#        surchargedCostMateriel:
#            groups: ['elastic']
#        surchargedCostRetrocessions:
#            groups: ['elastic']
#        surchargedCostDiversFormation:
#            groups: ['elastic']
    subAttributes:
        formation:
            prefix: ""
            attributes:
                startDate:
                    groups: ['elastic_coordinator']
                openingDate:
                    groups: ['elastic_coordinator']
                closingDate:
                    groups: ['elastic_coordinator']
                apiVariety:
                    serialized_name: "variety"
                    groups: ['elastic_coordinator']
        person:
            attributes:
                status:
                    groups: ['apiV2']
                "firstname":
                    groups: ['apiV2']
                "lastname":
                    groups: ['apiV2']
                "civility":
                    groups: ['apiV2']
                "email":
                    groups: ['apiV2']
                "job":
                    groups: ['apiV2']
                "address":
                    groups: ['apiV2']
                "address2":
                    groups: ['apiV2']
                "city":
                    groups: ['apiV2']
                "zipCode":
                    groups: ['apiV2']
                "phone":
                    groups: ['apiV2']
                "companyName":
                    groups: ['apiV2']
                "siret":
                    groups: ['apiV2']
                "departments":
                    groups: ['apiV2']
                "avatar":
                    groups: ['apiV2']
                invertedFullname:
                    serialized_name: "coordinatorName"
                coordinatorLbi: ~
            subAttributes:
                supervisor:
                    attributes:
                        invertedFullname:
                            serialized_name: "supervisorName"

Eduprat\DomainBundle\Entity\Formateur:
    attributes:
        id:
            groups: ['elastic']
        honorary:
            groups: ['elastic']
    subAttributes:
        formation:
            attributes:
                id:
                    groups: ['elastic']
                    serialized_name: "formationId"
                sessionNumber:
                    groups: ['elastic']
                startDate:
                    groups: ['elastic']
                closingDate:
                    groups: ['elastic']
                openingDate:
                    groups: ['elastic']
                apiVariety:
                    groups: ['elastic']
                    serialized_name: "variety"
            subAttributes:
                programme:
                    attributes:
                        id:
                            groups: [ 'elastic' ]
                            serialized_name: "programmeId"
                        reference:
                            groups: ['elastic']
                        title:
                            groups: ['elastic']
                        durationPresentielle:
                            groups: ['elastic']

        person:
            attributes:
                id:
                    serialized_name: "userId"
                    groups: ['elastic']
                invertedFullname:
                    serialized_name: "fullname"
                    groups: ['elastic']
                status:
                    groups: ['elastic']

Eduprat\AdminBundle\Entity\Person:
    attributes:
        id:
            groups: ['api', 'apiV2', 'elastic', 'apiV2--coordinators']
        firstname:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        lastname:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        invertedFullname:
            serialized_name: "fullname"
            groups: ['elastic']
        civility:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        email:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        job:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        address:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        address2:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        city:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        zipCode:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        phone:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        companyName:
            groups: ['api', 'apiV2--coordinators']
        siret:
            groups: ['api', 'apiV2--coordinators']
        departments:
            groups: ['api', 'apiV2', 'apiV2--coordinators']
        supervisor:
            groups: ['api_elearning']
        roleIdentifier:
            groups: ['api_elearning']
            serialized_name: 'role'
#        coordinators:
#            groups: ['elastic']
#            serialized_name: 'formations'
#        commissionByYear:
#            groups: ['elastic']
    subAttributes:
        supervisor:
            attributes:
                invertedFullname:
                    serialized_name: "supervisorName"
                    groups: ['elastic']
