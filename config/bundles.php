<?php

return [
    Symfony\Bundle\FrameworkBundle\FrameworkBundle::class => ['all' => true],
    Symfony\Bundle\SecurityBundle\SecurityBundle::class => ['all' => true],
    Symfony\Bundle\MonologBundle\MonologBundle::class => ['all' => true],
    Symfony\Bundle\TwigBundle\TwigBundle::class => ['all' => true],
    Twig\Extra\TwigExtraBundle\TwigExtraBundle::class => ['all' => true],
    Doctrine\Bundle\DoctrineBundle\DoctrineBundle::class => ['all' => true],
    Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle::class => ['all' => true],
    Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle::class => ['dev' => true, 'test' => true],
    Symfony\Bundle\MakerBundle\MakerBundle::class => ['dev' => true],
    Symfony\Bundle\WebProfilerBundle\WebProfilerBundle::class => ['dev' => true, 'test' => true],
    Knp\Bundle\SnappyBundle\KnpSnappyBundle::class => ['all' => true],
    Nelmio\CorsBundle\NelmioCorsBundle::class => ['all' => true],
    Stfalcon\Bundle\TinymceBundle\StfalconTinymceBundle::class => ['all' => true],
    Vich\UploaderBundle\VichUploaderBundle::class => ['all' => true],
    FOS\ElasticaBundle\FOSElasticaBundle::class => ['all' => true],
    Alienor\ElasticBundle\AlienorElasticBundle::class => ['all' => true],
    Alienor\EmailBundle\AlienorEmailBundle::class => ['all' => true],
    Alienor\FormBundle\AlienorFormBundle::class => ['all' => true],
    Alienor\ApiBundle\AlienorApiBundle::class => ['all' => true],
    Eduprat\DomainBundle\EdupratDomainBundle::class => ['all' => true],
    Eduprat\AdminBundle\EdupratAdminBundle::class => ['all' => true],
    Eduprat\CrmBundle\EdupratCrmBundle::class => ['all' => true],
    Eduprat\AuditBundle\EdupratAuditBundle::class => ['all' => true],
    Eduprat\ApiBundle\EdupratApiBundle::class => ['all' => true],
    Eduprat\PdfBundle\EdupratPdfBundle::class => ['all' => true],
    Symfony\WebpackEncoreBundle\WebpackEncoreBundle::class => ['all' => true],
    Symfony\UX\Chartjs\ChartjsBundle::class => ['all' => true],
    Nelmio\SecurityBundle\NelmioSecurityBundle::class => ['all' => true],
    Zenstruck\Foundry\ZenstruckFoundryBundle::class => ['dev' => true, 'test' => true],
    DAMA\DoctrineTestBundle\DAMADoctrineTestBundle::class => ['test' => true],
    Alienor\SymfonyProfilerExtraGit\SymfonyProfilerExtraGit::class => ['dev' => true],
    Scheb\TwoFactorBundle\SchebTwoFactorBundle::class => ['all' => true],
    Symfony\UX\StimulusBundle\StimulusBundle::class => ['all' => true],
    Alienor\UserBundle\AlienorUserBundle::class => ['all' => true],
    MeteoConcept\HCaptchaBundle\MeteoConceptHCaptchaBundle::class => ['all' => true],
];
