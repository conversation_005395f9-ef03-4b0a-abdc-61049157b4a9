{"identifier": "evaluation", "index": "evaluation", "type": "evaluation", "mapping": {"coordinatorName": {"type": "keyword"}, "supervisorName": {"type": "keyword"}, "title": {"type": "keyword"}, "fullTitle": {"type": "keyword"}, "presence": {"type": "keyword"}, "reference": {"type": "keyword"}, "sessionNumber": {"type": "keyword"}, "subject": {"type": "keyword"}, "userId": {"type": "keyword"}, "categories": {"type": "keyword"}, "specialities": {"type": "keyword"}, "category": {"type": "keyword"}, "speciality": {"type": "keyword"}, "exercisesMode": {"type": "keyword"}, "origin": {"type": "keyword"}, "originLabel": {"type": "keyword"}, "programmeId": {"type": "keyword"}, "formationId": {"type": "keyword"}, "programmeAnsweredId": {"type": "keyword"}, "formationAnsweredId": {"type": "keyword"}, "answers_questionId": {"type": "keyword"}, "answers_targets": {"type": "keyword"}, "answers_question": {"type": "keyword"}, "answers_group": {"type": "keyword"}, "formerReferentIds": {"type": "keyword"}, "startDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "openingDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "closingDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "variety": {"type": "keyword"}, "answered": {"type": "integer"}, "completed": {"type": "integer"}, "answers": {"type": "nested", "include_in_parent": true, "properties": {"id": {"type": "keyword"}, "question": {"type": "keyword"}, "questionId": {"type": "keyword"}, "group": {"type": "keyword"}, "origin": {"type": "keyword"}, "originLabel": {"type": "keyword"}, "targets": {"type": "keyword"}, "answer": {"type": "integer"}, "programmeAnsweredId": {"type": "keyword"}, "formationAnsweredId": {"type": "keyword"}, "formerCivility": {"type": "keyword"}, "formerName": {"type": "keyword"}, "formerJob": {"type": "keyword"}, "formerAddress": {"type": "keyword"}, "formerReferent": {"type": "integer"}, "title": {"type": "keyword"}, "coordinatorName": {"type": "keyword"}, "coordinatorLbi": {"type": "boolean"}, "supervisorName": {"type": "keyword"}}}}}