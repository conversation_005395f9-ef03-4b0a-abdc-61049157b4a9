monolog:
    channels: ['api']
    handlers:
        main:
            type: fingers_crossed
            action_level: error
            handler: nested
            excluded_http_codes: [404, 405]
            buffer_size: 50 # How many messages should be saved? Prevent memory leaks
        nested:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: error
            max_files: 180
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine"]
        mail:
            type: fingers_crossed
            action_level: critical
            handler: mail_buffered
        mail_buffered:
            type: buffer
            handler: mail_deduplicated
        mail_deduplicated:
            type: deduplication
            handler: mail_send
        mail_send:
            type: symfony_mailer
            from_email: <EMAIL>
            to_email:
                - <EMAIL>
                - <EMAIL>
                - <EMAIL>
                - <EMAIL>
            subject: '[Eduprat] Erreur critique : %%extra.http_method%% %%extra.url%%'
            level: critical
            formatter: monolog.formatter.html
            content_type: text/html
        api:
          type: rotating_file
          path: "%kernel.logs_dir%/api/%kernel.environment%.log"
          level: debug
          max_files: 30
          channels: 'api'
