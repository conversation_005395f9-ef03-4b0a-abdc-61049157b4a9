framework:
    secret: '%env(APP_SECRET)%'
    validation:      { enable_attributes: true }
    serializer:      { enable_attributes: true }
    property_info:   { enabled: true }
    #csrf_protection: true
    http_method_override: true
    assets:
        base_urls:
            - '%env(ROUTER_REQUEST_CONTEXT_SCHEME)%://%env(ROUTER_REQUEST_CONTEXT_HOST)%'
    annotations: false
    handle_all_throwables: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        storage_factory_id: session.storage.factory.native
        name: sess_dhQwffhK6d
        handler_id: null
        cookie_secure: true
        cookie_samesite: lax
        cookie_lifetime: 86400

    #esi: true
    #fragments: true
    ide: "%env(IDE_CONFIG)%"
    php_errors:
        log: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file

