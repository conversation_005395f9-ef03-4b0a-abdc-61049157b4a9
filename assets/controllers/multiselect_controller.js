import { Controller } from 'stimulus';
import 'multiselect';
import 'jquery.quicksearch';
import 'multiselect/css/multi-select.css';
import _ from 'lodash';

export default class extends Controller {
    static values = { limit: Number };

    connect() {
        this.selectLimit = this.limitValue;
        $(this.element).find('select').multiSelect(this.getMultiSelectConfig());
    }

    getMultiSelectConfig () {

        var self = this;

        function multiSelectLimiterAfterInit(that, container){
            that.selectCount = that.$element.find(':selected').length;
            if(that.$element.data('initialized') !== true && self.selectLimit > -1) {
                that.$element.data('initialized', true);
                multiSelectLimiterUpdate(that);
            }
        }

        function multiSelectLimiterAfterSelect(that, values){
            if(values){
                that.selectCount += values.length;
            }
            multiSelectLimiterUpdate(that);
        }

        function multiSelectLimiterAfterDeselect(that, values){
            if(values){
                that.selectCount -= values.length;
            }
            multiSelectLimiterUpdate(that);
        }

        function multiSelectLimiterUpdate(that){

            var selectedValues = that.$element.val();

            if(that.selectCount >= self.selectLimit){
                that.$element.children().each(function(){
                    if(!_.includes(selectedValues, this.value)){
                        $(this).attr('disabled', 'disabled');
                    }
                });

                that.$selectableUl.children().addClass(that.options.disabledClass);

            }
            else{
                that.$element.children().removeAttr('disabled');
                that.$selectableUl.children().removeClass(that.options.disabledClass);
            }

        }

        let selectableHeader = "<div class='mbs'><input type='text' class='form-control' style='margin-bottom: 10px' autocomplete='off' placeholder='Recherche ...'></div>";
        return {
            selectableHeader: selectableHeader,
            selectionHeader: selectableHeader,
            afterInit: function(ms){
                let that = this,
                    $selectableSearch = that.$selectableUl.prev().children().first(),
                    $selectionSearch = that.$selectionUl.prev().children().first(),
                    selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
                    selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

                that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
                    .on('keydown', function(e){
                        if (e.which === 40){
                            that.$selectableUl.focus();
                            return false;
                        }
                    });

                that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
                    .on('keydown', function(e){
                        if (e.which == 40){
                            that.$selectionUl.focus();
                            return false;
                        }
                    });
                that.multiselectInitialized = true;
                if (self.selectLimit) {
                    multiSelectLimiterAfterInit(this, ms);
                }
            },
            afterSelect: function(values){
                this.qs1.cache();
                this.qs2.cache();
                if (self.selectLimit) {
                    multiSelectLimiterAfterSelect(this, values);
                }
            },
            afterDeselect: function(values){
                this.qs1.cache();
                this.qs2.cache();
                if (self.selectLimit) {
                    multiSelectLimiterAfterDeselect(this, values);
                }
            }
        };
    }
}
