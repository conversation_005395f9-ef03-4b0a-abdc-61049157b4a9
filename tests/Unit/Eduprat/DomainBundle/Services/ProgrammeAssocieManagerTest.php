<?php

namespace App\Tests\unit\Eduprat\DomainBundle\Services;

use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\ProgrammesAssocies;
use Eduprat\DomainBundle\Services\ProgrammeAssocieManager;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ProgrammeAssocieManagerTest extends KernelTestCase
{
    protected ProgrammeAssocieManager $programmeAssocieManager;
    protected function setUp(): void
    {
        parent::setUp();
        self::bootKernel();
        $this->programmeAssocieManager = static::getContainer()->get(ProgrammeAssocieManager::class);
    }

    /**
     * Remplacement de la classe virtuelle
     * @return void
     */
    public function testRemplacementSurSite()
    {
        $programmeSS = (new Programme())->setPresence(Programme::PRESENCE_SITE)->setReference('sur site');
        $programmeEL = (new Programme())->setPresence(Programme::PRESENCE_ELEARNING)->setReference('elearning');
        $programmeCV = (new Programme())->setPresence(Programme::PRESENCE_VIRTUELLE)->setReference('classe virtuelle');

        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;
        $programmeSS2 = (new Programme())->setPresence(Programme::PRESENCE_SITE)->setReference('sur site 2');

        // Création liaison des 3 programmes de formation
        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;
        $programmeSS->setProgrammesAssocies($programmeAssocies);
        $programmeEL->setProgrammesAssocies($programmeAssocies);
        $programmeCV->setProgrammesAssocies($programmeAssocies);

        $programmeAssocies = clone($programmeAssocies);
        $programmeSS->getProgrammesAssocies()->setProgrammeSurSite($programmeSS2);

        $this->programmeAssocieManager->synchroAssociation($programmeEL, $programmeAssocies);
        $this->assertEquals($programmeEL, $programmeEL->getProgrammesAssocies()->getProgrammeElearning());
        $this->assertEquals($programmeCV, $programmeEL->getProgrammesAssocies()->getProgrammeClasseVirtuelle());
        $this->assertEquals($programmeSS2, $programmeEL->getProgrammesAssocies()->getProgrammeSurSite());

        $this->assertEquals(null, $programmeSS->getProgrammesAssocies());
    }


    /**
     * Remplacement de la classe virtuelle
     * @return void
     */
    public function testRemplacementClasseVirtuelle()
    {
        $programmeSS = (new Programme())->setPresence(Programme::PRESENCE_SITE)->setReference('sur site');
        $programmeEL = (new Programme())->setPresence(Programme::PRESENCE_ELEARNING)->setReference('elearning');
        $programmeCV = (new Programme())->setPresence(Programme::PRESENCE_VIRTUELLE)->setReference('classe virtuelle');

        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;

        $programmeCV2 = (new Programme())->setPresence(Programme::PRESENCE_VIRTUELLE)->setReference('classe virtuelle 2');

        // Création liaison des 3 programmes de formation
        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;
        $programmeSS->setProgrammesAssocies($programmeAssocies);
        $programmeEL->setProgrammesAssocies($programmeAssocies);
        $programmeCV->setProgrammesAssocies($programmeAssocies);

        $programmeAssocies = clone($programmeAssocies);
        $programmeSS->getProgrammesAssocies()->setProgrammeClasseVirtuelle($programmeCV2);

        $this->programmeAssocieManager->synchroAssociation($programmeSS, $programmeAssocies);
        $this->assertEquals($programmeEL, $programmeSS->getProgrammesAssocies()->getProgrammeElearning());
        $this->assertEquals($programmeCV2, $programmeSS->getProgrammesAssocies()->getProgrammeClasseVirtuelle());
        $this->assertEquals($programmeSS, $programmeSS->getProgrammesAssocies()->getProgrammeSurSite());

        $this->assertEquals(null, $programmeCV->getProgrammesAssocies());
    }

    /**
     * Remplacement de la classe virtuelle
     * @return void
     */
    public function testRemplacementElearning()
    {
        $programmeSS = (new Programme())->setPresence(Programme::PRESENCE_SITE)->setReference('sur site');
        $programmeEL = (new Programme())->setPresence(Programme::PRESENCE_ELEARNING)->setReference('elearning');
        $programmeCV = (new Programme())->setPresence(Programme::PRESENCE_VIRTUELLE)->setReference('classe virtuelle');

        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;

        $programmeEL2 = (new Programme())->setPresence(Programme::PRESENCE_ELEARNING)->setReference('elearning 2');

        // Création liaison des 3 programmes de formation
        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
            ->setProgrammeClasseVirtuelle($programmeCV)
        ;
        $programmeSS->setProgrammesAssocies($programmeAssocies);
        $programmeEL->setProgrammesAssocies($programmeAssocies);
        $programmeCV->setProgrammesAssocies($programmeAssocies);

        $programmeAssocies = clone($programmeAssocies);
        $programmeSS->getProgrammesAssocies()->setProgrammeElearning($programmeEL2);

        $this->programmeAssocieManager->synchroAssociation($programmeSS, $programmeAssocies);
        $this->assertEquals($programmeEL2, $programmeSS->getProgrammesAssocies()->getProgrammeElearning());
        $this->assertEquals($programmeCV, $programmeSS->getProgrammesAssocies()->getProgrammeClasseVirtuelle());
        $this->assertEquals($programmeSS, $programmeSS->getProgrammesAssocies()->getProgrammeSurSite());

        $this->assertEquals(null, $programmeEL->getProgrammesAssocies());
    }

    /**
     * Suppression
     * @return void
     */
    public function testSuppression()
    {
        $programmeSS = (new Programme())->setPresence(Programme::PRESENCE_SITE)->setReference('sur site');
        $programmeEL = (new Programme())->setPresence(Programme::PRESENCE_ELEARNING)->setReference('elearning');

        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
        ;

        // Création liaison des 3 programmes de formation
        $programmeAssocies = new ProgrammesAssocies();
        $programmeAssocies
            ->setProgrammeSurSite($programmeSS)
            ->setProgrammeElearning($programmeEL)
        ;
        $programmeSS->setProgrammesAssocies($programmeAssocies);
        $programmeEL->setProgrammesAssocies($programmeAssocies);

        $programmeAssocies = clone($programmeAssocies);
        $programmeSS->getProgrammesAssocies()->setProgrammeElearning(null);
        $programmeSS->getProgrammesAssocies()->setProgrammeClasseVirtuelle(null);

        $this->programmeAssocieManager->synchroAssociation($programmeSS, $programmeAssocies);
        $this->assertEquals(null, $programmeSS->getProgrammesAssocies());
        $this->assertEquals(null, $programmeEL->getProgrammesAssocies());
    }
}
