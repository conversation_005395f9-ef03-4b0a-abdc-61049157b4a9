<?php

namespace App\Tests\Eduprat\DomainBundle\Entity;

use Codeception\Test\Unit;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\AuditBundle\Services\CourseManager;


class ParticipationTest extends Unit
{
    public function getAvancementByUnity()
    {
        yield "Un participant ayant complété son etutorat de l'étape 1 à un avancement de 100% pour l'unité 1" => [
            100, "unity" => 1, "parcours" => [CourseManager::STEP1_ETUTORAT_LABEL => true]
        ];
        yield "Un participant ayant complété le module réunion de l'étape 2 à un avancement de 100% pour l'unité 2" => [
            100, "unity" => 2, "parcours" => [CourseManager::STEP2_REUNION_LABEL => true]
        ];
        yield "Un participant ayant complété le module synthese de l'étape 3 à un avancement de 100% pour l'unité 3" => [
            100, "unity" => 3, "parcours" => [CourseManager::STEP4_SYNTHESE_LABEL => true]
        ];
        yield "Un participant ayant complété le module finaliser la formation de l'étape 3 à un avancement de 100% pour l'unité 3" => [
            100, "unity" => 3, "parcours" => [CourseManager::STEP4_END_LABEL => true]
        ];

    }

    /**
     * @dataProvider getAvancementByUnity
     */
    public function testGetAvancementByUnity($assertAvancement, $unity, $parcours): void
    {
        $participation = new Participation();
        $courseState = [];
        foreach($parcours as $moduleId => $moduleCompleted) {
            $courseState[] = ["id" => $moduleId, "completed" => $moduleCompleted];
        }
        $participation->setCourse($courseState);

        $this->assertSame($assertAvancement, $participation->getAvancementByUnity($unity));
    }

    public function hasDpcFinanceSousMode(): void
    {
        $participation = new Participation();
        $financeSousModeDpc = new FinanceSousMode();
        $financeSousModeDpc->setPriseEnCharge(FinanceSousMode::ANDPC);

        $participation->setFinanceSousMode($financeSousModeDpc);

        self::assertTrue($participation->hasDpcFinanceSousMode(), 'Une participation associée à un finance sous mode DPC est DPC');


        // test sans sous mode de financement dpc
        $financeSousModeDpc->setPriseEnCharge(FinanceSousMode::FAF_PM);

        $participation->setFinanceSousMode($financeSousModeDpc);

        self::assertFalse($participation->hasDpcFinanceSousMode(), 'Une participation associée à un finance sous mode non DPC n\'est pas DPC');
    }
}