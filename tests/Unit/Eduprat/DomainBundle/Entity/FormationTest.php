<?php

namespace App\Tests\Eduprat\DomainBundle\Entity;

use Codeception\Test\Unit;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Invoice;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\UnityFormation;
use Eduprat\DomainBundle\Entity\UnitySession;
use Eduprat\DomainBundle\Entity\UnitySessionDate;

class FormationTest extends Unit
{
    public function isThirdUnityOpenProvider()
    {
        yield "retourne vrai si année inférieur à 2023" => [
            true, 2022, 'today + 1 day'
        ];
        yield "retourne faux si la date d'ouverture de l'unité 3 n'est pas passée" => [
            false, 2023, 'today + 1 day'
        ];
        yield "retourne vrai si la date d'ouverture de l'unité 3 est aujourd'hui" => [
            true, 2023, 'today'
        ];
        yield "retourne vrai si la date d'ouverture de l'unité 3 est passée" => [
            true, 2023, 'today - 1 day'
        ];
    }

    /**
     * @dataProvider isThirdUnityOpenProvider
     */
    public function testIsThirdUnityOpen($assert, $year, $openingDate = null): void
    {
        // setup
        $formation = new Formation();
        $programme = new Programme();
        $programme->setYear($year);
        $formation->setProgramme($programme);

        $formation->addUnity(new UnitySession());
        $formation->addUnity(new UnitySession());
        $formation->addUnity((new UnitySession())->setOpeningDate(new \DateTime($openingDate)));

        //asserts
        $this->assertEquals($assert, $formation->isThirdUnityOpen());
    }

    public function getFormTypePre()
    {
        yield "Retourne " . Formation::FORM_TYPE_VIGNETTE . " si formulaire vignette audit et que la vignette est PRE" => [
            Formation::FORM_TYPE_VIGNETTE, true, true, null, false, null,
        ];
        yield "Retourne " . Formation::FORM_TYPE_AUDIT . " si formulaire vignette audit et que l'audit est PRE" => [
            Formation::FORM_TYPE_AUDIT, true, false, null, false, null,
        ];
        yield "Retourne survey si le formulaire est de type survey" => [
            "survey", false, null, null, false, "survey",
        ];
        yield "Retourne le type de formulaire rattaché à la session s'il y en a un" => [
            "form_type", false, null, "form_type", true, false,
        ];
        yield "Retourne le type de formulaire rattaché au programme s'il n'y en a pas de rattaché à la session" => [
            "form_type", false, null, null, false, "form_type",
        ];
    }

    /**
     * @dataProvider getFormTypePre
     */
    public function testGetFormTypePre($assert, $isFormVignetteAudit ,$isVignetteFirst, $getType, $getForm, $getFormType): void
    {
        $formMock = $this->make(Audit::class, [
            'getType' => $getType,
        ]);
        $formationMock = $this->make(Formation::class, [
            'isFormVignetteAudit' => $isFormVignetteAudit,
            'isVignetteFirst' => $isVignetteFirst,
            'getForm' => $getForm ? $formMock : null,
            'getFormType' => $getFormType,
        ]);

        $this->assertSame($assert, $formationMock->getFormTypePre());
    }

    public function getFormTypePost()
    {
        yield "Retourne " . Formation::FORM_TYPE_AUDIT . " si formulaire vignette audit et que la vignette est PRE" => [
            Formation::FORM_TYPE_AUDIT, true, true, null, false, null,
        ];
        yield "Retourne " . Formation::FORM_TYPE_VIGNETTE . " si formulaire vignette audit et que l'audit est PRE" => [
            Formation::FORM_TYPE_VIGNETTE, true, false, null, false, null,
        ];
        yield "Retourne survey si le formulaire est de type survey" => [
            "survey", false, null, null, false, "survey",
        ];
        yield "Retourne le type de formulaire rattaché à la session s'il y en a un" => [
            "form_type", false, null, "form_type", true, false,
        ];
        yield "Retourne le type de formulaire rattaché au programme s'il n'y en a pas de rattaché à la session" => [
            "form_type", false, null, null, false, "form_type",
        ];
    }

    /**
     * @dataProvider getFormTypePost
     */
    public function testGetFormTypePost($assert, $isFormVignetteAudit ,$isVignetteFirst, $getType, $getForm, $getFormType): void
    {
        $formMock = $this->make(Audit::class, [
            'getType' => $getType,
        ]);
        $formationMock = $this->make(Formation::class, [
            'isFormVignetteAudit' => $isFormVignetteAudit,
            'isVignetteFirst' => $isVignetteFirst,
            'getForm' => $getForm ? $formMock : null,
            'getFormType' => $getFormType,
        ]);

        $this->assertSame($assert, $formationMock->getFormTypePost());
    }

    public function getCaTotal() {
        yield "Retourne 0 si il n'y aucune participations" => [
            0.0, [], false, null
        ];
        yield "Retourne 1500 si il y a 3 participations à un prix de 500" => [
            1500.0, [500, 500, 500], false, null
        ];
        yield "Retourne 200.75 si il y a 3 participations à un prix de 602.25" => [
            602.25, [200.75, 200.75, 200.75], false, null
        ];
        yield "Retourne 3000 si il y a 3 participations à un prix de 500 sur une formation de type Seed qui dure 2 jours" => [
            3000.0, [500, 500, 500], true, 2
        ];
    }

    /**
     * @dataProvider getCaTotal
     */
    public function testGetCaTotal($assert, $prices, $isSedd, $getDaysCount) {
        $participationsMocks = array_map(function ($price) {
            return $this->make(Participation::class, [
                'getPrice' => $price,
            ]);
        }, $prices);
        $formationMock = $this->make(Formation::class, [
            'getParticipations' => count($participationsMocks) > 0 ? new ArrayCollection($participationsMocks) : null,
            'isSedd' => $isSedd,
            'getDaysCount' => $getDaysCount,
        ]);

        $this->assertSame($assert, $formationMock->getCaTotal());
    }

    public function getCaTotalByCoordinator() {
        yield "Retourne 0 si il n'y aucune participations" => [
            0, null, [], false, null
        ];
        yield "Retourne 1500 si il y a 3 participations à un prix de 500" => [
            1500, null, [['price' => 500], ['price' => 500], ['price' => 500]], false, null
        ];
        yield "Retourne 200.75 si il y a 3 participations à un prix de 602.25" => [
            602.25, null, [['price' => 200.75], ['price' => 200.75], ['price' => 200.75]], false, null
        ];
        yield "Retourne 3000 si il y a 3 participations à un prix de 500 sur une formation de type Seed qui dure 2 jours" => [
            3000, null, [['price' => 500], ['price' => 500], ['price' => 500]], true, 2
        ];
        yield "Retourne 200 si il y a 3 participations aux prix de 200, 400 et 250 et que la première est liée au coordinateur demandé" => [
            200, 0, [['price' => 200, 'coordinator' => true], ['price' => 400, 'coordinator' => true], ['price' => 250, 'coordinator' => false]], false, null
        ];
        yield "Retourne 850 si il y a 3 participations aux prix de 200, 400 et 250 mais que le coordinateur demandé est le seul coordinateur rattaché à la formation" => [
            850, 0, [['price' => 200, 'coordinator' => true], ['price' => 400, 'coordinator' => false], ['price' => 250, 'coordinator' => false]], false, null
        ];
        yield "Retourne 400 si il y a 3 participations aux prix de 200, 400 et 250 et que la première est liée au coordinateur demandé, mais aussi que la formation est de type Sedd" => [
            400, 0, [['price' => 200, 'coordinator' => true], ['price' => 400, 'coordinator' => true], ['price' => 250, 'coordinator' => false]], true, 2
        ];
    }

    /**
     * @dataProvider getCaTotalByCoordinator
     */
    public function testGetCaTotalByCoordinator($assert, $coordinatorIndex, $participations, $isSedd, $getDaysCount) {
        $person = new Person();
        // création d'un mock par coordinateur et association de l'entité Person au coordinateur correspondant à $coordinatorIndex
        $coordinatorsMocks = [];
        $participationsMocks = [];
        foreach ($participations as $key => $participation) {
            if (isset($participation['coordinator']) && $participation['coordinator']) {
                $coordinatorsMocks[$key] = $this->make(Coordinator::class, [
                    'getPerson' => $key === $coordinatorIndex ? $person : null
                ]);
            }
            $participationsMocks[] = $this->make(Participation::class, [
                'getPrice' => $participation['price'],
                'getCoordinator' => $key === $coordinatorIndex && isset($coordinatorsMocks[$coordinatorIndex]) ? $coordinatorsMocks[$coordinatorIndex] : new Coordinator()
            ]);
        }
        $formationMock = $this->make(Formation::class, [
            'getCoordinators' => $coordinatorsMocks,
            'getParticipations' => count($participationsMocks) > 0 ? new ArrayCollection($participationsMocks) : null,
            'isSedd' => $isSedd,
            'getDaysCount' => $getDaysCount,
        ]);

        $this->assertSame($assert, $formationMock->getCaTotalByCoordinator($person));
    }

    public function updateDatesDataProvider(): iterable
    {
        yield "Si première et dernière session différente et avec date, la date d'ouverture et de fermeture correspondent à la d'ouverture de la première unité et de fermeture de la dernière session" => [
            null, null, '2023-01-01', '2023-01-30', [
                ['2023-01-01', '2023-01-10', Programme::PRESENCE_ELEARNING],
                ['2023-01-11', '2023-01-20', Programme::PRESENCE_ELEARNING],
                ['2023-01-21', '2023-01-30', Programme::PRESENCE_ELEARNING],
            ]
        ];
        yield "Si une session est sur site, la date de début et la date de fin de la formation correspondent aux dates de la seconde session (unité de session)" => [
            '2023-01-12', '2023-01-12', null, null, [
                ['2023-01-01', '2023-01-10', Programme::PRESENCE_ELEARNING],
                ['2023-01-11', '2023-01-20', Programme::PRESENCE_SITE],
                ['2023-01-21', '2023-01-30', Programme::PRESENCE_ELEARNING],
            ],
        ];
        yield "Si une session est virtuelle, la date de début et la date de fin de la formation correspondent aux dates de la seconde session (unité de session)" => [
            '2023-01-12', '2023-01-12', null, null, [
                ['2023-01-01', '2023-01-10', Programme::PRESENCE_ELEARNING],
                ['2023-01-11', '2023-01-20', Programme::PRESENCE_VIRTUELLE],
                ['2023-01-21', '2023-01-30', Programme::PRESENCE_ELEARNING],
            ],
        ];
//        yield "Si la première unité et la dernière sont égales avec des dates de debut et fin vides, les dates d'ouverture et de fermeture reprennent les dates de début et fin de la formation" => [
//            '2023-01-11', '2023-01-20', null, null, [
//                [null, null, Programme::PRESENCE_ELEARNING],
//                ['2023-01-11', '2023-01-20', Programme::PRESENCE_ELEARNING],
//                [null, null, Programme::PRESENCE_ELEARNING],
//            ],
//        ];
    }

    /**
     * @dataProvider updateDatesDataProvider
     */
    public function testUpdateDates(?string $assertStartDate, ?string $assertEndDate, ?string $assertOpeningDate, ?string $assertClosingDate, array $unitiesData): void
    {
        // setup
        $formation = new Formation();
        $programme = new Programme();

        foreach ($unitiesData as $unityData) {
            $unitySession = (new UnitySession())->setOpeningDate(new \DateTime($unityData[0]))->setClosingDate(new \DateTime($unityData[1]));
            $formation->addUnity($unitySession);
            $unityFormation = (new UnityFormation())->setPresence($unityData[2]);
            $programme->addUnity($unityFormation);

            $unitySessionDate = new UnitySessionDate();
            $unitySessionDate->setStartDate((new \DateTime($unityData[0]))->modify("+ 1 day"));
            $unitySessionDate->setEndDate((new \DateTime($unityData[0]))->modify("+ 1 day"));
            $unitySessionDate->setUnitySession($unitySession);
            $unitySession->addUnitySessionDate($unitySessionDate);
        }
        $formation->setProgramme($programme);
        $formation->updateDates(true);

        if ($assertStartDate !== null) {
            $this->assertEquals(new \DateTime($assertStartDate), $formation->getStartDate());
        }

        if ($assertEndDate !== null) {
            $this->assertEquals(new \DateTime($assertEndDate), $formation->getEndDate());
        }

        if ($assertOpeningDate !== null) {
            $this->assertEquals(new \DateTime($assertOpeningDate), $formation->getOpeningDate());
        }

        if ($assertClosingDate !== null) {
            $this->assertEquals(new \DateTime($assertClosingDate), $formation->getClosingDate());
        }
    }

    public function getNbPlacesRestantesProvider(): iterable
    {
        yield "Si aucun participant et 50 participants max alors 50 places restantes" => [50, 0, 50];
        yield "Si 1 participant et 50 participants max alors 49 places restantes" => [49, 1, 50];
    }

    /**
     * @dataProvider getNbPlacesRestantesProvider
     */
    public function testGetNbPlacesRestantes($expected, $nombreParticipants, $nombrePlacesInitiales)
    {
        $formation = new Formation();
        $formation->setNombrePlacesInitiales($nombrePlacesInitiales);

        for ($i = 0; $i < $nombreParticipants; $i++) {
            $formation->addParticipation((new Participation())->setParticipant(new Participant()));
        }
        $this->assertEquals($expected, $formation->getNbPlacesRestantes());
    }

    public function loadInfosFromProgrammeProvider(): iterable
    {
        yield "Lorsqu'un programme est une classe virtuelle, la formation qui en découle a par défaut 30 places" => [30, true, false, false];
        yield "Lorsqu'un programme est elearning, la formation qui en découle a par défaut 100 places" => [100, false, true, false];
        yield "Lorsqu'un programme est sur site, la formation qui en découle a par défaut 48 places" => [48, false, false, true];
    }
    /**
     * @dataProvider loadInfosFromProgrammeProvider
     */
    public function testLoadInfosFromProgramme($assert, $isClasseVirtuelle, $isElearning, $isSurSite) {
        $programmeMock = $this->construct(Programme::class, [], [
            'isClasseVirtuelle' => $isClasseVirtuelle,
            'isElearning' => $isElearning,
            'isSurSite' => $isSurSite,
        ]);
        $programmeMock->__construct();
        $formation = new Formation();
        $formation->loadInfosFromProgramme($programmeMock);
        $this->assertEquals($assert, $formation->getNombrePlacesInitiales());
    }

    public function isPluriAnnuelleProvider(): iterable
    {
        yield "1. Les formations dont l'année de la date de d'ouverture est la même que la date de fermeture ne sont pas pluriannuelles" => [false, '2023-12-01', '2023-12-03'];
        yield "2. Les formations dont l'année de la date de d'ouverture est différente de la date de fermeture sont pluriannuelles " => [true, '2023-12-01', '2024-02-01'];
        yield "3. isPluriAnnuelle return null si les dates de d'ouverture et de fermeture sont null " => [null, null, null];
    }
    /**
     * @dataProvider isPluriAnnuelleProvider
     */
    public function testIsPluriAnnuelle($assert, $openingDate, $closingDate) {

        $formation = new Formation();
        if ($openingDate) {
            $formation->setOpeningDate(new \DateTime($openingDate));
        }
        if ($closingDate) {
            $formation->setClosingDate(new \DateTime($closingDate));
        }

        $this->assertEquals($assert, $formation->isPluriAnnuelle());
    }

    public function getFirstUnityDateOfYearProvider(): iterable
    {
        yield "Vérification que la fonction retourne la date de début de la première unitée de l'année passée en paramètre pour année N"
            => ['2023-12-01', 2023, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30'], ['startDate' => '2024-12-01', 'closingDate' => '2024-12-30']] ];
        yield "Vérification que la fonction retourne la date de début de la première unitée de l'année passée en paramètre (N+1 par rapport au début de la formation)"
            => ['2024-12-01', 2024, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30'], ['startDate' => '2024-12-01', 'closingDate' => '2024-12-30']] ];
    }

    /**
     * @dataProvider getFirstUnityDateOfYearProvider
     */
    public function testGetFirstUnityDateOfYear($assert, $year, $unities) {

        $formation = new Formation();

        foreach ($unities as $unity) {
            $formation->addUnity((new UnitySession())->setOpeningDate(new \DateTime($unity['startDate']))->setClosingDate(new \DateTime($unity['closingDate'])));
        }

        $this->assertEquals($assert, $formation->getFirstUnityDateOfYear($year)->format("Y-m-d"));
    }

    public function getLastUnityDateOfYearProvider(): iterable
    {
        yield "Vérification que la fonction retourne la date de fin de la dernière unitée de l'année passée en paramètre pour année N"
            => ['2023-12-30', 2023, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30'], ['startDate' => '2024-12-01', 'closingDate' => '2024-12-30']] ];
        yield "Vérification que la fonction retourne la date de fin de la dernière unitée de l'année passée en paramètre (N+1 par rapport au début de la formation)"
            => ['2024-12-30', 2024, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30'], ['startDate' => '2024-12-01', 'closingDate' => '2024-12-30']] ];
    }

    /**
     * @dataProvider getLastUnityDateOfYearProvider
     */
    public function testGetLastUnityDateOfYear($assert, $year, $unities) {

        $formation = new Formation();

        foreach ($unities as $unity) {
            $formation->addUnity((new UnitySession())->setOpeningDate(new \DateTime($unity['startDate']))->setClosingDate(new \DateTime($unity['closingDate'])));
        }

        $this->assertEquals($assert, $formation->getLastUnityDateOfYear($year)->format("Y-m-d"));
    }
    
    public function containOfflineProvider(): iterable
    {
        yield "Les formations dont la première unité avec des heures hors ligne dont l'année est la même que le parametre passé contiennent des heures hors ligne sur cette année"
            => [true, 2023, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30', 'nbHoursOffline' => 1]] ];
        yield "Les formations dont une unité avec des heures hors ligne dont l'année est la même que le parametre passé contiennent des heures hors ligne sur cette année"
            => [true, 2024, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30', 'nbHoursOffline' => 1], ['startDate' => '2024-01-01', 'closingDate' => '2024-01-01', 'nbHoursOffline' => 1]] ];
        yield "Les formations qui ne possèdent pas d'unité avec des heures hors ligne ne contiennent pas d'heure hors ligne sur cette année"
            => [false, 2023, [['startDate' => '2023-12-01', 'closingDate' => '2023-12-30', 'nbHoursOffline' => 0]] ];
        yield "Les formations dont une l'unité n'est pas celle du paramètre ne contiennent pas d'heure hors ligne sur cette année"
            => [false, 2024, [['startDate' => '2023-12-03', 'closingDate' => '2023-12-04', 'nbHoursOffline' => 10]] ];
    }

    /**
     * @dataProvider containOfflineProvider
     */
    public function testContainOffline($assert, $year, $unities) {

        $formation = new Formation();
        $programme = new Programme();

        foreach ($unities as $unity) {
            $programme->addUnity((new UnityFormation())->setNbHoursOffline($unity['nbHoursOffline']));
            $formation->addUnity((new UnitySession())->setOpeningDate(new \DateTime($unity['startDate']))->setClosingDate(new \DateTime($unity['closingDate'])));
        }
        $formation->setProgramme($programme);

        $this->assertEquals($assert, $formation->containOffline($year));
    }

    public function hasReunionOnYearProvider(): iterable
    {
        yield "1. Formation avec la réunion sur la même année que le paramètre" => [true, '2023-01-01', 2023];
        yield "2. Formation avec la réunion sur la année différente que le paramètre" => [false, '2023-01-01', 2024];
    }

    /**
     * @dataProvider hasReunionOnYearProvider
     */
    public function testHasReunionOnYear($assert, $date, $year) : void
    {
        $formation = new Formation();
        $formation->setStartDate(new \DateTime($date));
        $this->assertEquals($assert, $formation->hasReunionOnYear($year));
    }

    public function isCloturedProvider(): iterable
    {
        yield "1. Une formation dont la date de cloture est aujourd'hui 00h00:00 n'est pas cloturée et ne sera cloturée que demain" => [false, "today 00:00:00"];
        yield "2. Une formation dont la date de cloture est aujourd'hui 23h59h59 n'est pas cloturée'" => [false, "today 23:59:59"];
        yield "3. Une formation dont la date de cloture était hier à 23h59h59 est cloturée" => [true, "yesterday 23:59:59"];
    }

    /**
     * @dataProvider isCloturedProvider
     */
    public function testIsClotured($assert, $date)
    {
        $formation = new Formation();
        $formation->setClosingDate(new \DateTime($date));
        $this->assertEquals($assert, $formation->isClotured());
    }

    public function updateSearchDatesProvider(): iterable
    {
        $debut = new \DateTime('2024-02-12 08:00:00');
        $fin = new \DateTime('2024-02-19 08:00:00');

        yield "1. Les formations de type ELearning en présence ELearning ont comme searchDate la date de début et de fin de la formation." =>
            [$debut, $fin, ['openingDate' => $debut, 'closingDate' => $fin, 'isElearning' => true], ['isElearning' => true]];
        yield "2. Les formations de type ELearning en non présence ELearning ont comme searchDate la date de début et de fin de l'unité de réunion." =>
        [$debut, $fin, ['startDate' => $debut, 'endDate' => $fin, 'isElearning' => true], ['isElearning' => false]];
        yield "3. Les formations de type non ELearning ont comme searchDate la date de début et de fin de l'unité de réunion." =>
            [$debut, $fin, ['startDate' => $debut, 'endDate' => $fin, 'isElearning' => false], []];
    }

    /**
     * @dataProvider updateSearchDatesProvider
     */
    public function testUpdateSearchDates(?\DateTime $debut, ?\DateTime $fin, array $propertiesDates, array $propertiesProgramme)
    {
        $formation = $this->make(Formation::class, $propertiesDates);
        $programme = $this->make(Programme::class, $propertiesProgramme);
        $formation->setProgramme($programme);

        $formation->updateSearchDates();

        self::assertEquals($formation->getSearchStartDate(), $debut);
        self::assertEquals($formation->getSearchEndDate(), $fin);
        if ($formation->isElearning() && $programme->isElearning()) {
            self::assertEquals($formation->getQuerySearchStartDate(), $debut);
            self::assertEquals($formation->getQuerySearchEndDate(), $debut);
        }
    }

    public function hasElearningUnityProvider(): iterable
    {
        yield "1. Vérification qu'une formation avec une unity de présence Elearning est considéré comme ayant au moins une unité Elearning ." => [true, true];
        yield "2. Vérification qu'une formation sans unity de présence Elearning n'est pas considéré comme ayant au moins une unité Elearning ." => [false, false];
        yield "3. Vérification qu'une formation sans unity n'est pas considéré comme ayant au moins une unité Elearning ." => [false, null];
    }

    /**
     * @dataProvider hasElearningUnityProvider
     */
    public function testHasElearningUnity($assert, $boolPresenceElearning): void
    {
        $unitySession = new UnitySession();
        $unityFormation = new UnityFormation();
        $unityFormation->setPresence($boolPresenceElearning ? Programme::PRESENCE_ELEARNING : Programme::PRESENCE_SITE);

        $programme = new Programme();
        $formation = new Formation();
        if ($boolPresenceElearning !== null) {
            $programme->addUnity($unityFormation);
            $formation->addUnity($unitySession);
        }
        $formation->setProgramme($programme);

        self::assertEquals($assert, $formation->hasElearningUnity());
    }

    public function testGetInvoicePerFinanceSousMode(): void
    {
        $formation = new Formation();
        $financeSousMode = new FinanceSousMode();
        $financeSousMode->setIdentifiant('valide');

        $financeSousMode2 = new FinanceSousMode();
        $financeSousMode2->setIdentifiant('invalide');

        $invoice2023 = new Invoice($formation, $financeSousMode, 123, 2023);
        $invoice2024 = new Invoice($formation, $financeSousMode, 456, 2024);
        $formation->addInvoice($invoice2023);
        $formation->addInvoice($invoice2024);

        self::assertEquals($invoice2024, $formation->getInvoicePerFinanceSousMode($financeSousMode, 2024), "Vérification récupération de la facture à partir de l'année et du sous mode de financement");
        self::assertEquals(null, $formation->getInvoicePerFinanceSousMode($financeSousMode, 2022), "Vérification qu'on ne récupère pas de facture à partir d'une mauvaise année");
        self::assertEquals(null, $formation->getInvoicePerFinanceSousMode($financeSousMode2, 2024), "Vérification qu'on ne récupère pas de facture à partir d'un mauvais sous mode de financement");
    }
}