<?php

namespace App\Tests\unit\Eduprat\DomainBundle\Entity;

use Codeception\Test\Unit;
use Eduprat\DomainBundle\Entity\UnitySession;
use Eduprat\DomainBundle\Entity\UnitySessionDate;

class UnitySessionTest extends Unit
{

    public function synchroDateProvider(): iterable
    {
        yield "1. une unité de session sans openingDate ni closingDate récupère startDate et endDate de son unique jour (unitySessionDate)"
            => ['2023-12-12 01:00:00', '2023-12-14 01:00:00', [
            ['startDate' => '2023-12-12 01:00:00', 'endDate' => '2023-12-14 01:00:00']
        ]];
        yield "2. 1 unité de session sans openingDate ni closingDate récupère la plus ancienne startDate et le plus récente endDate de ses jours (unitySessionDate)"
            => ['2023-12-12 01:00:00', '2023-12-13 10:00:00', [
            ['startDate' => '2023-12-12 01:00:00', 'endDate' => '2023-12-12 10:00:00'],
            ['startDate' => '2023-12-13 01:00:00', 'endDate' => '2023-12-13 10:00:00'],
        ]];
        yield "3. 1 unité de session sans openingDate ni closingDate récupère la plus ancienne startDate et le plus récente endDate de ses jours (cas 3 unitySessionDate)"
            => ['2023-12-12 01:00:00', '2023-12-16 08:00:00', [
            ['startDate' => '2023-12-12 01:00:00', 'endDate' => '2023-12-14 01:00:00'],
            ['startDate' => '2023-12-14 01:00:00', 'endDate' => '2023-12-15 01:00:00'],
            ['startDate' => '2023-12-16 01:00:00', 'endDate' => '2023-12-16 08:00:00'],
        ]];
        yield "4. 1 unité de session sans openingDate ni closingDate récupère la plus ancienne startDate et le plus récente endDate de ses jours (cas 3 unitySessionDate non triées)"
            => ['2023-12-12 01:00:00', '2023-12-16 08:00:00', [
            ['startDate' => '2023-12-12 01:00:00', 'endDate' => '2023-12-14 01:00:00'],
            ['startDate' => '2023-12-16 01:00:00', 'endDate' => '2023-12-16 08:00:00'],
            ['startDate' => '2023-12-14 01:00:00', 'endDate' => '2023-12-15 01:00:00'],
        ]];
    }

    /**
     * @dataProvider synchroDateProvider
     */
    public function testSynchroDate($assertStartDate, $assertEndDate, $dates): void
    {
        $unitySession = new UnitySession();
        foreach ($dates as $date) {
            $unitySession->addUnitySessionDate(
                (new UnitySessionDate())
                    ->setStartDate(new \DateTime($date['startDate']))
                    ->setEndDate(new \DateTime($date['endDate']))
            );
        }
        $unitySession->synchroDate();
        self::assertEquals(new \DateTime($assertStartDate), $unitySession->getOpeningDate());
        self::assertEquals(new \DateTime($assertEndDate), $unitySession->getClosingDate());
    }
}
