<?php

namespace App\Tests\Eduprat\AuditBundle\Services;

use Codeception\Test\Unit;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\AuditBundle\Services\SurveyManager;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\ConfigManager;


class ParticipationAccessManagerTest extends Unit
{
    protected $participationAccessManager;
    protected Participation $participation;

    protected function _before(): void
    {
        $this->formation = $this->getMockBuilder(Formation::class)->getMock();
        $this->participation = $this->getMockBuilder(Participation::class)->getMock();

    }

    public function canAnswerFormDataProvider() {
        yield "Doit retourner false si la formation n'a pas de formulaire associé" => [false, false];
//        yield "Doit retourner false si la fonction utilise le formulaire POST, mais que le formulaire PRE n'est pas complété" => [false, true, false];
        yield "Doit retourner false si la fonction utilise le formulaire POST, mais que le formulaire PRE n'est pas complété" => [false, true, false];
        yield "Doit retourner false s'il n'est possible de répondre au formulaire" => [false, true, true, false];
        yield "Doit retourner true s'il est possible de répondre au formulaire" => [true, true, true, true];
    }

    /**
     *
     * @dataProvider canAnswerFormDataProvider
     */
    public function testCanAnswerForm($assert, $hasLinkedForm, $isFormCompleted = null, $isAnswerable = null) {
        $participationAccessManagerMock = $this->getMockBuilder(ParticipationAccessManager::class)->onlyMethods(['isFormCompleted', 'isAnswerable'])->disableOriginalConstructor()->getMock();
        $this->formation->method('hasLinkedForm')->willReturn($hasLinkedForm);
        $this->participation->method('getFormation')->willReturn($this->formation);
        $participationAccessManagerMock->method('isFormCompleted')->willReturn($isFormCompleted);
        $participationAccessManagerMock->method('isAnswerable')->willReturn($isAnswerable);

        $this->assertSame($assert, $participationAccessManagerMock->canAnswerForm($this->participation, 2));
    }

    public function canAccessMeeting() {
        yield "Doit retourner true si le formulaire PRE d'une formation e-learning est complété" => [true, true, new \DateTime(), true];
        yield "Doit retourner false si la date de fermeture est > à maintenant" => [false, false, new \DateTime('today - 2 days'), null];
        yield "Doit retourner false si le formulaire PRE n'est pas complété" => [false, false, new \DateTime(), false];
        yield "Doit retourner false si le formulaire PRE est complété mais que la date de fermeture est <= à maintenant" => [true, false, new \DateTime('today'), true];
    }

    /**
     * @dataProvider canAccessMeeting
     */
    public function testCanAccessMeeting($assert, $isElearning, $getClosingDate, $isFormCompleted) {
        $participationAccessManagerMock = $this->getMockBuilder(ParticipationAccessManager::class)->onlyMethods(['isFormCompleted'])->disableOriginalConstructor()->getMock();
        if ($isFormCompleted != null) {
            $participationAccessManagerMock->method('isFormCompleted')->willReturn($isFormCompleted);
        }
        $this->formation->method('isElearning')->willReturn($isElearning);
        $this->formation->method('getClosingDate')->willReturn($getClosingDate);
        $this->participation->method('getFormation')->willReturn($this->formation);
        $this->assertSame($assert, $participationAccessManagerMock->canAccessMeeting($this->participation));
    }

    /****************************
     * testCanGetPrerestitution *
     ***************************/

    public function canGetPrerestitution() {
        yield "Doit retourner true si un formulaire est associé à la formation et que le formulaire pré est complété" => [true, true, true];
        yield "Doit retourner false si le formulaire pré n'est pas complété" => [false, true, false];
        yield "Doit retourner false si la formation n'a pas de formulaire associé" => [false, false, true];
    }

    /**
     * @dataProvider canGetPrerestitution
     */
    public function testCanGetPrerestitution($assert, $hasLinkedForm, $isFormCompleted) {
        $participationAccessManagerMock = $this->getMockBuilder(ParticipationAccessManager::class)->onlyMethods(['isFormCompleted'])->disableOriginalConstructor()->getMock();
        $this->formation->method('hasLinkedForm')->willReturn($hasLinkedForm);
        $this->participation->method('getFormation')->willReturn($this->formation);
        $participationAccessManagerMock->method('isFormCompleted')->willReturn($isFormCompleted);

        $this->assertSame($assert, $participationAccessManagerMock->canGetPrerestitution($this->participation));
    }

     /****************************
     * testIsBothFormCompleted *
     ***************************/

     public function isBothFormCompleted() {
        yield "Doit retourner true si les formulaires PRE et POST sont complétés" => [true, true, [true, true]];
        yield "Doit retourner false si le formulaire POST n'est pas complété" => [false, true, [true, false]];
        yield "Doit retourner false si le formulaire PRE n'est pas complété" => [false, true, [false, true]];
        yield "Doit retourner true si le formulaire PRE est complété" => [true, false, [true, null]];
        yield "Doit retourner false si le formulaire PRE n\'est pas complété" => [false, false, [false, null]];
    }

    /**
     * @dataProvider isBothFormCompleted
     */
    public function testIsBothFormCompleted($assert, $hasFormPost, $isFormCompleted) {
        $participationAccessManagerMock = $this->getMockBuilder(ParticipationAccessManager::class)->onlyMethods(['isFormCompleted'])->disableOriginalConstructor()->getMock();
        $this->formation->method('hasFormPost')->willReturn($hasFormPost);
        $this->participation->method('getFormation')->willReturn($this->formation);
        $participationAccessManagerMock->method('isFormCompleted')->willReturnOnConsecutiveCalls(...$isFormCompleted);

        $this->assertSame($assert, $participationAccessManagerMock->isBothFormCompleted($this->participation));
    }

    /****************************
     * testIsFormCompleted *
     ***************************/

    public function isFormCompleted() {
        yield "Retourne false s'il n'y a pas de formulaire rattaché à la formation" => [
            false, 1, false
        ];
        yield "Retourne false si c'est une formation vignette/audit et qu'il y a pas de formulaire POST rattaché" => [
            false, 2, true, true, null
        ];
        yield "Retourne true si le formulaire n'est pas présentiel et complété" => [
            true, 1, true, false, true, false, true
        ];
        yield "Retourne true si le formulaire est présentiel et complété" => [
            true, 1, true, false, true, true, false, true, false, false
        ];
        yield "Retourne true si le formulaire est présentiel, complété, lié à une formation certifiante et qu'il atteint le score minimal" => [
            true, 1, true, false, true, true, false, true, true, true
        ];
    }

    /**
     * @dataProvider isFormCompleted
     * @throws \Exception
     */
    public function testIsFormCompleted($assert, $formId, $hasLinkedForm, $isFormVignetteAudit = null, $getAudit2 = true, $isFormPresentielle = null, $auditIsCompleted = null, $surveyIsCompleted = null, $isCertifying = null, $hasReachedMinScore = null) {

        $programmeMock = $this->make(Programme::class, [
            'isCertifying' => $isCertifying,
        ]);
        $surveyManagerMock  = $this->make(SurveyManager::class, [
            'isCompleted' => $surveyIsCompleted,
            'hasReachedMinScore' => $hasReachedMinScore,
        ]);
        $auditManager  = $this->make(AuditManager::class, [
            'isCompleted' => $auditIsCompleted,
        ]);
        $formManagerFactory = $this->make(FormManagerFactory::class, [
            'getAuditManager' => $auditManager,
            'getSurveyManager' => $surveyManagerMock,
        ]);
        $formationMock = $this->make(FormationVignette::class, [
            'hasLinkedForm' => $hasLinkedForm,
            'isFormVignetteAudit' => $isFormVignetteAudit,
            'getAudit2' => $getAudit2,
            'isFormPresentielle' => $isFormPresentielle,
            'getProgramme' => $programmeMock,
        ]);
        $participationMock = $this->make(Participation::class, [
            'getFormation' => $formationMock
        ]);
        $participationAccessManagerMock = $this->construct(ParticipationAccessManager::class, [
            $formManagerFactory,
            $this->makeEmpty(EntityManagerInterface::class),
            $this->makeEmpty(ConfigManager::class),
        ]);

        $this->assertSame($assert, $participationAccessManagerMock->isFormCompleted($participationMock, $formId));
    }
}
