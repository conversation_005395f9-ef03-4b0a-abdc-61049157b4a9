<?php

namespace App\Tests\Utils;

use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Factory\FormationPresentielleFactory;
use Eduprat\DomainBundle\Factory\ProgrammeFactory;
use Eduprat\DomainBundle\Factory\UnityFormationFactory;
use Eduprat\DomainBundle\Factory\UnitySessionFactory;

final class ExpiredUnityPresentielFormationStory
{
    public function create(?array $options = []): void
    {
        $unities = $this->createUnitiesFormation();

        $programmeOption = [
            'presence' => Programme::PRESENCE_SITE,
            'unities' => $unities,
            'sessionType' => Formation::TYPE_VIGNETTE_AUDIT,
        ];
        if (isset($options['programme.reference'])) {
            $programmeOption['reference'] = $options['programme.reference'];
        }

        $formation = FormationPresentielleFactory::createOne([
            'startDate' => new \DateTime(),
            'programme' => ProgrammeFactory::new($programmeOption),
        ]);

        $this->createUnitiesSession($formation);
    }

    /**
     * @return array
     */
    public function createUnitiesFormation(): array
    {
        $unities = [
            UnityFormationFactory::new([
                'method' => Programme::METHOD_CONTINUE,
                'presence' => Programme::PRESENCE_SITE,
            ]),
        ];
        return $unities;
    }

    /**
     * @param $formation
     * @param $nbUnity
     * @return void
     */
    public function createUnitiesSession($formation): void
    {
        UnitySessionFactory::createOne([
            'formation' => $formation,
            'openingDate' => new \DateTime('today - 4 days'),
            'closingDate' => new \DateTime('today - 3 days'),
        ]);
    }
}
