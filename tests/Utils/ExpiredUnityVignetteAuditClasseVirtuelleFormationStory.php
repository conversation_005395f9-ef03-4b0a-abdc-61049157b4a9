<?php

namespace App\Tests\Utils;

use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Factory\FormationPresentielleFactory;
use Eduprat\DomainBundle\Factory\ProgrammeFactory;
use Eduprat\DomainBundle\Factory\UnityFormationFactory;
use Eduprat\DomainBundle\Factory\UnitySessionFactory;

final class ExpiredUnityVignetteAuditClasseVirtuelleFormationStory
{
    public function create(): void
    {
        $unities = $this->createUnitiesFormation();

        $formation = FormationPresentielleFactory::createOne([
            'startDate' => new \DateTime(),
            'programme' => ProgrammeFactory::new([
                'presence' => Programme::PRESENCE_VIRTUELLE,
                'unities' => $unities,
                'sessionType' => Formation::TYPE_VIGNETTE_AUDIT,
            ]),
        ]);

        $this->createUnitiesSession($formation);
    }

    /**
     * @return array
     */
    public function createUnitiesFormation(): array
    {
        return [
            UnityFormationFactory::new([
                'method' => Programme::METHOD_CONTINUE,
                'presence' => Programme::PRESENCE_ELEARNING,
            ]),
            UnityFormationFactory::new([
                'method' => Programme::METHOD_CONTINUE,
                'presence' => Programme::PRESENCE_SITE,
            ]),
            UnityFormationFactory::new([
                'method' => Programme::METHOD_CONTINUE,
                'presence' => Programme::PRESENCE_ELEARNING,
            ]),
        ];
    }

    /**
     * @param $nbUnity
     * @return void
     */
    public function createUnitiesSession($formation): void
    {
        UnitySessionFactory::createSequence([
            [
                'formation' => $formation,
                'openingDate' => new \DateTime('today - 4 days'),
                'closingDate' => new \DateTime('today - 3 days'),
            ],
            [
                'formation' => $formation,
                'openingDate' => new \DateTime('today - 3 days'),
                'closingDate' => new \DateTime('today - 2 days'),
            ],
            [
                'formation' => $formation,
                'openingDate' => new \DateTime('today - 2 days'),
                'closingDate' => new \DateTime('today - 1 days'),
            ],
        ]);
    }
}
