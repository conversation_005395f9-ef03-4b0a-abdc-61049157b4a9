<?php

namespace App\Tests\functional;

use Eduprat\DomainBundle\DataFixtures\ProgrammeFixtures;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;

class SmokeTest extends WebTestCase
{
    private ?KernelBrowser $client = null;
    private ParameterBagInterface $parameterBag;

    public function setUp(): void
    {
        $this->client = static::createClient(array(), array(
            'HTTPS' => true,
            'HTTP_ACCEPT_LANGUAGE' => 'fr',
        ));
    }

    public function provideTestApiUrls(): \Generator
    {
        // API
        yield '/apiSite/V2/coordinateurs/' => ['/apiSite/V2/coordinateurs/', 'GET', Response::HTTP_OK];
        yield '/apiSite/V2/coordinateurs/1' => ['/apiSite/V2/coordinateurs/1', 'GET', Response::HTTP_OK];
//        yield '/apiSite/V2/formations' => ['/apiSite/V2/formations', 'POST', Response::HTTP_OK, ['reference' => 57201700281]];
        yield '/apiSite/V2/professions' => ['/apiSite/V2/professions', 'GET', Response::HTTP_OK];
//        yield '/apiSite/V2/places-restantes/' . ProgrammeFixtures::REFERENCE_ENTITY1 => ['/apiSite/V2/places-restantes/' . ProgrammeFixtures::REFERENCE_ENTITY1, 'GET', Response::HTTP_OK];
//        yield '/api/login' => ['/api/login', 'GET', Response::HTTP_OK];
    }

    /**
     * @dataProvider provideTestApiUrls
     */
    public function testAPIUrls(string $url, string $method, int $statusCode, $paramsBody = null): void
    {
        // (1) boot the Symfony kernel
        self::bootKernel();
        // (2) use self::$container to access the service container
        $container = self::getContainer();
        // (3) run some service & test the result
        $parameterBag = $container->get(ParameterBagInterface::class);

        $paramsBody = $paramsBody ? json_encode($paramsBody) : null;
        $crawler = $this->client->request($method, $url, [], [], ['HTTP_api-key' => $parameterBag->get('api_key')], $paramsBody);
        $this->assertSame($statusCode, $this->client->getResponse()->getStatusCode());
        $this->assertSame('application/json', $this->client->getResponse()->headers->get('Content-Type'));
    }

    public function provideTestApiUrlsWithout(): \Generator
    {
        yield '/apiSite/V2/coordinateurs/' => ['/apiSite/V2/coordinateurs/', 'GET', Response::HTTP_OK];
        yield '/apiSite/V2/coordinateurs/1' => ['/apiSite/V2/coordinateurs/1', 'GET', Response::HTTP_OK];
        yield '/apiSite/V2/formations' => ['/apiSite/V2/formations', 'POST', Response::HTTP_OK, ['reference' => ProgrammeFixtures::REFERENCE_ENTITY1]];
        yield '/apiSite/V2/professions' => ['/apiSite/V2/professions', 'GET', Response::HTTP_OK];
        yield '/apiSite/V2/places-restantes/' . ProgrammeFixtures::REFERENCE_ENTITY1 => ['/apiSite/V2/places-restantes/' . ProgrammeFixtures::REFERENCE_ENTITY1, 'GET', Response::HTTP_OK];
    }
    /**
     * @dataProvider provideTestApiUrlsWithout
     */
    public function testAPIUrlsWithoutApiKeys(string $url, string $method, int $statusCode, $paramsBody = null): void
    {
        $paramsBody = $paramsBody ? json_encode($paramsBody) : null;
        $crawler = $this->client->request($method, $url, [], [], [], $paramsBody);
        $this->assertSame(Response::HTTP_UNAUTHORIZED, $this->client->getResponse()->getStatusCode(), $this->client->getResponse()->getContent());
    }

//    private function logIn()
//    {
//        $testUser = self::getContainer()->get(EntityManagerInterface::class)->getRepository(User::class)->findOneByUsername('admin');
//        $this->client->loginUser($testUser);
//    }
}