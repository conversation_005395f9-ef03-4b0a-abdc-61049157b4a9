<?php

namespace App\Tests\functional\Eduprat\DomainBundle\Services\Email;

use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationVfc;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Factory\FormationActaliansFactory;
use Eduprat\DomainBundle\Factory\FormationAuditFactory;
use Eduprat\DomainBundle\Factory\FormationCongresFactory;
use Eduprat\DomainBundle\Factory\FormationElearningFactory;
use Eduprat\DomainBundle\Factory\FormationFactory;
use Eduprat\DomainBundle\Factory\FormationPowerpointFactory;
use Eduprat\DomainBundle\Factory\FormationPresentielleFactory;
use Eduprat\DomainBundle\Factory\FormationSeddFactory;
use Eduprat\DomainBundle\Factory\FormationVfcFactory;
use Eduprat\DomainBundle\Factory\FormationVignetteAuditFactory;
use Eduprat\DomainBundle\Factory\FormationVignetteFactory;
use Eduprat\DomainBundle\Factory\ParticipantFactory;
use Eduprat\DomainBundle\Factory\ParticipationFactory;
use Eduprat\DomainBundle\Factory\PersonFactory;
use Eduprat\DomainBundle\Services\Email\EmailFormationConvocation;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\ResetDatabase;
use function Zenstruck\Foundry\Persistence\flush_after;

class EmailFormationConvocationTest extends KernelTestCase
{
    use Factories;
    use ResetDatabase;

    protected EmailFormationConvocation $serviceMail;

    protected function setUp(): void
    {
        parent::setUp();
        // (1) boot the Symfony kernel
        self::bootKernel();

        // (2) use static::getContainer() to access the service container
        $container = static::getContainer();

        // (3) run some service & test the result
        $this->serviceMail = $container->get(EmailFormationConvocation::class);
    }

    public function testArchivedParticipationIgnore(): void
    {
        ParticipationFactory::createOne([
            'archived' => true,
            'formation' => FormationAuditFactory::createOne([
                'startDate' => (new \DateTime('today + 1 day'))->setTime(2,0,0),
            ]),
        ]);
        $loadedItems = $this->serviceMail->loadItems();
        self::assertEmpty($loadedItems, 'Les participations archivés ne sont pas convoqués à la formation');
    }

    public function testNotTomorrowParticipationIgnore(): void
    {
        flush_after(function() {
            ParticipationFactory::createOne([
                'formation' => FormationAuditFactory::new([
                    'startDate' => (new \DateTime('today'))->setTime(23, 59, 59),
                ]),
            ]);
            ParticipationFactory::createOne([
                'formation' => FormationAuditFactory::new([
                    'startDate' => (new \DateTime('today + 2 days'))->setTime(0, 0, 0),
                ]),
            ]);
        });
        self::assertEmpty($this->serviceMail->loadItems(), 'Les participations qui ne sont pas programmées demain ne sont pas convoqués à la formation');
    }

    public function testArchivedParticipantIgnore(): void
    {
        ParticipationFactory::createOne([
            'formation' => FormationAuditFactory::new([
                'startDate' => new \DateTime('today + 1 day'),
            ]),
            'participant' => ParticipantFactory::new([
                'user' => PersonFactory::new([
                    'isArchived' => true,
                ])
            ]),
        ]);
        $loadedItems = $this->serviceMail->loadItems();
        self::assertEmpty($loadedItems, 'Les participants archivés ne sont pas convoqués à la formation');
    }

    public function testArchivedFormationIgnore(): void
    {
        ParticipationFactory::createOne([
            'formation' => FormationAuditFactory::new([
                'startDate' => new \DateTime('today + 1 day'),
                'archived' => true,
            ]),
        ]);
        $loadedItems = $this->serviceMail->loadItems();
        self::assertEmpty($loadedItems, 'Les participants liés à une formation archivées ne sont pas convoqués à la formation');
    }

    public function testBadTypeFormationIgnore(): void
    {
        $classes = [
            FormationVignetteAudit::class,
            FormationAudit::class,
            FormationPresentielle::class,
            FormationVignette::class,
            FormationElearning::class,
            FormationVfc::class,
        ];
        $classesFactories = [
            FormationVignetteAuditFactory::class,
            FormationAuditFactory::class,
            FormationPresentielleFactory::class,
            FormationVignetteFactory::class,
            FormationElearningFactory::class,
            FormationVfcFactory::class,
        ];

        $classesNotAllowedFactories = [
            FormationActaliansFactory::class,
            FormationCongresFactory::class,
            FormationPowerpointFactory::class,
            FormationSeddFactory::class,
        ];
        flush_after(function() use($classesNotAllowedFactories) {
            /** @var FormationFactory $classFactory */
            foreach ($classesNotAllowedFactories as $classFactory) {
                ParticipationFactory::createOne([
                    'formation' => $classFactory::new([
                        'startDate' => new \DateTime('today + 1 day'),
                    ]),
                ]);
            }
        });
        $loadedItems = $this->serviceMail->loadItems();
        self::assertCount(0, $loadedItems, 'Aucun type de formation exclu ne génèrent des convocations à la formation.');


        flush_after(function() use($classesFactories) {
            foreach ($classesFactories as $classFactory) {
                ParticipationFactory::createOne([
                    'formation' => $classFactory::new([
                        'startDate' => new \DateTime('today + 1 day'),
                    ]),
                ]);
            }
        });
        $loadedItems = $this->serviceMail->loadItems();
        $types = array_map(function(Participation  $p) {
            return get_class($p->getFormation());
        }, $loadedItems);
        foreach ($classes as $class) {
            self::assertContains($class, $types, sprintf('Les formations de type %s génèrent des convocations à la formation.', $class));
        }
    }
}
