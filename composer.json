{"type": "project", "license": "proprietary", "autoload": {"psr-4": {"Eduprat\\DomainBundle\\": "src/Eduprat/DomainBundle/", "Eduprat\\AuditBundle\\": "src/Eduprat/AuditBundle/", "Eduprat\\AdminBundle\\": "src/Eduprat/AdminBundle/", "Eduprat\\CrmBundle\\": "src/Eduprat/CrmBundle/", "Eduprat\\PdfBundle\\": "src/Eduprat/PdfBundle/", "Eduprat\\ApiBundle\\": "src/Eduprat/ApiBundle/", "Alienor\\ApiBundle\\": "vendor/Alienor/ApiBundle/", "Alienor\\FormBundle\\": "vendor/Alienor/FormBundle/", "Alienor\\UserBundle\\": "vendor/Alienor/UserBundle/", "Alienor\\EmailBundle\\": "vendor/alienor/email-bundle/", "Alienor\\ElasticBundle\\": "vendor/alienor/elastic-bundle/", "App\\": "src/"}}, "require": {"php": "^8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-zip": "*", "alienor/api-bundle": "^7", "alienor/elastic-bundle": "dev-feature/php-8--update-carbon", "alienor/email-bundle": "^7", "alienor/form-bundle": "^7", "alienor/user-bundle": "^7", "beberlei/doctrineextensions": "^1.3", "composer/package-versions-deprecated": "*********", "dama/doctrine-test-bundle": "8.*", "doctrine/annotations": "^2.0", "doctrine/common": "^3", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.3", "friendsofsymfony/elastica-bundle": "^6.0", "getbrevo/brevo-php": "^v1.0.2", "gotenberg/gotenberg-php": "^2.1", "incenteev/composer-parameter-handler": "^2.0", "knplabs/knp-snappy-bundle": "^1.4", "meteo-concept/hcaptcha-bundle": "^4.2", "nelmio/cors-bundle": "^2.3", "nelmio/security-bundle": "^3.0", "nesbot/carbon": "^3.8.5", "phpdocumentor/reflection-docblock": "^5.2", "ruflin/elastica": "^7.0.0", "scheb/2fa-bundle": "^6.12", "scheb/2fa-email": "^6.12", "scheb/2fa-trusted-device": "^6.12", "scienta/doctrine-json-functions": "^6.3", "shipmonk/doctrine-entity-preloader": "^1.0", "silvertipsoftware/wkhtmltopdf-amd64": "0.12.5", "ssilence/php-imap-client": "dev-master", "stfalcon/tinymce-bundle": "^3.0", "symfony/apache-pack": "^1.0", "symfony/asset": "7.2.*", "symfony/config": "7.2.*", "symfony/console": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/dependency-injection": "7.2.*", "symfony/dom-crawler": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^1.3.1", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/lock": "7.2.*", "symfony/mailer": "7.2.*", "symfony/monolog-bundle": "^3.5", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/rate-limiter": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/ux-chartjs": "^2.0", "symfony/validator": "7.2.*", "symfony/webpack-encore-bundle": "^2.0", "symfony/yaml": "7.2.*", "twig/extra-bundle": "^3.1", "twig/intl-extra": "^3.1", "twig/twig": "^3.0", "vich/uploader-bundle": "^2.3", "webklex/php-imap": "^6.1.0", "zenstruck/foundry": "^v2.3"}, "require-dev": {"alienor/**********************-git": "^6.4", "brianium/paratest": "7.8.0", "codeception/module-asserts": "^2.0", "codeception/module-symfony": "^3.1", "cweagans/composer-patches": "^1.7", "doctrine/doctrine-fixtures-bundle": "^4.0", "phpunit/phpunit": "^11.3", "rector/rector": "^1.0", "symfony/browser-kit": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/maker-bundle": "^1.50", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*", "symplify/vendor-patches": "^11.3"}, "repositories": [{"type": "git", "url": "**********************:dev-symfony2/EmailBundle.git", "name": "alienor/email-bundle"}, {"type": "git", "url": "**********************:dev-projets-alienor/**********************-git.git", "name": "alienor/**********************-git"}, {"type": "git", "url": "**********************:dev-symfony2/ElasticBundle.git", "name": "alienor/elastic-bundle"}, {"type": "git", "url": "**********************:dev-symfony2/ApiBundle.git", "name": "alienor/api-bundle"}, {"type": "git", "url": "**********************:dev-symfony2/formbundle.git", "name": "alienor/form-bundle"}, {"type": "git", "url": "**********************:dev-symfony2/UserBundle.git", "name": "alienor/user-bundle"}], "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "symfony/runtime": true, "cweagans/composer-patches": true, "php-http/discovery": true}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*", "sensio/framework-extra-bundle": "*"}, "extra": {"patches": {"scheb/2fa-trusted-device": ["patches/scheb-2fa-trusted-device-security-twofactor-trusted-trusteddevicetoken-php.patch"]}, "symfony": {"allow-contrib": false, "require": "7.2.*"}}}