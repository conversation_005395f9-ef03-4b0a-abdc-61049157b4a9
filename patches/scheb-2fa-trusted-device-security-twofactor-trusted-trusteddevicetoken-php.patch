--- /dev/null
+++ ../Security/TwoFactor/Trusted/TrustedDeviceToken.php
@@ -19,7 +19,8 @@
     public function authenticatesRealm(string $username, string $firewallName): bool
     {
         return $this->jwtToken->claims()->get(JwtTokenEncoder::CLAIM_USERNAME) === $username
-            && $this->jwtToken->claims()->get(JwtTokenEncoder::CLAIM_FIREWALL) === $firewallName;
+//            && $this->jwtToken->claims()->get(JwtTokenEncoder::CLAIM_FIREWALL) === $firewallName;
+        ;
     }
 
     public function versionMatches(int $version): bool
