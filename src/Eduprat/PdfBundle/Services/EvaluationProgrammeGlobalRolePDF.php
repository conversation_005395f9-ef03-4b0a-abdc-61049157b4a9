<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\DomainBundle\Entity\Programme;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class EvaluationProgrammeGlobalRolePDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly EvaluationReporting $evaluationReporting,
        private readonly TranslatorInterface $translator,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $programme = $params['programme'] ?? throw new \LogicException('parameter programme not found');
        $role = $params['role'] ?? throw new \LogicException('parameter role not found');

        $content = $this->generateContent($programme, $role);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 0.80, 0, 0]; // margin-top: 0, margin-bottom: 20
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getHeaderTwigPath(): ?string
    {
        return null;
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Programme $programme, string $role): string
    {
        $mappedRole = Person::ROLE_MAPPING[$role];
        $reporting = $this->evaluationReporting->getProgrammeReport($programme, $mappedRole, true);

        return $this->twig->render('pdf/base-report.html.twig', array(
            'programme' => $programme,
            'role' => $mappedRole,
            'reporting' => $reporting,
            'evaluation_title' => sprintf("Bilan pédagogique de la formation «&nbsp;%s&nbsp;»", $programme->getFormations()->first()->getProgramme()->getReference()),
            'evaluation_subtitle' => $this->translator->trans('evaluation.subtitle.' . $mappedRole)
        ));
    }
}
