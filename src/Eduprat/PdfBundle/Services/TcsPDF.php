<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;

class TcsPDF extends AbstractPDFGenerator
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $participation = $params['participation'] ?? throw new \LogicException('parameter participation not found');

        $content = $this->generateContent($participation);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Participation $participation): string
    {
        $participation = $this->entityManager->getRepository(Participation::class)->findWithJoins($participation->getId());
        $formation = $participation->getFormation();
        $questionnaireTcs = $formation->getAudit();

        return $this->twig->render('pdf/tcs.html.twig', array(
            'participation' => $participation,
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs
        ));
    }
}
