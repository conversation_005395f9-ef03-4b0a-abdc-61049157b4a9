<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Form\FicheProgressionCollectionType;
use Eduprat\DomainBundle\Entity\FicheProgression;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Twig\Environment;

class ProgressionEmptyPDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly FormFactoryInterface $formFactory,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $request = $params['request'] ?? throw new \LogicException('parameter request not found');

        $content = $this->generateContent($formation, $request);

        $paramsHeader['formation'] = $formation;
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.50, 1, 0, 0]; // margin-top: 36, margin-bottom: 20
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, Request $request): string
    {
        $participation = new Participation();
        $participation->setFormation($formation);

        /** @var FormationAudit $formation */
        $formation = $participation->getFormation();

        foreach ($formation->getAudit()->getPatientDescriptions() as $patient) {
            $sampleProgression = new FicheProgression();
            $sampleProgression->setPatient($patient);
            $participation->addFicheProgression($sampleProgression);
        }

        $form = $this->formFactory->create(FicheProgressionCollectionType::class, $participation, array(
            "editable" => true
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        return $this->twig->render('pdf/empty/progression.html.twig', array(
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form->createView(),
            "editable" => true
        ));
    }
}
