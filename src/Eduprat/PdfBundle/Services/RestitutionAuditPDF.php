<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Twig\Environment;

class RestitutionAuditPDF extends AbstractPDFGenerator
{
    private readonly RestitutionCalculator $restitutionCalculator;

    #[Required]
    public function withRestitutionCalculator(RestitutionCalculator $restitutionCalculator): void
    {
        $this->restitutionCalculator = $restitutionCalculator;
    }


    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $participation = $params['participation'] ?? throw new \LogicException('parameter participation not found');

        $content = $this->generateContent($participation);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Participation $participation): string
    {
        $restitutionDatas = $this->restitutionCalculator->getRestitution($participation, array(
            "axis_audit" => 2,
            "auditId" => 2,
        ));

        return $this->twig->render('pdf/restitution_audit.html.twig', array_merge(array(
            'participation' => $participation,
            'oneCoordinator' => true,
            'formation' => $participation->getFormation(),
        ), $restitutionDatas));
    }
}
