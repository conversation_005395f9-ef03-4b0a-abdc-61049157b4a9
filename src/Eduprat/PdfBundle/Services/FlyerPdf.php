<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\LogoPartenaire;
use Eduprat\DomainBundle\Entity\LogoPartenaireUsage;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Psr\Http\Message\ResponseInterface;
use Symfony\Contracts\Service\Attribute\Required;

class FlyerPdf extends AbstractPDFGenerator
{

    private PlaquetteManager $plaquetteManager;

    #[Required]
    public function withPlaquetteManager(PlaquetteManager $plaquetteManager): void
    {
        $this->plaquetteManager = $plaquetteManager;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $titre = $params['titre'] ?? throw new \LogicException('parameter titre not found');
        $formationType = $params['formationType'] ?? "classique";
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $formation2 = $params['formation2'] ?? null;
        $formation3 = $params['formation3'] ?? null;
        $formation4 = $params['formation4'] ?? null;
        $logoPartenaire = $params['logoPartenaire'] ?? null;
        $paramsFooter['coordinateur'] ?? throw new \LogicException('parameter coordinateur in $paramsFooter not found');

        $content = $this->generateContent($titre, $paramsFooter['coordinateur'], $formation, $formation2, $formation3, $formation4, $formationType, $logoPartenaire);

        $hasDPC = $formation->getProgramme()->hasPrisesEnChargeDpc();
        if ($formation2) {
            $hasDPC = $hasDPC || $formation2->getProgramme()->hasPrisesEnChargeDpc();
        }

        if ($formation3) {
            $hasDPC = $hasDPC || $formation3->getProgramme()->hasPrisesEnChargeDpc();
        }

        if ($formation4) {
            $hasDPC = $hasDPC || $formation4->getProgramme()->hasPrisesEnChargeDpc();
        }
        $paramsFooter = array_merge($paramsFooter,["offset" => 0, 'hasDPC' => $hasDPC]);
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    private function generateContent(
        string $titre,
        Person $coordinator,
        Formation $formation,
        ?Formation $formation2 = null,
        ?Formation $formation3 = null,
        ?Formation $formation4 = null,
        string $formationType = "classique",
        ?LogoPartenaire $logoPartenaire = null
    ): string
    {
        $formations = [$formation];
        if ($formation2) {
            $formations[] = $formation2;
        }
        if ($formation3) {
            $formations[] = $formation3;
        }
        if ($formation4) {
            $formations[] = $formation4;
        }
        $formateurs = [];
        $adresseCommune = true;
        $isFullDPC = true;
        $hasElarning = false;
        $indemnisations = [];
        $sameStartDate = true;
        $sameEndDate = true;
        $startDate = $formations[0]->getStartDate();
        $endDate = $formations[0]->getEndDate();
        $samesDate = false;
        $samePicture = $formation->getProgramme()->getPicture();
        $everySessionHavePicture = true;
        $sameProgramme = $formation->getProgramme()->getReference();
        $showFormatorsPictures = true;
        $showFormatorsInHeader = 0;
        $creationDate = new \DateTime();

        /** @var Formation $formation */
        foreach ($formations as $formation) {
            $formatorsKey = 0; 
            foreach ($formation->getFormateurs() as $formateur) {
                if (count($formateurs) === 4) {
                    continue;
                }
                $formateurs[$formateur->getPerson()->getId()] = $formateur->getPerson();
            }
            if ($adresseCommune && ($formation->getAddress() != $formations[0]->getAddress() || $formation->getAddress2() != $formations[0]->getAddress2()
                    || $formation->getZipCode() != $formations[0]->getZipCode() || $formation->getCity() != $formations[0]->getCity())) {
                $adresseCommune = false;
            }
            if ($isFullDPC && !$formation->getProgramme()->hasPrisesEnChargeDpc()) {
                $isFullDPC = false;
            }

            if ($sameStartDate && $startDate->format("Y-m-d") === $formation->getStartDate()->format("Y-m-d")) {
                $sameStartDate = true;
                $startDate = $startDate < $formation->getStartDate() ? $startDate : $formation->getStartDate();
            } else {
                $sameStartDate = false;
            }

            if ($sameEndDate && $endDate->format("Y-m-d") === $formation->getEndDate()->format("Y-m-d")) {
                $sameEndDate = true;
                $endDate = $endDate > $formation->getEndDate() ? $endDate : $formation->getEndDate();
            } else {
                $sameEndDate = false;
            }

            if ($formation->getProgramme()->getPicture() === null){
                $everySessionHavePicture = false;
                $samePicture = false;
            } else {
                $samePicture = $samePicture && $formation->getProgramme()->getPicture() === $samePicture ? $samePicture : false;
            }            

            if ($sameProgramme && $sameProgramme !== $formation->getProgramme()->getReference()) {
                $sameProgramme = false;
            }

            if ($formation->getFormateurs()->count() > 2) {
                $showFormatorsPictures = false;
            }
            
            foreach ($formation->getFormateurs() as $formateur) {
                if (!$formateur->getPerson()->getAvatar()) {
                    $showFormatorsPictures = false;
                }
                $formatorsKey+= $formateur->getPerson()->getId();
            }

            // On essaie de voir si la somme des id des formateurs est systématiquement identique
            // pour savoir si toutes les sessions ont le même formateur
            if ($showFormatorsInHeader === 0) {
                $showFormatorsInHeader = $formatorsKey;
            } else {
                if ($showFormatorsInHeader !== false) {
                    $showFormatorsInHeader = $showFormatorsInHeader === $formatorsKey ? $showFormatorsInHeader : false;
                }
            }

            $indemnisations[] = $this->plaquetteManager->calculateIndemnisation($formation);
            
            $hasElearning = $formation->getProgramme()->isElearning() ? true : $hasElarning;

            if ($logoPartenaire) {
                $newLogoUsage = new LogoPartenaireUsage($coordinator, $logoPartenaire, $formation, $titre, $creationDate);
                $this->entityManager->persist($newLogoUsage);
            }
        }
        $this->entityManager->flush();

        if  ($sameStartDate && $sameEndDate) {
            $fakeFormation = new Formation();
            $fakeFormation->setStartDate($startDate);
            $fakeFormation->setEndDate($endDate);
            $samesDate = $fakeFormation->sameDatesToString();
        }

        $showFormatorsInHeader = $showFormatorsInHeader && $samePicture && $showFormatorsPictures && $formationType === "classique" && count($formations) > 1;
        
        return $this->twig->render("pdf/plaquette_flyer.html.twig", array(
            'formations' => $formations,
            'hasElearning' => $hasElearning,
            'samesDate' => $samesDate,
            'samePicture' => $samePicture,
            'everySessionHavePicture' => $everySessionHavePicture,
            'sameProgramme' => $sameProgramme ? $formation->getProgramme() : false,
            'showFormatorsPictures' => $showFormatorsPictures,
            'showFormatorsInHeader' => $showFormatorsInHeader, 
            'nbFormateurs' => count($formateurs),
            'indemnisations' => $indemnisations,
            'titre' => $titre,
            'formationType' => $formationType,
            'formateurs' => $formateurs,
            'adresseCommune' => $adresseCommune,
            'isFullDPC' => $isFullDPC,
            'logoPartenaire' => $logoPartenaire,
        ));
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-coordinateur.html.twig';
    }

    public function getHeaderTwigPath(): ?string
    {
        return null;
    }

    protected function margins(): array
    {
        return [0, 1.85, 0, 0];
    }
}
