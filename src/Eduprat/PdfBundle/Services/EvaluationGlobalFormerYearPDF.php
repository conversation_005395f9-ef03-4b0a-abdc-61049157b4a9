<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Psr\Http\Message\ResponseInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Symfony\Contracts\Translation\TranslatorInterface;

class EvaluationGlobalFormerYearPDF extends AbstractPDFGenerator
{
    private EvaluationReporting $evaluationReporting;
    private TranslatorInterface $translator;

    #[Required]
    public function withEvaluationReporting(EvaluationReporting $evaluationReporting): void
    {
        $this->evaluationReporting = $evaluationReporting;
    }

    #[Required]
    public function withTranslator(TranslatorInterface $translator): void
    {
        $this->translator = $translator;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $person = $params['person'] ?? throw new \LogicException('parameter person not found');
        $year = $params['year'] ?? throw new \LogicException('parameter year not found');

        $reporting = $this->evaluationReporting->getFormerReportYear($person, $year, true);

        $content = $this->twig->render('pdf/base-report.html.twig', array(
            'year' => $year,
            'person' => $person,
            'reporting' => $reporting,
            'evaluation_title' => $this->translator->trans('evaluationGlobal.titles.former_annual', array(
                "%civility%" => $person->getCivility(),
                "%fullname%" => $person->getFullname(),
                "%year%" => $year,
            )),
            'evaluation_subtitle' => $this->translator->trans('evaluationGlobal.titles.former_annual_2')
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 1, 0, 0];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getHeaderTwigPath(): string
    {
        return '';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
