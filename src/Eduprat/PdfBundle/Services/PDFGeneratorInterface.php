<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;

interface PDFGeneratorInterface
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string;

    public function buildHeader(?array $paramsHeader = []): string|false;

    public function buildFooter(?array $paramsFooter = []): string|false;

    public function getHeaderTwigPath(): ?string;

    public function getFooterTwigPath(): ?string;
}
