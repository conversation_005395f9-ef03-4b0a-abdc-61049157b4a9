<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Services\FormManagerFactory;
use Eduprat\DomainBundle\Entity\Participation;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;

class AuditPDF extends AbstractPDFGenerator
{
    public function __construct(Environment            $twig,
                                LoggerInterface        $pdfLogger,
                                EntityManagerInterface $entityManager,
                                ParameterBagInterface  $parameterBag,
                                private FormManagerFactory     $formManagerFactory)
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $participation = $params['participation'] ?? throw new \LogicException('parameter participation not found');
        $patient = $params['patient'] ?? null;
        $auditId = $params['auditId'] ?? throw new \LogicException('parameter auditId not found');
        $request = $params['request'] ?? throw new \LogicException('parameter request not found');


        $content = [];
        if ($patient === null) {
            $nbPatients = $participation->getFormation()->getAudit()->getNbPatients();
            for ($i = 1; $i <= $nbPatients; $i++) {
                $content[] = $this->generateContent($participation, $i, $auditId, $request);
            }
        } else {
            $content[] = $this->generateContent($participation, $patient, $auditId, $request);
        }

        return $this->build(join('<div class="page-break"></div>', $content), $paramsHeader, $paramsFooter, $file);
    }

    /**
     * @param mixed $participation
     * @param mixed $patient
     * @param mixed $auditId
     * @param mixed $request
     * @return string
     * @throws \Twig\Error\LoaderError
     * @throws \Twig\Error\RuntimeError
     * @throws \Twig\Error\SyntaxError
     */
    public function generateContent(mixed $participation, mixed $patient, mixed $auditId, mixed $request): string
    {
        $participation = $this->entityManager->getRepository(Participation::class)->findWithJoins($participation->getId());
        if ($patient !== null) {
            $patient = (int)$patient;
        }
        $auditId = (int)$auditId;

        $auditManager = $this->formManagerFactory->getAuditManager($participation, $auditId, $patient);
        $form = $auditManager->getForm();
        $form->handleRequest($request);

        $content = $this->twig->render('pdf/audit.html.twig', array(
            'participation' => $participation,
            'patient' => $patient,
            'auditId' => $auditId,
            'audit' => $auditManager->getAudit(),
            'form' => $form->createView(),
            'auditLabel' => $participation->getFormation()->getAuditLabel($auditId)
        ));
        return $content;
    }

    protected function margins(): array
    {
        return [1.5, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
