<?php

namespace Eduprat\PdfBundle\Services;

use Ed<PERSON><PERSON>\DomainBundle\Services\RestitutionCalculator;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;

class RestitutionGroupePDF extends AbstractPDFGenerator
{

    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        protected readonly RestitutionCalculator  $restitutionCalculator,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');

        $restitutionDatas = $this->restitutionCalculator->getRestitutionFormation($formation, array(
            "audit" => 2
        ));

        $content = $this->twig->render("pdf/restitution_audit_groupe.html.twig", array_merge(array(
            "auditId" => 2,
        ), $restitutionDatas));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.5, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}