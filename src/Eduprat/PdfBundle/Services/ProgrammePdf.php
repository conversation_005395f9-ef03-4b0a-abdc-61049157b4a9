<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Psr\Http\Message\ResponseInterface;
use Symfony\Contracts\Service\Attribute\Required;

class ProgrammePdf extends AbstractPDFGenerator
{

    private PlaquetteManager $plaquetteManager;

    #[Required]
    public function withPlaquetteManager(PlaquetteManager $plaquetteManager): void
    {
        $this->plaquetteManager = $plaquetteManager;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $favori = $params['favori'] ?? throw new \LogicException('parameter favori not found');
        $user = $params['user'] ?? null;

        $content = $this->generateContent($favori, $user);
        
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    private function generateContent(
        $favori,
        Person $user
    ): string
    {
        $results = $this->plaquetteManager->getProgrammes($user, $favori);

        return $this->twig->render("pdf/plaquette_programmes.html.twig", array(
            'sessions' => $results["sessions"],
            'indemnisations' => $results["indemnisations"],
            'coordinator' => $user,
            'professions' => $results["professions"],
            'specialites' => $results["specialites"],
        ));
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getFooterTwigPath(): ?string
    {
        return 'pdf/footers/gotenberg-plaquette-programme.html.twig';
    }

    public function getHeaderTwigPath(): ?string
    {
        return null;
    }

    protected function margins(): array
    {
        return [0, 0.3, 0, 0];
    }
}
