<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Formateur;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class EvaluationGlobalFormerPDF extends AbstractPDFGenerator
{
    private EvaluationReporting $evaluationReporting;
    private TranslatorInterface $translator;

    #[Required]
    public function withEvaluationReporting(EvaluationReporting $evaluationReporting): void
    {
        $this->evaluationReporting = $evaluationReporting;
    }

    #[Required]
    public function withTranslator(TranslatorInterface $translator): void
    {
        $this->translator = $translator;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): string|ResponseInterface
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $former = $params['former'] ?? throw new \LogicException('parameter former not found');

        $content = $this->generateContent($formation, $former);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 1, 0, 0];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getHeaderTwigPath(): string
    {
        return '';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, Formateur $former): string
    {
        $reporting = $this->evaluationReporting->getFormerReport($former, true);

        return $this->twig->render('pdf/base-report.html.twig', array(
            'formation' => $formation,
            'former' => $former,
            'reporting' => $reporting,
            'evaluation_title' => $this->translator->trans('evaluationGlobal.titles.former_programme', array(
                "%civility%" => $former->getPerson()->getCivility(),
                "%fullname%" => $former->getPerson()->getFullname(),
            )),
            'evaluation_subtitle' => $this->translator->trans('evaluationGlobal.titles.former_programme_2', array(
                "%reference%" => $formation->getProgramme()->getReference(),
                "%session%" => $formation->getSessionNumber()
            ))
        ));
    }
}
