<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class EvaluationGlobalTopoPDF extends AbstractPDFGenerator
{
    private EvaluationReporting $evaluationReporting;
    private TranslatorInterface $translator;

    #[Required]
    public function withEvaluationReporting(EvaluationReporting $evaluationReporting): void
    {
        $this->evaluationReporting = $evaluationReporting;
    }

    #[Required]
    public function withTranslator(TranslatorInterface $translator): void
    {
        $this->translator = $translator;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $title = $params['title'] ?? throw new \LogicException('parameter title not found');
        $role = $params['role'] ?? throw new \LogicException('parameter role not found');
        $year = $params['year'] ?? throw new \LogicException('parameter year not found');

        // Convert role string to mapped role
        $mappedRole = Person::ROLE_MAPPING[$role];
        $reporting = $this->evaluationReporting->getTopoByYearAndRole($title, $year, $mappedRole, true);

        $content = $this->twig->render('pdf/base-report.html.twig', array(
            'title' => $title,
            'role' => $mappedRole,
            'year' => $year,
            'reporting' => $reporting,
            'evaluation_title' => $this->translator->trans('evaluationGlobal.titles.topo_' . ($mappedRole === Person::ROLE_PARTICIPANT ? 'participant' : 'former'), array(
                "%title%" => $title,
                "%year%" => $year
            )),
            'evaluation_subtitle' => $this->translator->trans('evaluation.subtitle.' . $mappedRole)
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 1, 0, 0];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }


    public function getHeaderTwigPath(): string
    {
        return '';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
