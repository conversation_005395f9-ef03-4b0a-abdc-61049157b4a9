<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Service\Attribute\Required;
use Twig\Environment;

class AutoEvaluationsPDF extends AbstractPDFGenerator
{
    private readonly RestitutionCalculator $restitutionCalculator;

    #[Required]
    public function withRestitutionCalculator(RestitutionCalculator $restitutionCalculator): void
    {
        $this->restitutionCalculator = $restitutionCalculator;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');

        $datas = $this->restitutionCalculator->getAutoEvalsDatas($formation);

        $content = $this->twig->render('pdf/auto_evaluations.html.twig', [
            'formation' => $formation,
            'datas' => $datas
        ]);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
