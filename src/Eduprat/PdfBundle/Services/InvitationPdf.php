<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\LogoPartenaire;
use Eduprat\DomainBundle\Entity\LogoPartenaireUsage;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Psr\Http\Message\ResponseInterface;
use Symfony\Contracts\Service\Attribute\Required;

class InvitationPdf extends AbstractPDFGenerator
{

    private PlaquetteManager $plaquetteManager;

    #[Required]
    public function withPlaquetteManager(PlaquetteManager $plaquetteManager): void
    {
        $this->plaquetteManager = $plaquetteManager;
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $coordinateur = $paramsFooter['coordinateur'] ?? throw new \LogicException('parameter coordinateur in $paramsFooter not found');
        $indemnisations = $this->plaquetteManager->calculateIndemnisation($formation);
        $logoPartenaire = $params['logoPartenaire'] ?? null;
        $titre = $params['titre'] ?? null;

        $content = $this->generateContent($formation, $indemnisations, $coordinateur, $logoPartenaire, $titre);

        $paramsFooter = array_merge($paramsFooter,["offset" => 0]);
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    private function generateContent(
        Formation $formation,
        array $indemnisations,
        Person $coordinateur,
        ?LogoPartenaire $logoPartenaire = null,
        $titre = null,
    ): string
    {

        if ($logoPartenaire) {
            $creationDate = new \DateTime();
            $newLogoUsage = new LogoPartenaireUsage($coordinateur, $logoPartenaire, $formation, $titre, $creationDate);
            $this->entityManager->persist($newLogoUsage);
            $this->entityManager->flush();
        }

        return $this->twig->render("pdf/plaquette_invitation.html.twig", array(
            'formation' => $formation,
            'indemnisations' => $indemnisations,
            'coordinateur' => $coordinateur,
            'logoPartenaire' => $logoPartenaire,
        ));
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-plaquette-programme.html.twig';
    }

    public function getHeaderTwigPath(): ?string
    {
        return null;
    }

    protected function margins(): array
    {
        return [0, 0, 0, 0];
    }
}
