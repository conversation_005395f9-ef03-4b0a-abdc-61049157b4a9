<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Form\EtutoratType;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Twig\Environment;

class SyntheseEmptyPDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly FormFactoryInterface $formFactory,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $participationId = $params['participationId'] ?? throw new \LogicException('parameter participationId not found');
        $request = $params['request'] ?? throw new \LogicException('parameter request not found');

        $content = $this->generateContent($formation, $participationId, $request);

        $paramsHeader['formation'] = $formation;
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.25, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, $participationId, Request $request): string
    {
        $participant = null;
        if ($participationId != "null") {
            $participationEntity = $this->entityManager->getRepository(Participation::class)->findOneBy(array('id' => $participationId));
            $participant = $participationEntity->getParticipant();
        }

        $participation = new Participation();
        $participation->setFormation($formation);

        $form = $this->formFactory->create(EtutoratType::class, null, array(
            "participation" => $participation,
            "synthese" => true,
            "disabled" => false
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        return $this->twig->render('pdf/empty/synthese.html.twig', array(
            "participant" => $participant,
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form->createView(),
        ));
    }
}
