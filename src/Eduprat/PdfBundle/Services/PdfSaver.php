<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;

class PdfSaver implements PDFGeneratorInterface
{

    public function __construct(private readonly PDFGeneratorInterface $abstractPdfService)
    {
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        if ($file === null) {
            throw new \LogicException('file is null');
        }
        return $this->abstractPdfService->generate($params, $paramsHeader, $paramsFooter, $file);
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return $this->abstractPdfService->buildHeader($paramsHeader);
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        return $this->abstractPdfService->buildFooter($paramsFooter);
    }

    public function getHeaderTwigPath(): string
    {
        return $this->abstractPdfService->getHeaderTwigPath();
    }

    public function getFooterTwigPath(): string
    {
        return $this->abstractPdfService->getFooterTwigPath();
    }
}