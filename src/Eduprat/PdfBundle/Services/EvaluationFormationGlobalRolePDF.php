<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

class EvaluationFormationGlobalRolePDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly EvaluationReporting $evaluationReporting,
        private readonly TranslatorInterface $translator,
        private readonly RestitutionCalculator $restitutionCalculator,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $role = $params['role'] ?? throw new \LogicException('parameter role not found');

        $content = $this->generateContent($formation, $role);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 0.80, 0, 0]; // margin-top: 0, margin-bottom: 20
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function getHeaderTwigPath(): ?string
    {
        return null;
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, string $role): string
    {
        $syntheseDatas = $role == "participant" ? $this->restitutionCalculator->getFicheSyntheseDatas($formation) : null;
        $mappedRole = Person::ROLE_MAPPING[$role];
        $reporting = $this->evaluationReporting->getFormationReport($formation, $mappedRole, true);

        return $this->twig->render('pdf/base-report.html.twig', array(
            'formation' => $formation,
            'role' => $mappedRole,
            'reporting' => $reporting,
            'evaluation_title' => sprintf("Bilan pédagogique de la session %s n°%s", $formation->getProgramme()->getReference(), $formation->getSessionNumber()),
            'evaluation_subtitle' => $this->translator->trans('evaluation.subtitle.session.' . $mappedRole),
            'syntheseDatas' => $syntheseDatas
        ));
    }
}
