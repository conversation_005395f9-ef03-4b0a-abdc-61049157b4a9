<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Services;

use Eduprat\DomainBundle\Entity\Formation;
use Psr\Http\Message\ResponseInterface;

class TcsEmptyPDF extends AbstractPDFGenerator
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');

        $content = $this->generateContent($formation);

        $paramsHeader['formation'] = $formation;
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0]; // [marginTop, marginBot, marginLeft, marginRight]
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation): string
    {
        $formation = $this->entityManager->getRepository(Formation::class)->findOneById($formation->getId());
        $questionnaireTcs = $formation->getAudit();

        return $this->twig->render('pdf/tcs_empty.html.twig', array(
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs
        ));
    }
}
