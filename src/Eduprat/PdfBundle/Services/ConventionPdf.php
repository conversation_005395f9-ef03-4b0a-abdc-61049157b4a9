<?php

namespace Eduprat\PdfBundle\Services;

class ConventionPdf extends AbstractPDFGenerator
{

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface
    {
        $formation = $params['formation'] ?? throw new \LogicException('formation not found');
        $financeSousMode = $params['financeSousMode'] ?? null;

        $content = $this->twig->render('pdf/convention.html.twig', array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.25, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
