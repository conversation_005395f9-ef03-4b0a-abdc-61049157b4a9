<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Exceptions\NoOutputFileInResponse;
use Gotenberg\Gotenberg;
use Got<PERSON>\Stream;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Twig\Environment;

abstract class AbstractPDFGenerator implements PDFGeneratorInterface
{
    public function __construct(
        protected readonly Environment            $twig,
        protected readonly LoggerInterface        $pdfLogger,
        protected readonly EntityManagerInterface $entityManager,
        protected readonly ParameterBagInterface  $parameterBag,
    )
    {
    }

    protected function build(string $content, ?array $paramsHeader = [], ?array $paramsFooter = [], $file = null): ResponseInterface|string
    {
        // FALLBACK TEMPORAIRE : Utiliser wkhtmltopdf car Gotenberg est cassé
        $this->pdfLogger->warning('Fallback vers wkhtmltopdf - Gotenberg non fonctionnel');

        return $this->buildWithWkhtmltopdf($content, $paramsHeader, $paramsFooter, $file);

        // DEBUG: Désactiver header et footer
        /*
        if ($header = $this->buildHeader($paramsHeader)) {
            $chromium->header(Stream::string("header", $header));
        }
        if ($footer = $this->buildFooter($paramsFooter)) {
            $chromium->footer(Stream::string("footer", $footer));
        }
        */

        try {
            echo "<pre>Contenu HTML envoyé à Gotenberg:\n" . htmlspecialchars($content) . "\n</pre>";

            if ($file) {
                preg_match('/^(.+\/)*(.+)\.(.+)$/', $file, $matches);
                $chromium->outputFilename($matches[2]);
                $request = $chromium->html(Stream::string('index.html', $content));
                $folder = substr($matches[1], 0, -1);
                if (!file_exists($folder)) {
                    mkdir($folder, 0777, true);
                }
                echo "<pre>Envoi requête Gotenberg avec fichier: " . $file . "</pre>";
                $response = Gotenberg::save($request, $folder);
            } else {
                $request = $chromium->html(Stream::string('index.html', $content));
                echo "<pre>Envoi requête Gotenberg sans fichier</pre>";
                $response = Gotenberg::send($request);
            }
        } catch (GotenbergApiErrored $e) {
            // Debug ultra-détaillé - AFFICHER AVANT DE LANCER L'EXCEPTION
            $responseBody = '';
            $statusCode = 0;
            $headers = [];

            if ($e->getResponse()) {
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
                $headers = $e->getResponse()->getHeaders();
            }

            // Sauvegarder le HTML pour inspection
            $debugFile = '/tmp/gotenberg_error_' . date('Y-m-d_H-i-s') . '.html';
            file_put_contents($debugFile, $content);

            // FORCER L'AFFICHAGE IMMÉDIAT
            echo "<pre>";
            echo "=== GOTENBERG ERROR DEBUG ===\n";
            echo "URL: " . $this->parameterBag->get('EDUPRAT_GOTENBERG_URL') . "\n";
            echo "Status: " . $statusCode . "\n";
            echo "Response Body: " . $responseBody . "\n";
            echo "HTML saved to: " . $debugFile . "\n";
            echo "Gotenberg Trace: " . json_encode($e->getGotenbergTrace()) . "\n";
            echo "Exception Message: " . $e->getMessage() . "\n";
            echo "Stack Trace: " . $e->getTraceAsString() . "\n";
            echo "</pre>";

            // Aussi logger pour les logs
            $this->pdfLogger->critical('GOTENBERG ERROR DÉTAILLÉ', [
                'message' => $e->getMessage(),
                'gotenberg_url' => $this->parameterBag->get('EDUPRAT_GOTENBERG_URL'),
                'response_status' => $statusCode,
                'response_body' => $responseBody,
                'response_headers' => $headers,
                'gotenberg_trace' => $e->getGotenbergTrace(),
                'request_id' => $e->getGotenbergTrace('Request-Id'),
                'html_file' => $debugFile,
                'html_size' => strlen($content),
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Arrêter ici pour voir les infos
            die('=== DEBUG STOP - Voir les informations ci-dessus ===');

            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        } catch (NoOutputFileInResponse $e) {
        }
        return $response;
    }

    protected function margins(): array
    {
        return [0.39, 0.39, 0.39, 0.39];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        // DEBUG MODE: Header simplifié
        return '<div style="text-align: center; font-size: 12px; padding: 10px;">Header Test</div>';
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        // DEBUG MODE: Footer simplifié
        return '<div style="text-align: center; font-size: 10px; padding: 5px;">Footer Test - Page</div>';
    }

    /**
     * Test de connectivité Gotenberg avec diagnostic complet
     */
    protected function testGotenbergConnectivity(string $gotenbergUrl): void
    {
        echo "<pre>";
        echo "=== TEST CONNECTIVITÉ GOTENBERG ===\n";
        echo "URL: " . $gotenbergUrl . "\n";

        // Test 1: Ping basique
        $parsedUrl = parse_url($gotenbergUrl);
        $host = $parsedUrl['host'] ?? 'unknown';
        $port = $parsedUrl['port'] ?? 80;

        echo "Host: " . $host . ", Port: " . $port . "\n";

        // Test 2: Health check
        try {
            $healthUrl = rtrim($gotenbergUrl, '/') . '/health';
            echo "Testing health URL: " . $healthUrl . "\n";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'ignore_errors' => true,
                    'method' => 'GET'
                ]
            ]);

            $response = @file_get_contents($healthUrl, false, $context);
            $httpResponseHeader = $http_response_header ?? [];

            echo "Health response: " . ($response ?: 'EMPTY') . "\n";
            echo "HTTP headers: " . json_encode($httpResponseHeader) . "\n";

            if ($response === false) {
                echo "❌ GOTENBERG NON ACCESSIBLE\n";
                echo "</pre>";
                die('GOTENBERG NON ACCESSIBLE - ARRÊT DU DEBUG');
            } else {
                echo "✅ GOTENBERG ACCESSIBLE\n";
            }

        } catch (\Exception $e) {
            echo "❌ ERREUR CONNECTIVITÉ: " . $e->getMessage() . "\n";
            echo "</pre>";
            die('ERREUR CONNECTIVITÉ - ARRÊT DU DEBUG');
        }

        echo "=== FIN TEST CONNECTIVITÉ ===\n";
        echo "</pre>";
    }

    /**
     * Fallback temporaire vers wkhtmltopdf
     */
    protected function buildWithWkhtmltopdf(string $content, ?array $paramsHeader = [], ?array $paramsFooter = [], $file = null): ResponseInterface|string
    {
        // Créer un fichier temporaire pour le HTML
        $tempHtml = tempnam(sys_get_temp_dir(), 'pdf_') . '.html';
        file_put_contents($tempHtml, $content);

        // Créer un fichier temporaire pour le PDF
        $tempPdf = tempnam(sys_get_temp_dir(), 'pdf_') . '.pdf';

        // Commande wkhtmltopdf basique
        $command = sprintf(
            'wkhtmltopdf --page-size A4 --margin-top 10mm --margin-bottom 10mm --margin-left 10mm --margin-right 10mm %s %s',
            escapeshellarg($tempHtml),
            escapeshellarg($tempPdf)
        );

        // Exécuter la commande
        exec($command . ' 2>&1', $output, $returnCode);

        if ($returnCode !== 0) {
            $this->pdfLogger->error('Erreur wkhtmltopdf', ['command' => $command, 'output' => $output]);
            throw new \RuntimeException('Erreur génération PDF avec wkhtmltopdf: ' . implode("\n", $output));
        }

        // Lire le contenu du PDF
        $pdfContent = file_get_contents($tempPdf);

        // Nettoyer les fichiers temporaires
        unlink($tempHtml);
        unlink($tempPdf);

        if ($file) {
            // Sauvegarder dans le fichier spécifié
            $folder = dirname($file);
            if (!file_exists($folder)) {
                mkdir($folder, 0777, true);
            }
            file_put_contents($file, $pdfContent);
            return $file;
        } else {
            // Retourner une réponse HTTP
            return new Response($pdfContent, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="document.pdf"'
            ]);
        }
    }
}
