<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Exceptions\NoOutputFileInResponse;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Twig\Environment;

abstract class AbstractPDFGenerator implements PDFGeneratorInterface
{
    public function __construct(
        protected readonly Environment            $twig,
        protected readonly LoggerInterface        $pdfLogger,
        protected readonly EntityManagerInterface $entityManager,
        protected readonly ParameterBagInterface  $parameterBag,
    )
    {
    }

    protected function build(string $content, ?array $paramsHeader = [], ?array $paramsFooter = [], $file = null): ResponseInterface|string
    {
        $content="test test test v test v test v test test test";
        // Validation du contenu HTML
        if (empty($content) || strlen($content) < 10) {
            $this->pdfLogger->error('Contenu HTML vide ou trop court', ['content_length' => strlen($content)]);
            throw new \InvalidArgumentException('Contenu HTML invalide');
        }

        // Remplacer les liens CSS externes par du CSS inline pour éviter les erreurs Gotenberg
        $content = $this->inlineCssResources($content);

        // Log de la taille du contenu pour diagnostic
        $this->pdfLogger->info('Génération PDF', [
            'content_size' => strlen($content),
            'gotenberg_url' => $this->parameterBag->get('EDUPRAT_GOTENBERG_URL')
        ]);
        $chromium = Gotenberg::chromium($this->parameterBag->get('EDUPRAT_GOTENBERG_URL'))
            ->pdf()
            ->printBackground()
            ->paperSize(8.27, 11.7)
            ->margins(...$this->margins())
            ->emulateScreenMediaType()
            ->trace('debug', 'Request-Id'); // Ajouter le tracing pour debug
        if ($header = $this->buildHeader($paramsHeader)) {
            $chromium->header(Stream::string("header", $header));
        }
        if ($footer = $this->buildFooter($paramsFooter)) {
            $chromium->footer(Stream::string("footer", $footer));
        }

        try {
            if ($file) {
                preg_match('/^(.+\/)*(.+)\.(.+)$/', $file, $matches);
                $chromium->outputFilename($matches[2]);
                $request = $chromium->html(Stream::string('index.html', $content));
                $folder = substr($matches[1], 0, -1);
                if (!file_exists($folder)) {
                    mkdir($folder, 0777, true);
                }
                $response = Gotenberg::save($request, $folder);
            } else {
                $request = $chromium->html(Stream::string('index.html', $content));
                $response = Gotenberg::send($request);
            }
        } catch (GotenbergApiErrored $e) {
            // Log détaillé pour diagnostic
            $responseBody = '';
            $statusCode = 0;
            if ($e->getResponse()) {
                $statusCode = $e->getResponse()->getStatusCode();
                $responseBody = $e->getResponse()->getBody()->getContents();
            }


            $this->pdfLogger->critical('GotenbergApiErrored: ' . $e->getMessage(), [
                'gotenberg_url' => $this->parameterBag->get('EDUPRAT_GOTENBERG_URL'),
                'response_status' => $statusCode,
                'response_body' => $responseBody,
                'gotenberg_trace' => $e->getGotenbergTrace(),
                'request_id' => $e->getGotenbergTrace('Request-Id'),
                'content_preview' => substr($content, 0, 500) . '...',
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Message d'erreur plus informatif
            $errorMessage = "Erreur Gotenberg (Status: {$statusCode}): " . $e->getMessage();
            if ($responseBody) {
                $errorMessage .= " - Détails: " . substr($responseBody, 0, 200);
            }

            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $errorMessage);
        } catch (NoOutputFileInResponse $e) {
        }
        return $response;
    }

    protected function margins(): array
    {
        return [0.39, 0.39, 0.39, 0.39];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        $paramsHeader ??= [];
        return $this->twig->render(null, $paramsHeader);
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        $paramsFooter ??= [];
        return $this->twig->render(null, $paramsFooter);
    }

    /**
     * Remplace les liens CSS externes par du CSS inline pour éviter les erreurs Gotenberg
     */
    protected function inlineCssResources(string $content): string
    {
        // Pattern pour trouver les liens CSS
        $pattern = '/<link[^>]*href=["\']([^"\']*\.css[^"\']*)["\'][^>]*>/i';

        return preg_replace_callback($pattern, function($matches) {
            $cssUrl = $matches[1];

            // Si c'est une URL relative, la convertir en absolue
            if (!preg_match('/^https?:\/\//', $cssUrl)) {
                $baseUrl = $this->parameterBag->get('ROUTER_REQUEST_CONTEXT_SCHEME') . '://' .
                          $this->parameterBag->get('ROUTER_REQUEST_CONTEXT_HOST');
                $cssUrl = $baseUrl . $cssUrl;
            }

            try {
                // Essayer de récupérer le contenu CSS avec un timeout court
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5, // 5 secondes de timeout
                        'ignore_errors' => true
                    ]
                ]);
                $cssContent = @file_get_contents($cssUrl, false, $context);
                if ($cssContent !== false && !empty($cssContent)) {
                    return "<style>\n" . $cssContent . "\n</style>";
                }
            } catch (\Exception $e) {
                $this->pdfLogger->warning('Impossible de charger le CSS: ' . $cssUrl, ['error' => $e->getMessage()]);
            }

            // Si on ne peut pas charger le CSS, on supprime le lien pour éviter l'erreur Gotenberg
            $this->pdfLogger->info('CSS supprimé pour éviter erreur Gotenberg: ' . $cssUrl);
            return '<!-- CSS non accessible supprimé: ' . $cssUrl . ' -->';
        }, $content);
    }
}
