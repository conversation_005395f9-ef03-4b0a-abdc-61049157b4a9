<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Exceptions\NoOutputFileInResponse;
use Got<PERSON>\Gotenberg;
use Got<PERSON>\Stream;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Twig\Environment;

abstract class AbstractPDFGenerator implements PDFGeneratorInterface
{
    public function __construct(
        protected readonly Environment            $twig,
        protected readonly LoggerInterface        $pdfLogger,
        protected readonly EntityManagerInterface $entityManager,
        protected readonly ParameterBagInterface  $parameterBag,
    )
    {
    }

    protected function build(string $content, ?array $paramsHeader = [], ?array $paramsFooter = [], $file = null): ResponseInterface|string
    {
        // Optimisations pour les PDF longs/complexes
        set_time_limit(300); // 5 minutes
        ini_set('memory_limit', '1024M'); // 1GB

        // Optimiser le contenu HTML pour réduire la complexité
        $content = $this->optimizeHtmlForPdf($content);

        $this->pdfLogger->info('Génération PDF avec timeouts étendus', [
            'content_size' => strlen($content),
            'memory_limit' => ini_get('memory_limit'),
            'time_limit' => ini_get('max_execution_time')
        ]);

        $chromium = Gotenberg::chromium($this->parameterBag->get('EDUPRAT_GOTENBERG_URL'))
            ->pdf()
            ->printBackground()
            ->paperSize(8.27, 11.7)
            ->margins(...$this->margins())
            ->emulateScreenMediaType()
            ->waitDelay('3s') // Attendre 3 secondes pour le chargement complet
            ->waitForExpression('document.readyState === "complete"', '30s'); // Attendre jusqu'à 30s que le DOM soit prêt
        if ($header = $this->buildHeader($paramsHeader)) {
            $chromium->header(Stream::string("header", $header));
        }
        if ($footer = $this->buildFooter($paramsFooter)) {
            $chromium->footer(Stream::string("footer", $footer));
        }

        try {
            if ($file) {
                preg_match('/^(.+\/)*(.+)\.(.+)$/', $file, $matches);
                $chromium->outputFilename($matches[2]);
                $request = $chromium->html(Stream::string('index.html', $content));
                $folder = substr($matches[1], 0, -1);
                if (!file_exists($folder)) {
                    mkdir($folder, 0777, true);
                }
                $response = Gotenberg::save($request, $folder);
            } else {
                $request = $chromium->html(Stream::string('index.html', $content));
                $response = Gotenberg::send($request);
            }
        } catch (GotenbergApiErrored $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        } catch (NoOutputFileInResponse $e) {
        }
        return $response;
    }

    protected function margins(): array
    {
        return [0.39, 0.39, 0.39, 0.39];
    }

    /**
     * Optimise le HTML pour réduire la complexité et améliorer les performances PDF
     */
    protected function optimizeHtmlForPdf(string $content): string
    {
        // Supprimer les scripts JavaScript qui peuvent causer des timeouts
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);

        // Supprimer les liens CSS externes qui peuvent causer des timeouts
        $content = preg_replace('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', '', $content);

        // Supprimer les commentaires HTML pour réduire la taille
        $content = preg_replace('/<!--.*?-->/s', '', $content);

        // Supprimer les espaces multiples et retours à la ligne inutiles
        $content = preg_replace('/\s+/', ' ', $content);
        $content = preg_replace('/>\s+</', '><', $content);

        $this->pdfLogger->info('HTML optimisé pour PDF', [
            'original_size' => strlen($content),
            'optimized_size' => strlen($content)
        ]);

        return $content;
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        $paramsHeader ??= [];
        return $this->twig->render($this->getHeaderTwigPath(), $paramsHeader);
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        $paramsFooter ??= [];
        return $this->twig->render($this->getFooterTwigPath(), $paramsFooter);
    }
}
