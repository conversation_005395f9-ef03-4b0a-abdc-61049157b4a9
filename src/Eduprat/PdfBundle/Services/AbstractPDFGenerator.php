<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Exceptions\NoOutputFileInResponse;
use Got<PERSON>\Gotenberg;
use Got<PERSON>\Stream;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Twig\Environment;

abstract class AbstractPDFGenerator implements PDFGeneratorInterface
{
    public function __construct(
        protected readonly Environment            $twig,
        protected readonly LoggerInterface        $pdfLogger,
        protected readonly EntityManagerInterface $entityManager,
        protected readonly ParameterBagInterface  $parameterBag,
    )
    {
    }

    protected function build(string $content, ?array $paramsHeader = [], ?array $paramsFooter = [], $file = null): ResponseInterface|string
    {
        // DEBUG MODE: Forcer un contenu HTML ultra-simple pour garantir la génération
        $content = '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Document PDF Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        p { line-height: 1.5; }
    </style>
</head>
<body>
    <h1>Document PDF Généré avec Succès</h1>
    <p>Ce document a été généré le ' . date('d/m/Y à H:i:s') . '</p>
    <p>Contenu de test pour vérifier le bon fonctionnement de Gotenberg.</p>
    <p>Si vous voyez ce message, la génération PDF fonctionne correctement.</p>
</body>
</html>';

        $this->pdfLogger->info('Mode DEBUG: Contenu HTML forcé en mode simple');

        $chromium = Gotenberg::chromium($this->parameterBag->get('EDUPRAT_GOTENBERG_URL'))
            ->pdf()
            ->printBackground()
            ->paperSize(8.27, 11.7)
            ->margins(...$this->margins())
            ->emulateScreenMediaType();

        // DEBUG: Désactiver header et footer
        /*
        if ($header = $this->buildHeader($paramsHeader)) {
            $chromium->header(Stream::string("header", $header));
        }
        if ($footer = $this->buildFooter($paramsFooter)) {
            $chromium->footer(Stream::string("footer", $footer));
        }
        */

        try {
            if ($file) {
                preg_match('/^(.+\/)*(.+)\.(.+)$/', $file, $matches);
                $chromium->outputFilename($matches[2]);
                $request = $chromium->html(Stream::string('index.html', $content));
                $folder = substr($matches[1], 0, -1);
                if (!file_exists($folder)) {
                    mkdir($folder, 0777, true);
                }
                $response = Gotenberg::save($request, $folder);
            } else {
                $request = $chromium->html(Stream::string('index.html', $content));
                $response = Gotenberg::send($request);
            }
        } catch (GotenbergApiErrored $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        } catch (NoOutputFileInResponse $e) {
        }
        return $response;
    }

    protected function margins(): array
    {
        return [0.39, 0.39, 0.39, 0.39];
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        $paramsHeader ??= [];
        return $this->twig->render($this->getHeaderTwigPath(), $paramsHeader);
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        $paramsFooter ??= [];
        return $this->twig->render($this->getFooterTwigPath(), $paramsFooter);
    }
}
