<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;

class ConventionPharmaciePDF extends AbstractPDFGenerator
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $financeSousMode = $params['financeSousMode'] ?? throw new \LogicException('parameter financeSousMode not found');

        $content = $this->twig->render('pdf/convention_pharmacie.html.twig', array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }
}
