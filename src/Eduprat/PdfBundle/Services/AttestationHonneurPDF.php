<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participant;

class AttestationHonneurPDF extends AbstractPDFGenerator
{
    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $participant = $params['participant'] ?? null;
        $person = $params['person'] ?? null;
        $n1 = $params['n1'] ?? null;

        $content = $this->generateContent($formation, $participant, $person, $n1);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [
            1.3,
            1,
            0,
            0
        ];
    }

    public function getHeaderTwigPath(): string
    {
        return '';
    }

    public function getFooterTwigPath(): string
    {
        return '';
    }

    public function buildHeader(?array $paramsHeader = []): string|false
    {
        return false;
    }

    public function buildFooter(?array $paramsFooter = []): string|false
    {
        return false;
    }

    private function generateContent(Formation $formation, ?Participant $participant = null, ?Person $person = null, $n1 = null): string
    {
        if ($participant) {
            $participants = [$participant];
        } else {
            $participants = [];
            foreach($formation->getParticipations() as $participation) {
                if($person) {
                    if($participation->getCoordinator() && $participation->getCoordinator()->getPerson() == $person) {
                        $participants[] = $participation->getParticipant();
                    }
                } else {
                    $participants[] = $participation->getParticipant();
                }
            }
        }

        $unities = $formation->getProgramme()->getUnities()->toArray();
        $unity1 = $unities[0] ?? null;
        $unity3 = $unities[2] ?? null;

        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int)$openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
            $unity1 = $n1 ? null : $unity1; // L'unité 1 si existante est forcément en année n+0 donc inclue uniquement pour l'attestation n+0
            $unity3 = $n1 ? $unity3 : null; // L'unité 3 si existante est forcément en année n+1 donc inclue uniquement pour l'attestation n+1
        }

        usort($participants, function($a, $b) {return strcmp($a->getLastName(), $b->getLastName());});
        $template = $year >= 2023 ? "pdf/attestation_honneur_2023.html.twig" : "pdf/attestation_honneur.html.twig";

        return $this->twig->render($template, [
            'formation' => $formation,
            'participants' => $participants, 
            "unity1" => $unity1,
            "unity3" => $unity3,
            "openingDate" => $openingDate,
            "closingDate" => $closingDate,
            "year" => $year
        ]);
    }
}
