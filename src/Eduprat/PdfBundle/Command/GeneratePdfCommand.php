<?php

namespace Eduprat\PdfBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\CertificateAllParticipationPDF;
use Knp\Snappy\Pdf;
use Symfony\Bundle\FrameworkBundle\Routing\Router;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Form\Exception\LogicException;
use Symfony\Component\HttpKernel\Profiler\Profiler;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(name: 'eduprat:pdf', description: "Génère le fichier PDF qui contient les attestations de participation d'une formation")]
class GeneratePdfCommand extends Command
{
    const TYPE_PARTICIPATION = "participation";
    const TYPE_PARTICIPATION_HORARY = "participation_horary";
    const TYPE_AUDIT1 = "audit1";
    const TYPE_AUDIT2 = "audit2";
    const TYPE_SURVEY1 = "survey1";
    const TYPE_SURVEY2 = "survey2";
    const TYPE_RESTITUTION_AUDIT = "restitution_audit";
    const TYPE_PRERESTITUTION_AUDIT = "prerestitution_audit";
    const TYPE_ATTESTATION_HONNEUR = "attestation_honneur";
    const TYPE_ATTESTATION_HONNEUR_N1 = "attestation_honneur_n1";
    const TYPE_REALISATION = "realisation";
    const TYPE_PRESENCE = "presence";

    /** @var Router */
    private $router;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /** @var string */
    private $projectDir;
    /**
     * @var Pdf
     */
    private $snappy;
    /**
     * @var Profiler
     */
    private $profiler;
    private CertificateAllParticipationPDF $certificateAllParticipationPDF;

    public function __construct(
        string $projectDir,
        RouterInterface $router,
        EntityManagerInterface $entityManager,
        Pdf $snappy,
        CertificateAllParticipationPDF $certificateAllParticipationPDF,
        Profiler $profiler = null)
    {
        parent::__construct();
        $this->router = $router;
        $this->entityManager = $entityManager;
        $this->projectDir = $projectDir;
        $this->snappy = $snappy;
        $this->profiler = $profiler;
        $this->certificateAllParticipationPDF = $certificateAllParticipationPDF;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addArgument('id', InputArgument::REQUIRED, 'ID de la formation')
            ->addArgument('type', InputArgument::REQUIRED, 'Type de fichier à générer')
            ->addArgument('financeSousMode', InputArgument::OPTIONAL, 'ID du sous mode de financement')
            ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $type = $input->getArgument('type');
        $this->snappy->setTimeout(900);
        /** @var Formation $formation */
        $formation = $this->entityManager->getRepository(Formation::class)->find($input->getArgument('id'));
        /** @var FinanceSousMode $financeSousMode */
        if ($input->hasArgument('financeSousMode') && $type !== self::TYPE_ATTESTATION_HONNEUR && $type !== self::TYPE_ATTESTATION_HONNEUR_N1) {
            $financeSousMode = $this->entityManager->getRepository(FinanceSousMode::class)->find($input->getArgument('financeSousMode'));
        } else {
            $financeSousMode = null;
        }

        if (is_null($formation)) {
            exit;
        }

        try {

            if (!is_dir($formation->getGeneratedFileTmpBasePath($type, $financeSousMode))) {
                mkdir($formation->getGeneratedFileTmpBasePath($type, $financeSousMode), 0777, true);
            }

            if ($formation->hasGeneratedFile($type, $financeSousMode)) {
                unlink($formation->getGeneratedFilePath($type, $financeSousMode));
            }
            if ($formation->hasGeneratedFileError($type, $financeSousMode)) {
                unlink($formation->getGeneratedFileErrorPath($type, $financeSousMode));
            }
            touch($formation->getGeneratedFilePath($type, $financeSousMode));

            $this->snappy->setOption('orientation', "Portrait");
            $this->setOptions($this->snappy, $type);

            if ($this->profiler) {
                $this->profiler->disable();
            }

            $nbParticipations = $formation->getParticipations()->count();
            $batch = 5;
            $start = 0;
            $i = 0;
            $files = array();
            $pagesOffset = 0;

            $outputFileTemp = $formation->getGeneratedFileTmpPath($type, $financeSousMode);
            $outputFile = $formation->getGeneratedFilePath($type, $financeSousMode);

            if ($type == self::TYPE_ATTESTATION_HONNEUR || $type == self::TYPE_ATTESTATION_HONNEUR_N1) {

                $participations = $this->entityManager->getRepository(Participation::class)
                    ->createQueryBuilder("pa")
                    ->innerJoin("pa.participant", "p")
                    ->where("pa.formation = :formation")
                    ->orderBy("p.lastname", "ASC")
                    ->addOrderBy("p.firstname", "ASC")
                    ->setParameter("formation", $formation->getId())
                    ->getQuery()->getResult()
                ;
                foreach ($participations as $i => $participation) {
                    if ($type == self::TYPE_ATTESTATION_HONNEUR_N1) {
                        if ($participation->getAttestationHonneurN1()) {
                            $fileTmp = $formation->getGeneratedFileBatchTmpPath($type, $i, $financeSousMode);
                            copy($participation->getAttestationHonneurFileN1()->getRealPath(), $fileTmp);
                            $files[] = $fileTmp;
                        }
                    } else {
                        if ($participation->getAttestationHonneur()) {
                            $fileTmp = $formation->getGeneratedFileBatchTmpPath($type, $i, $financeSousMode);
                            copy($participation->getAttestationHonneurFile()->getRealPath(), $fileTmp);
                            $files[] = $fileTmp;
                        }
                    }
                }
            } elseif ($type == self::TYPE_PARTICIPATION_HORARY) {
                $fileTmp = $formation->getGeneratedFileBatchTmpPath($type, $i, $financeSousMode);
                $pageUrl = $pageUrl = $this->getUrl($type, $formation, $financeSousMode, 0, 0);
                $output->writeln($pageUrl);
                $this->snappy->generate(array($pageUrl), $fileTmp);
                $files[] = $fileTmp;
            } elseif ($type == self::TYPE_AUDIT1 && $formation->isTcs()) {
                $fileTmp = $formation->getGeneratedFileBatchTmpPath($type, $i, $financeSousMode);
                $pageUrl = $pageUrl = $this->getUrl($type, $formation, $financeSousMode, 0, 0);
                $output->writeln($pageUrl);
                $this->snappy->generate(array($pageUrl), $fileTmp);
                $files[] = $fileTmp;
            } else {
                do {
                    $fileTmp = $formation->getGeneratedFileBatchTmpPath($type, $i, $financeSousMode);
                    if ($type === self::TYPE_PARTICIPATION) {
                        $this->certificateAllParticipationPDF->generate(
                            array('formation' => $formation, 'financeSousMode' => $financeSousMode, 'start' => $start, 'batch' => $batch),
                            [], [],
                            $fileTmp
                        );
                    }
                    elseif($type == self::TYPE_RESTITUTION_AUDIT || $type == self::TYPE_PRERESTITUTION_AUDIT) {
                        $pageUrls = [];
                        $participationRepo = $this->entityManager->getRepository(Participation::class);
                        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);
                        if($i > 0) {
                            $pagesOffset += $this->getPDFPages($formation->getGeneratedFileBatchTmpPath($type, $i-1, $financeSousMode));
                        }

                        foreach($participations as $participation) {
                            $url = $type == self::TYPE_RESTITUTION_AUDIT ? 'pdf_audit_restitution' : 'pdf_audit_restitution_groupe_individuelle';
                            $pageUrls[] = $this->router->generate($url, array(($type == self::TYPE_RESTITUTION_AUDIT ? 'id' : 'participation') => $participation->getId()), RouterInterface::ABSOLUTE_URL);
                        }

                        $output->writeln(join("\n", $pageUrls));

                        $this->snappy->generate($pageUrls, $fileTmp);
                    }
                    else {
                        $pageUrl = $this->getUrl($type, $formation, $financeSousMode, $start, $batch);
                        $output->writeln($pageUrl);
                        $this->snappy->generate(array($pageUrl), $fileTmp);
                    }
                    $start += $batch;
                    $files[] = $fileTmp;
                    $i++;
                } while ($start < $nbParticipations);
            }

            $process = new Process([
                'pdftk',
                ...$files,
                "cat",
                "output",
                $outputFileTemp
            ]);
            $process->run();
            rename($outputFileTemp, $outputFile);
            foreach ($files as $file) {
                unlink($file);
            }
            rmdir($formation->getGeneratedFileTmpBasePath($type, $financeSousMode));

        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
            file_put_contents($formation->getGeneratedFileErrorPath($type, $financeSousMode), $e->getMessage());
            if ($formation->hasGeneratedFile($type, $financeSousMode)) {
                unlink($formation->getGeneratedFilePath($type, $financeSousMode));
            }

            $dirToRemove = $formation->getGeneratedFileTmpBasePath($type, $financeSousMode);

            if($dirToRemove && file_exists($dirToRemove)) {
                array_map('unlink', glob("$dirToRemove/*.*"));
                rmdir($dirToRemove);
            }
        }
        $output->writeln('Traitement terminé');
        return Command::SUCCESS;
    }

    protected function getUrl($type, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch) {
        switch ($type) {
            case self::TYPE_PARTICIPATION_HORARY:
                return $this->router->generate('pdf_certificate_participation_horary_formation_opti_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId()), RouterInterface::ABSOLUTE_URL);
                break;
            case self::TYPE_AUDIT1:
                if ($formation->isTcs()) {
                    return $this->router->generate('pdf_tcs_answers_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId()), RouterInterface::ABSOLUTE_URL);
                } else {
                    return $this->router->generate('pdf_audit_answers_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'auditId' => 1, 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                }
                break;
            case self::TYPE_AUDIT2:
                return $this->router->generate('pdf_audit_answers_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'auditId' => 2, 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                break;
            case self::TYPE_SURVEY1:
                return $this->router->generate('pdf_survey_answers_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'surveyId' => 1, 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                break;
            case self::TYPE_SURVEY2:
                return $this->router->generate('pdf_survey_answers_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'surveyId' => 2, 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                break;
            case self::TYPE_REALISATION:
                return $this->router->generate('pdf_document_realisation_formation_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                break;
            case self::TYPE_PRESENCE:
                    return $this->router->generate('pdf_attestation-presence_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'start' => $start, 'batch' => $batch), RouterInterface::ABSOLUTE_URL);
                    break;
            case self::TYPE_ATTESTATION_HONNEUR:
                break;
            case self::TYPE_ATTESTATION_HONNEUR_N1:
                break;
            default:
                throw new LogicException('Ce type de document n\'existe pas.');
                break;
        }
    }

    protected function setOptions($snappy, $type) {
        switch ($type) {
            case self::TYPE_PARTICIPATION:
            case self::TYPE_PARTICIPATION_HORARY:
            case self::TYPE_AUDIT1:
            case self::TYPE_AUDIT2:
            case self::TYPE_SURVEY1:
            case self::TYPE_SURVEY2:
            case self::TYPE_RESTITUTION_AUDIT:
            case self::TYPE_PRERESTITUTION_AUDIT:
                $this->snappy->setOption('header-html', $this->router->generate('pdf_header_evaluation_global', array(), RouterInterface::ABSOLUTE_URL));
                $this->snappy->setOption('footer-html', $this->router->generate('pdf_footer_evaluation_global', array(), RouterInterface::ABSOLUTE_URL));
                $this->snappy->setOption('margin-top', 36);
                $this->snappy->setOption('margin-left', 0);
                $this->snappy->setOption('margin-right', 0);
                //$this->>snappy->setOption('margin-bottom', 20);
                $this->snappy->setOption('javascript-delay', 3000);
                break;
            case self::TYPE_REALISATION:
                    $this->snappy->setOption('header-html', $this->router->generate('pdf_header_realisation', array(), RouterInterface::ABSOLUTE_URL));
                    $this->snappy->setOption('footer-html', $this->router->generate('pdf_footer_evaluation_global', array(), RouterInterface::ABSOLUTE_URL));
                    break;
            case self::TYPE_PRESENCE:
                    $this->snappy->setOption('header-html', $this->router->generate('pdf_header_attestation_presence_document', array(), RouterInterface::ABSOLUTE_URL));
                    $this->snappy->setOption('margin-top', 38);
                    break;
            case self::TYPE_ATTESTATION_HONNEUR:
                break;
            case self::TYPE_ATTESTATION_HONNEUR_N1:
                break;
            default:
                throw new LogicException('Ce type de document n\'existe pas.');
                break;
        }

    }

    static public function getPDFPages($document)
    {
        $process = new Process([
            "pdfinfo",
            $document
        ]);

        $process->run();

        $output = array_filter(explode("\n", $process->getOutput()), 'strlen');

        $pagecount = 0;
        foreach($output as $op)
        {
            if(preg_match("/Pages:\s*(\d+)/i", $op, $matches) === 1)
            {
                $pagecount = intval($matches[1]);
                break;
            }
        }
        return $pagecount;
    }
}
