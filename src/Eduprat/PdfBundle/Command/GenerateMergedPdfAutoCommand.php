<?php

namespace Eduprat\PdfBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(name:'eduprat:pdf_merged_auto', description: "Génère les fichiers PDF globaux des formations cloturées")]

class GenerateMergedPdfAutoCommand extends Command
{
    /**
     * @var ParticipationAccessManager
     */
    private $accessManager;

    /**
     * @var \Swift_Mailer
     */
    private $mailer;

    /**
     * @var RouterInterface
     */
    private $router;
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(ParticipationAccessManager $accessManager, MailerInterface $mailer, RouterInterface $router, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->accessManager = $accessManager;
        $this->mailer = $mailer;
        $this->router = $router;
        $this->entityManager = $entityManager;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var Formation[] $formations */
        $formations = $this->entityManager->getRepository(Formation::class)->findForPdfGeneration();

        /** @var Formation $formation */
        foreach ($formations as $formation) {
            try {
                $filename = $formation->getGeneratedFilePath("fusion");
                if (!$filename || !file_exists($filename) || filesize($filename) === 0) {
                    if ($formation->isAudit() && count($formation->getParticipations()) > 0 && $this->accessManager->areAllFormCompleted($formation)) {
                        $output->writeln(sprintf("Génération formation %s", $formation->getid()));
                        $parserCommand = $this->getApplication()->find('eduprat:pdf_merged');
                        $arguments = array("id" => $formation->getId());
                        $input = new ArrayInput($arguments);
                        $parserCommand->run($input, $output);

                        $hasError = !$filename || !file_exists($filename) || filesize($filename) === 0;

                        $url = $this->router->generate('admin_formation_show', array("id" => $formation->getId()), RouterInterface::ABSOLUTE_URL);

                        $message = (new Email())
                            ->subject(sprintf("[EDUPRAT] Document généré formation %s session %s %s", $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $hasError ? "(Erreur)" : ""))
                            ->from("<EMAIL>")
                            ->to("<EMAIL>")
                            ->cc("<EMAIL>")
                            ->html(
                                sprintf("%s : <br><br>Titre : %s <br>Session : %s <br>Reference : %s<br> <a href='%s'>%s</a>", $hasError ? "Erreur lors de la génération du document fusionné" : "Le document fusionné de la formation suivante a été généré", $formation->getProgramme()->getTitle(), $formation->getSessionNumber(), $formation->getProgramme()->getReference(), $url, $url),
                            )
                        ;
                        $this->mailer->send($message);
                    }
                }

                if (date('H') > 6 && date('H') < 19) {
                    $output->writeln("Arrêt de la procédure !");
                    exit();
                }

            } catch (\Exception $e) {
                $output->writeln(sprintf("Erreur formation %s : %s", $formation->getid(), $e->getMessage()));
            }
        }
        return Command::SUCCESS;
    }
}
