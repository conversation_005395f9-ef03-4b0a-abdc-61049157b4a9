<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\PreRestitutionGroupeIndividuellePDF;
use Eduprat\PdfBundle\Services\PreRestitutionGroupePDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class PreRestitutionGroupeController extends AbstractPdfController
{
    private PreRestitutionGroupePDF $preRestitutionGroupePDF;
    private PreRestitutionGroupeIndividuellePDF $preRestitutionGroupeIndividuellePDF;

    #[Required]
    public function withPreRestitutionGroupePDF(PreRestitutionGroupePDF $preRestitutionGroupePDF, PreRestitutionGroupeIndividuellePDF $preRestitutionGroupeIndividuellePDF): void
    {
        $this->preRestitutionGroupePDF = $preRestitutionGroupePDF;
        $this->preRestitutionGroupeIndividuellePDF = $preRestitutionGroupeIndividuellePDF;
    }

    #[Route(path: '/pdf/restitution-audit-groupe/{id}/pdf/{token}', name: 'pdf_restitution_audit_groupe_pdf', methods: ['GET'])]
    public function restitutionAuditGroupe(Request $request, Formation $formation, $token = null): Response
    {
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);

        $scoringOrPr = $formation->isVignette() ? "scoring" : 'pre-restitution';
        $filename = $scoringOrPr."-audit-1-".$formation->getProgramme()->getReference().".pdf";

        $response =  $this->preRestitutionGroupePDF->generate(
            ['formation' => $formation], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    /**
     * @param Participation $participation
     * @param null                                       $token
     * @return Response
     */
    #[Route(path: '/pdf/restitution-audit-groupe-individuelle/{id}/pdf/{token}', name: 'pdf_audit_restitution_groupe_individuelle_pdf', methods: ['GET'])]
    public function restitutionAuditGroupeIndividuelle(Request $request, Participation $participation, $token = null)
    {
        
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($participation, $token);

        $filename = $participation->getFormation()->getProgramme()->getReference()."-prerestitution-".$participation->getParticipant()->getLastname().".pdf";

        $response =  $this->preRestitutionGroupeIndividuellePDF->generate(
            ['participation' => $participation], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}