<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\ConventionPharmaciePDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class ConventionPharmaciePdfController extends AbstractPdfController
{
    private ConventionPharmaciePDF $conventionPharmaciePDF;

    #[Required]
    public function withConventionPharmaciePDF(ConventionPharmaciePDF $conventionPharmaciePDF): void
    {
        $this->conventionPharmaciePDF = $conventionPharmaciePDF;
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param null $token
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\AdminBundle\Entity\Person $person
     */
    #[Route(path: '/pdf/convention-pharmacie/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_convention_pharmacie_pdf', methods: ['GET'])]
    public function conventionPharmacie(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $parameters = [
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
        ];

        $paramsFooter = [];
        if ($pageOffset !== null) {
            $paramsFooter['pageOffset'] = $pageOffset;
        }

        $response = $this->conventionPharmaciePDF->generate($parameters, [], $paramsFooter);

        $filename = $formation->getProgramme()->getReference()."-convention-pharmacie.pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
