<?php

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON>rat\AdminBundle\Form\DownloadedPlaquetteFileType;
use <PERSON><PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Ed<PERSON>rat\PdfBundle\DTO\ParamsPDFDTO;
use Eduprat\PdfBundle\Services\PdfSaver;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Service\Attribute\Required;

abstract class AbstractPlaquettePdfController extends AbstractPdfController implements PlaquettePdfControllerInterface
{
    //> @TODO remove this when LoggerInterface $pdfLogger will be added in parent class
    protected LoggerInterface $pdfLogger;
    // </ TODO
    //> @TODO remove this when LoggerInterface $pdfLogger will be added in parent class
    #[Required]
    public function withPdfLogger(LoggerInterface $pdfLogger): void
    {
        $this->pdfLogger = $pdfLogger;
    }
    // </ TODO

    protected function generatePdfAndResponseLocal($downloadedPlaquetteFile, ParamsPDFDTO $paramsPDFDTO, UserInterface $user, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $filename): JsonResponse
    {
        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $new_download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);
        $filename = $this->stripAccents($filename);
        try {
            $directLink = sprintf(DownloadedPlaquetteFile::PATHS_DIR[$downloadedPlaquetteFile->getType()], $user->getId(), $filename);
            $filefullroot = $this->getParameter('kernel.project_dir') . '/public/' . $directLink;
            $downloadedPlaquetteFile->setDirectLink($directLink);
            $downloadedPlaquetteFileManager->removeFile($downloadedPlaquetteFile);

            $pdfSaver = new PdfSaver($this->getPdfService());
            $pdfSaver->generate(
                $paramsPDFDTO->params,
                $paramsPDFDTO->paramsHeader,
                $paramsPDFDTO->paramsFooter,
                $filefullroot,
            );
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            return new JsonResponse([
                'etat' => false,
                'error' => $e->getMessage(),
                'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                    'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
                )),
            ]);
        }
        $downloadedPlaquetteFileManager->addDownloadPlaquetteFile($downloadedPlaquetteFile);
        return new JsonResponse([
            'etat' => true,
            'pdf' => $filename,
            'filefullroot' => $filefullroot,
            'directLink' => $directLink,
            'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
            )),
        ]);
    }

    protected function stripAccents($str): string
    {
        return strtr(mb_convert_encoding($str, 'ISO-8859-1', 'UTF-8'), mb_convert_encoding('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ', 'ISO-8859-1', 'UTF-8'), 'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }
}