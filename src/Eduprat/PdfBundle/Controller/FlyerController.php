<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Ed<PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\PdfBundle\DTO\ParamsPDFDTO;
use Eduprat\PdfBundle\Services\AbstractPDFGenerator;
use Eduprat\PdfBundle\Services\FlyerPdf;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class FlyerController extends AbstractPlaquettePdfController
{

    private FlyerPdf $flyerPdf;

    #[Required]
    public function withFlyerPdf(FlyerPdf $flyerPdf): void
    {
        $this->flyerPdf = $flyerPdf;
    }


    #[Route(path: '/pdf/flyer/pdf/{user}/{tokenUser}/{id}/{token}', name: 'pdf_flyer_pdf', methods: ['POST', 'GET'])]
    #[Route(path: '/pdf/flyer/pdf/{user}/{tokenUser}/{id}/{token}/{id2}/{token2}', name: 'pdf_flyer_pdf_2', methods: ['POST', 'GET'])]
    #[Route(path: '/pdf/flyer/pdf/{user}/{tokenUser}/{id}/{token}/{id2}/{token2}/{id3}/{token3}', name: 'pdf_flyer_pdf_3', methods: ['POST', 'GET'])]
    #[Route(path: '/pdf/flyer/pdf/{user}/{tokenUser}/{id}/{token}/{id2}/{token2}/{id3}/{token3}/{id4}/{token4}', name: 'pdf_flyer_pdf_4', methods: ['POST', 'GET'])]
    public function flyer(
        Formation $formation,
        Person $user, $tokenUser,
        Request $request,
        DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager,
        #[MapEntity(id: 'id2')]
        ?Formation $formation2 = null,
        $token = null,
        $token2 = null,
        #[MapEntity(id: 'id3')]
        ?Formation $formation3 = null,
        $token3 = null,
        #[MapEntity(id: 'id4')]
        ?Formation $formation4 = null,
        $token4 = null
    ): JsonResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);
        $this->denyAccessIfInvalidToken($user, $tokenUser);

        if ($formation2) {$this->denyAccessIfInvalidToken($formation2, $token2);}
        if ($formation3) {$this->denyAccessIfInvalidToken($formation3, $token3);}
        if ($formation4) {$this->denyAccessIfInvalidToken($formation4, $token4);}

        $downloadedPlaquetteFile = new DownloadedPlaquetteFile();
        $downloadedPlaquetteFile->setType(DownloadedPlaquetteFile::TYPE_FLYER);
        $download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $downloadedPlaquetteFile);
        $download_plaquette_file_form->handleRequest($request);

        // si formulaire non valide ou non soumis
        if (!($download_plaquette_file_form->isSubmitted() && $download_plaquette_file_form->isValid())) {

            return new JsonResponse(['etat' => false]);
        }
        $downloadedPlaquetteFile->setPerson($user);
        $referencesArray = [
            $formation->getProgramme()->getReference(),
        ];
        if (isset($formation2)) { $referencesArray[] = $formation2->getProgramme()->getReference();}
        if (isset($formation3)) { $referencesArray[] = $formation3->getProgramme()->getReference();}
        if (isset($formation4)) { $referencesArray[] = $formation4->getProgramme()->getReference();}

        $references = implode('-', $referencesArray);
        $filename = "flyer-" . $references.'-'.uniqid().".pdf";

        $paramsPDFDTO = new ParamsPDFDTO(
            [
                'titre' => $downloadedPlaquetteFile->getTitre(),
                'formationType' => $downloadedPlaquetteFile->getFormationType(),
                'formation' => $formation,
                'formation2' => $formation2,
                'formation3' => $formation3,
                'formation4' => $formation4,
                'logoPartenaire' => $downloadedPlaquetteFile->getLogoPartenaire(),
            ],
            null,
            [
                'coordinateur' => $user,
            ]
        );

        return $this->generatePdfAndResponseLocal($downloadedPlaquetteFile, $paramsPDFDTO, $user, $downloadedPlaquetteFileManager, $filename);
    }

    public function getPdfService(): AbstractPDFGenerator
    {
        return $this->flyerPdf;
    }
}
