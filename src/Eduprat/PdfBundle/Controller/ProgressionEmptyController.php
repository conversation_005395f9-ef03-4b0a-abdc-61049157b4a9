<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\ProgressionEmptyPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class ProgressionEmptyController extends AbstractPdfController
{

    private ProgressionEmptyPDF $progressionEmptyPDF;

    #[Required]
    public function withProgressionEmptyPDF(ProgressionEmptyPDF $progressionEmptyPDF): void
    {
        $this->progressionEmptyPDF = $progressionEmptyPDF;
    }

    #[Route(path: '/pdf/fiche-progression/empty/{id}/pdf/{token}', name: 'pdf_progression_empty_pdf', methods: ['GET'])]
    public function progressionEmpty(Request $request, Formation $formation, string $token): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $filename = "progression-vierge-".$formation->getProgramme()->getReference().".pdf";

        $response = $this->progressionEmptyPDF->generate(
            ['formation' => $formation, 'request' => $request],
            ['formation' => $formation],
            ['offset' => $pageOffset]
        );

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
