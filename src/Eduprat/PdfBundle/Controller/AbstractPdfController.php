<?php

namespace Eduprat\PdfBundle\Controller;

use <PERSON><PERSON><PERSON>\DomainBundle\Entity\ObjectWithTokenInterface;
use Ed<PERSON>rat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Ed<PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use <PERSON><PERSON><PERSON>\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Knp\Snappy\Pdf;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

abstract class AbstractPdfController extends AbstractController
{
    public function __construct(
        protected readonly AuthorizationCheckerInterface $authorizationChecker,
        protected Pdf $snappy,
        protected LoggerInterface $pdfLogger,
        protected ParameterBagInterface $parameterBag,
    )
    {}

    protected function denyAccessIfInvalidToken(ObjectWithTokenInterface $object, $token, $message = 'Access Denied.'): void
    {
        if (!($this->authorizationChecker->isGranted('IS_AUTHENTICATED_FULLY') && $this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')) && $object->getToken() !== $token) {
            throw new AccessDeniedHttpException();
        }
    }

    protected function getPdfResponse($filename, $pageUrls) {
        try {
            $content = $this->snappy->getOutput((array)$pageUrls);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
        return new Response(
            $content,
            Response::HTTP_OK,
            array(
                'Content-Type'          => 'application/pdf',
                'Content-Disposition'   => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    protected function getEvaluationGlobalResponse($pageUrl) {
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 0);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    public function generatePdfAndResponse($downloadedPlaquetteFile, $user, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $pageUrl, $filename)
    {
        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $new_download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);
        $filename = $this->stripAccents($filename);
        try {
            $directLink = sprintf(DownloadedPlaquetteFile::PATHS_DIR[$downloadedPlaquetteFile->getType()], $user->getId(), $filename);
            $filefullroot = $this->parameterBag->get('kernel.project_dir').'/public/'.$directLink;
            $downloadedPlaquetteFile->setDirectLink($directLink);
            $downloadedPlaquetteFileManager->removeFile($downloadedPlaquetteFile);
            $this->snappy->generate($pageUrl, $filefullroot);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            return new JsonResponse([
                'etat' => false,
                'error' => $e->getMessage(),
                'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                    'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
                )),
            ]);
        }
        $downloadedPlaquetteFileManager->addDownloadPlaquetteFile($downloadedPlaquetteFile);
        return new JsonResponse([
            'etat' => true,
            'pdf' => $filename,
            'filefullroot' => $filefullroot,
            'directLink' => $directLink,
            'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
            )),
        ]);
    }

    protected function stripAccents($str): string
    {
        return strtr(mb_convert_encoding($str, 'ISO-8859-1', 'UTF-8'), mb_convert_encoding('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ', 'ISO-8859-1', 'UTF-8'), 'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }
}
