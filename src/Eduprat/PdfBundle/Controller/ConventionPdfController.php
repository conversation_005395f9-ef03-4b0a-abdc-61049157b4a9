<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\ConventionPdf;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Service\Attribute\Required;

#[Route(path: '/pdf')]
class ConventionPdfController extends AbstractPdfController
{
    private ConventionPdf $conventionPdf;

    #[Required]
    public function withConventionPdf(ConventionPdf $conventionPdf): void
    {
        $this->conventionPdf = $conventionPdf;
    }

    #[Route(path: '/convention/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_convention_pdf', methods: ['GET'])]
    #[Route(path: '/convention/{id}/pdf/{token}', name: 'pdf_convention_empty_pdf', methods: ['GET'])]
    public function convention(Formation $formation, FinanceSousMode $financeSousMode = null, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $parameters = array('formation' => $formation);
        if ($financeSousMode) {
            $parameters['financeSousMode'] = $financeSousMode;
        }
        $response =  $this->conventionPdf->generate($parameters, null, []);

        $financeSousModeName = $financeSousMode ? "-" . $financeSousMode->getName() : "";
        $filename = "Convention-" . $formation->getProgramme()->getReference()."-". $financeSousModeName .".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
