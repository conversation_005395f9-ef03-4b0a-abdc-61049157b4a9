<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON><PERSON>\DomainBundle\Entity\FormationPresentielle;
use Eduprat\PdfBundle\Services\SurveyEmptyPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class SurveyEmptyController extends AbstractPdfController
{

    private SurveyEmptyPDF $surveyEmptyPdf;

    #[Required]
    public function withSurveyEmptyPDF(SurveyEmptyPDF $surveyEmptyPdf): void
    {
        $this->surveyEmptyPdf = $surveyEmptyPdf;
    }

    #[Route(path: '/survey/empty/{id}/{surveyId}/pdf/{token}', name: 'pdf_survey_empty_pdf', methods: ['GET'])]
    public function surveyEmpty(
        FormationPresentielle $formation,
        string $token,
        int $surveyId = 1,
    ): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $response =  $this->surveyEmptyPdf->generate(
            ['formation' => $formation, 'surveyId' => $surveyId], null, null
        );

        $filename = "questionnaire-vierge-".$formation->getProgramme()->getReference().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
