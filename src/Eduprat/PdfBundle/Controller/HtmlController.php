<?php

namespace Eduprat\PdfBundle\Controller;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\AuditBundle\Form\EtutoratType;
use Eduprat\AuditBundle\Form\FicheProgressionCollectionType;
use Eduprat\AuditBundle\Form\SurveyType;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\DomainBundle\Entity\FicheProgression;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\InvoiceManager;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Profiler\Profiler;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\AdminBundle\Services\EvaluationCoordinatorByCoordinatorManager;
use Eduprat\DomainBundle\Entity\EvaluationCoordinator;
use Eduprat\DomainBundle\Entity\EvaluationProgramme;
use Eduprat\AdminBundle\Services\EvaluationProgrammeManager;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route(path: '/pdf')]
class HtmlController extends AbstractPdfController
{

    private ?Profiler $profiler;
    private CourseManager $courseManager;
    private PlaquetteManager $plaquetteManager;

    public function __construct(CourseManager $courseManager, PlaquetteManager $plaquetteManager, Profiler $profiler = null)
    {
        $this->profiler = $profiler;
        $this->courseManager = $courseManager;
        $this->plaquetteManager = $plaquetteManager;
    }

    public function disableToolbar(): void
    {
        $this->profiler?->disable();
    }

    /*** HEADERS ***/
    #[Route(path: '/footer-evaluation', name: 'pdf_footer_evaluation', methods: ['GET'])]
    public function footerEvaluation(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/evaluation.html.twig');
    }

    /**
     * @param Formation|null $formation
     * @return Response
     */
    #[Route(path: '/header-evaluation-global/{id}', name: 'pdf_header_evaluation_global', defaults: ['id' => null], methods: ['GET'])]
    public function headerEvaluationGlobal(Formation $formation = null): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/evaluation-global.html.twig', array(
            "formation" => $formation
        ));
    }


    #[Route(path: '/footer-evaluation-global', name: 'pdf_footer_evaluation_global', methods: ['GET'])]
    public function footerEvaluationGlobal(Request $request): Response
    {
        $this->disableToolbar();
	// Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
	sleep(2);
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/evaluation-global.html.twig', array(
            "offset" => $pageOffset
        ));
    }

    #[Route(path: '/footer-coordinateur/{coordinateur}/{hasDPC}', name: 'pdf_footer_coordinateur', methods: ['GET'])]
    public function footerCoordinateur(Request $request, Person $coordinateur, $hasDPC): Response
    {
        $this->disableToolbar();
        // Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/coordinateur.html.twig', array(
            "offset" => $pageOffset,
            "coordinateur" => $coordinateur,
            "hasDPC" => $hasDPC,
        ));
    }

    /*** FOOTERS ***/
    #[Route(path: '/footer-dpc', name: 'pdf_footer_dpc', methods: ['GET'])]
    public function footerDpc(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/dpc.html.twig');
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param $start
     * @param $batch
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary-formation/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_certificate_participation_horary_formation_html', methods: ['GET'])]
    public function certificateParticipationHoraryFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();
        return $this->render('pdf/certificate_participation_horary_all.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary-formation-opti/{id}/{financeSousMode}/html', name: 'pdf_certificate_participation_horary_formation_opti_html', methods: ['GET'])]
    public function certificateParticipationHoraryOptiFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode): Response
    {

        $participations = [];

        foreach($formation->getParticipations() as $participation) {
            if($participation->getFinanceSousMode() == $financeSousMode) {
                $participations[] = $participation;
            }
        }

        $this->disableToolbar();
        return $this->render('pdf/certificate_participation_horary_all.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/tcs-answers/{id}/{financeSousMode}/html', name: 'pdf_tcs_answers_html', methods: ['GET'])]
    public function tcsAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode): Response
    {
        $participations = [];
        $questionnaireTcs = $formation->getAudit();

        foreach($formation->getParticipations() as $participation) {
            if ($participation->getFinanceSousMode() == $financeSousMode && $participation->isStepCompleted("form_presession")) {
                $participations[] = $participation;
            }
        }

        $this->disableToolbar();
        return $this->render('pdf/tcs_answers.html.twig', array(
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs,
            'participations' => $participations
        ));
    }

    #[Route(path: '/emargement/{id}/{financeSousMode}/html', name: 'pdf_emargement_html', methods: ['GET'])]
    #[Route(path: '/emargement/{id}/{financeSousMode}/{unityPosition}/html', name: 'pdf_emargement_unity_html', methods: ['GET'])]
    public function emargement(Formation $formation, FinanceSousMode $financeSousMode, Request $request, $unityPosition = null): Response
    {
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));
        $this->disableToolbar();

        $formateurs = $formation->getFormateurs();

        $template = $financeSousMode->isHorsDPC() || $financeSousMode->isFif() || $financeSousMode->isActalians() ? 'emargement_hors_dpc' : 'emargement';

        return $this->render('pdf/' . $template . '.html.twig', array(
            'participations' => $participations,
            'formation' => $formation,
            'formateurs' => $formateurs,
            'date' => $request->query->get('date'),
            'unityPosition' => $unityPosition,
        ));
    }




    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-contract', name: 'pdf_footer_contract', methods: ['GET'])]
    public function footerContract(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/contract.html.twig');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-audit', name: 'pdf_footer_audit', methods: ['GET'])]
    public function footerAudit(Request $request): Response
    {
        $this->disableToolbar();
        $pageOffset = $request->query->get('offset');
        $padding = $request->query->get('padding');
        return $this->render('pdf/footers/audit.html.twig', array('offset' => $pageOffset, 'padding' => $padding));
    }

    /**
     * @param Formation $formation
     * @param $auditId
     * @param int $patient
     * @param Request $request
     * @param FormManagerFactory $formManagerFactory
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $auditId
     */
    #[Route(path: '/audit/empty/{id}/{auditId}/{patient}/html', name: 'pdf_audit_empty_html')]
    public function auditEmpty(Formation $formation, $auditId, $patient, Request $request, FormManagerFactory $formManagerFactory): Response
    {
        $patient = (int) $patient;
        $auditId = (int) $auditId;

        $questions = $auditId === AuditManager::SECOND_AUDIT && $formation->isVignette() ? $formation->getAudit2()->getQuestions() : $formation->getAudit()->getQuestions();
        $questions->forAll(function($key, $question) {
            $question->setIndex($key);
            return true;
        });

        $participation = new Participation();
        $participation->setParticipant(new Participant());
        $participation->setFormation($formation);

        $auditManager = $formManagerFactory->getAuditManager($participation, $auditId, $patient);

        $audit = $auditManager->getAudit();
        $form = $auditManager->getForm();

        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/audit.html.twig', array(
            'patient' => $patient,
            "participation" => $participation,
            'form' => $form,
            'auditLabel' => $formation->getAuditLabel($auditId),
            'auditId' => $auditId,
            'audit' => $audit
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/etutorat/empty/{id}/html', name: 'pdf_etutorat_empty_html')]
    public function etutoratEmpty(Formation $formation, Request $request): Response
    {
        $participation = new Participation();
        $participation->setFormation($formation);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/etutorat.html.twig', array(
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
            'forPdf' => true,
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */


    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */


    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/fiche-synthese/full/{id}/{participation}/html', name: 'pdf_synthese_full_html')]
    public function syntheseFull(EntityManagerInterface $entityManager, $participation, Request $request): Response
    {
        $participant = null;
        if ($participation != "null") {
            $participation = $entityManager->getRepository(Participation::class)->findOneBy(array('id' => $participation));
            $participant = $participation->getParticipant();
        }

        // $participation = new Participation();
        // $participation->setFormation($formation);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation,
            "synthese" => true,
            "disabled" => false
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/synthese.html.twig', array(
            "participant" => $participant,
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
        ));
    }





    /**
     * @param Participation $participation
     * @param int                                       $surveyId
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey/{id}/{surveyId}/html', name: 'pdf_survey_html')]
    public function survey(Participation $participation, $surveyId, Request $request, FormManagerFactory $formManagerFactory): Response
    {
        $surveyId = (int) $surveyId;

        $surveyManager = $formManagerFactory->getSurveyManager($participation, $surveyId);

        $form = $surveyManager->getForm();

        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/survey.html.twig', array(
            'participation' => $participation,
            'surveyId' => $surveyId,
            'form' => $form,
            'surveyLabel' => $participation->getFormation()->getQuestionnaire()->getLabel()
        ));
    }

    /**
     * @param Formation $formation
     * @param int                                   $auditId
     * @return Response
     */
    #[Route(path: '/audit-answers/{id}/{financeSousMode}/{auditId}/{start}/{batch}/html', name: 'pdf_audit_answers_html')]
    public function auditAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $auditId, $start, $batch): Response
    {
        $nbPatients = $formation->getAudit()->getNbPatients();

        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();

        return $this->render('pdf/audit_answers.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'nbPatients' => $nbPatients,
            'auditId' => (int) $auditId
        ));
    }

    /**
     * @param Formation $formation
     * @param int                                       $surveyId
     * @return Response
     */
    #[Route(path: '/survey-answers/{id}/{financeSousMode}/{surveyId}/{start}/{batch}/html', name: 'pdf_survey_answers_html')]
    public function surveyAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $surveyId, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();

        return $this->render('pdf/survey_answers.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'surveyId' => (int) $surveyId
        ));
    }

    /**
     * @param Formation $formation
     * @return Response
     */
    #[Route(path: '/fusion-header/{id}/html', name: 'pdf_fusion_header_html')]
    public function fusionHeader(Formation $formation): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/fusion/header.html.twig', array(
            'formation' => $formation,
        ));
    }

    /**
     * @param Formation $formation
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fusion-methodologie/{id}/html', name: 'pdf_fusion_methodologie_html')]
    public function fusionMethodologie(Formation $formation, Request $request): Response
    {
        $this->disableToolbar();
        $isActalians = json_decode($request->query->get('actalians'), true);
        $template = $isActalians ? 'pdf/fusion/methodologie_actalians.html.twig' : 'pdf/fusion/methodologie.html.twig';
        return $this->render($template, array("formation" => $formation));
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-summary/html', name: 'pdf_fusion_summary_html')]
    public function fusionSummary(Request $request): Response
    {
        $this->disableToolbar();
        $summary = json_decode($request->request->get('summary'), true);
        return $this->render('pdf/fusion/summary.html.twig', array(
            'summary' => $summary,
        ));
    }










    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param $start
     * @param $batch
     * @return Response
     */
    #[Route(path: '/document_realisation/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_document_realisation_formation_html', methods: ['GET'])]
    public function documentRealisationFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();
        return $this->render('pdf/document_realisation.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @return Response
     */
    #[Route(path: '/header-realisation/{id}', name: 'pdf_header_realisation', defaults: ['id' => null], methods: ['GET'])]
    public function headerRealisation(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/realisation.html.twig');
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/attestation-presence/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_attestation-presence_html', methods: ['GET'])]
    public function attestationPresenceDocument(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {

        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);
        $this->disableToolbar();

        return $this->render('pdf/attestation_presence.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    #[Route(path: '/header-attestation-presence-document', name: 'pdf_header_attestation_presence_document', methods: ['GET'])]
    public function headerAttestationPresenceDocument(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/presence.html.twig');
    }

     #[Route(path: '/footer-plaquette-programme', name: 'pdf_footer_plaquette_programme', methods: ['GET'])]
    public function footerPlaquette(Request $request): Response
     {
        $this->disableToolbar();
        // Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/plaquette-programme.html.twig', array(
            "offset" => $pageOffset
        ));
    }
}
