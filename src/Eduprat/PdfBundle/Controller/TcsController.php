<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\TcsPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class TcsController extends AbstractPdfController
{
    private TcsPDF $tcsPDF;

    #[Required]
    public function withTcsPDF(TcsPDF $tcsPDF): void
    {
        $this->tcsPDF = $tcsPDF;
    }

    #[Route(path: '/pdf/tcs/{id}/pdf/{token}', name: 'pdf_tcs_pdf', methods: ['GET'])]
    public function tcs(Request $request, Participation $participation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $response = $this->tcsPDF->generate(
            ['participation' => $participation],
            ['formation' => $participation->getFormation()],
            ['offset' => $pageOffset]
        );

        $filename = $participation->getFormation()->getProgramme()->getReference()."-tcs-".$participation->getParticipant()->getLastname().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
