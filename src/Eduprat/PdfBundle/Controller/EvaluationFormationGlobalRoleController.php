<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\EvaluationFormationGlobalRolePDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationFormationGlobalRoleController extends AbstractPdfController
{

    private EvaluationFormationGlobalRolePDF $evaluationFormationGlobalRolePDF;

    #[Required]
    public function withEvaluationFormationGlobalRolePDF(EvaluationFormationGlobalRolePDF $evaluationFormationGlobalRolePDF): void
    {
        $this->evaluationFormationGlobalRolePDF = $evaluationFormationGlobalRolePDF;
    }

    #[Route(path: '/pdf/formation/{formation}/{role}/pdf/{token}', name: 'pdf_evaluation_formation_global_role_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormation(Formation $formation, string $role, string $token): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $filename = "evaluation-formation-{$formation->getProgramme()->getReference()}-session-{$formation->getSessionNumber()}-{$role}.pdf";

        $response = $this->evaluationFormationGlobalRolePDF->generate(
            ['formation' => $formation, 'role' => $role],
            [],
            []
        );

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
