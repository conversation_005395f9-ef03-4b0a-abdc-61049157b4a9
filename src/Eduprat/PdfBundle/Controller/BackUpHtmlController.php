<?php

namespace Eduprat\PdfBundle\Controller;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\EvaluationCoordinatorByCoordinatorManager;
use Eduprat\AdminBundle\Services\EvaluationProgrammeManager;
use Ed<PERSON>rat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\EvaluationCoordinator;
use Eduprat\DomainBundle\Entity\EvaluationProgramme;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

trait BackUpHtmlController
{
    /*** HEADERS ***/
    /**
     * @param Participation|null $participation
     * @return Response
     */
    #[Route(path: '/header-participation/{id}', name: 'pdf_header_participation', defaults: ['id' => null], methods: ['GET'])]
    public function headerParticipation(Participation $participation = null): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/participation.html.twig', array(
            "participation" => $participation
        ));
    }

    #[Route(path: '/header-dpc-attendance', name: 'pdf_header_dpc_attendance', methods: ['GET'])]
    public function headerDpcAttendance(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/dpc.html.twig', array(
            'title' => "Attestation de suivi des étapes non présentielles d'un programme / d'une action de DPC"
        ));
    }

    #[Route(path: '/header-admin_budget_cr_total', name: 'pdf_header_admin_budget_cr_total', methods: ['GET'])]
    public function headerAdminBudgetCrTotal(TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/budget-coordinator.html.twig', array(
            'title' => $translator->trans('compensation.crTitle')
        ));
    }

    #[Route(path: '/footer-budget-coordinator', name: 'pdf_footer_budget_coordinator', methods: ['GET'])]
    public function footerBudgetCoordinator(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/budget-coordinator.html.twig');
    }

    #[Route(path: '/header-coordinator_honorary', name: 'pdf_header_coordinator_honorary', methods: ['GET'])]
    public function headerCoordinatorHonorary(TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/budget-coordinator.html.twig', array(
            'title' => $translator->trans('compensation.detailsCrTitle')
        ));
    }

    #[Route(path: '/header-restitution', name: 'pdf_header_restitution')]
    public function headerRestitution(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/restitution.html.twig');
    }

    #[Route(path: '/certificate-attendance/{id}/html', name: 'pdf_certificate_attendance_html', methods: ['GET'])]
    public function certificateAttendance(Formation $formation): Response
    {
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipations();
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));
        return $this->render('pdf/certificate_attendance.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    #[Route(path: '/coordinator_honorary/{formation}/{coordinator}/{n1}/html', name: 'pdf_coordinator_honorary_html', methods: ['GET'])]
    public function coordinatorHonorary(Formation $formation, Coordinator $coordinator, CoordinatorHonorary $coordinatorHonoraryService, $n1 = null): Response
    {
        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int) $openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
        }

        $compensations = $coordinatorHonoraryService->calculHonorarySession($formation, $coordinator, $n1);

        $totalHonorary = $coordinatorHonoraryService->calculTotalHonorary($formation, $coordinator, true, false, true, $n1);

        return $this->render('pdf/coordinator_honorary.html.twig', array(
            'compensations' => $compensations,
            'formation' => $formation,
            'coordinator' => $coordinator,
            'newHonorary' => $formation->getStartDate() >= new \DateTime($this->getParameter('honoraires.migration_date')),
            'totalHonorary' => $totalHonorary,
            'openingDate' => $openingDate,
            'closingDate' => $closingDate,
            'year' => $year,
            'n1' => $n1,
        ));
    }

    /**
     * @param Coordinator $coordinator
     * @param Programme $programme
     * @return Response
     */
    #[Route(path: '/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/html/', name: 'pdf_evaluation_coordinator_by_coordinator_html', methods: ['GET'])]
    public function evaluationCoordinatorByCoordinator(Coordinator $coordinator, Programme $programme, EntityManagerInterface $entityManager): Response
    {

        /** @var EvaluationProgramme[] $evaluations */
        $evaluationsP = $entityManager->getRepository(EvaluationProgramme::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitionsP = [];

        for ($i = 1; $i <= EvaluationProgrammeManager::NB_QUESTION; $i++) {
            $repartitionsP[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluationsP as $evaluation) {
            $repartitionsP[$evaluation->getQuestion()]["count"]++;
            $repartitionsP[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitionsP as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        /** @var EvaluationProgramme[] $evaluations */
        $evaluationsC = $entityManager->getRepository(EvaluationCoordinator::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitionsC = [];

        for ($i = 1; $i <= EvaluationCoordinatorByCoordinatorManager::NB_QUESTION-1; $i++) {
            $repartitionsC[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluationsC as $evaluation) {
            if($evaluation->getQuestion() != 5) {
                $repartitionsC[$evaluation->getQuestion()]["count"]++;
                $repartitionsC[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
            }
            else {
                $commentaire = $evaluation->getAnswer();
            }
        }

        foreach ($repartitionsC as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        $participants = array();
        foreach($programme->getFormations() as $formation) {
            $participants[$formation->getId()] = $formation->getParticipants()->count();
        }

        return $this->render('pdf/evaluation_coordinator_by_coordinator.html.twig', array(
            'evaluationP' => $repartitionsP,
            'evaluationC' => $repartitionsC,
            'coordinator' => $coordinator,
            'programme' => $programme,
            'commentaire' => $commentaire,
            'participants' => $participants
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-restitution', name: 'pdf_footer_restitution', methods: ['GET'])]
    public function footerRestitution(Request $request): Response
    {
        $this->disableToolbar();
        $pageOffset = $request->query->get('offset');
        $padding = $request->query->get('padding');
        return $this->render('pdf/footers/restitution.html.twig', array(
            'title' => $request->query->get('title'),
            'offset' => $pageOffset,
            'padding' => $padding
        ));
    }
}