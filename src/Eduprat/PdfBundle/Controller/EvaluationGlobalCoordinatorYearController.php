<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Eduprat\PdfBundle\Services\EvaluationGlobalCoordinatorYearPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationGlobalCoordinatorYearController extends AbstractPdfController
{
    private EvaluationGlobalCoordinatorYearPDF $evaluationGlobalCoordinatorYearPDF;

    #[Required]
    public function withEvaluationGlobalCoordinatorYearPDF(EvaluationGlobalCoordinatorYearPDF $evaluationGlobalCoordinatorYearPDF): void
    {
        $this->evaluationGlobalCoordinatorYearPDF = $evaluationGlobalCoordinatorYearPDF;
    }

    #[Route(path: '/pdf/coordinator/{person}/{year}/pdf/{token}', name: 'pdf_evaluation_global_coordinator_year_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalCoordinatorYear(Person $person, $year, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($person, $token);

        $response = $this->evaluationGlobalCoordinatorYearPDF->generate(
            ['person' => $person, 'year' => $year]
        );

        $filename = "evaluation-global-coordinator-{$person->getFullname()}-{$year}.pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
