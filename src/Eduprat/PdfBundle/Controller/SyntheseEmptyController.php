<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\SyntheseEmptyPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class SyntheseEmptyController extends AbstractPdfController
{

    private SyntheseEmptyPDF $syntheseEmptyPDF;

    #[Required]
    public function withSyntheseEmptyPDF(SyntheseEmptyPDF $syntheseEmptyPDF): void
    {
        $this->syntheseEmptyPDF = $syntheseEmptyPDF;
    }

    #[Route(path: '/pdf/fiche-synthese/empty/{id}/{participation}/pdf/{token}', name: 'pdf_synthese_empty_pdf', methods: ['GET'])]
    public function syntheseEmpty(Request $request, Formation $formation, $participation, string $token): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $filename = "synthese-vierge-".$formation->getProgramme()->getReference().".pdf";

        $response = $this->syntheseEmptyPDF->generate(
            ['formation' => $formation, 'participationId' => $participation, 'request' => $request],
            ['formation' => $formation],
            ['offset' => $pageOffset]
        );

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
