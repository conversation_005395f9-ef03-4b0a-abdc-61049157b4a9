<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\PdfBundle\Services\EvaluationGlobalTopoPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationGlobalTopoController extends AbstractPdfController
{
    private EvaluationGlobalTopoPDF $evaluationGlobalTopoPDF;

    #[Required]
    public function withEvaluationGlobalTopoPDF(EvaluationGlobalTopoPDF $evaluationGlobalTopoPDF): void
    {
        $this->evaluationGlobalTopoPDF = $evaluationGlobalTopoPDF;
    }

    #[Route(path: '/pdf/topo/{year}/{role}/{title}/pdf/{token}', name: 'pdf_evaluation_global_topo_pdf', requirements: ['title' => '.+'], methods: ['GET'])]
    public function pdfEvaluationGlobalTopo(string $title, $role, $year, $token = null): Response
    {
        // Note: No token validation for this route based on original code
        
        $response = $this->evaluationGlobalTopoPDF->generate(
            ['title' => $title, 'role' => $role, 'year' => $year],
        );

        $filename = "evaluation-global-topo-{$title}-{$role}-{$year}.pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
