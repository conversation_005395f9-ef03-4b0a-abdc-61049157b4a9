<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\CertificateParticipationPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class CertificateParticipationPdfController extends AbstractPdfController
{
    private CertificateParticipationPDF $certificateParticipationPDF;

    #[Required]
    public function withCertificateParticipationPDF(CertificateParticipationPDF $certificateParticipationPDF): void
    {
        $this->certificateParticipationPDF = $certificateParticipationPDF;
    }

    #[Route(path: '/pdf/certificate-participation/{id}/pdf/{token}', name: 'pdf_certificate_participation_pdf', methods: ['GET'])]
    public function certificateParticipation(Request $request, Participation $participation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $filename = $participation->getFormation()->getProgramme()->getReference() . "-attestation-participation-" . $participation->getParticipant()->getLastname() . ".pdf";

        $response =  $this->certificateParticipationPDF->generate(
            ['participation' => $participation], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}