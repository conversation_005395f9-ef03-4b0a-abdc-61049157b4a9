<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\RestitutionGroupePDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class RestitutionAuditGroupe2Controller extends AbstractPdfController
{

    private RestitutionGroupePDF $restitutionGroupePDF;

    #[Required]
    public function withRestitutionGroupePDF(RestitutionGroupePDF $restitutionGroupePDF): void
    {
        $this->restitutionGroupePDF = $restitutionGroupePDF;
    }

    #[Route(path: '/pdf/restitution-audit-groupe-2/{id}/pdf/{token}', name: 'pdf_restitution_audit_groupe_2_pdf', methods: ['GET'])]
    public function restitutionAuditGroupe2(Request $request, Formation $formation, $token = null)
    {

        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);

        $scoringOrPr = $formation->isVignette() ? "scoring" : 'restitution';
        $filename = $scoringOrPr."-audit-2-".$formation->getProgramme()->getReference().".pdf";

        $response =  $this->restitutionGroupePDF->generate(
            ['formation' => $formation], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}