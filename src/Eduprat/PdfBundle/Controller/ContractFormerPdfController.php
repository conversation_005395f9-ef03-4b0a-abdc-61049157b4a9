<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON>rat\PdfBundle\Services\ContractFormerPDF;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Service\Attribute\Required;

#[Route(path: '/pdf')]
class ContractFormerPdfController extends AbstractPdfController
{
    private ContractFormerPDF $contractFormerPDF;

    #[Required]
    public function withContractFormerPDF(ContractFormerPDF $contractFormerPDF): void
    {
        $this->contractFormerPDF = $contractFormerPDF;
    }

    #[Route(path: '/contract-former/{id}/{formation}/pdf/{token}', name: 'pdf_contract_former_pdf', methods: ['GET'])]
    public function contractFormer(Formateur $former, Formation $formation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $parameters = [
            'formation' => $formation,
            'former' => $former,
        ];
        $response =  $this->contractFormerPDF->generate($parameters, null, []);

        $filename = $formation->getProgramme()->getReference()."-contrat-".$former->getPerson()->getLastname().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
