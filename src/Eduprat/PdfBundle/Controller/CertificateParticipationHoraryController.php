<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\CertificateParticipationHoraryPDF;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Contracts\Service\Attribute\Required;

class CertificateParticipationHoraryController extends AbstractPdfController
{
    private CertificateParticipationHoraryPDF $certificateParticipationHoraryPDF;

    #[Required]
    public function withCertificateParticipationHoraryPDF(CertificateParticipationHoraryPDF $certificateParticipationHoraryPDF): void
    {
        $this->certificateParticipationHoraryPDF = $certificateParticipationHoraryPDF;
    }

    #[Route(path: '/pdf/certificate-participation-horary/{id}/pdf/{token}', name: 'pdf_certificate_participation_horary_pdf', methods: ['GET'])]
    public function certificateParticipationHorary(Request $request, Participation $participation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $filename = $participation->getFormation()->getProgramme()->getReference()."-attestation-participation-".$participation->getParticipant()->getLastname().".pdf";

        $paramsHeader = [];
        $paramsFooter = ['pageOffset' => $pageOffset];

        $response = $this->certificateParticipationHoraryPDF->generate(
            ['participation' => $participation], 
            $paramsHeader, 
            $paramsFooter
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
