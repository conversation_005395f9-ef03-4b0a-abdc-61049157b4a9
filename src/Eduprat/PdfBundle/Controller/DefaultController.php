<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Ed<PERSON>rat\AuditBundle\Services\AuditManager;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Ed<PERSON>rat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

#[Route(path: '/pdf')]
class DefaultController extends AbstractPdfController
{
    use BackUpPdfController;

    public function denyAccessIfInvalidToken($object, $token, $message = 'Access Denied.'): void
    {
        if (!($this->authorizationChecker->isGranted('IS_AUTHENTICATED_FULLY') && $this->isGranted('ROLE_SUPER_ADMIN')) && method_exists($object, 'getToken') && $object->getToken() !== $token) {
            throw new AccessDeniedHttpException();
        }
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode|null $financeSousMode
     * @param null $token
     * @param                                           $type
     * @param Request $request
     */
    #[Route(path: '/formation-generate-file/{type}/{id}/{financeSousMode}/generate/{token}', name: 'pdf_formation_generate_file', methods: ['GET'], defaults: ['financeSousMode' => null])]
    public function formationGenerateFile(Formation $formation, $type, FinanceSousMode $financeSousMode = null, $token = null): JsonResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);
        if ($type === "fusion") {
            $command = sprintf("eduprat:pdf_merged %s %s", $formation->getId(), $financeSousMode->getId());
        } else {
            $command = sprintf("eduprat:pdf %s %s %s", $formation->getId(), $type, $financeSousMode->getId());
        }

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf('php %s/bin/console %s', $projectDir, $command);
        $cmd = sprintf("timeout -k 5s 43200s %s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(["status" => "ok", "pid" => $pid]);
    }

    /**
     * @param Formation $formation
     * @param null                                      $token
     * @param                                           $type
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/formation-generate-file-get/{type}/{id}/generate/{token}/{financeSousMode}', name: 'pdf_formation_generate_file_get', methods: ['GET'])]
    public function formationGenerateFileGet(Formation $formation, $type, FinanceSousMode $financeSousMode = null, $token = null): BinaryFileResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $filename = "";

        if($type == 'fusion') {
            $filename = "tracabilite-%s-%s.pdf";
        }
        else if ($type == 'restitution_audit') {
            $scoringOrPr = $formation->isVignette() ? "scoring" : 'restitution';
            $filename = $scoringOrPr."-%s-%s.pdf";
        }
        else if ($type == 'audit1') {
            $filename = "audit1-reponses-%s-%s.pdf";
        }
        else if ($type == 'audit2') {
            $filename = "audit2-reponses-%s-%s.pdf";
        }
        else if ($type == 'participation') {
            $filename = "attestations-participation-%s-%s.pdf";
        }
        else if ($type == 'attestation_honneur') {
            $filename = "attestations-honneurs-%s.pdf";
        }
        else if ($type == 'attestation_honneur_n1') {
            $filename = "attestations-honneurs-n1-%s.pdf";
        }
        else if ($type === 'prerestitution_audit') {
            $scoringOrPr = $formation->isVignette() ? "scoring" : 'pre-restitution';
            $filename = $scoringOrPr."-%s-%s.pdf";
        }

        $filename = sprintf($filename, urlencode($formation->getProgramme()->getReference()), $financeSousMode ? $financeSousMode->getId() : "");

        if ($formation->hasGeneratedFile($type, $financeSousMode) && $formation->generateFileIsFinished($type, $financeSousMode)) {
            $response = new BinaryFileResponse($formation->getGeneratedFilePath($type, $financeSousMode));

            if($filename) {
                $response->setContentDisposition(
                    ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                    $filename);
            }
            return $response;
        }

        throw new NotFoundHttpException();
    }

    /**
     * @param Formation $formation
     * @param int $auditId
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/audit/empty/{id}/{auditId}/pdf/{token}', name: 'pdf_audit_empty_pdf', requirements: ['id' => '^\d+$', 'auditId' => '^\d+$'], methods: ['GET'])]
    public function auditEmpty(Request $request, Formation $formation, int $auditId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        $nbPatients = (int) $auditId === AuditManager::SECOND_AUDIT && $formation->isVignette() ? $formation->getAudit2()->getNbPatients() : $formation->getAudit()->getNbPatients();

        for($i=1; $i<=$nbPatients; $i++) {
            $pageUrls[] = $this->generateUrl('pdf_audit_empty_html', array('id' => $formation->getId(), 'auditId' => $auditId, 'patient' => $i), UrlGeneratorInterface::ABSOLUTE_URL);
        }

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('javascript-delay', 2000);

        $filename = "audit-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/etutorat/empty/{id}/pdf/{token}', name: 'pdf_etutorat_empty_pdf', methods: ['GET'])]
    public function etutoratEmpty(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_etutorat_empty_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "etutorat-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param Participation $participation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fiche-synthese/full/{id}/{participation}/pdf/{token}', name: 'pdf_synthese_full_pdf', methods: ['GET'])]
    public function syntheseFull(Request $request, Formation $formation, $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_synthese_full_html', array('id' => $formation->getId(), 'participation' => $participation), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "synthese-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Participation $participation
     * @param int                                       $surveyId
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey/{id}/{surveyId}/pdf/{token}', name: 'pdf_survey_pdf', methods: ['GET'])]
    public function survey(Request $request, Participation $participation, $surveyId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageUrl = $this->generateUrl('pdf_survey_html', array('id' => $participation->getId(), 'surveyId' => $surveyId), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $participation->getFormation()->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "questionnaire-".$surveyId."-".$participation->getParticipant()->getFullname()."-".$participation->getFormation()->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param null                                       $token
     * @return Response
     */
    #[Route(path: '/fusion-header/{id}/pdf/{token}', name: 'pdf_fusion_header_pdf', methods: ['GET'])]
    public function fusionHeader(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_fusion_header_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $pageOffset = $request->query->get('offset');
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => $pageOffset, 'padding' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-methodologie/{id}/pdf', name: 'pdf_fusion_methodologie_pdf', methods: ['GET'])]
    public function fusionMethodologie(Formation $formation, Request $request)
    {
        $pageUrl = $this->generateUrl('pdf_fusion_methodologie_html', array('id' => $formation->getId(), 'actalians' => json_decode($request->query->get('actalians'), true)), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $pageOffset = $request->query->get('offset');
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => $pageOffset, 'padding' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('footer-spacing', 0);
        $this->snappy->setOption('margin-bottom', 25);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-summary/pdf', name: 'pdf_fusion_summary_pdf', methods: ['POST'])]
    public function fusionSummary(Request $request): Response
    {
        $pageUrl = $this->generateUrl('pdf_fusion_summary_html', array(), UrlGeneratorInterface::ABSOLUTE_URL);

        $postdata = http_build_query(
            array(
                'summary' => $request->request->get('summary')
            )
        );
        $opts = array('http' =>
              array(
                  'method'  => 'POST',
                  'header'  => 'Content-type: application/x-www-form-urlencoded',
                  'content' => $postdata
              ),
              "ssl" => array(
                  "verify_peer" => false,
                  "verify_peer_name" => false,
              ),
        );
        $context  = stream_context_create($opts);
        $html = file_get_contents($pageUrl, false, $context);

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 5);
        $this->snappy->setOption('margin-right', 5);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return new Response(
            $this->snappy->getOutputFromHtml($html),
            Response::HTTP_OK,
            array(
                'Content-Type'          => 'application/pdf',
                'Content-Disposition'   => 'inline; filename="file.pdf"'
            )
        );
    }

    /** todo à remove à terme */

    public function generatePdfAndResponse($downloadedPlaquetteFile, $user, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $pageUrl, $filename)
    {
        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $new_download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);
        $filename = $this->stripAccents($filename);
        try {
            $directLink = sprintf(DownloadedPlaquetteFile::PATHS_DIR[$downloadedPlaquetteFile->getType()], $user->getId(), $filename);
            $filefullroot = $this->parameterBag->get('kernel.project_dir').'/public/'.$directLink;
            $downloadedPlaquetteFile->setDirectLink($directLink);
            $downloadedPlaquetteFileManager->removeFile($downloadedPlaquetteFile);
            $this->snappy->generate($pageUrl, $filefullroot);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            return new JsonResponse([
                'etat' => false,
                'error' => $e->getMessage(),
                'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                    'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
                )),
            ]);
        }
        $downloadedPlaquetteFileManager->addDownloadPlaquetteFile($downloadedPlaquetteFile);
        return new JsonResponse([
            'etat' => true,
            'pdf' => $filename,
            'filefullroot' => $filefullroot,
            'directLink' => $directLink,
            'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
            )),
        ]);
    }

    public function stripAccents($str): string
    {
        return strtr(utf8_decode($str), utf8_decode('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ'), 'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }

    public function getPdfResponse($filename, $pageUrls) {
        try {
            $content = $this->snappy->getOutput((array)$pageUrls);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
        return new Response(
            $content,
            Response::HTTP_OK,
            array(
                'Content-Type'          => 'application/pdf',
                'Content-Disposition'   => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
