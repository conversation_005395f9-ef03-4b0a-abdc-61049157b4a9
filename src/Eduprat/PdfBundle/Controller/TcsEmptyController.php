<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\TcsEmptyPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class TcsEmptyController extends AbstractPdfController
{
    private TcsEmptyPDF $tcsEmptyPDF;

    #[Required]
    public function withTcsEmptyPDF(TcsEmptyPDF $tcsEmptyPDF): void
    {
        $this->tcsEmptyPDF = $tcsEmptyPDF;
    }

    #[Route(path: '/pdf/tcs/empty/{id}/pdf/{token}', name: 'pdf_tcs_empty_pdf', methods: ['GET'])]
    public function tcsEmpty(Request $request, Formation $formation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $response = $this->tcsEmptyPDF->generate(
            ['formation' => $formation],
            ['formation' => $formation],
            ['offset' => $pageOffset]
        );

        $filename = "tcs-vierge-".$formation->getProgramme()->getReference().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
