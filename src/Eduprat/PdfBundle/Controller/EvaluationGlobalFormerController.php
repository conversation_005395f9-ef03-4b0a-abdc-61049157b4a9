<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\PdfBundle\Services\EvaluationGlobalFormerPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationGlobalFormerController extends AbstractPdfController
{
    private EvaluationGlobalFormerPDF $evaluationGlobalFormerPDF;

    #[Required]
    public function withEvaluationGlobalFormerPDF(EvaluationGlobalFormerPDF $evaluationGlobalFormerPDF): void
    {
        $this->evaluationGlobalFormerPDF = $evaluationGlobalFormerPDF;
    }

    #[Route(path: '/pdf/programme/{formation}/former/{former}/pdf/{token}', name: 'pdf_evaluation_formation_global_former_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormer(Formation $formation, Formateur $former, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($former, $token);

        $response = $this->evaluationGlobalFormerPDF->generate(
            ['formation' => $formation, 'former' => $former]
        );

        $filename = "evaluation-global-former-{$former->getPerson()->getFullname()}-{$formation->getProgramme()->getReference()}.pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
