<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Ed<PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\PdfBundle\DTO\ParamsPDFDTO;
use Eduprat\PdfBundle\Services\ProgrammePdf;
use Eduprat\PdfBundle\Services\PdfSaver;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Service\Attribute\Required;

class ProgrammePdfController extends AbstractPdfController
{

    private ProgrammePdf $programmePdf;

    //> @TODO remove this when LoggerInterface $pdfLogger will be added in parent class
    protected LoggerInterface $pdfLogger;
    // </ TODO
    #[Required]
    public function withProgrammePdf(ProgrammePdf $programmePdf): void
    {
        $this->programmePdf = $programmePdf;
    }

    //> @TODO remove this when LoggerInterface $pdfLogger will be added in parent class
    #[Required]
    public function withPdfLogger(LoggerInterface $pdfLogger): void
    {
        $this->pdfLogger = $pdfLogger;
    }
    // </ TODO

    #[Route(path: '/programmes/{id}/{favori}/pdf', name: 'pdf_programmes_pdf', methods: ['POST'])]
    public function programmes(Person $user, $favori, Request $request, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $token = null)
    {
        // $this->denyAccessIfInvalidToken($formation, $token);
        $downloadedPlaquetteFile = new DownloadedPlaquetteFile();
        $downloadedPlaquetteFile->setType(DownloadedPlaquetteFile::TYPE_PROGRAMME);
        $download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $downloadedPlaquetteFile);
        $download_plaquette_file_form->handleRequest($request);

        // si formulaire non valide ou non soumis
        if (!($download_plaquette_file_form->isSubmitted() && $download_plaquette_file_form->isValid())) {
            return new JsonResponse(['etat' => false]);
        }
        $downloadedPlaquetteFile->setPerson($user);

        $pageOffset = $request->query->get('offset');

        $paramsPDFDTO = new ParamsPDFDTO(
            [
                'user' => $user,
                'favori' => $favori,
            ],
            null,
            [
                'offset' => $pageOffset,
            ]
        );

        $filename = 'programmes-'.uniqid().'.pdf';

        return $this->generatePdfAndResponseLocal($downloadedPlaquetteFile, $paramsPDFDTO, $user, $downloadedPlaquetteFileManager, $filename);
    }

    public function generatePdfAndResponseLocal($downloadedPlaquetteFile, ParamsPDFDTO $paramsPDFDTO, UserInterface $user, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $filename): JsonResponse
    {
        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $new_download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);
        $filename = $this->stripAccents($filename);
        try {
            $directLink = sprintf(DownloadedPlaquetteFile::PATHS_DIR[$downloadedPlaquetteFile->getType()], $user->getId(), $filename);
            $filefullroot = $this->getParameter('kernel.project_dir').'/public/'.$directLink;
            $downloadedPlaquetteFile->setDirectLink($directLink);
            $downloadedPlaquetteFileManager->removeFile($downloadedPlaquetteFile);

            $pdfSaver = new PdfSaver($this->programmePdf);
            $pdfSaver->generate(
                $paramsPDFDTO->params,
                $paramsPDFDTO->paramsHeader,
                $paramsPDFDTO->paramsFooter,
                $filefullroot,
            );
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            return new JsonResponse([
                'etat' => false,
                'error' => $e->getMessage(),
                'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                    'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
                )),
            ]);
        }
        $downloadedPlaquetteFileManager->addDownloadPlaquetteFile($downloadedPlaquetteFile);
        return new JsonResponse([
            'etat' => true,
            'pdf' => $filename,
            'filefullroot' => $filefullroot,
            'directLink' => $directLink,
            'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
            )),
        ]);
    }

    protected function stripAccents($str): string
    {
        return strtr(mb_convert_encoding($str, 'ISO-8859-1', 'UTF-8'), mb_convert_encoding('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ', 'ISO-8859-1', 'UTF-8'), 'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }
}
