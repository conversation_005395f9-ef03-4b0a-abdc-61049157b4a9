<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\PdfBundle\Services\EvaluationGlobalFormerYearPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationGlobalFormerYearController extends AbstractPdfController
{
    private EvaluationGlobalFormerYearPDF $evaluationGlobalFormerYearPDF;

    #[Required]
    public function withEvaluationGlobalFormerYearPDF(EvaluationGlobalFormerYearPDF $evaluationGlobalFormerYearPDF): void
    {
        $this->evaluationGlobalFormerYearPDF = $evaluationGlobalFormerYearPDF;
    }

    #[Route(path: '/pdf/former/{person}/{year}/pdf/{token}', name: 'pdf_evaluation_global_former_year_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormerYear(Person $person, $year, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($person, $token);

        $response = $this->evaluationGlobalFormerYearPDF->generate(
            ['person' => $person, 'year' => $year]
        );

        $filename = "evaluation-global-former-{$person->getFullname()}-{$year}.pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
