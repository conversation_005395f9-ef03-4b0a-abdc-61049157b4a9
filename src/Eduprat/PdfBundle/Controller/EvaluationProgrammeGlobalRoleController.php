<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\PdfBundle\Services\EvaluationProgrammeGlobalRolePDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class EvaluationProgrammeGlobalRoleController extends AbstractPdfController
{

    private EvaluationProgrammeGlobalRolePDF $evaluationProgrammeGlobalRolePDF;

    #[Required]
    public function withEvaluationProgrammeGlobalRolePDF(EvaluationProgrammeGlobalRolePDF $evaluationProgrammeGlobalRolePDF): void
    {
        $this->evaluationProgrammeGlobalRolePDF = $evaluationProgrammeGlobalRolePDF;
    }

    #[Route(path: '/pdf/programme/{programme}/{role}/pdf/{token}', name: 'pdf_evaluation_programme_global_role_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalProgramme(Programme $programme, string $role, string $token): Response
    {
        $this->denyAccessIfInvalidToken($programme, $token);

        $filename = "evaluation-programme-{$programme->getReference()}-{$role}.pdf";

        $response = $this->evaluationProgrammeGlobalRolePDF->generate(
            ['programme' => $programme, 'role' => $role],
            [],
            []
        );

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
