<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\PdfBundle\Services\RestitutionAuditPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class RestitutionAuditController extends AbstractPdfController
{
    private RestitutionAuditPDF $restitutionAuditPDF;

    #[Required]
    public function withRestitutionAuditPDF(RestitutionAuditPDF $restitutionAuditPDF): void
    {
        $this->restitutionAuditPDF = $restitutionAuditPDF;
    }

    #[Route(path: '/pdf/restitution-audit/{id}/pdf/{token}', name: 'pdf_restitution_audit_pdf', methods: ['GET'])]
    public function restitutionFormation(Request $request, Participation $participation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $response = $this->restitutionAuditPDF->generate(
            ['participation' => $participation],
            [],
            ['offset' => $pageOffset]
        );

        $filename = $participation->getFormation()->getProgramme()->getReference()."-restitution-".$participation->getParticipant()->getLastname().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
