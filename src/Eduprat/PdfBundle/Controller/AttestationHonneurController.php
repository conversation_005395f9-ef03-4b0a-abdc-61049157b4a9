<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\PdfBundle\Services\AttestationHonneurPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class AttestationHonneurController extends AbstractPdfController
{
    private AttestationHonneurPDF $attestationHonneurPDF;

    #[Required]
    public function withAttestationHonneurPDF(AttestationHonneurPDF $attestationHonneurPDF): void
    {
        $this->attestationHonneurPDF = $attestationHonneurPDF;
    }

    #[Route(path: '/pdf/attestation-honneur/{id}/{participant}/{person}/{n1}/pdf/{token}', name: 'pdf_attestation_honneur_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function attestationHonneur(Request $request, Formation $formation, $token = null, Participant $participant = null, Person $person = null, $n1 = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $participantName = $participant ? $participant->getInvertedFullname() : "";

        $response = $this->attestationHonneurPDF->generate([
            'formation' => $formation,
            'participant' => $participant,
            'person' => $person,
            'n1' => $n1
        ]);

        $filename = sprintf("attestation-participation-%s-%s-%s.pdf", $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $participantName);

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
