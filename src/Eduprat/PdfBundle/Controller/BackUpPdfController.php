<?php

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Coordinator;
use Ed<PERSON>rat\DomainBundle\Entity\FinanceSousMode;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Ed<PERSON>rat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

trait BackUpPdfController
{
    #[Route(path: '/certificate-attendance/{id}/pdf/{token}', name: 'pdf_certificate_attendance_pdf', methods: ['GET'])]
    public function certificateAttendance(Formation $formation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_certificate_attendance_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_dpc_attendance', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_dpc', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $headerH = 58;
        $headerS = 10;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('margin-bottom', 24.2);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "attestations-participation-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param Coordinator $coordinator
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/coordinator_honorary/{formation}/{coordinator}/{n1}/pdf/{token}', name: 'pdf_coordinator_honorary_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function coordinatorHonorary(Request $request, Formation $formation, Coordinator $coordinator, $n1 = null, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_coordinator_honorary_html', array('formation' => $formation->getId(), 'coordinator' => $coordinator->getId(), 'n1' => $n1), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $coordinateur = $coordinator->getPerson()->getLastname().' '.$coordinator->getPerson()->getFirstname();

        $filename = "honoraire-coordinateur-".$coordinateur.".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Programme $programme
     * @param Person $person
     * @param null                                   $token
     * @return Response
     */
    #[Route(path: '/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/pdf/{token}', name: 'pdf_evaluation_coordinator_by_coordinator_pdf', methods: ['GET'])]
    public function evaluationCoordinatorByCoordinator(Coordinator $coordinator, Programme $programme, $token = null)
    {
        $this->denyAccessIfInvalidToken($programme, $token);

        $pageUrl = $this->generateUrl('pdf_evaluation_coordinator_by_coordinator_html', array('coordinator' => $coordinator->getId(), 'programme' => $programme->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_coordinator_honorary', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $headerH = 40;
        $headerS = 15;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('margin-left', 5);
        $this->snappy->setOption('margin-right', 5);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        $coordinateur = $coordinator->getPerson()->getInvertedFullname();

        $filename = "evaluation-coordinateur-".$coordinateur.".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param FormationActalians $formation
     * @param null $token
     * @return Response
     */
    #[Route(path: '/restitution-actalians/{id}/pdf/{token}', name: 'pdf_restitution_actalians_pdf', methods: ['GET'])]
    public function restitutionActalians(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_actalians_restitution', array('id' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "restitution-questionnaire-".$participation->getFormation()->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Person $person
     * @return Response
     */
    #[Route(path: '/coordinator-table/{id}/pdf', name: 'pdf_coordinator_table_pdf', methods: ['GET'])]
    public function coordinatorTable(Person $person)
    {
        $pageUrl = $this->generateUrl('pdf_coordinator_table', array('id' => $person->getId(), 'year' => 2016), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Landscape");

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param int $auditId
     * @param null $token
     * @return Response
     */
    #[Route(path: '/audit-answers/{id}/{financeSousMode}/{auditId}/pdf/{token}', name: 'pdf_audit_answers_pdf', methods: ['GET'])]
    public function auditAnswers(Formation $formation, FinanceSousMode $financeSousMode, $auditId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        $nbPatients = $formation->getAudit()->getNbPatients();
        foreach($formation->getParticipations() as $participation) {
            for($i=1; $i<=$nbPatients; $i++) {
                $pageUrls[] = $this->generateUrl('pdf_audit_answers_html', array('id' => $participation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'auditId' => $auditId, 'patient' => $i), UrlGeneratorInterface::ABSOLUTE_URL);
            }
        }

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_participation', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $headerH = 40;
        $headerS = 5;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('margin-left', 5);
        $this->snappy->setOption('margin-right', 5);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return $this->getPdfResponse("file.pdf", $pageUrls);
    }


    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param null $token
     * @return Response
     */
    #[Route(path: '/attestation-presence/{id}/pdf/{token}', name: 'pdf_attestation-presence_pdf', methods: ['GET'])]
    public function attestationPresence(Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_attestation-presence_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_attestation_presence_document', array("actalians" => false, 'isClasseVirtuelleZoom' => $formation->getProgramme()->isClasseVirtuelle() && false), UrlGeneratorInterface::ABSOLUTE_URL));
        // $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_traceability_document', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        // $this->snappy->setOption('margin-bottom', 24.2);

        $headerH = 30;
        $headerS = 5;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $formRef = $formation->getProgramme()->getReference();
        $filename = "doc-de-tracabilite-".$formRef.".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param int $surveyId
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey-answers/{id}/{financeSousMode}/{surveyId}/pdf/{token}', name: 'pdf_survey_answers_pdf', methods: ['GET'])]
    public function surveyAnswers(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $surveyId = 1, $token = null)
    {
//        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        foreach($formation->getParticipations() as $participation) {
            $pageUrls[] = $this->generateUrl('pdf_survey_answers_html', array('id' => $participation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'surveyId' => $surveyId), UrlGeneratorInterface::ABSOLUTE_URL);
        }

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        return $this->getPdfResponse("file.pdf", $pageUrls);
    }
}