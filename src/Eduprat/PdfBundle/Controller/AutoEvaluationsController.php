<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\AutoEvaluationsPDF;
use Gotenberg\Exceptions\GotenbergApiErrored;
use <PERSON><PERSON>\Gotenberg;
use Gotenberg\Stream;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class AutoEvaluationsController extends AbstractPdfController
{
    private AutoEvaluationsPDF $autoEvaluationsPDF;

    #[Required]
    public function withAutoEvaluationsPDF(AutoEvaluationsPDF $autoEvaluationsPDF): void
    {
        $this->autoEvaluationsPDF = $autoEvaluationsPDF;
    }

    #[Route(path: '/pdf/auto-evaluations/{id}/pdf/{token}', name: 'pdf_auto_evaluations_pdf', methods: ['GET'])]
    public function autoEvals(Request $request, Formation $formation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');
        $filename = sprintf("etutorat-1-%s-%s.pdf", $formation->getProgramme()->getReference(), $formation->getSessionNumber());

        // Paramètres pour le footer (équivalent à l'ancien footer-html)
        $paramsFooter = [];
        if ($pageOffset) {
            $paramsFooter['offset'] = $pageOffset;
        }

        try {
            $response = $this->autoEvaluationsPDF->generate(
                ['formation' => $formation],
                ['formation' => $formation], // paramsHeader
                $paramsFooter
            );

            return new Response(
                $response->getBody()->getContents(),
                Response::HTTP_OK,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => sprintf('inline; filename="%s"', $filename)
                ]
            );
        } catch (GotenbergApiErrored $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }
}
