<?php

namespace Eduprat\AdminBundle\Twig;

use Eduprat\AdminBundle\Services\AccountSwitcher;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class AccountSwitcherExtension extends AbstractExtension
{
    /**
     * @var AccountSwitcher
     */
    private $accountSwitcher;

    public function __construct(AccountSwitcher $accountSwitcher)
    {
        $this->accountSwitcher = $accountSwitcher;
    }

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('canSwitchAccount', $this->canSwitchAccount(...)),
            new TwigFunction('getSwitchableAccounts', $this->getSwitchableAccounts(...)),
        );
    }

    public function canSwitchAccount(): bool
    {
        return $this->accountSwitcher->canSwitchAccount();
    }

    public function getSwitchableAccounts() {
        return $this->accountSwitcher->getSwitchableAccounts();
    }

    public function getName(): string
    {
        return 'account_switcher_extension';
    }
}