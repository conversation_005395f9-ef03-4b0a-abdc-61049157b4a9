<?php

namespace Eduprat\AdminBundle\Twig;

use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class PreprodHeaderExtension extends AbstractExtension
{
    public function __construct(
        #[Autowire('%env(PREPROD_HEADER_TEXT)%')]
        private readonly ?string $envPreprodHeaderText = null,
    )
    {
    }

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('loadPreprodHeaderText', $this->loadPreprodHeaderText(...)),
        );
    }

    public function loadPreprodHeaderText(): string
    {
        return $this->envPreprodHeaderText;
    }

    public function getName(): string
    {
        return 'load_preprod_header_text_extension';
    }
}