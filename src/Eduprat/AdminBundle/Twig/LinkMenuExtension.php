<?php

namespace Eduprat\AdminBundle\Twig;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Routing\RouterInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class LinkMenuExtension extends AbstractExtension
{
    public function __construct(
        private RouterInterface $router,
        private Security $security
    ) {

    }

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('loadMenuAdminFormationIndexForNonAdmin', $this->loadMenuAdminFormationIndexForNonAdmin(...)),
        );
    }

    public function loadMenuAdminFormationIndexForNonAdmin(): string
    {
        return $this->router->generate('admin_formation_index', [
            'archived' => 'non',
            'year' => 'tous',
            'querySearchStartDate' => '2024-01-01',
            'querySearchEndDate' => '2025-04-15',
        ]);
    }


    public function getName()
    {
        return 'link_menu_extension';
    }
}
