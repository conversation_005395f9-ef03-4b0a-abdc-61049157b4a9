<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Tag;
use Eduprat\DomainBundle\Form\TagType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Vich\UploaderBundle\Mapping\PropertyMappingFactory;
use Vich\UploaderBundle\Storage\StorageInterface;

/**
 * Class TagController
 */
#[Route(path: '/tag')]
#[IsGranted('ROLE_WEBMASTER')]
class TagController extends EdupratController
{
    /**
     * Lists all Tag entities.
     *
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_tag_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $tags = $em->getRepository(Tag::class)->findBy(array(), array('name' => 'ASC'));
        return $this->render('admin/tag/index.html.twig', array(
            'tags' => $tags,
        ));
    }

    

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_tag_create')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function create(Request $request, EntityManagerInterface $entityManager, StorageInterface $storage, PropertyMappingFactory $propertyMappingFactory) {

        $tag = new Tag("");
        $form = $this->createForm(TagType::class, $tag);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($tag);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.tag.create.success');
            return $this->redirectToRoute('admin_tag_index');
        }
        return $this->render('admin/tag/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing Tag entity.
     *
     * @param Request $request
     * @param Tag $tag
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_tag_edit')]
    public function edit(Request $request, Tag $tag, EntityManagerInterface $entityManager) {
        $form = $this->createForm(TagType::class, $tag);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            $entityManager->persist($tag);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.tag.edit.success');
            return $this->redirectToRoute('admin_tag_index');
        }

        return $this->render('admin/tag/create.html.twig', array(
            'tag' => $tag,
            'form' => $form,
        ));
    }

    /**
     * Deletes a Tag entity.
     *
     * @param Request $request
     * @param Tag $tag
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_tag_delete')]
    public function delete(Request $request, EntityManagerInterface $em, Tag $tag)
    {
        $form = $this->createDeleteForm($tag);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($tag);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('admin.tag.delete.constraint_error');
                return $this->redirectToRoute('admin_tag_index');
            }
            $this->flashMessages->addSuccess('admin.tag.delete.success');
            return $this->redirectToRoute('admin_tag_index');
        }

        return $this->render('admin/tag/delete.html.twig', array(
            'tag' => $tag,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a Tag entity.
     */
    private function createDeleteForm(Tag $tag): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_tag_delete', array('id' => $tag->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }
}
