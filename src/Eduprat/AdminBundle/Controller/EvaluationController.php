<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\AdminBundle\Model\EvaluationUserSearch;
use Eduprat\AdminBundle\Form\EvaluationUserSearchType;
use Eduprat\AdminBundle\Repository\PersonRepository;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\ProgrammeSearch;
use Eduprat\DomainBundle\Form\ProgrammeSearchType;
use Ed<PERSON>rat\DomainBundle\Services\RestitutionCalculator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class EvalutionController
 */
#[Route(path: '/evaluation')]
#[IsGranted('ROLE_COORDINATOR_LBI')]
class EvaluationController extends AbstractController
{
    /**
     * @param Request $request
     * @param Programme $programme
     * @param Coordinator $coordinator
     * @return RedirectResponse|Response
     */
    #[Route(path: '/coordinator/{coordinator}/programme/{programme}', methods: ['GET', 'POST'], name: 'evaluation_coordinator_by_coordinator')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationCoordinatorbyCoordinator(Request $request, Programme $programme, Coordinator $coordinator, FormManagerFactory $formManagerFactory)
    {
        $evaluationCoordinatorByCoordinatorManager = $formManagerFactory->getEvaluationCoordinatorByCoordinatorManager($programme, $coordinator);

        $form = $evaluationCoordinatorByCoordinatorManager->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $evaluationCoordinatorByCoordinatorManager->save($form);

            return $this->redirectToRoute('admin_programme_index');
        }

        return $this->render('admin/evaluation/coordinator.html.twig', array(
            'programme' => $programme,
            'coordinator' => $coordinator,
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param Person $former
     * @param \Eduprat\AdminBundle\Entity\Coordinator        $coordinator
     * @return RedirectResponse|Response
     */
    #[Route(path: '/programme/{programme}/former/{former}/coordinator/{coordinator}', methods: ['GET', 'POST'], name: 'evaluation_former_by_coordinator')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationFormerByCoordinator(Request $request, Programme $programme, Person $former, Coordinator $coordinator, FormManagerFactory $formManagerFactory)
    {
        $evaluationFormerByCoordinatorManager = $formManagerFactory->getEvaluationFormerByCoordinatorManager($programme, $former, $coordinator);

        $form = $evaluationFormerByCoordinatorManager->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $evaluationFormerByCoordinatorManager->save($form);
            /** @var Person $person */
            foreach ($programme->getFormateursPersons() as $person) {
                if (!$formManagerFactory->getEvaluationFormerByCoordinatorManager($programme, $person, $coordinator)->isCompleted()) {
                    // On cherche le formateur suivant, si son évaluation n'est pas complétée on redirige dessus
                    return $this->redirectToRoute('evaluation_former_by_coordinator', array('programme' => $programme->getId(), 'former' => $person->getId(), 'coordinator' => $coordinator->getId()));
                }
            }
            // si toutes les evaluations sont complétées, on redirige vers la page d'accueil
            return $this->redirectToRoute('admin_programme_index');
        }

        return $this->render('admin/evaluation/former.html.twig', array(
            'programme' => $programme,
            'former' => $former,
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param \Eduprat\AdminBundle\Entity\Coordinator        $coordinator
     * @return RedirectResponse|Response
     */
    #[Route(path: '/programme/{programme}/coordinator/{coordinator}', methods: ['GET', 'POST'], name: 'evaluation_programme_by_coordinator')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationProgrammebyCoordinator(Request $request, Programme $programme, Coordinator $coordinator, FormManagerFactory $formManagerFactory)
    {
        $evaluationProgrammeByCoordinatorManager = $formManagerFactory->getEvaluationProgrammeByCoordinatorManager($programme, $coordinator);

        $form = $evaluationProgrammeByCoordinatorManager->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $evaluationProgrammeByCoordinatorManager->save($form);

            return $this->redirectToRoute('admin_programme_index');
        }

        return $this->render('admin/evaluation/programme.html.twig', array(
            'programme' => $programme,
            'coordinator' => $coordinator,
            'form' => $form,
        ));
    }

    /*** NOUVELLES EVALUATIONS ***/
    /**
     * @param Request $request
     * @param Programme $programme
     * @return RedirectResponse|Response
     */
    #[Route(path: '/programme/{programme}/{role}', methods: ['GET'], name: 'evaluation_programme_global_role')]
    public function evaluationGlobalProgramme(Programme $programme, $role, EvaluationReporting $evaluationReporting): Response
    {
        $reporting = $evaluationReporting->getProgrammeReport($programme, Person::ROLE_MAPPING[$role]);
        return $this->render('admin/evaluation/programme-global.html.twig', array(
                'programme' => $programme,
                'role' => Person::ROLE_MAPPING[$role],
                'reporting' => $reporting,
                'pdf' => $this->generateUrl(
                    'pdf_evaluation_programme_global_role_pdf',
                    array(
                        'programme' => $programme->getId(),
                        'role' => $role,
                        'token' => $programme->getToken()
                    ),
                    true)
            )
        );
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/formation/{formation}/{role}', methods: ['GET'], name: 'evaluation_formation_global_role')]
    public function evaluationGlobalFormation(Formation $formation, $role, EvaluationReporting $evaluationReporting, RestitutionCalculator $restitutionCalculator): Response
    {
        $reporting = $evaluationReporting->getFormationReport($formation, Person::ROLE_MAPPING[$role]);
        $syntheseDatas = $role == "participant" ? $restitutionCalculator->getFicheSyntheseDatas($formation) : null;
        return $this->render('admin/evaluation/programme-global.html.twig', array(
                'formation' => $formation,
                'role' => Person::ROLE_MAPPING[$role],
                'reporting' => $reporting,
                'syntheseDatas' => $syntheseDatas,
                'pdf' => $this->generateUrl(
                'pdf_evaluation_formation_global_role_pdf',
                array(
                    'formation' => $formation->getId(),
                    'role' => $role,
                    'token' => $formation->getToken()
                ),
            true)
            )
        );
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param Formateur $former
     * @param EvaluationReporting $evaluationReporting
     * @return RedirectResponse|Response
     */
    #[Route(path: '/formation/{formation}/former/{former}', methods: ['GET'], name: 'evaluation_formation_global_former')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationGlobalFormer(Formation $formation, Formateur $former, EvaluationReporting $evaluationReporting): Response
    {
        $reporting = $evaluationReporting->getFormerReport($former);
        return $this->render('admin/evaluation/former-global.html.twig', array(
            'formation' => $formation,
            'former' => $former,
            'reporting' => $reporting,
            'pdf' => $this->generateUrl(
                'pdf_evaluation_formation_global_former_pdf',
                array(
                    'formation' => $formation->getId(),
                    'former' => $former->getId(),
                    'token' => $former->getToken()
                ),
                true)
            )
        );
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     * @return RedirectResponse|Response
     */
    #[Route(path: '/former/{person}/{year}', methods: ['GET'], name: 'evaluation_global_former_year')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationGlobalFormerYear(Person $person, $year, EvaluationReporting $evaluationReporting): Response
    {
        $reporting = $evaluationReporting->getFormerReportYear($person, $year);
        return $this->render('admin/evaluation/former-global-year.html.twig', array(
            'year' => $year,
            'person' => $person,
            'reporting' => $reporting,
            'pdf' => $this->generateUrl(
                'pdf_evaluation_global_former_year_pdf',
                array(
                    'person' => $person->getId(),
                    'year' => $year,
                    'token' => $person->getToken()
                ),
                true)
            )
        );
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     * @return RedirectResponse|Response
     */
    #[Route(path: '/coordinator/{person}/{year}', methods: ['GET'], name: 'evaluation_global_coordinator_year')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationGlobalCoordinatorYear(Person $person, $year, EvaluationReporting $evaluationReporting): Response
    {
        $reporting = $evaluationReporting->getCoordinatorReportYear($person, $year);
        return $this->render('admin/evaluation/coordinator-global-year.html.twig', array(
            'year' => $year,
            'person' => $person,
            'reporting' => $reporting,
            'pdf' => $this->generateUrl(
                'pdf_evaluation_global_coordinator_year_pdf',
                array(
                    'person' => $person->getId(),
                    'year' => $year,
                    'token' => $person->getToken()
                ),
                true)
            )
        );
    }

    /**
     * @param Request $request
     * @param $title
     * @param $role
     * @param $year
     * @return Response
     */
    #[Route(path: '/topo/{year}/{role}/{title}', methods: ['GET'], name: 'evaluation_global_topo', requirements: ['title' => '.+'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationGlobalTopo(string $title, $role, $year, EvaluationReporting $evaluationReporting): Response
    {
        $reporting = $evaluationReporting->getTopoByYearAndRole($title, $year, Person::ROLE_MAPPING[$role]);
        return $this->render('admin/evaluation/topo-global-year.html.twig', array(
            'title' => $title,
            'role' => Person::ROLE_MAPPING[$role],
            'year' => $year,
            'reporting' => $reporting,
            'pdf' => $this->generateUrl(
                'pdf_evaluation_global_topo_pdf',
                array(
                    'title' => $title,
                    'role' => $role,
                    'year' => $year,
                ),
                true)
            )
        );
    }

    /**
     * @param Request $request
     * @param $year
     * @param $page
     * @return Response
     */
    #[Route(path: '/topo/{year}/{page}', methods: ['GET'], name: 'evaluation_global_topo_index', defaults: ['year' => 'tous', 'page' => '1'], requirements: ['year' => '^(tous|\d{4})$', 'page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationGlobalTopoList(Request $request, string $year, string $page, EvaluationReporting $evaluationReporting, EntityManagerInterface $em): Response
    {
        $search = new ProgrammeSearch();
        $search->handleRequest($request);

        if($year && $year != 'tous') {
            $search->setStart(new \DateTime($year.'-01-01'));
            $search->setEnd(new \DateTime($year.'-12-31'));
            $search->setYear($year);
        }

        $programmesRepository = $em->getRepository(Programme::class);

        $sortBy = $request->query->get('sortBy');
        $order = $request->query->get('order');

        $nbPerPage = 30;
        $count = $programmesRepository->countSearchResultsByTitle($search, $this->getUser());
        $programmes = $programmesRepository->findSearchResultsByTitle($search, $page, $nbPerPage, $this->getUser(), $sortBy, $order);
        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $max = 10;
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max - 1) / 2)));
        $sup = min($inf + ($max - 1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'programmes' => $programmes,
        );

        $searchForm = $this->createForm(ProgrammeSearchType::class, $search, array(
            'action' => $this->generateUrl('evaluation_global_topo_list_dosearch'),
            'method' => 'POST',
        ));

        return $this->render('admin/evaluation/topo.html.twig', array_merge($pagination, array(
            'year' => $year,
            'minYear' => $search->getStart() ? $search->getStart()->format('Y') : null,
            'maxYear' => $search->getEnd() ? $search->getEnd()->format('Y') : null,
            'evaluationReporting' => $evaluationReporting,
            'search' => $search,
            'search_form' => $searchForm->createView(),
            'search_route' => 'evaluation_global_topo_index',
            'extraParams' => array_merge($search->toArray(), array(
                'sortBy' => $sortBy,
                'order' => $order,
                'year' => $year
            ))
        )));
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    #[Route(path: '/', methods: ['POST'], name: 'evaluation_global_topo_list_dosearch')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function doSearch(Request $request): RedirectResponse {
        $search = new ProgrammeSearch();
        $form = $this->createForm(ProgrammeSearchType::class, $search);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('evaluation_global_topo_index', $search->getParams());
        }
        return $this->redirectToRoute('evaluation_global_topo_index');
    }

    #[Route(path: '/user/{page}', methods: ['GET'], name: 'evaluation_global_user_index', defaults: ['page' => '1'], requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_WEBMASTER')]
    public function evaluationGlobalUserList(Request $request, string $page, EvaluationReporting $evaluationReporting, EntityManagerInterface $em): Response
    {
        $sortBy = $request->query->get('sortBy');
        $order = $request->query->get('order');

        $search = new EvaluationUserSearch();
        $search->handleRequest($request);

        $searchForm = $this->createForm(EvaluationUserSearchType::class, $search, array(
            'method' => 'POST',
            'action' => $this->generateUrl('evaluation_global_user_list_dosearch'),
        ));

        $searchForm->handleRequest($request);

        $nbPerPage = 20;
        $max = 10;

        /** @var PersonRepository $personRepository */
        $personRepository = $em->getRepository(Person::class);

        $count = $personRepository->countSearchResultsEvaluation($search);
        $persons = $personRepository->findSearchResultsEvaluation($search, $page, $nbPerPage, $sortBy, $order);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'users' => $persons,
        );

        return $this->render('admin/evaluation/user.html.twig', array_merge($pagination, array(
            'evaluationReporting' => $evaluationReporting,
            'search' => $search,
            'search_form' => $searchForm->createView(),
            'search_route' => 'evaluation_global_user_index',
            'extraParams' => array_merge($search->toArray(), array(
                'sortBy' => $sortBy,
                'order' => $order,
            ))
        )));
    }



    /**
     * @param Request $request
     * @return RedirectResponse
     */
    #[Route(path: '/user/', methods: ['POST'], name: 'evaluation_global_user_list_dosearch')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function doSearchUser(Request $request): RedirectResponse {
        $search = new EvaluationUserSearch();
        $form = $this->createForm(EvaluationUserSearchType::class, $search);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('evaluation_global_user_index', $search->getParams());
        }
        return $this->redirectToRoute('evaluation_global_user_index');
    }
}
