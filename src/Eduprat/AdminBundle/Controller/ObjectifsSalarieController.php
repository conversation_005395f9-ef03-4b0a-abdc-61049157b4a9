<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AdminBundle\Entity\CoordinatorPerson;
use Ed<PERSON>rat\DomainBundle\Controller\EdupratController;
use Ed<PERSON>rat\DomainBundle\Entity\Objectif\ObjectifsSalarie;
use Ed<PERSON>rat\DomainBundle\Form\ObjectifsSalarieDTO;
use Eduprat\DomainBundle\Form\ObjectifsSalarieType;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ObjectifsSalarieController extends EdupratController
{

    #[Route(path: '/objectifs/{id}/create/{year}', name: 'admin_objectifs_salarie_create')]
    public function create(Request $request, CoordinatorPerson $coordinatorPerson, int $year, EntityManagerInterface $entityManager): RedirectResponse|Response
    {
        if (!$coordinatorPerson->crIsSalarie()) {
            $this->flashMessages->addError('admin.objectifs_salarie.notSalarie');
            return $this->redirectToRoute('admin_user_monitoring', ['id' => $coordinatorPerson->getId()]);
        }

        $objectifsSalarieRepo = $entityManager->getRepository(ObjectifsSalarie::class);
        $objectifsSalarie = $objectifsSalarieRepo->findOneBy(['year' => $year, 'person' => $coordinatorPerson]);

        if ($objectifsSalarie) {
            $objectifsSalarieDTO = new ObjectifsSalarieDTO($coordinatorPerson, $year, $objectifsSalarie->getObjectifCA(), $objectifsSalarie->getTauxCumulMarge());
        } else {
            $objectifsSalarieDTO = new ObjectifsSalarieDTO($coordinatorPerson, $year);
        }

        $form = $this->createForm(ObjectifsSalarieType::class, $objectifsSalarieDTO);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            if ($objectifsSalarie) {
                $objectifsSalarieDTO->updateObjectifsSalarie($objectifsSalarie);
                $entityManager->persist($objectifsSalarie);
            } else {
                $entityManager->persist(ObjectifsSalarie::create($objectifsSalarieDTO));
            }
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.objectifs_salarie.create.success');
            return $this->redirectToRoute('admin_user_monitoring', ['id' => $coordinatorPerson->getId()]);
        }

        return $this->render('admin/objectifs_salarie/create.html.twig', array(
            'form' => $form,
            'coordinator' => $coordinatorPerson,
            'year' => $year,
        ));
    }
}
