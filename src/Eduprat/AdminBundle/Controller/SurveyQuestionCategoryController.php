<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\SurveyQuestionCategory;
use Eduprat\DomainBundle\Form\SurveyQuestionCategoryType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * SurveyQuestionCategory controller.
 */
#[Route(path: '/survey-question-category')]
#[IsGranted('ROLE_WEBMASTER')]
class SurveyQuestionCategoryController extends Ed<PERSON>ratController
{
    /**
     * Lists all SurveyQuestionCategory entities.
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_survey_question_category_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $surveyCategories = new ArrayCollection($em->getRepository(SurveyQuestionCategory::class)->findAll());

        return $this->render('admin/survey_question_category/index.html.twig', array(
            'surveyCategories' => $surveyCategories,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_survey_question_category_create')]
    public function create(Request $request, EntityManagerInterface $entityManager)
    {
        $surveyQuestionCategory = new SurveyQuestionCategory();
        $form = $this->createForm(SurveyQuestionCategoryType::class, $surveyQuestionCategory);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($surveyQuestionCategory);
            $entityManager->flush();

            return $this->redirectToRoute('admin_survey_question_category_index');
        }

        return $this->render('admin/survey_question_category/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing SurveyQuestionCategory entity.
     *
     * @param Request $request
     * @param SurveyQuestionCategory $surveyQuestionCategory
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_survey_question_category_edit')]
    public function edit(Request $request, EntityManagerInterface $entityManager, SurveyQuestionCategory $surveyQuestionCategory)
    {
        $form = $this->createForm(SurveyQuestionCategoryType::class, $surveyQuestionCategory);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($surveyQuestionCategory);
            $entityManager->flush();

            $this->flashMessages->addSuccess('survey.category.edit.success');
            return $this->redirectToRoute('admin_survey_question_category_index');
        }

        return $this->render('admin/survey_question_category/create.html.twig', array(
            'surveyQuestionCategory' => $surveyQuestionCategory,
            'form' => $form,
        ));
    }

    /**
     * Deletes a SurveyQuestionCategory entity.
     *
     * @param Request $request
     * @param SurveyQuestionCategory $surveyQuestionCategory
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_survey_question_category_delete')]
    public function delete(Request $request, SurveyQuestionCategory $surveyQuestionCategory, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($surveyQuestionCategory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($surveyQuestionCategory);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('survey.category.delete.constraint_error');
                return $this->redirectToRoute('admin_survey_question_category_index');
            }
            $this->flashMessages->addSuccess('survey.category.delete.success');
            return $this->redirectToRoute('admin_survey_question_category_index');
        }

        return $this->render('admin/survey_question_category/delete.html.twig', array(
            'surveyQuestionCategory' => $surveyQuestionCategory,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a SurveyQuestionCategory entity.
     */
    private function createDeleteForm(SurveyQuestionCategory $surveyQuestionCategory): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_survey_question_category_delete', array('id' => $surveyQuestionCategory->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

}
