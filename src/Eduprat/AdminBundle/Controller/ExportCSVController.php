<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Finder\Finder;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;

/**
 * Class ExportCSVController
 */
#[Route(path: '/export-csv')]
#[IsGranted('ROLE_COORDINATOR')]
class ExportCSVController extends AbstractController
{
    private function myComparison($a, $b){
        return (key($a) < key($b)) ? -1 : 1;
    }

    private $types = ["coordinator","supervisor","collaborators","satisfaction", "etutorat", "sessions", "sessionsFull", "sessionsFullJson", "invoices", "coordinators", "participations", "former_with_siret", "former_without_siret", "detail_session", 'commissionnement', 'lead_inscrits', 'lead_without_referent', 'finance_sous_mode'];

    /**
     * @param $year
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-export-file-get/{type}/{start}/{end}', methods: ['GET'], name: 'csv_export_file_get')]
    #[IsGranted('ROLE_SUPERVISOR')]
    public function csvFileGet($type, \DateTime $start, \DateTime $end): BinaryFileResponse|RedirectResponse
    {
        $filename = sprintf('eduprat_export_%s_%s_%s.csv', $type, $start->format('Y-m-d'), $end->format('Y-m-d'));
        if ($type == "sessionsFullJson") {
            $filename = sprintf('eduprat_export_%s_%s_%s.json', $type, $start->format('Y-m-d'), $end->format('Y-m-d'));
        }
        $projectDir = $this->getParameter('kernel.project_dir');
        $folder = ($this->getUser()->isSupervisor() && !$this->getUser()->isWebmaster()) ? ("sup-" . $this->getUser()->getId() . "/") : "";
        $file = $projectDir . "/uploads/bilan/".$folder.$filename;

        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $filename);

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    /**
     * @param $type
     * @param \DateTime $start
     * @param \DateTime $end
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/bilan-generate-csv-export/{type}/{start}/{end}/{jsonReferenceDate}', methods: ['GET'], name: 'bilan_generate_csv_export')]
    #[IsGranted('ROLE_SUPERVISOR')]
    public function bilanGenerateCsvExport($type, \DateTime $start, \DateTime $end, $jsonReferenceDate = null): JsonResponse
    {
        $command = sprintf("eduprat:csv_export %s %s %s", $type, $start->format('Y-m-d'), $end->format('Y-m-d'));
        if ($type === "sessionsFullJson") {
            $command = sprintf("eduprat:csv_export_bis %s %s", $start->format('Y-m-d'), $end->format('Y-m-d'));
        }

        if ($jsonReferenceDate == "creation" || $jsonReferenceDate == "edition") {
            $command.= " jsonReferenceDate=" . $jsonReferenceDate;
        }

        if ($this->getUser()->isSupervisor() && !$this->getUser()->isWebmaster()) {
            $command .= " --supervisor=" . $this->getUser()->getId();
        }
        
        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf('php %s/bin/console %s', $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @return Response
     */
    #[Route(path: '/', methods: ['GET'], name: 'export-csv-type')]
    public function coordinators(): Response
    {
        $projectDir = $this->getParameter('kernel.project_dir');

        $folder = ($this->getUser()->isSupervisor() && !$this->getUser()->isWebmaster()) ? ("sup-" . $this->getUser()->getId() . "/") : "";

        $start = new \DateTime();
        $end = new \DateTime();
        $path = $projectDir . "/uploads/bilan/" . $folder;
        if (!is_dir($path)) {
            mkdir($path);
        }

        $starts = [];
        $ends = [];

        foreach($this->types as $type) {

            $finder = new Finder();

            if ($type == "sessionsFullJson") {
                $finder->files()->in($path)->name(sprintf("eduprat_export_%s_*.json", $type))->sortByChangedTime();
            } else {
                $finder->files()->in($path)->name(sprintf("eduprat_export_%s_*.csv", $type))->sortByChangedTime();
            }

            $f = iterator_to_array($finder->getIterator());

            if (count($f)) {
                end($f);
                $last = $f[key($f)];

                if ($type == "sessionsFullJson") {
                    preg_match(sprintf('/eduprat_export_%s_([0-9\-]*)_([0-9\-]*).json/', $type), $last->getRealPath(), $matches);
                } else {
                    preg_match(sprintf('/eduprat_export_%s_([0-9\-]*)_([0-9\-]*).csv/', $type), $last->getRealPath(), $matches);
                }
 
                $start = new \DateTime($matches[1]);
                $end = new \DateTime($matches[2]);
            }

            $starts[$type] = $start->format('Y-m-d');
            $ends[$type] = $end->format('Y-m-d');

            if ($type == "sessionsFullJson") {
                $file = $projectDir . "/uploads/bilan/". $folder.sprintf('eduprat_export_%s_%s_%s.json', $type, $starts[$type], $ends[$type]);
            } else {
                $file = $projectDir . "/uploads/bilan/". $folder.sprintf('eduprat_export_%s_%s_%s.csv', $type, $starts[$type], $ends[$type]);
            }
            $fileError = $projectDir . "/uploads/bilan/". $folder.sprintf('eduprat_export_%s_%s_%s.log', $type, $starts[$type], $ends[$type]);

            $fileLastUpdate[$type] = '';

            if($fileError && file_exists($fileError)) {
                $fileStatus[$type] = 'error';
            }
            else {
                if(!$file || !file_exists($file)) {
                    $fileStatus[$type] = 'generate';
                }
                else if(filesize($file) == 0) {
                    $fileStatus[$type] = 'generating';
                }
                else {
                    $fileStatus[$type] = 'generated';
                    $fileLastUpdate[$type] = date('d/m/Y', filectime($file));
                }
            }
        }
        return $this->render('admin/export_csv/index.html.twig', array(
            'types' => $this->types,
            'start' => $starts,
            'end' => $ends,
            'fileStatus' => $fileStatus,
            'fileLastUpdate' => $fileLastUpdate
        ));
    }
}
