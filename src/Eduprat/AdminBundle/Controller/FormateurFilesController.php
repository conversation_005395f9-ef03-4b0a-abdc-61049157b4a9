<?php

namespace Eduprat\AdminBundle\Controller;

use <PERSON><PERSON><PERSON>\DomainBundle\Entity\FormateurFiles;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class FormateurFilesController
 */
#[Route(path: '/formateur_files')]
#[IsGranted('ROLE_COORDINATOR')]
class FormateurFilesController extends AbstractController
{
    #[Route(path: '/{id}/file/{fileField}', name: 'admin_formateur_file')]
    public function formateurFileDownload(FormateurFiles $formateurFiles, $fileField, DownloadHandler $downloadHandler)
    {
        $getter = "get" . ucfirst(str_replace("File", "", $fileField));
        $filename = call_user_func(array($formateurFiles, $getter));
        return $downloadHandler->downloadObject($formateurFiles, $fileField, null, $filename, false);
    }

}
