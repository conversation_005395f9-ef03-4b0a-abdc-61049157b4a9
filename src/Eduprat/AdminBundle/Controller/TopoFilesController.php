<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Alienor\ApiBundle\Services\FlashMessages;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\DocumentsPedagogiquesFiles;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\ToolProgrammeFiles;
use Eduprat\DomainBundle\Entity\TopoFiles;
use Eduprat\DomainBundle\Entity\TopoProgrammeFiles;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Vich\UploaderBundle\Handler\DownloadHandler;

/**
 * Class TopoFilesController
 */
#[Route(path: '/topo_files')]
#[IsGranted('ROLE_COORDINATOR')]
class TopoFilesController extends AbstractController
{

        /**
     * @var FlashMessages
     */
    public $flashMessages;

    public function __construct(FlashMessages $flashMessages)
    {
        $this->flashMessages = $flashMessages;
    }
    
    #[Route(path: '/{id}/file/{fileField}', name: 'admin_topo_file')]
    public function topoFileDownload(TopoFiles $topoFile, $fileField, DownloadHandler $downloadHandler)
    {
        $filename = $topoFile->getTopoOriginalName();
        return $downloadHandler->downloadObject($topoFile, $fileField, null, $filename, false);
    }

    #[Route(path: '/{id}/fileProgramme/{fileField}', name: 'admin_topo_programme_file')]
    public function topoProgrammeFileDownload(TopoProgrammeFiles $topoFile, $fileField, DownloadHandler $downloadHandler)
    {
        $filename = $topoFile->getTopoOriginalName();
        return $downloadHandler->downloadObject($topoFile, $fileField, null, $filename, false);
    }

    #[Route(path: '/{id}/fileProgrammeDelete/{formation}', name: 'admin_topo_programme_delete_file')]
    public function topoProgrammeFileDelete(TopoProgrammeFiles $topoFile, EntityManagerInterface $entityManager, Formation $formation): RedirectResponse
    {
        $formation->deletedTopoProgramme($topoFile);
        $entityManager->persist($formation);
        $entityManager->flush();
        $this->flashMessages->addSuccess('formation.topoProgrammeDeleted');
        return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
    }

    #[Route(path: '/{id}/fileToolProgramme/{fileField}', name: 'admin_topo_tool_programme_file')]
    public function topoProgrammeToolFileDownload(ToolProgrammeFiles $topoFile, $fileField, DownloadHandler $downloadHandler)
    {
        $filename = $topoFile->getTopoOriginalName();
        return $downloadHandler->downloadObject($topoFile, $fileField, null, $filename, false);
    }

    #[Route(path: '/{id}/fileDocumentsPedagogique/{fileField}', name: 'admin_document_pedagogique_file')]
    public function documentsPedagogiquesFileDownload(DocumentsPedagogiquesFiles $topoFile, $fileField, DownloadHandler $downloadHandler)
    {
        $filename = $topoFile->getTopoOriginalName();
        return $downloadHandler->downloadObject($topoFile, $fileField, null, $filename, false);
    }
}
