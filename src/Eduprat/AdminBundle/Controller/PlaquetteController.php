<?php

namespace Eduprat\AdminBundle\Controller;

use Eduprat\DomainBundle\Entity\LogoPartenaire;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\AuditCategory;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Entity\SelectionFormation;
use Eduprat\DomainBundle\Form\DownloadPlaquetteFileDeleteType;
use Eduprat\DomainBundle\Form\LogoPartenaireType;
use Eduprat\DomainBundle\Form\SelectionSessionFavoriDeleteType;
use Eduprat\DomainBundle\Form\SelectionSessionFavoriType;
use Eduprat\DomainBundle\Model\PlaquetteSearch;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Plaquette controller.
 */
#[Route(path: '/plaquette')]
#[IsGranted('ROLE_COORDINATOR')]
class PlaquetteController extends EdupratController
{
    /**
     * .
     */
    #[Route(path: '/{page}', name: 'admin_plaquette_search', methods: ['GET', 'POST'], requirements: ['page' => '^\d+$'])]
    public function search(Request $request, SearchHandler $searchHandler, EntityManagerInterface $em, PlaquetteManager $plaquetteManager,
                                 RouterInterface $router, int $page = 1): Response
    {
        /** @var Person $user */
        $user = $this->getUser();

        if (!$user->isCoordinator() && !$user->hasRole('ROLE_SUPER_ADMIN')) {
            throw $this->createAccessDeniedException();
        }

        $this->clearPlaquetteSearchIfUserComeElsewhere($request, $router, $plaquetteManager, $user);

        $sfRepo = $em->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $selectionsFormation */
        $selectionsFormation = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);
        $params = $selectionsFormation ? $selectionsFormation->getLastSearchFields() : [];

        $searchHandle = $searchHandler->handle(PlaquetteSearch::class, [
            'disabled_edit' => !($selectionsFormation === null || ($selectionsFormation && $selectionsFormation->isLastSelectionEmpty())),
        ], $params, array(
            "user" => $user,
        ));

        /** @var PlaquetteSearch $search */
        $search = $searchHandle["search"];

        $nbPerPage = 30;
        $pagination = [];
        $formations = null;

        // On vérifie si des parametres du formulaire sont passés en GET pour forcer la validation
        $requestGetParams = $request->query->all();
        $hasGetParameterValid = false;
        foreach (array_keys($requestGetParams) as $requestGetParam) {
            if (property_exists($searchHandle['search'], $requestGetParam)) {
                $hasGetParameterValid = true;
                break;
            }
        }
        if ($searchHandle["searchForm"]->isSubmitted() || $hasGetParameterValid || ($selectionsFormation && !$selectionsFormation->isLastSelectionEmpty())) {
            $count = $plaquetteManager->getSearch($search, $page, $nbPerPage, $user, false, true);
            $formations = $plaquetteManager->getSearch($search, $page, $nbPerPage, $user);
            $pagination = $searchHandler->getPagination($count, $formations, $page, $nbPerPage);
        }

        $favoris = $this->getUser()->getSelectionFormationsFavori();
        $delete_favoris_form = [];
        /** @var SelectionFormation $favori */
        foreach ($favoris as $favori) {
            $delete_favoris_form[] = $this->createForm(
                SelectionSessionFavoriDeleteType::class,
                $favori,
                ['action' => $this->generateUrl('admin_plaquette_favori_delete', ['selectionFormation' => $favori->getId()])]
            )
            ->createView();
        }

        $downloadPlaquetteFiles = $this->getUser()->getDownloadedPlaquetteFiles();
        $delete_dpf_form = [];
        /** @var DownloadedPlaquetteFile $favori */
        foreach ($downloadPlaquetteFiles as $dpf) {
            $delete_dpf_form[] = $this->createForm(
                DownloadPlaquetteFileDeleteType::class,
                $dpf,
                ['action' => $this->generateUrl('admin_plaquette_dpf_delete', ['downloadPlaquetteFile' => $dpf->getId()])]
            )
            ->createView();
        }

        $search_reinit_form = $this->createFormBuilder()
            ->add('Reinit', SubmitType::class)
            ->setAction($this->generateUrl('admin_plaquette_search_reinit'))
            ->getForm();
        $search_reinit_form2 = clone ($search_reinit_form);

        $search_add_form = $this->createFormBuilder()
            ->add('Ajouter', SubmitType::class)
            ->setAction($this->generateUrl('admin_plaquette_search_add'))
            ->getForm();

        $clear_current_search_form = $this->createFormBuilder()
            ->add('Supprimer_la_recherche_en_cours', SubmitType::class)
            ->setAction($this->generateUrl('admin_plaquette_clear_current_search'))
            ->getForm();

        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);

        if ($selectionsFormation) {
            $selectionsFormation->setNomFavori('Favori-'.(new \DateTime())->format('dmY'));
        }
        $favori_form = $this->createForm(SelectionSessionFavoriType::class, $selectionsFormation, [
            'action' => $this->generateUrl('admin_plaquette_search_convert_as_favori'),
        ]);

        // on parcourt les formations selectionnées par la recherche
        $selectedOrNotSession = [];
        $sessionsAlreadyInclude = [];
        if ($selectionsFormation) {
            $lastExclusions = $selectionsFormation->getLastExclusion();
            if ($formations && !$selectionsFormation->isLastSelectionEmpty()) {
                /** @var Formation $formation */
                foreach ($formations as $formation) {
                    if ($formation->getProgramme()->isElearning()) {
                        $find = false;
                        // On parcourt toutes les formations du programme pour vérifier si une session est exclue
                        $formationsInProgramme = $formation->getProgramme()->getAllFormations();
                        foreach ($formationsInProgramme as $programmeFormation) {
                            if (in_array($programmeFormation->getId(), $lastExclusions)) {
                                $find = true;
                                break;
                            }
                        }
                        // On parcourt toutes les formations du programme pour leur assigner si elles sont exclues
                        foreach ($formationsInProgramme as $programmeFormation) {
                            $selectedOrNotSession[$programmeFormation->getId()] = !$find;
                        }
                    } else {
                        $selectedOrNotSession[$formation->getId()] = !in_array($formation->getId(), $lastExclusions);
                    }
                }
            }

            $previousRequests = $plaquetteManager->loadSessionsInSelection($user, $selectionsFormation, false, false);
            $countSearchFields = $user->getCurrentSelectionFormation()->getCountSearchFields();
            if ($countSearchFields > 1) {
                for ($i = 0; $i <= $countSearchFields-2; $i++) {
                    foreach($previousRequests[$i] as $session) {
                        array_push($sessionsAlreadyInclude, $session["id"]);
                    }
                }
            }
        }
        $logoPartenairesRepo = $em->getRepository(LogoPartenaire::class);
        $logosPartenaire = $logoPartenairesRepo->findAll();

        // Créer un formulaire LogoPartenaire pour obtenir le token CSRF
        $logoPartenaire = new LogoPartenaire();
        $logoPartenaireForm = $this->createForm(LogoPartenaireType::class, $logoPartenaire);

        return $this->render('admin/plaquette/search.html.twig', array_merge($pagination, array(
            'search' => $search,
            'search_form' => $searchHandle["searchForm"]->createView(),
            'search_reinit_form' => $search_reinit_form->createView(),
            'search_reinit_form2' => $search_reinit_form2->createView(),
            'clear_current_search_form' => $clear_current_search_form->createView(),
            'search_add_form' => $search_add_form->createView(),
            'favori_form' => $favori_form->createView(),
            'download_plaquette_file_form' => $download_plaquette_file_form->createView(),
            'formations' => $formations,
            'open' => $request->query->get('open'),
            'selectionsFormation' => $selectionsFormation,
            'selectedOrNotSession' => $selectedOrNotSession,
            'sessionsAlreadyInclude' => $sessionsAlreadyInclude,
            'nbSessionSelection' =>  $plaquetteManager->countCurrentSelection($user, null, false),
            'delete_favoris_form' => $delete_favoris_form,
            'favoris' => $favoris,
            'downloadPlaquetteFiles' => $downloadPlaquetteFiles,
            'delete_dpf_form' => $delete_dpf_form,
            'logosPartenaire' => $logosPartenaire,
            'logoPartenaireForm' => $logoPartenaireForm->createView(),
        )));
    }

    /**
     * .
     */
    #[Route(path: '/search/save', name: 'admin_plaquette_search_save', methods: ['POST'])]
    public function saveSearch(Request $request, SearchHandler $searchHandler, PlaquetteManager $plaquetteManager, EntityManagerInterface $entityManager): JsonResponse
    {
        /** @var Person $user */
        $user = $this->getUser();
        $search = null;
        if ($request->request->has('eduprat_search')) {
            $searchHandle = $searchHandler->handle(PlaquetteSearch::class, array(), [], array(
                "user" => $user,
            ));

            /** @var PlaquetteSearch $search */
            $search = $searchHandle["search"];
        }

        $numRequest = $request->request->has('numRequest') ? (int)$request->request->get('numRequest') : null;
        $selectionSessionId = $request->request->has('selectionSession') ? (int)$request->request->get('selectionSession') : null;
        if ($request->request->has('selectAll')) {
            $option = filter_var($request->request->get('selectAll'), FILTER_VALIDATE_BOOLEAN) ? PlaquetteManager::INCLUDE_ALL : PlaquetteManager::EXCLUDE_ALL;
            $exclusion = [];
        } else {
            $option = filter_var($request->request->get('include'), FILTER_VALIDATE_BOOLEAN) ? PlaquetteManager::INCLUDE_ELEM : PlaquetteManager::REMOVE_INCLUDE_ELEM;
            $exclusion = $request->request->get('session');
        }
        $plaquetteManager->saveSearch($user, $search, $exclusion, $option, $numRequest, $selectionSessionId);

        $sfRepo = $entityManager->getRepository(SelectionFormation::class);
        /** @var ?SelectionFormation $sf */
        $sf = null;
        if ($selectionSessionId) {
            $sf = $sfRepo->findOneBy(['person' => $user, 'parentFavori' => $selectionSessionId]);
        }
        return new JsonResponse([
            'nbSessionSelection' => $plaquetteManager->countCurrentSelection($user, $sf, $sf && $sf->isFavori()),
        ]);
    }

    /**
     * .
     */
    #[Route(path: '/search/reinit', name: 'admin_plaquette_search_reinit')]
    public function reinitSearch(Request $request, PlaquetteManager $plaquetteManager): RedirectResponse
    {
        $search_reinit_form = $this->createFormBuilder()
            ->add('Reinit', SubmitType::class)
            ->getForm();
        $search_reinit_form->handleRequest($request);
        if ($search_reinit_form->isSubmitted() && $search_reinit_form->isValid()) {
            $plaquetteManager->reinitSearch($this->getUser());
            $this->flashMessages->addSuccess('Réinitialisation des recherches en cours effectuée');
        }
        return $this->redirectToRoute('admin_plaquette_search');
    }

    /**
     * .
     */
    #[Route(path: '/search/add', name: 'admin_plaquette_search_add')]
    public function addSearch(Request $request, PlaquetteManager $plaquetteManager, EntityManagerInterface $em): RedirectResponse
    {
        /** @var Person $user */
        $user = $this->getUser();

        $sfRepo = $em->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $selectionsFormation */
        $selectionsFormation = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);

        $search_add_form = $this->createFormBuilder()
            ->add('Ajouter', SubmitType::class)
            ->getForm();
        $search_add_form->handleRequest($request);
        if ($search_add_form->isSubmitted() && $search_add_form->isValid()) {
            if ($selectionsFormation && !$selectionsFormation->isLastSelectionEmpty()) {
                $plaquetteManager->newSearch($this->getUser());
            }
        }
        return $this->redirectToRoute('admin_plaquette_search');
    }

    /**
     * .
     */
    #[Route(path: '/search/clear_current_searh', name: 'admin_plaquette_clear_current_search')]
    public function clearCurrentSearch(Request $request, PlaquetteManager $plaquetteManager, EntityManagerInterface $em): RedirectResponse
    {
        /** @var Person $user */
        $user = $this->getUser();

        $sfRepo = $em->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $selectionsFormation */
        $selectionsFormation = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);

        $clear_current_search_form = $this->createFormBuilder()
            ->add('Supprimer_la_recherche_en_cours', SubmitType::class)
            ->getForm();
        $clear_current_search_form->handleRequest($request);
        if ($clear_current_search_form->isSubmitted() && $clear_current_search_form->isValid()) {
            if ($selectionsFormation && !$selectionsFormation->isLastSelectionEmpty()) {
                $plaquetteManager->clearCurrentSearch($this->getUser());
            }
        }
        return $this->redirectToRoute('admin_plaquette_search');
    }

    /**
     * .Récupère les sessions associées à selectionFormation
     * Si selectionFormation null alors on prend le selectionFormation en cours
     * Sinon on récupère le selectionFormation favori
     */
    #[Route(path: '/currentSelection/{selectionFormation}', name: 'admin_plaquette_currentSelection', methods: ['POST'])]
    public function currentSelection(EntityManagerInterface $em, PlaquetteManager $plaquetteManager, TranslatorInterface $translator, $selectionFormation = null): JsonResponse
    {
        $isFavori = $selectionFormation ? true : false;
        /** @var SelectionFormation $selectionFormation */
        if ($selectionFormation) {
            $sfRepo = $em->getRepository(SelectionFormation::class);
            $selectionFormation = $sfRepo->findOneById($selectionFormation);
        } else {
            $selectionFormation = $this->getUser()->getCurrentSelectionFormation();
        }

        $fields = [];
        foreach ($selectionFormation->getSearchFields() as $searchFields) {
            $tmp = [];
            foreach ($searchFields as $key => $searchField) {
                if ($searchField !== null && (!is_array($searchField) || !empty($searchField))) {
                    if ($isFavori) {
                        if (in_array($key, ['start', 'end'])) {
                            continue;
                        }
                    } elseif (in_array($key, ['start', 'end'])) {
                        $searchField = (new \DateTime($searchField['date']))->format('d/m/Y');
                    }
                    // get values financeModes
                    if ($key === 'financeModes') {
                        $financeModesRepo = $em->getRepository(FinanceMode::class);
                        $financeModesAC = $financeModesRepo->findAll();
                        $financeModes = [];
                        foreach ($financeModesAC as $financeModeAC) {
                            foreach ($searchField as $value) {
                                if ($financeModeAC->getId() == $value) {
                                    $financeModes[] = $financeModeAC->getName();
                                }
                            }
                        }
                        $searchField = $financeModes;
                    }
                    // get values prisesEnCharge
                    if ($key === 'prisesEnCharge') {
                        $prisesEnChargeRepo = $em->getRepository(PriseEnCharge::class);
                        $prisesEnChargeAC = $prisesEnChargeRepo->findAll();
                        $prisesEnCharge = [];
                        foreach ($prisesEnChargeAC as $priseEnChargeAC) {
                            foreach ($searchField as $value) {
                                if ($priseEnChargeAC->getId() == $value) {
                                    $prisesEnCharge[] = $priseEnChargeAC->getName();
                                }
                            }
                        }
                        $searchField = $prisesEnCharge;
                    }
                    // get values departements
                    if ($key === 'departements') {
                        $tmpDepartements = [];
                        foreach ($searchField as $value) {
                             $tmpDepartements[] = AdressesService::getDepartementName($value . '000'); // zipcode attendu
                        }
                        $searchField = $tmpDepartements;
                    }

                    // get values thematiques
                    if ($key === 'thematiques') {
                        $auditCategoryRepo = $em->getRepository(AuditCategory::class);
                        $auditCategoryAC = $auditCategoryRepo->findAll();
                        $auditCategories = [];
                        foreach ($auditCategoryAC as $auditCategory) {
                            foreach ($searchField as $value) {
                                if ($auditCategory->getId() == $value) {
                                    $auditCategories[] = $auditCategory->getName();
                                }
                            }
                        }
                        $searchField = $auditCategories;
                    }

                    if ($key == "virtualUnrelated" || $key == "elearningUnrelated") {
                        $searchField = "Toutes";
                    }
                    $tmp[$translator->trans('admin.plaquette.'.$key)] = $searchField;
                }
            }
            $fields[] = $tmp;
        }
        $requests = $plaquetteManager->loadSessionsInSelection($this->getUser(), $selectionFormation, false, $isFavori);
        $sessionIdsList = [];
        foreach($requests as $requestContent) {
            foreach($requestContent as $formation) {
                if(!isset($sessionIdsList[$formation["id"]])) {
                    $sessionIdsList[$formation["id"]] = false;
                }
            }
        }

        return new JsonResponse([
            'formations' => $requests,
            'count' => $isFavori ? $plaquetteManager->countCurrentSelection($this->getUser(), $selectionFormation, $isFavori) : false,
            'filters' => $fields,
            'title' => $selectionFormation->getNomFavori(),
            'sessionIdsList' => $sessionIdsList,
        ]);
    }

    /**
     * .
     */
    #[Route(path: '/search/convert_as_favori', name: 'admin_plaquette_search_convert_as_favori')]
    public function convertAsFavori(Request $request, PlaquetteManager $plaquetteManager, EntityManagerInterface $em, TranslatorInterface $translator): JsonResponse
    {
        /** @var Person $user */
        $user = $this->getUser();

        $parent_favori_number = $request->request->has('parent_favori_number') ? (int)$request->request->get('parent_favori_number') : null;
        $is_save_as = filter_var($request->request->get('is_save_as', false), FILTER_VALIDATE_BOOLEAN);

        /** @var SelectionFormation $selectionsFormation */
        if ($parent_favori_number === null) {
            $selectionsFormation = $user->getCurrentSelectionFormation();
        } else {
            $sfRepo = $em->getRepository(SelectionFormation::class);
            /** @var ?SelectionFormation $sf */
            $selectionsFormation = $sfRepo->findOneBy(['person' => $user, 'parentFavori' => $parent_favori_number]);
        }
        $favori_form = $this->createForm(SelectionSessionFavoriType::class, $selectionsFormation);
        $favori_form->handleRequest($request);

        if ($favori_form->isSubmitted()) {
            if ($favori_form->isValid()) {
                // Si currentSelection ou click sur enregistrer sous, on ajoute un élément en base
                // pas lorsque l'on clique sur 'enregistrer' (simple modification de l'existant)
                if (!$parent_favori_number || $is_save_as) {
                    list($error, $codeError) = $plaquetteManager->canAddFavori($user, $is_save_as);
                    if ($error) {
                        return new JsonResponse([
                            'etat' => false,
                            'error' => $error,
                        ], $codeError);
                    }
                }

                // Si on enregistre en cours ou si on enregistre sous, on donne juste un nom au SelectionFormation
                // Lorsque l'on enregistre un favori temporaire, alors il faut modifier le SelectionFormation parent et supprimer le temporaire
                $selectionsFormation->setIsFavori(true);
                $selectionsFormation->resetTemporary();
                $resSelectionsFormation = $selectionsFormation;
                if ($parent_favori_number && !$is_save_as) {
                    /** @var ?SelectionFormation $parentSelectionsFormation */
                    $parentSelectionsFormation = $sfRepo->findOneBy(['person' => $user, 'id' => $parent_favori_number]);
                    $parentSelectionsFormation->mergeAsSave($selectionsFormation);
                    $em->flush($parentSelectionsFormation);
                    $em->remove($selectionsFormation);
                    $resSelectionsFormation = $parentSelectionsFormation;
                }
                if ($parent_favori_number && $is_save_as) {
                    $selectionsFormation->setNomFavori($favori_form->get('nomFavori2')->getData());
                }

                try {
                    $em->flush($selectionsFormation);
                } catch (UniqueConstraintViolationException $e) {
                    return new JsonResponse([
                        'etat' => false,
                        'error' => 'Une selection enregistrée possède déjà ce nom',
                    ], Response::HTTP_FORBIDDEN);
                }
            } else {
                return new JsonResponse([
                    'etat' => false,
                    'error' => false,
                    'errorMessage' => isset($favori_form->get('nomFavori')->getErrors()[0]) ?
                        $favori_form->get('nomFavori')->getErrors()[0]->getMessage() :
                        "seuls les lettres, chiffres et caractères spéciaux sont autorisés : -_.",
                ], Response::HTTP_FORBIDDEN);
            }
        }
        $formSuppression = $this->createForm(
            SelectionSessionFavoriDeleteType::class,
            $resSelectionsFormation,
            ['action' => $this->generateUrl('admin_plaquette_favori_delete', ['selectionFormation' => $resSelectionsFormation->getId()])]
        );
        return new JsonResponse([
            'etat' => true,
            'message'=> $translator->trans('admin.plaquette.favori.addConfirmation'),
            // On retourne la selectionFormation modifiée
            'selectionFormationId' => $resSelectionsFormation->getId(),
            'selectionFormationNom' => $resSelectionsFormation->getNomFavori(),
            'selectionFormationDate' => $resSelectionsFormation->getDateAjoutFavori()->format('d/m/Y'),
            'formSuppression' => $this->renderView('admin/plaquette/formSuppression.html.twig', array(
                'formSuppression' => $formSuppression->createView(),
            )),
            'needCompleteListFavori' => !$parent_favori_number || $is_save_as,
        ]);
    }

    /**
     * .
     */
    #[Route(path: '/search/delete_favori/{selectionFormation}', name: 'admin_plaquette_favori_delete')]
    public function deleteFavori(Request $request, SelectionFormation $selectionFormation, EntityManagerInterface $entityManager, TranslatorInterface $translator): JsonResponse
    {
        if ($selectionFormation->getPerson() != $this->getUser()) {
            throw new AccessDeniedException();
        }
        $delete_favori_form = $this->createForm(SelectionSessionFavoriDeleteType::class, $selectionFormation);
        $delete_favori_form->handleRequest($request);
        if ($delete_favori_form->isSubmitted() && $delete_favori_form->isValid()) {
            $entityManager->remove($selectionFormation);
            $entityManager->flush();
            return new JsonResponse([
                'etat' => true,
                'message'=> $translator->trans('admin.plaquette.favori.deleteConfirmation'),
            ]);
        }
        return new JsonResponse([
            'etat' => false,
        ]);
    }


    /**
     * .
     */
    #[Route(path: '/search/delete_dpf/{downloadPlaquetteFile}', name: 'admin_plaquette_dpf_delete')]
    public function deleteDownloadPlaquetteFile(Request $request, DownloadedPlaquetteFile $downloadPlaquetteFile, EntityManagerInterface $entityManager, DownloadedPlaquetteFileManager $dpfManager, TranslatorInterface $translator): JsonResponse
    {
        if ($downloadPlaquetteFile->getPerson() != $this->getUser()) {
            throw new AccessDeniedException();
        }
        $delete_dpf_form = $this->createForm(DownloadPlaquetteFileDeleteType::class, $downloadPlaquetteFile);
        $delete_dpf_form->handleRequest($request);
        if ($delete_dpf_form->isSubmitted() && $delete_dpf_form->isValid()) {
            $dpfManager->removeFile($downloadPlaquetteFile);
            $entityManager->remove($downloadPlaquetteFile);
            $entityManager->flush();
            return new JsonResponse([
                'etat' => true,
                'message'=> $translator->trans('admin.plaquette.downloadPlaquetteFile.deleteConfirmation'),
            ]);
        }
        return new JsonResponse([
            'etat' => false,
        ]);
    }

    /**
     * .
     */
    #[Route(path: '/search/delete_favori_temporaire/{parentSelectionFormation}', name: 'plaquette_delete_favori_temporaire')]
    public function deleteTemporaryFavori(SelectionFormation $parentSelectionFormation, EntityManagerInterface $entityManager, TranslatorInterface $translator): JsonResponse
    {
        $sfRepo = $entityManager->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $selectionsFormation */
        $selectionFormation = $sfRepo->findOneBy(['person' => $this->getUser(), 'isTemporary' => true, 'parentFavori' => $parentSelectionFormation]);

        if (!$selectionFormation) {
            throw new NotFoundHttpException();
        }

        $entityManager->remove($selectionFormation);
        $entityManager->flush();
        return new JsonResponse([
            'etat' => true,
            'message'=> $translator->trans('admin.plaquette.favori.deleteConfirmation'),
        ]);
    }

    /**
     * @param Request $request
     * @param PlaquetteManager $plaquetteManager
     * @param Person $user
     * @return void
     */
    public function clearPlaquetteSearchIfUserComeElsewhere(Request $request, RouterInterface $router, PlaquetteManager $plaquetteManager, Person $user): void
    {
        $referer = (string)$request->headers->get('referer');
        if ($referer) {
            $refererPathInfo = Request::create($referer)->getPathInfo();
            // Remove the scriptname if using a dev controller like app_dev.php (Symfony 3.x only)
            $refererPathInfo = str_replace($request->getScriptName(), '', $refererPathInfo);
            // try to match the path with the application routing
            $routeInfos = $router->match($refererPathInfo);
            // get the Symfony route name
            $refererRoute = $routeInfos['_route'] ?? '';
            if (!in_array($refererRoute, ['admin_plaquette_search', 'admin_plaquette_clear_current_search', 'admin_plaquette_search_reinit', 'admin_plaquette_search_add'])) {
                $plaquetteManager->reinitSearch($user);
            }
        }
    }
}
