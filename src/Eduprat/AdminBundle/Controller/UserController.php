<?php

namespace Eduprat\AdminBundle\Controller;

use Alienor\EmailBundle\Services\EmailAddressBuilder;
use Eduprat\AdminBundle\Entity\CoordinatorPerson;
use Eduprat\DomainBundle\DTO\CAObjectifRealiseDTO;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\DomainBundle\Repository\FormationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\PersonSearch;
use Eduprat\AdminBundle\Form\ChangePasswordType;
use Eduprat\AdminBundle\Form\EditPasswordType;
use Eduprat\AdminBundle\Form\PersonSearchType;
use Eduprat\AdminBundle\Repository\PersonRepository;
use Eduprat\AdminBundle\Services\AccountSwitcher;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\CoordinatorDashboard;
use Eduprat\AdminBundle\Services\UserManager;
use Eduprat\AuditBundle\Services\EvaluationFormerManager;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\EvaluationFormerAnswer;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Attribute\Route;
use Eduprat\AdminBundle\Form\PersonType;
use Eduprat\AdminBundle\Entity\Person;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Eduprat\DomainBundle\Entity\Coordinator;

use Eduprat\AdminBundle\Services\EvaluationFormerByCoordinatorManager;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\EvaluationFormerByCoordinator;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Eduprat\DomainBundle\Model\SessionSearch;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File as FormFile;

/**
 * Person controller.
 */
#[Route(path: '/user')]
class UserController extends EdupratController
{
    /**
     * Lists all Person entities.
     *
     * @param Request                                   $request
     * @param                                           $page
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{page}', methods: ['GET'], defaults: ['page' => '1'], name: 'admin_user_index', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_SUPERVISOR')]
    public function index(Request $request, string $page, EntityManagerInterface $em): Response
    {
        $search = new PersonSearch();
        $search->handleRequest($request);

        $searchForm = $this->createForm(PersonSearchType::class, $search, array(
            'method' => 'POST',
            'action' => $this->generateUrl('admin_user_dofilter'),
            'displayRoles' => $this->isGranted('ROLE_WEBMASTER')
        ));

        $searchForm->handleRequest($request);

        $nbPerPage = 20;
        $max = 10;

        /** @var PersonRepository $personRepository */
        $personRepository = $em->getRepository(Person::class);

        if ($this->getUser()->isSupervisor()) {
            $search->setRole("ROLE_COORDINATOR");
            $search->setSupervisor($this->getUser());
        }

        $count = $personRepository->countSearchResults($search);
        $persons = $personRepository->findSearchResults($search, $page, $nbPerPage);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'users' => $persons,
        );

        return $this->render('admin/user/index.html.twig', array_merge($pagination, array(
            'form' => $searchForm->createView(),
            'search' => $search,
            'search_route' => 'admin_user_index'
        )));
    }

    #[Route(path: '/filter/', methods: ['POST'], name: 'admin_user_dofilter')]
    #[IsGranted('ROLE_SUPERVISOR')]
    public function doFilter(Request $request): RedirectResponse {
        $filter = new PersonSearch();
        $form = $this->createForm(PersonSearchType::class, $filter);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('admin_user_index', $filter->getParams());
        }
        return $this->redirectToRoute('admin_user_index');
    }

    /**
     * Lists all Coordinators entities.
     *
     * @param Request                                   $request
     * @param                                           $page
     * @return RedirectResponse|Response
     */
    #[Route(path: '/coordinators/{page}', methods: ['GET'], defaults: ['page' => '1'], name: 'admin_user_coordinators', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_SUPERVISOR')]
    public function coordinators(string $page, EntityManagerInterface $em): Response
    {
        /** @var Person $user */
        $user = $this->getUser();
        $nbPerPage = 20;
        $max = 10;
        $search = (new PersonSearch());

        if ($user->isSupervisor()) {
            $count = $user->getCoordinatorsPerson()->count();
            $persons = $user->getCoordinatorsPerson()->slice(($page - 1) * $nbPerPage, $nbPerPage);
        } else {
            /** @var PersonRepository $personRepository */
            $personRepository = $em->getRepository(Person::class);
            $search->setRole('ROLE_COORDINATOR');
            $count = $personRepository->countSearchResults($search);
            $persons = $personRepository->findSearchResults($search, $page, $nbPerPage);
        }

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'users' => $persons,
        );

        return $this->render('admin/user/index.html.twig', array_merge($pagination, array(
            'search' => $search,
            'search_route' => 'admin_user_coordinators'
        )));
    }

    #[Route(path: '/register', name: 'admin_user_register')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function register(Request $request, UserManager $userManager, TranslatorInterface $translator, EntityManagerInterface $em) {

        $person = $userManager->createUser();
        $templatesLocation = $this->getParameter('alienor_user.templatesLocation');

        $form = $this->createForm($userManager->createForm(), $person);

        // Gestion du cas ou l'user a déjà un compte participant
        // @todo faire un dto pour mapper tous
        $email = $request->request->all('eduprat_person') && $request->request->all('eduprat_person')['email'];
        $existingParticipant = $em->getRepository(Person::class)->findWithParticipantByEmail($email);
        if ($existingParticipant !== null) {
            $person = $existingParticipant;
            // On remplace le formulaire par un form d'edition
            $form = $this->createForm(PersonType::class, $person, array('user' => $this->getUser()));
            $req = $request->request->all();
            // On retire le password de la requête car il ne peut pas être présent en édition
            unset($req['eduprat_person']['plainPassword']);
            $request->request->replace($req);
        }

        $form->handleRequest($request);

        if (is_null($existingParticipant) && $person->hasRole('ROLE_FORMER')) {
            $person->setPlainPassword(uniqid());
        }

        if ($form->isSubmitted()) {
            $existingUser = $em->getRepository(Person::class)->findByEmailRole($person->getEmail(), $person->getRole());
            if ($existingUser !== null) {
                $form->get('email')->addError(new FormError($translator->trans("admin.user.emailRoleExist")));
            }
        }

        if ($form->isSubmitted() && $form->isValid()) {
            $person->setIsEnabled(true);
            $userManager->updateUser($person);

            if ($existingParticipant === null) {
                $this->flashMessages->addSuccess('user.confirm.success');
            } else {
                $this->flashMessages->addSuccess('user.confirm.participant');
            }
            return $this->redirectToRoute('admin_user_index');
        }

        return $this->render($templatesLocation . '/user/register.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing Person entity.
     *
     * @param Request                            $request
     * @param Person $person
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_user_edit')]
    public function edit(Request $request, Person $person, UserManager $userManager, TranslatorInterface $translator, EntityManagerInterface $entityManager)
    {
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_WEBMASTER') && $user->isCoordinator() && $user->getId() !== $person->getId()) {
            throw $this->createAccessDeniedException();
        }

        $form = $this->createForm(PersonType::class, $person, array('user' => $user));
        $form->handleRequest($request);

        $formPass = $this->createForm(ChangePasswordType::class, $person, array(
            'validation_groups' => array('password_edit', 'password_edit_extranet'),
        ));
        $formPass->handleRequest($request);

        if ($form->isSubmitted()) {
            $existingUser = $entityManager->getRepository(Person::class)->findByEmailRole($person->getEmail(), $person->getRole());
            if ($existingUser !== null && $existingUser->getId() !== $person->getId()) {
                $form->get('email')->addError(new FormError($translator->trans("admin.user.emailRoleExist")));
            }
        }

        if (($form->isSubmitted() &&  $form->isValid()) || ($formPass->isSubmitted() &&  $formPass->isValid())) {
            $userManager->updateUser($person, true);
            $this->flashMessages->addSuccess('user.edit.success');
            $entityManager->flush();
            if ($user->isCoordinator()) {
                return $this->redirectToRoute('admin_user_monitoring', array('id' => $user->getId()));
            } else {
                return $this->redirectToRoute('admin_user_index');
            }
        }

        return $this->render('admin/user/register.html.twig', array(
            'person' => $person,
            'form' => $form,
            'formPass' => $formPass,
        ));
    }

    /**
     * Deletes a Person entity.
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_user_delete')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function delete(Request $request, Person $person, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($person);
        $form->handleRequest($request);
        $errorMessage = false;

        if($person->getFormers()->count() || $person->getCoordinators()->count() || $person->getCoordinatorsPerson()->count() || $person->getEvaluationGlobalAnswers()->count()) {

            if($person->getCoordinatorsPerson()->count()) {
                $errorMessage = 'user.delete.supervisor.error';
            }
            else {
                $errorMessage = 'user.delete.error';
            }
        }

        if ($form->isSubmitted() && $form->isValid()) {
            $em->remove($person);
            $em->flush();
            $this->flashMessages->addSuccess('user.delete.success');
            return $this->redirectToRoute('admin_user_index');
        }

        return $this->render('admin/user/delete.html.twig', array(
            'person' => $person,
            'form' => $form,
            'errorMessage' => $errorMessage,
        ));
    }

    #[Route(path: '/register/confirm', name: 'alienor_user_register_confirm')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function confirm(): RedirectResponse {
        $this->flashMessages->addSuccess('user.confirm.success');
        return $this->redirectToRoute('admin_user_index');
    }

    /**
     * Creates a form to delete a Person entity.
     */
    private function createDeleteForm(Person $person): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_user_delete', array('id' => $person->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * Demande de reset de mot de passe
     */
    #[Route(path: '/reset', name: 'admin_user_reset')]
    public function reset(Request $request, UserManager $userManager, TranslatorInterface $translator, MailerInterface $mailer, EntityManagerInterface $em, EmailAddressBuilder $emailAddressBuilder)
    {
        $form = $this->createFormBuilder()
            ->add('email', TextType::class)
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            if (!$person = $em->getRepository(Person::class)->findOneBy(array(
                    'email' => $form->get('email')->getData(),
                    'isEnabled' => true,
                )
            )) {
                $form->get('email')->addError(new FormError('Cet utilisateur n\'existe pas ou est désactivé'));
            }
            else {
                $person->setTokenExpireAt((new \DateTime())->modify('+ 7 day'));
                $person->setToken(md5($person->getSalt().uniqid()));

                $userManager->updateUser($person);

                // envoyer le mail
                $message = (new Email())
                    ->subject($translator->trans('user.mail.reset'))
                    ->from(...$emailAddressBuilder->buildAddress($this->getParameter('alienor_user.contact_sender')))
                    ->to(...$emailAddressBuilder->buildAddress($person->getEmail()))
                    ->html(
                        $this->renderView(
                            'admin/mail/reset.html.twig', array('person' => $person)
                        )
                    )
                ;
                try {
                    $mailer->send($message);
                } catch (TransportExceptionInterface) {
                    $this->flashMessages->addSuccess('user.reset.error');
                    return $this->redirectToRoute('admin_user_reset');
                }

                $this->flashMessages->addSuccess('user.reset.success');

                if ($person->getParticipant()) {
                    return $this->redirectToRoute('eduprat_audit_login');
                } else {
                    return $this->redirectToRoute('alienor_user_login');
                }
            }

        }

        return $this->render('admin/user/reset.html.twig', array(
            'form' => $form,
            'participant' => $request->query->get('participant') === "true"
        ));
    }

    /**
     * @param Request $request
     * @param         $token
     * @return array|RedirectResponse
     */
    #[Route(path: '/password-reset/{token}', name: 'eduprat_password_reset')]
    public function passwordReset(Request $request, $token, FormFactoryInterface $formFactory, UserManager $userManager, EntityManagerInterface $em)
    {
        /** @var Person $person */
        $person = $em->getRepository(Person::class)->findOneByToken($token);
        if (is_null($person) || (!is_null($person->getTokenExpireAt()) && $person->getTokenExpireAt() < new \DateTime())) {
            return $this->render('admin/user/change_password.html.twig', array(
                'error' => 'user.reset.error',
            ));
        }

        $validationGroups = array('password_edit');
        if ($person->getRole()) {
            $validationGroups[] = 'password_edit_extranet';
        }
        $form = $formFactory->create(ChangePasswordType::class, $person, array(
            'validation_groups' => $validationGroups,
        ));

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $newPassword = $form->getData()->getPlainPassword();
            $person->setPlainPassword($newPassword);
            $person->setToken(md5($person->getSalt().uniqid()));
            $person->setHasCreatedPassword(true);
            $userManager->updateUser($person, true);

            $this->flashMessages->addSuccess('user.resetted.success');

            if ($person->getParticipant()) {
                return $this->redirectToRoute('eduprat_audit_login');
            } else {
                return $this->redirectToRoute('alienor_user_login');
            }
        }

        return $this->render('admin/user/change_password.html.twig', array(
            'form' => $form,
            'person' => $person,
            'register' => $request->query->get('register') === "true"
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/password-edit', name: 'eduprat_password_edit')]
    public function editPassword(Request $request, FormFactoryInterface $formFactory, UserManager $userManager)
    {
        /** @var Person $user */
        $user = $this->getUser();

        $form = $formFactory->create(EditPasswordType::class, $user, array(
            "email" => $this->getUser()->getUsername(),
            'validation_groups' => array('password_edit')
        ));
        $form->handleRequest($request);


        if ($form->isSubmitted() && $form->isValid()) {
            $newPassword = $form->getData()->getPlainPassword();
            $user->setPlainPassword($newPassword);
            $user->setHasCreatedPassword(true);
            $userManager->updateUser($user, true);

            $this->flashMessages->addSuccess('user.resetted.success');
            return $this->redirectToRoute('admin_index');
        }

        return $this->render('admin/user/edit_password.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * show an existing Participant entity.
     *
     * @param Request                            $request
     * @param Person $person
     * @internal param \Eduprat\DomainBundle\Entity\Participant $participant
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/suivi/{page}', name: 'admin_user_monitoring', defaults: ['page' => '1'], requirements: ['page' => '^\d+$'])]
    public function show(Request $request, Person $person, string $page, ParticipationAccessManager $participationAccessManager, FormFactoryInterface $formFactory, CourseManager $courseManager, EntityManagerInterface $em)
    {
        $this->denyAccessUnlessCanShowPerson($person);

        $vars = array(
            'user' => $person,
            'accessManager' => $participationAccessManager,
            'courseManager' => $courseManager,
        );

        if (!$person->alreadyInIpList($_SERVER['REMOTE_ADDR'])) {
            $person->addIpList($_SERVER['REMOTE_ADDR']);
            $em->persist($person);
            $em->flush();
        }

        if ($person->isCoordinator()) {
            /** @var FormationRepository $personRepository */
            $formationRepository = $em->getRepository(Formation::class);
            $search = new SessionSearch();
            $search->archived = "non";

            $nbPerPage = 10;
            $max = 10;
            $count = $formationRepository->countSearchResults($search, $person);
            $formations = $formationRepository->findSearchResults($search, $page, $nbPerPage, $person);
            $npages = ceil($count / $nbPerPage);
            $current = intval($page);
            $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
            $sup = min($inf + ($max-1), $npages);
            $pageRange = range($inf, $sup);

            $forms_attestation_views = [];
            $forms_attestation_viewsN1 = [];
            foreach($formations as $formation) {
                if (!$formation->getProgramme()->isFormatPresentiel()) {
                    foreach($formation->getParticipations() as $participation) {
                        $form_attestation_views = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);
                        $form_attestation_viewsN1 = $this->drawUploadAttestationForm($request, 'attestationHonneurFileN1', $participation, $formFactory);

                        if (($form_attestation_views->isSubmitted() && $form_attestation_views->isValid())
                            || ($form_attestation_viewsN1->isSubmitted() && $form_attestation_viewsN1->isValid())) {
                            $em->persist($participation);
                            $em->flush();

                            if (($form_attestation_views->isSubmitted() && $participation->getAttestationHonneur())
                            || ($form_attestation_viewsN1->isSubmitted() && $participation->getAttestationHonneurN1())) {
                                $this->flashMessages->addSuccess('admin.participation.attestation_honneur.importSucess');
                            } else {
                                $this->flashMessages->addError('admin.participation.attestation_honneur.importError');
                            }

                            return $this->redirectToRoute('admin_user_monitoring', array(
                                'id' => $person->getId(),
                                'page' => $page
                            ));
                        }

                        $forms_attestation_views[$participation->getId()] = $form_attestation_views->createView();
                        $forms_attestation_viewsN1[$participation->getId()] = $form_attestation_viewsN1->createView();
                    }
                }
            }

            $vars = array_merge($vars, array(
                'nb' => $count,
                'page' => $page,
                'npages' => $npages,
                'page_range' => $pageRange,
                'formations' => $formations,
                'search' => $search,
                'search_route' => 'admin_user_monitoring',
                'forms_attestation_honneur' => $forms_attestation_views,
                'forms_attestation_honneurN1' => $forms_attestation_viewsN1
            ));
        }

        if ($person->isFormer()) {
            $formCv = $formFactory->createNamedBuilder("cvFile", FormType::class, $person)
                ->add('cvFile', VichFileType::class, array(
                    'download_uri' => false,
                    'label' => false,
                    'required' => false,
                    'translation_domain' => 'messages',
                    'constraints' => new FormFile(
                        array(
                            'mimeTypes' => array(
                                'image/jpeg', 'application/pdf', 'application/x-pdf'
                            )
                        )
                    )
                ))
                ->add('submit', SubmitType::class, array(
                    'label' => 'admin.global.send'
                ))
                ->getForm();

            $formDli = $formFactory->createNamedBuilder("dliFile", FormType::class, $person)
                ->add('dliFile', VichFileType::class, array(
                    'download_uri' => false,
                    'label' => false,
                    'required' => false,
                    'validation_groups' => ['dli'],
                    'translation_domain' => 'messages',
                    'constraints' => new FormFile(
                        array(
                            'mimeTypes' => array(
                                'image/jpeg', 'application/pdf', 'application/x-pdf'
                            )
                        )
                    )
                ))
                ->add('submit', SubmitType::class, array(
                    'label' => 'admin.global.send'
                ))
                ->getForm();

            
            $programmes = $em->getRepository(Programme::class)->findByFormer($person);

            $formCv->handleRequest($request);
            $formDli->handleRequest($request);


            if (($formCv->isSubmitted() && $formCv->isValid()) || $formDli->isSubmitted() && $formDli->isValid()) {
                $em->persist($person);
                $em->flush();
                return $this->redirectToRoute('admin_user_monitoring', array('id' => $person->getId()));
            }

            $vars = array_merge($vars, array(
                'formCv' => $formCv->createView(),
                'formDli' => $formDli->createView(),
                'programmes' => $programmes
            ));
        }

        $template = $person->isFormer()  ? 'former' : 'coordinator';

        return $this->render('admin/user/monitoring_' . $template . '.html.twig', $vars);
    }

    /**
     * @param Request                                $request
     * @param Person $person
     * @param Formation $formation
     * @return RedirectResponse|Response
     * @internal param \Eduprat\DomainBundle\Entity\Participant $participant
     */
    #[Route(path: '/{id}/suivi/evaluation/{formation}', name: 'admin_user_evaluation')]
    public function evaluation(Person $person, Formation $formation, EntityManagerInterface $em): Response
    {
        $this->denyAccessUnlessCanShowPerson($person);

        /** @var EvaluationFormerAnswer[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormerAnswer::class)->findByFormerAndFormation($person, $formation);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationFormerManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }


        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormerByCoordinator::class)->findByFormerProgramme($person, $formation->getProgramme());

        $repartitionsByCoordinator = [];

        for ($i = 1; $i <= EvaluationFormerByCoordinatorManager::NB_QUESTION; $i++) {
            $repartitionsByCoordinator[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitionsByCoordinator[$evaluation->getQuestion()]["count"]++;
            $repartitionsByCoordinator[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitionsByCoordinator as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }


        return $this->render('admin/user/evaluation.html.twig', array(
            'evaluation' => $repartitions,
            'evaluationByCoordinators' => $repartitionsByCoordinator,
            'user' => $person,
            'formation' => $formation
        ));
    }

    /**
     * @param Person $person
     * @return RedirectResponse|Response
     * @internal param \Eduprat\DomainBundle\Entity\Participant $participant
     */
    #[Route(path: '/{id}/suivi/evaluation-recap/{year}', name: 'admin_user_evaluation_recap')]
    public function evaluationRecap(Person $person, $year, EntityManagerInterface $em): Response
    {
        $this->denyAccessUnlessCanShowPerson($person);

        /** @var EvaluationFormerAnswer[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormerAnswer::class)->findByFormerAndYear($person, $year);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationFormerManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if($repartition["count"]) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        return $this->render('admin/user/evaluation_recap.html.twig', array(
            'evaluation' => $repartitions,
            'user' => $person
        ));

//        return $this->render('admin/user/evaluation_recap.html.twig', array());
    }

    /**
     * @param Person $person
     * @param                                    $year
     * @return Response
     */
    #[Route(path: '/coordinator-table/{id}/{year}', name: 'admin_coordinator_table')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function coordinatorTable(Person $person, $year, CoordinatorHonorary $coordinatorHonoraryService, EntityManagerInterface $em): Response
    {
        $this->denyAccessUnlessCanShowPerson($person);

        /** @var ArrayCollection|Formation[] $formations */
        $formations = $person->getFormationDones($year, false);

        $total = array(
            "participants" => $this->arrayCollectionSum($formations, function(Formation $f) use ($person) {
                if($f->getCoordinators()->count() > 1) {
                    return sizeof($f->getParticipationsByCoordinator($person));
                }
                else {
                    return sizeof($f->getParticipations());
                }
            }),
            "orateurs" => $this->arrayCollectionSum($formations, function(Formation $f) { return $f->getFormerHonorary();}),
            "restaurants" => $this->arrayCollectionSum($formations, function(Formation $f) use ($person) { return $f->getRestaurationHonoraryByCoordinator($person);}),
            "cr" => 0
        );

        $repartitions = [];
        for ($i = 1; $i <= 12; $i++) {
            $repartitions[$i] = 0;
        }

        $budgetCRs = array();

        $coordinatorRepo = $em->getRepository(Coordinator::class);

        foreach ($formations as $key => $formation) {
            $budgetCRs[$formation->getId()]['coordinator'] = $coordinatorRepo->findCoordinatorByFormationAndPerson($formation, $person);
            $budgetCRs[$formation->getId()]['honorary'] = $coordinatorHonoraryService->calculTotalHonorary($formation, $budgetCRs[$formation->getId()]['coordinator'], false, true, false);
            $total['cr'] += $budgetCRs[$formation->getId()]['honorary']['total'];
            $budgetCRs[$formation->getId()]['displayPDF'] = $coordinatorHonoraryService->displayCoordinatorsHonoraryByProgramme($formation);
        }

        $formations->forAll(function($i, Formation $formation) use ($repartitions) {
            if(!isset($repartitions[$formation->getOpeningDate()->format('m')])) {
                $repartitions[$formation->getOpeningDate()->format('m')] = 0;
            }
            $repartitions[$formation->getOpeningDate()->format('m')]++;
        });

        return $this->render('admin/default/coordinator_table.html.twig', array(
            'first_year' => $em->getRepository(Formation::class)->findFirst()->getStartDate()->format('Y'),
            'person' => $person,
            'year' => $year,
            'formations' => $formations,
            'total' => $total,
            'maxMonth' => max(array_values($repartitions)),
            'budgetCRs' => $budgetCRs,
            'newHonorary' => new \DateTime("$year-01-01") >= new \DateTime($this->getParameter('honoraires.migration_date'))
        ));
    }

    /**
     * @param Person $person
     * @param                                    $year
     * @return Response
     */
    #[Route(path: '/coordinator-dashboard/{id}/{year}', name: 'admin_coordinator_dashboard')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function coordinatorDashboard(CoordinatorPerson $person, $year, CoordinatorDashboard $coordinatorDashboardService): Response
    {
        $this->denyAccessUnlessCanShowPerson($person);

        $indicators = [
            'weekRegistrations' => $coordinatorDashboardService->getWeeklyRegistrationsCount($person),
            'monthRegistrations' => $coordinatorDashboardService->getMonthlyRegistrationsCount($person),
            'inscriptionsWithoutCosts' => $coordinatorDashboardService->getInscriptionsWithoutCosts($person),
            'weekUnsubscriptions' => 0,
            'missingFiles' => $coordinatorDashboardService->getMissingFiles($person),
            'missingModules' => $coordinatorDashboardService->getMissingModules($person),
            'missingModules72Hours' => $coordinatorDashboardService->getMissingModules($person, $echeance72Hours = true),
            'missingAttestations' => $coordinatorDashboardService->getMissingAttestations($person),
            'missingAttestations2Weeks' => $coordinatorDashboardService->getMissingAttestations($person, $echeance2Weeks = true),
        ];


        /** @var ArrayCollection|Programme[] $programmes */
        $programmes = $person->getFormationDones($year, true);

        $total = $person->getCommissionExceptionnelle($year, true);

        /** @var Participation[] $participations */
        $participations = $person->getCoordinatorsFormationsParticipationsByCoordinator($year);

        $results = array();
        
        foreach ($participations as $participation) {
            $category = $participation->getParticipant()->getCategory();
            if (!isset($results[$category])) {
                $results[$category] = array(
                    "participations" => array(),
                    "participants" => array(),
                );
            }
            $results[$category]["participations"][] = $participation->getId();
            $results[$category]["participants"][] = $participation->getParticipant()->getId();
        }

        $totals =  array(
            "participations" => 0,
            "participants" => 0,
        );

        foreach ($results as $key => $result) {
            $results[$key]["participations"] = count(array_unique($results[$key]["participations"]));
            $results[$key]["participants"] = count(array_unique($results[$key]["participants"]));
            $totals["participations"] += $results[$key]["participations"];
            $totals["participants"] += $results[$key]["participants"];
        }

        return $this->render('admin/default/coordinator_dashboard.html.twig', array(
            'first_year' => (new \DateTime($this->getParameter('honoraires.migration_date')))->format('Y'),
            'person' => $person,
            'year' => $year,
            'total' => $total,
            'counts' => array("totals" => $totals, "categories" => $results),
            'programmes' => $programmes,
            'allProgrammes' => $person->getFormationDones($year, false),
            'CAObjectifRealiseDTO' => new CAObjectifRealiseDTO($person, $year, $total, $totals["participations"]),
            'indicators' => $indicators,
        ));
    }

    function arrayCollectionSum(ArrayCollection $collection, $getter) {
        return array_reduce(
            $collection->map($getter)->toArray(),
            function($a, $b) { $a += $b; return $a; }
        );
    }

    #[Route(path: '/{id}/file/cv', name: 'admin_user_file_cv')]
    public function fileCv(Person $person, DownloadHandler $downloadHandler)
    {
        return $downloadHandler->downloadObject($person, $fileField = 'cvFile', null, $person->getCv(), false);
    }

    #[Route(path: '/{id}/file/dli', name: 'admin_user_file_dli')]
    public function fileDli(Person $person, DownloadHandler $downloadHandler)
    {
        return $downloadHandler->downloadObject($person, $fileField = 'dliFile', null, $person->getDli(), false);
    }

    /**
     * @param Person $user
     * @param Request                            $request
     * @return RedirectResponse
     */
    #[Route(path: '/simulation/{id}', name: 'admin_user_simulation')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function simulation(Person $user, Request $request, EventDispatcherInterface $eventDispatcher, TokenStorageInterface $tokenStorage, AccountSwitcher $accountSwitcher, EntityManagerInterface $em): RedirectResponse
    {
        $session = $request->getSession();
        if ($session->get('real_user')) {
            $this->exitSimulation($request, $eventDispatcher, $tokenStorage, $accountSwitcher, $em);
        }
        
        if ($this->getUser()->isCoordinator()) {
            if ($user->getParticipant()) {
                $this->denyAccessUnlessGranted('view', $user->getParticipant());
            } else {
                throw $this->createAccessDeniedException();
            }
        }
        
        if (!$user) {
            throw new UserNotFoundException("Utilisateur inconnu");
        } else {
            if (count($user->getRoles()) === 0) {
                $user->setRoles(array('ROLE_USER'));
            }
            $session->set('real_user', $this->getUser()->getId());
            $token = new UsernamePasswordToken($user, 'main', $user->getRoles());
            $tokenStorage->setToken($token); //now the user is logged in

            //now dispatch the login event
            $event = new InteractiveLoginEvent($request, $token);
            $eventDispatcher->dispatch($event, "security.interactive_login");

            $accountSwitcher->prepareAccountSwitching($user);

            if ($user->getRole() === "ROLE_USER" || $request->query->has('role')) {
                $session->set('real_user_redirect', $request->headers->get('referer'));
                return $this->redirectToRoute('eduprat_audit_index');
            } else {
                $session->set('real_user_redirect', $request->headers->get('referer'));
                return $this->redirectToRoute('admin_index');
            }
        }
    }

    /**
     * @param Person $user
     * @param Request                            $request
     * @return RedirectResponse
     */
    #[Route(path: '/switch/{id}/{role}', name: 'admin_user_switch')]
    public function switch(Person $user, $role, Request $request, AccountSwitcher $accountSwitcher): RedirectResponse
    {
        if (!$user) {
            throw new UsernameNotFoundException("Utilisateur inconnu");
        } else {
            $accountSwitcher->switchAccount($request, $user);
            $accountSwitcher->prepareAccountSwitching($user);

            if ($user->getParticipant() && $role === Person::ROLE_PARTICIPANT) {
                return $this->redirectToRoute('eduprat_front_formations', array('year' => (new \DateTime())->format('Y')));
            } else {
                return $this->redirectToRoute('admin_index');
            }
        }
    }

    /**
     * @param Person $user
     * @param Request                            $request
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/transfer', name: 'admin_user_transfer')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function transfer(Person $person, Request $request, EntityManagerInterface $entityManager)
    {
        if (!$person->isCoordinator()) {
            throw new NotFoundHttpException();
        }

        $form = $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_user_transfer', array('id' => $person->getId())))
            ->setMethod(Request::METHOD_POST)
            ->add('coordinator', EntityType::class, array(
                'class' => 'Eduprat\AdminBundle\Entity\Person',
                'label'    => 'admin.participant.coordinator',
                'choice_label' => function($person) {
                    return $person->getLastname().' '.$person->getFirstname();
                },
                'expanded' => false,
                'multiple' => false,
                'required' => true,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->setParameter('roles', '%"ROLE_COORDINATOR%')
                        ->orderBy('p.lastname', 'ASC');
                },
            ))
            ->getForm()
        ;

        $form->handleRequest($request);

        if ($form->isSubmitted()) {

            /** @var Person $newCoordinator */
            $newCoordinator = $form->get("coordinator")->getData();

            foreach ($person->getParticipants() as $index => $participant) {
                $participant->setCoordinator($newCoordinator);
                $entityManager->persist($participant);

                if ($index % 100 === 0) {
                    $entityManager->flush();
                }
            }

            $entityManager->flush();

            $this->flashMessages->addSuccess('user.transfer.success');

            return $this->redirectToRoute('admin_user_edit', array("id" => $person->getId()));
        }

        return $this->render('admin/user/transfer.html.twig', array(
            'person' => $person,
            'form' => $form,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse
     */
    #[Route(path: '/simulation_exit', name: 'admin_user_simulation_exit')]
    public function simulationExit(Request $request, EventDispatcherInterface $eventDispatcher, TokenStorageInterface $tokenStorage, AccountSwitcher $accountSwitcher, EntityManagerInterface $em): RedirectResponse
    {
        $session = $request->getSession();
        if (!$session->get('real_user')) {
            throw new AccessDeniedHttpException("Vous ne simuler l'utilisation d'aucun compte utilisateur");
        } else {
            $this->exitSimulation($request, $eventDispatcher, $tokenStorage, $accountSwitcher, $em);
            $redirectRoute = $this->generateUrl('admin_participant_index');
            if ($session->has('real_user_redirect') && $redirectRoute = $session->get('real_user_redirect')) {
                $session->remove('real_user_redirect');
            }
            return $this->redirect($redirectRoute);
        }
    }

    public function exitSimulation(Request $request, EventDispatcherInterface $eventDispatcher, TokenStorageInterface $tokenStorage, AccountSwitcher $accountSwitcher, EntityManagerInterface $em) {
        $session = $request->getSession();
        $user = $session->get('real_user');
        /** @var Person $user */
        $user = $em->getRepository(Person::class)->find($user);
        $token = new UsernamePasswordToken($user, "main", $user->getRoles());
        $tokenStorage->setToken($token); //now the user is logged in
        //now dispatch the login event
        $event = new InteractiveLoginEvent($request, $token);
        $eventDispatcher->dispatch($event, "security.interactive_login");
        $session->remove('real_user');
        $accountSwitcher->prepareAccountSwitching($user);
    }

    public function denyAccessUnlessCanShowPerson(Person $person) {
        if (!$this->isGranted('ROLE_WEBMASTER') && ($person->getId() !== $this->getUser()->getId())) {
            if ($this->getUser()->isSupervisor() && !$this->getUser()->getCoordinatorsPerson()->contains($person)) {
                throw $this->createAccessDeniedException();
            }
        }
    }

    public function drawUploadAttestationForm(Request $request, $fieldName, $participation, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participation->getId(), FormType::class, $participation);
        $this->drawUploadFormField($genForm, $fieldName, false, '12M');
        $form = $genForm->getForm();
        $form->handleRequest($request);
        return $form;
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize='128M') {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new FormFile(
                array(
                    'mimeTypes' => array(
                        'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesAttestation',
                    'maxSize' => $maxSize
                )
            )
        ));
    }

}
