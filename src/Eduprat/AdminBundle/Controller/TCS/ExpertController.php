<?php

namespace Eduprat\AdminBundle\Controller\TCS;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\DTO\ExpertDto;
use Ed<PERSON>rat\DomainBundle\Entity\TCS\Expert;
use Eduprat\DomainBundle\Form\ExpertType;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\DomainBundle\Entity\TCS\ExpertTCSAnswer;
use Eduprat\DomainBundle\Form\TCS\AnswerTcsQuestionGroupType;
use Eduprat\DomainBundle\Entity\TCS\ExpertTCSAnswersDto;
use Eduprat\DomainBundle\Entity\TCS\GroupeQuestionTCS;
use Eduprat\DomainBundle\Entity\TCS\QuestionnaireTCS;
use Eduprat\DomainBundle\Entity\TCS\QuestionnaireTCSExpert;
use Eduprat\DomainBundle\Repository\TCS\ExpertRepository;
use Eduprat\DomainBundle\Services\TcsQuestionScoreCalculator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/expert')]
class ExpertController extends AbstractController
{
    #[Route('/', name: 'app_eduprat_admin_bundle_controller_expert_index', methods: ['GET'])]
    public function index(ExpertRepository $expertRepository): Response
    {
        return $this->render('admin/expert/index.html.twig', [
            'experts' => $expertRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_eduprat_admin_bundle_controller_expert_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $expert = new ExpertDto();
        $form = $this->createForm(ExpertType::class, $expert);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $newExpert = Expert::expertFromExpertDTO($expert);
            $entityManager->persist($newExpert);
            $entityManager->flush();

            if ($request->query->has("redirectQuestionnaire")) {
                return $this->redirectToRoute('app_admin_tcs_questionnaire_create_tcs_answer', array("id" => $request->query->get('redirectQuestionnaire'), 'expert' => $newExpert->getId()));
            }

            return $this->redirectToRoute('app_eduprat_admin_bundle_controller_expert_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/expert/new.html.twig', [
            'expert' => $expert,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_eduprat_admin_bundle_controller_expert_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Expert $expert, EntityManagerInterface $entityManager): Response
    {

        $expertDto = (new ExpertDto)->fromExpert($expert);
        $form = $this->createForm(ExpertType::class, $expertDto);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $expert->loadValueFromDto($expertDto);
            $entityManager->flush();

            return $this->redirectToRoute('app_eduprat_admin_bundle_controller_expert_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/expert/edit.html.twig', [
            'expert' => $expert,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_eduprat_admin_bundle_controller_expert_delete', methods: ['POST'])]
    public function delete(Request $request, Expert $expert, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$expert->getId(), $request->request->get('_token'))) {
            $entityManager->remove($expert);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_eduprat_admin_bundle_controller_expert_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/{expert}/create_tcs_answer', name: 'app_admin_tcs_questionnaire_create_tcs_answer', methods: ['GET', 'POST'])]
    public function createQuestionnaireTcsExpert(Expert $expert, QuestionnaireTCS $questionnaireTC, EntityManagerInterface $entityManager): Response
    {
        if (!$questionnaireTC->isEditable()) {
            throw $this->createAccessDeniedException();
        }

        $questionnaireTcsExpert = new QuestionnaireTCSExpert($expert, $questionnaireTC);
        $questionnaireTcsExpert->getQuestionnaireTCS()->forceUpdateAt();
        $entityManager->persist($questionnaireTcsExpert);
        $entityManager->flush();

        return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_answer', array("questionnaireTcsExpert" => $questionnaireTcsExpert->getId()));
    }

    #[Route('/{questionnaireTcsExpert}/tcs_answer', name: 'app_admin_tcs_questionnaire_tcs_answer', methods: ['GET', 'POST'])]
    public function indexAnswer(QuestionnaireTCSExpert $questionnaireTcsExpert): Response
    {
        $groupeQuestionTCS = $questionnaireTcsExpert->getQuestionnaireTCS()->getGroupeQuestionsTCS()->first();
        return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_answer_group', array("questionnaireTcsExpert" => $questionnaireTcsExpert->getId(), "groupeQuestionTCS" => $groupeQuestionTCS->getId()));
    }

    #[Route('/{questionnaireTcsExpert}/{groupeQuestionTCS}/tcs_answer_group', name: 'app_admin_tcs_questionnaire_tcs_answer_group', methods: ['GET', 'POST'])]
    public function answerTcsQuestionGroup(Request $request, QuestionnaireTCSExpert $questionnaireTcsExpert, GroupeQuestionTCS $groupeQuestionTCS, EntityManagerInterface $entityManager, TcsQuestionScoreCalculator $tcsQuestionScoreCalculator): Response
    {
        $expertTCSAnswers = new ArrayCollection();
        foreach ($groupeQuestionTCS->getQuestionsTCS() as $question) {
            $expertTCSAnswers[] = $questionnaireTcsExpert->getQuestionAnswers($question) ? $questionnaireTcsExpert->getQuestionAnswers($question) : new ExpertTCSAnswer($questionnaireTcsExpert, $question);
        }

        $expertTCSAnswersDto = new ExpertTCSAnswersDto($expertTCSAnswers);

        $form = $this->createForm(AnswerTcsQuestionGroupType::class, $expertTCSAnswersDto, ['canEdit' => $questionnaireTcsExpert->getQuestionnaireTCS()->isEditable()]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            foreach ($expertTCSAnswersDto->expertTCSAnswers as $expertTCSAnswer) {
                $entityManager->persist($expertTCSAnswer);
            }
            $questionnaireTcsExpert->getQuestionnaireTCS()->forceUpdateAt();

            $entityManager->flush();

            $nextTcsGroup = $groupeQuestionTCS->getQuestionnaireTCS()->getNextTcsGroup($groupeQuestionTCS);

            if ($nextTcsGroup) {
                return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_answer_group', array("questionnaireTcsExpert" => $questionnaireTcsExpert->getId(), "groupeQuestionTCS" => $nextTcsGroup->getId()));
            } else {
                return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_show', [
                    'id' => $groupeQuestionTCS->getQuestionnaireTCS()->getId()
                ], Response::HTTP_SEE_OTHER);
            }
        }

        return $this->render('admin/tcs/questionnaire_tcs/expert_answer.html.twig', [
            'questionnaire_tcs' => $questionnaireTcsExpert->getQuestionnaireTCS(),
            'questionnaireTcsExpert' => $questionnaireTcsExpert,
            'groupeQuestionTCS' => $groupeQuestionTCS,
            'form' => $form,
        ]);
    }

    #[Route('/{questionnaireTcsExpert}/deleteExpertAnswers', name: 'app_admin_tcs_question_tcs_expert_answers_delete', methods: ['GET', 'POST'])]
    public function deleteExpertAnswers(Request $request, QuestionnaireTCSExpert $questionnaireTcsExpert, EntityManagerInterface $entityManager): Response
    {
        $questionnaireTCS = $questionnaireTcsExpert->getQuestionnaireTCS();

        if (!$questionnaireTCS->isEditable()) {
            throw $this->createAccessDeniedException();
        }

        if ($this->isCsrfTokenValid('delete'.$questionnaireTcsExpert->getId(), $request->request->get('_token'))) {
            $entityManager->remove($questionnaireTcsExpert);
            $questionnaireTCS->forceUpdateAt();

            $entityManager->flush();
            return $this->redirectToRoute('app_admin_tcs_questionnaire_tcs_show', [
                'id' => $questionnaireTCS->getId(),
            ], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/tcs/questionnaire_tcs/deleteExpertAnswer.html.twig', [
            'questionnaireTcsExpert' => $questionnaireTcsExpert,
        ]);
    }
}
