<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\Persistence\ObjectManager;
use Alienor\ApiBundle\Services\FlashMessages;
use Alienor\EmailBundle\Services\Sender;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Http\CsvFileResponse;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\CsvBilanExport;
use Eduprat\AdminBundle\Services\CsvImporter;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\CsvImporterZoom;
use Eduprat\AdminBundle\Services\ElasticSynchronizer;
use Eduprat\AdminBundle\Services\IpPersonSaver;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\AdminBundle\Services\FacturationStateManager;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\ConventionFile;
use Eduprat\DomainBundle\Entity\EmargementFile;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\CoordinatorFiles;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\FormateurFiles;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\FormationHistory;
use Eduprat\DomainBundle\Entity\ParticipantActionSheet;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\TopoFiles;
use Eduprat\DomainBundle\Events\ModuleDoneAndNextAssignIfExists;
use Eduprat\DomainBundle\Events\SessionEdited;
use Eduprat\DomainBundle\Form\FormationType;
use Eduprat\DomainBundle\Repository\CoordinatorFilesRepository;
use Eduprat\DomainBundle\Repository\FormateurFilesRepository;
use Eduprat\DomainBundle\Services\AttestationService;
use Eduprat\DomainBundle\Services\Email\BaseEmailParticipation;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Symfony\Component\Routing\Attribute\Route;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Eduprat\DomainBundle\Model\SessionSearch;
use Symfony\Component\Asset\Packages;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\DomainBundle\Form\CoordinatorType;
use Eduprat\DomainBundle\Form\FormateurType;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Eduprat\AdminBundle\Services\FormationBuilderInterface;
use Eduprat\AdminBundle\Services\FormationBuilder;
use Eduprat\AdminBundle\Services\FormationVfcBuilder;
use Eduprat\AdminBundle\Services\FormationTcsBuilder;
use Eduprat\DomainBundle\Entity\FactureState;
use Symfony\Component\Process\Process;

/**
 * Class SessionController
 */
#[Route(path: '/session')]
#[IsGranted('ROLE_COORDINATOR_LBI')]
class SessionController extends EdupratController
{
    /**
     * @var PropertyAccessorInterface
     */
    private $propertyAccessor;

    private ParticipationLogger $participationLogger;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler, PropertyAccessorInterface $propertyAccessor, TranslatorInterface $translator, Packages $packages, UploaderHelper $uploaderHelper, ParticipationLogger $participationLogger)
    {
        parent::__construct($flashMessages, $searchHandler, $translator, $packages, $uploaderHelper);
        $this->propertyAccessor = $propertyAccessor;
        $this->participationLogger = $participationLogger;
    }

    /**
     * Lists all Formation entities.
     *
     * @param int $programme
     * @return Response
     */
    #[Route(path: '/{programme}', methods: ['GET'], name: 'admin_formation_index')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function index($programme, EntityManagerInterface $em): Response
    {
        $formationsRepository = $em->getRepository(Formation::class);
        $formations = $formationsRepository->findByProgramme($programme);

        return $this->render('admin/formation/index.html.twig', array(
            'formations' => $formations,
        ));
    }

    /**
     * Lists all Formation entities.
     *
     * @param int $page
     * @param int $year
     * @param int $programme
     * @param Request $request
     * @param CoordinatorHonorary $coordinatorHonoraryService
     * @return Response
     */
    #[Route(path: '/{year}/{page}/{programme}', methods: ['GET', 'POST'], defaults: ['year' => 'tous', 'page' => '1', 'programme' => null], name: 'admin_formation_index', requirements: ['year' => '^(tous|\d{4})$', 'page' => '^\d+$', 'programme' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR_LBI')]
    public function index2(string $year, string $page, Request $request, CoordinatorHonorary $coordinatorHonoraryService, ParticipationAccessManager $participationAccessManager, SearchHandler $searchHandler, EntityManagerInterface $em, string $programme = null)
    {
        if ($programme) {
            $programmeRepository = $em->getRepository(Programme::class);
            $programme = $programmeRepository->findOneById($programme);
        }
        /** @var Person $user */
        $user = $this->getUser();

        $searchHandle = $searchHandler->handle(SessionSearch::class, array(
            'year' => $year != 'tous' ? $year : null,
        ), array(
            "year" => $year,
            "programme" => $programme
        ), array(
            "user" => $user
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var SessionSearch $search */
        $search = $searchHandle["search"];
        if ($year) {
            $search->year = intval($year);
        }

        $nbPerPage = 10;

        $formationsRepository = $em->getRepository(Formation::class);

        $count = $formationsRepository->countSearchResults($search, $user);

        $formations = $formationsRepository->findSearchResults($search, $page, $nbPerPage, $user);

        $pagination = $searchHandler->getPagination($count, $formations, $page, $nbPerPage);

        $budgetCRs = array();

        foreach ($formations as $formation) {
            $budgetCRs[$formation->getId()] = array();
            $budgetCRs[$formation->getId()]['displayPDF'] = $coordinatorHonoraryService->displayCoordinatorsHonoraryByProgramme($formation);
        }

        $formationsHasParticipation = $em->getRepository(Formation::class)->hasParticipation($formations);

        return $this->render('admin/formation/index2.html.twig', array_merge($pagination, array(
            'search' => $search,
            'search_form' => $searchHandle["searchForm"]->createView(),
            'formations' => $formations,
            'open' => $request->query->get('open'),
            'budgetCRs' => $budgetCRs,
            'accessManager' => $participationAccessManager,
            'formationsHasParticipation' => $formationsHasParticipation,
        )));
    }

    /**
     * @param Programme $programme
     * @return Response $type
     */
    #[Route(path: '/choice_type/{programme}', name: 'admin_formation_choice_type')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function choiceType(Programme $programme) {
        $canBeActalians = false;

        // foreach ($programme->getFormateursPersons() as $person) {
        //     $canBeActalians = $canBeActalians || $person->hasRole("ROLE_FORMER_PHARMACIE");
        // }

        if ($programme->getSessionType()) {
            return $this->redirectToRoute('admin_formation_create', array(
               'programme' => $programme->getId(),
               'type' => $programme->getSessionType()
            ));
        }

        return $this->render('admin/formation/choice_type.html.twig', array(
            'programme' => $programme->getId(),
            'canBeActalians' => $canBeActalians,
            'types' => Formation::TYPES
        ));
    }

    /**
     * @param Programme $programme
     * @return Response $type
     */
    #[Route(path: '/choice_type_elearning/{programme}', name: 'admin_formation_choice_type_elearning')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function choiceTypeElearning(Programme $programme): Response {
        return $this->render('admin/formation/choice_type_elearning.html.twig', array(
            'programme' => $programme->getId(),
            'types' => Formation::TYPES
        ));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param                                           $type
     * @param EntityManagerInterface $entityManager
     * @param EmailSender $emailSender
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create/{programme}/{type}', name: 'admin_formation_create')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function create(Request $request, Programme $programme, $type, EntityManagerInterface $entityManager, EmailSender $emailSender, CourseManager $courseManager) {
        $formationClass = Formation::getClassByType($type);
        $formClass = Formation::getFormClassByType($type);

        $builder = $this->getFormationBuilder($formationClass);
        if ($request->query->has("copy")) {
            $baseFormation = $entityManager->getRepository(Formation::class)->find($request->query->get('copy'));
            if ($baseFormation === null) {
                throw new NotFoundHttpException();
            }
            $formation = $baseFormation->createFormationFrom();
            $formation->setCourse($builder->buildCourse());
            $formation->setFormOpeningDate(null);
            $formation->setFormClosingDate(null);
            $formation->addModuleTime(clone $baseFormation->getModuleTimes()->first());
            $formation->addModuleMinTime(clone $baseFormation->getModuleMinTimes()->first());
        } else {
            $formation = $builder->buildFormation($formationClass, $programme);
        }


        $formation->loadInfosFromProgramme($programme);

        $form = $this->createForm($formClass, $formation, ['isVignette' => $formation->isVignette(), 'programme' => $formation->getProgramme()]);
        $form->handleRequest($request);
        $exercisesMode = '';

        if ($form->isSubmitted() && $form->isValid()) {
            // if($form->get("exercisesMode")->getData()) {
            //     $exercisesMode = $form->get("exercisesMode")->getData();
            // }

            // if($exercisesMode == 'lib_sant_conv') {
            //     $formation->setExercisesMode(array('Libéral', 'Salarié en centre de santé conventionné'));
            // }
            // else {
            //     $formation->setExercisesMode(array($exercisesMode));
            // }

            $date = New DateTime('now');
            $formation->updateDates(true);

            // différence entre nouvelle date et aujourd'hui
            $dateDiff = date_diff($date, $formation->getStartDate());
            $dateDiff = abs($dateDiff->format('%R%a'));

            if ($dateDiff <= 30) {
                foreach($form->get("formateurs")->getData() as $formateur) {
                    if (is_null($formateur->getPerson()->getSiret())) {
                        $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, true);
                    }
                }
            }

            $programme->setSessionType($type);
            $formation->refreshFinanceSousModeFactures();

            if ($formation->getApplyTimesToAll() && count($formation->getProgramme()->getFormations()) > 1) {
                foreach ($formation->getProgramme()->getFormations() as $sisFormation) {
                    if ($sisFormation != $formation && $sisFormation->getFormOpeningDate() > new DateTime()) {
                        $sisFormation->cloneModuleTimes($formation);
                        $sisFormation->cloneModuleMinTimes($formation);
                    }
                }
            }

            $entityManager->persist($formation);

            $factureState = new FactureState($formation);
            $entityManager->persist($factureState);
            $formation->setFactureState($factureState);

            $entityManager->flush();
            $this->flashMessages->addSuccess('formation.create.success');
            return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
        }

        $reunionUnity = $formation->getUnityByPosition($formation->getProgramme()->getReunionUnityPosition());
        return $this->render('admin/formation/create.html.twig', array(
            'form' => $form,
            'programme' => $programme,
            'type' => $type,
            'showArchivedAlert' => false,
            'threeUnityComposed' => $formation->isThreeUnity(),
            'nbDays' => $reunionUnity ? count($reunionUnity->getUnitySessionDates()) : false,
            'elearningOneUnity' => $formation->getProgramme()->isElearningOneUnity(),
            "modules" => $courseManager->getModuleList($formation)
        ));
    }

    /**
     * Displays a form to edit an existing Formation entity.
     *
     * @param Request   $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_formation_edit')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function edit(Request $request, Formation $formation, FormFactoryInterface $formFactory,  EmailSender $emailSender, CourseManager $courseManager, EntityManagerInterface $em, EventDispatcherInterface $eventDispatcher)
    {
        $session = $request->getSession();
        $hasForm = $formation->hasLinkedForm();
        $formationForm = $hasForm ? clone $formation->getForm() : null;
        $formClass = Formation::getFormClassByType($formation->getDiscr());

        $formationFormateursBefore = [];
        foreach ($formation->getFormateurs() as $formateur) {
            $formationFormateursBefore[] = clone $formateur;
        }
        $firstDate = clone $formation->getStartDate();
        $partenariat = $formation->getPartenariat();
        $ots = $formation->getOts();
        $firstAdresse = $formation->getAddress() ?: null;
        $firstAdresse2 = $formation->getAddress2() ? $formation->getAddress() : null;
        $firstFromateurs = $formation->getFormateursNames() ? $formation->getFormateursNames() : null;

        /** @var Formation $formation */
        $form = $formFactory->create($formClass, $formation, array(
            'exercisesMode' => $formation->getProgramme()->getExercisesMode(),
            'locked' => $formation->isLocked(),
            'isVignette' => $formation->isVignette(),
            'programme' => $formation->getProgramme(),
        ));
        $form->handleRequest($request);

        if (!$form->isSubmitted() && $request->headers->get('referer') != $request->getUri()) {
            $session->set('session-edit-redirect', $request->headers->get('referer'));
        }

        $cloneFormationUnities = clone $formation->getUnities();
        if ($form->isSubmitted() &&  $form->isValid()) {

            $formation->updateDates(false, $cloneFormationUnities);

            if($hasForm) {
                if(!$formation->getForm() || ($formationForm->getId() != $formation->getForm()->getId())) {
                    foreach($formation->getAllParticipations() as $participation) {
                        if($participation->getCompletedForm1()) {
                            $participation->setCompletedForm1(false);
                            foreach($participation->getAuditAnswers() as $answer){
                                $em->remove($answer);
                            }
                            $participation->setAuditAnswers(new ArrayCollection());
                        }
                    }
                }
            }

            // Gestion du champ type de mofification pour la notification de l'application
            $dateChanged = false;
            $addressChanged = false;
            $formateursChanged = false;
            if ($firstDate != $formation->getStartDate()) {
                $dateChanged = true;
            }
            if ($firstAdresse != $formation->getAddress() || $firstAdresse2 != $formation->getAddress2()) {
                $addressChanged = true;
            }

            if ($firstFromateurs != $formation->getFormateursNames()) {
                $formateursChanged = true;
            }
            $formation->setTypeModifFromChange($dateChanged, $addressChanged, $formateursChanged);

            if($partenariat != $formation->getPartenariat() || $ots != $formation->getOts()) {
                foreach($formation->getParticipations() as $participation) {
                    $participant = $participation->getParticipant();
                    if(!$participant->isLeadOn()) {
                        if($partenariat != $formation->getPartenariat()) {
                            $participation->setPartenariat($formation->getPartenariat());
                            if(!$participant->getPartenariat()) {
                                $participant->setPartenariat($formation->getPartenariat());
                                $em->persist($participant);
                            }
                        }
                        if($ots != $formation->getOts()) {
                            $participation->setOts($formation->getOts());
                            if(!$participant->getOts()) {
                                $participant->setOts($formation->getOts());
                                $em->persist($participant);
                            }
                        }
                        $em->persist($participation);
                    }
                }
            }


            $date = New DateTime('now');

            // différence entre nouvelle date et aujourd'hui
            $dateDiff = date_diff($date, $formation->getStartDate());
            $dateDiff = abs($dateDiff->format('%R%a'));

            // différence entre ancienne date et aujourd'hui
            $firstDateDiff = date_diff($date, $firstDate);
            $firstDateDiff = abs($firstDateDiff->format('%R%a'));

            $diffStartNew = abs(date_diff($firstDate, $formation->getStartDate())->format('%R%a'));

            if ($diffStartNew !== 0) {
                // Cas sortie 30 jours glissants
                if (($firstDate > $date) && ($firstDateDiff <= 30) && ($dateDiff > 30)) {
                    foreach ($formationFormateursBefore as $formateur) {
                        if (is_null($formateur->getPerson()->getSiret())) {
                            $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, false);
                        }
                    }
                }
                // Cas rentré dans 30 jours glissants
                elseif (($firstDate > $date) && ($firstDateDiff > 30) && ($dateDiff <= 30)) {
                    foreach($form->get("formateurs")->getData() as $formateur) {
                        if (is_null($formateur->getPerson()->getSiret())) {
                            $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, true);
                        }
                    }
                }
                // Cas déjà dans 30 jours glissants et y reste
                elseif (($firstDate > $date) && ($firstDateDiff <= 30) && ($dateDiff <= 30)) {
                    foreach ($formationFormateursBefore as $formateur) {
                        if (is_null($formateur->getPerson()->getSiret())) {
                            $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, false);
                        }
                    }
                    foreach($form->get("formateurs")->getData() as $formateur) {
                        if (is_null($formateur->getPerson()->getSiret())) {
                            $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, true);
                        }
                    }
                }
            }
            // Cas date inchangée : vérifier ajout / supression
            elseif ($diffStartNew === 0 && ($dateDiff <= 30)) {
                $formationFormateursAfter = [];

                foreach($form->get("formateurs")->getData() as $formateur) {
                    $formationFormateursAfter[] = clone $formateur;
                }

                foreach($formationFormateursAfter as $formateur) {
                    if (is_null($formateur->getPerson()->getSiret()) && !in_array($formateur->getPerson()->getId(), array_map(function(Formateur $f) { return $f->getPerson()->getId(); }, $formationFormateursBefore))) {
                        $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, true);
                    }
                }

                foreach ($formationFormateursBefore as $formateur) {
                    if (is_null($formateur->getPerson()->getSiret()) && !in_array($formateur->getPerson()->getId(), array_map(function(Formateur $f) { return $f->getPerson()->getId(); }, $formationFormateursAfter))) {
                        $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, false);
                    }
                }
            }

            if($formation->getCoordinators()->count() <= 1) {
                $coodinator = null;
                foreach($formation->getCoordinators() as $c) {
                    $coodinator = $c;
                }

                foreach($formation->getParticipations() as $participation) {
                    $participation->setCoordinator($coodinator);
                    if ($participation == $participation->getParticipant()->getlastParticipation() && !$formation->isPrivate()) {
                        $participation->getParticipant()->setCoordinator($coodinator->getPerson());
                    }
                    $em->persist($participation);
                }
            }

            foreach ($formation->getParticipations() as $participation) {
                // $participation->setExerciseMode($participationEsxerciseMode);
                $em->persist($participation);
            }

            if ($formation->getApplyTimesToAll() && count($formation->getProgramme()->getFormations()) > 1) {
                foreach ($formation->getProgramme()->getFormations() as $sisFormation) {
                    if ($sisFormation != $formation && $sisFormation->getFormOpeningDate() > new DateTime()) {
                        $sisFormation->cloneModuleTimes($formation);
                        $sisFormation->cloneModuleMinTimes($formation);

                    }
                }
            }

            $formation->refreshFinanceSousModeFactures();
            $eventDispatcher->dispatch(new SessionEdited($formation));

            $em->persist($formation);
            $em->flush();

            // Envoi du mail d'ouverture de la formation (après passage du cron)
            if(!$hasForm && $formation->hasLinkedForm()) {
                $now = new DateTime('now');
                if ($now->getTimestamp() >= $formation->getFormOpeningDate()->setTime(8, 0)->getTimestamp()) {
                    $this->asychrOpeningEmails($formation);
                }
            }

            $this->flashMessages->addSuccess('formation.edit.success');

            if ($session->has('session-edit-redirect')) {
                $sessionEditRedirect = $session->get('session-edit-redirect');
                $session->remove('session-edit-redirect');
                if ($sessionEditRedirect) {
                    return $this->redirect($sessionEditRedirect);
                }
            }

            return $this->redirectToRoute("admin_formation_edit", array("id" => $formation->getId()));
        }

        $showArchivedAlert = false;
        if(!$formation->formAlreadyAnswered()) {
            if($formation->getForm()) {
                $showArchivedAlert = true;
            }
        }

        $unityStartEnd = array();
        foreach ($formation->getUnities() as $keyUnity => $unity) {
            foreach ($unity->getUnitySessionDates() as $keyUnitySessionDate => $unitySessionDate) {
                array_push($unityStartEnd, array(
                    "unity" => $keyUnity,
                    "unitySessionDate" => $keyUnitySessionDate,
                    "startTimeHour" => $unitySessionDate->getStartDate() ? intVal($unitySessionDate->getStartDate()->format("H")) : 0,
                    "startTimeMin" => $unitySessionDate->getStartDate() ? intVal($unitySessionDate->getStartDate()->format("i")) : 0,
                    "endTimeHour" => $unitySessionDate->getEndDate() ? intVal($unitySessionDate->getEndDate()->format("H")): 0,
                    "endTimeMin" => $unitySessionDate->getEndDate() ? intVal($unitySessionDate->getEndDate()->format("i")) : 0
                    )
                );
            }
        }

        $reunionUnity = $formation->getUnityByPosition($formation->getProgramme()->getReunionUnityPosition());
        return $this->render('admin/formation/create.html.twig', array(
            'formation' => $formation,
            'programme' => $formation->getProgramme(),
            'form' => $form,
            'showArchivedAlert' => $showArchivedAlert,
            'unityStartEnd' => $unityStartEnd,
            'threeUnityComposed' => $formation->isThreeUnity(),
            'nbDays' => $reunionUnity ? count($reunionUnity->getUnitySessionDates()) : false,
            'elearningOneUnity' => $formation->getProgramme()->isElearningOneUnity(),
            "modules" => $courseManager->getModuleList($formation)
        ));
    }

    /**
     * Finds and displays a Formation entity.
     *
     * @param Request   $request
     * @param Formation $formation
     * @param int $page
     */
    #[Route(path: '/{id}/show/{page}', methods: ['GET', 'POST'], defaults: ['page' => 1], name: 'admin_formation_show', requirements: ['page' => '^\d+$'])]
    public function show(
        Request $request,
        Formation $formation,
        int $page,
        EntityManagerInterface $entityManager,
        FormFactoryInterface $formFactory,
        EmailSender $emailSender,
        CourseManager $courseManager,
        FacturationStateManager $facturationStateManager,
        IpPersonSaver $ipPersonSaver
    )
    {
        $this->denyAccessUnlessGranted('view', $formation);

        if (isset($_SERVER['REMOTE_ADDR']) && '' !== $_SERVER['REMOTE_ADDR']) {
            $ipPersonSaver->saveIp($_SERVER['REMOTE_ADDR']);
        }

        $form_andpc = $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_formation_import_formation', array('id' => $formation->getId())))
            ->add('andpcFile', FileType::class, array(
                'label' => false,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'text/plain', 'text/csv', 'application/octet-stream'
                        ),
                       'mimeTypesMessage' => 'Le fichier choisi n\'est pas un fichier au format CSV'
                    )
                )
            ))
            ->getForm();

        $form_zoom = $formFactory->createNamedBuilder("form_zoom")
            ->setAction($this->generateUrl('admin_formation_import_zoom', array('id' => $formation->getId())))
            ->add('zoomFile', FileType::class, array(
                'label' => false,
                'attr' => array("class" => "btn-download-file"),
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'text/plain', 'text/csv', 'application/octet-stream'
                        ),
                       'mimeTypesMessage' => 'Le fichier choisi n\'est pas un fichier au format CSV'
                    )
                )
            ))
            ->getForm();

        $form_emargement = $this->drawUploadForm($request, $formation, 'emargementFile', $formFactory);
        $form_facture_resto = $this->drawUploadForm($request, $formation, 'factureRestaurationFile', $formFactory);
        $form_justificatif_suivi = $this->drawUploadForm($request, $formation, 'justificatifSuiviFile', $formFactory);

        $coordinators = $formation->getCoordinators();

        $forms_facture_coordinator_views = [];
        $forms_facture_coordinator_viewsN1 = [];
        $fichiersCoordinator = [];
        $fichiersCoordinatorN1 = [];
        foreach ($coordinators as $coordinator) {
            $fichiersCoordinator[$coordinator->getId()] = array();
            $fichiersCoordinatorN1[$coordinator->getId()] = array();

            /** @var CoordinatorFilesRepository $coordinatorFilesRepository */
            $coordinatorFilesRepository = $entityManager->getRepository(CoordinatorFiles::class);
            $coordinatorFiles = $coordinatorFilesRepository->findOneBy(array(
                'formation' => $formation,
                'coordinator' => $coordinator
            ));

            $res = $this->generateFormCoordinatorFiles($coordinatorFiles, $coordinator, $formation, $fichiersCoordinator, $request, $formFactory, $entityManager, false);
            if ($res instanceof RedirectResponse) {
                return $res;
            }
            list($fichiersCoordinator, $form_facture_coordinator_views) = $res;
            $forms_facture_coordinator_views[$coordinator->getId()] = $form_facture_coordinator_views->createView();

            if ($formation->shouldDisplayAttestationN1()) {
                $res = $this->generateFormCoordinatorFiles($coordinatorFiles, $coordinator, $formation, $fichiersCoordinatorN1, $request, $formFactory, $entityManager, true);
                if ($res instanceof RedirectResponse) {
                    return $res;
                }
                list($fichiersCoordinatorN1, $form_facture_coordinator_viewsN1) = $res;
                $forms_facture_coordinator_viewsN1[$coordinator->getId()] = $form_facture_coordinator_viewsN1->createView();
            }
        }

        $forms_emargement_views = [];
        $fichiersEmargement = [];
        $fichiersConvention = [];
        foreach($formation->getFinanceSousModes() as $financeSousMode) {
            $emargementFiles = $this->loadEmargementFiles($financeSousMode, $entityManager, $formation);
            $fichiersEmargement[$financeSousMode->getId()] = $emargementFiles;

            foreach ($emargementFiles as $key => $emargementFile) {
                $form_emargement_views[$key] = $this->drawUploadEmargementForm($request, $emargementFile, 'emargementFile', $financeSousMode->getId(), $formFactory, $key);

                if ($form_emargement_views[$key]->isSubmitted() && $form_emargement_views[$key]->isValid()) {
                    $entityManager->persist($emargementFile);
                    $entityManager->flush();

                    $this->flashMessages->addSuccess('admin.formation.upload.success');
                    return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
                }
                $forms_emargement_views[$financeSousMode->getId()][] = $form_emargement_views[$key]->createView();
            }

            $fichiersConvention[$financeSousMode->getId()] = array();

            /** @var $conventionFileRepository $conventionFileRepository */
            $conventionFileRepository = $entityManager->getRepository(ConventionFile::class);
            $conventionFile = $conventionFileRepository->findOneBy(array(
                'formation' => $formation,
                'financeSousMode' => $financeSousMode
            ));

            if(!$conventionFile) {
                $conventionFile = new ConventionFile();
                $conventionFile->setFinanceSousMode($financeSousMode);
                $conventionFile->setFormation($formation);
            }
            else {
                $fichiersConvention[$financeSousMode->getId()] = $conventionFile;
            }
            $form_convention_views = $this->drawUploadConventionForm($request, $conventionFile, 'conventionFile', $financeSousMode->getId(), $formFactory);

            if ($form_convention_views->isSubmitted() && $form_convention_views->isValid()) {
                $entityManager->persist($conventionFile);
                $entityManager->flush();

                $this->flashMessages->addSuccess('admin.formation.upload.success');
                return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
            }
            $forms_convention_views[$financeSousMode->getId()] = $form_convention_views->createView();
        }

        $formateurs = $formation->getFormateurs();
        $forms_contrat_formateur_views = [];
        $forms_facture_formateur_views = [];
        $fichiersFormateur = [];
        foreach($formateurs as $formateur) {
            $fichiersFormateur[$formateur->getId()] = array();

            /** @var FormateurFilesRepository $formateurFilesRepository */
            $formateurFilesRepository = $entityManager->getRepository(FormateurFiles::class);
            $formateurFiles = $formateurFilesRepository->findOneBy(array(
                'formation' => $formation,
                'formateur' => $formateur
            ));

            if(!$formateurFiles) {
                $formateurFiles = new FormateurFiles();
                $formateurFiles->setFormateur($formateur);
                $formateurFiles->setFormation($formation);
            }
            else {
                $fichiersFormateur[$formateur->getId()] = $formateurFiles;
            }
            $form_contrat_formateur_views = $this->drawUploadFormateurForm($request, $formateurFiles, 'contratFile', $formateur->getId(), $formFactory);
            $form_facture_formateur_views = $this->drawUploadFormateurForm($request, $formateurFiles, 'factureFile', $formateur->getId(), $formFactory);

            if (($form_contrat_formateur_views->isSubmitted() && $form_contrat_formateur_views->isValid())
                || ($form_facture_formateur_views->isSubmitted() && $form_facture_formateur_views->isValid())) {
                $entityManager->persist($formateurFiles);
                $entityManager->flush();

                $this->flashMessages->addSuccess('admin.formation.upload.success');
                return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
            }
            $forms_contrat_formateur_views[$formateur->getId()] = $form_contrat_formateur_views->createView();
            $forms_facture_formateur_views[$formateur->getId()] = $form_facture_formateur_views->createView();
        }

        $forms_attestation_views = [];
        $forms_attestation_viewsN1 = [];
        if (!$formation->getProgramme()->isFormatPresentiel()) {
            foreach($formation->getParticipations() as $participation) {
                $form_attestation_views = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);
                $form_attestation_viewsN1 = $this->drawUploadAttestationForm($request, 'attestationHonneurFileN1', $participation, $formFactory);

                if (($form_attestation_views->isSubmitted() && $form_attestation_views->isValid())
                    || ($form_attestation_viewsN1->isSubmitted() && $form_attestation_viewsN1->isValid())) {
                    $entityManager->persist($participation);
                    $entityManager->flush();

                    if (($form_attestation_views->isSubmitted() && $participation->getAttestationHonneur())
                    || ($form_attestation_viewsN1->isSubmitted() && $participation->getAttestationHonneurN1())) {
                        $this->flashMessages->addSuccess('admin.participation.attestation_honneur.importSucess');
                    } else {
                        $this->flashMessages->addError('admin.participation.attestation_honneur.importError');
                    }

                    return $this->redirectToRoute('admin_formation_show', array(
                        'id' => $formation->getId(),
                        'page' => $page,
                        '_fragment' => sprintf("row-participation-%s", $participation->getId())
                    ));
                }

                $forms_attestation_views[$participation->getId()] = $form_attestation_views->createView();
                $forms_attestation_viewsN1[$participation->getId()] = $form_attestation_viewsN1->createView();
            }
        }

        $fichiersTopo = [];
        $forms_topo_views = [];

        $topoFileNew = new TopoFiles();
        $topoFileNew->setFormation($formation);
        $form_topo_new_view = $this->drawUploadTopoForm($request, $topoFileNew, 'topoFile', $formFactory);

        if ($form_topo_new_view->isSubmitted() && $form_topo_new_view->isValid()
        ) {
            $filename = null;
            foreach($request->files as $file) {
                $filename = $file['topoFile']['file']->getClientOriginalName();
            }

            if($filename) {
                $topoFileNew->setTopoOriginalName($filename);
            }

            $entityManager->persist($topoFileNew);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.formation.upload.success');

            if(count($formation->getTopoFiles()) == 1) {
                $emailSender->sendNewTopoAvailableEmail($formation, $topoFileNew);
            }

            return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
        }

        $topoFilesRepository = $entityManager->getRepository(TopoFiles::class);
        $topoFiles = $topoFilesRepository->findBy(array(
            'formation' => $formation
        ));

        if(!$topoFiles) {
            $topoFile = new TopoFiles();
            $topoFile->setFormation($formation);
            $topoFiles = array($topoFile);
        }
        else {
            $fichiersTopo = $topoFiles;
        }

        $topoUploadDates = array();

        foreach ($topoFiles as $topoFile) {
            $form_topo_views = $this->drawUploadTopoForm($request, $topoFile, 'topoFile', $formFactory);

            if ($form_topo_views->isSubmitted() && $form_topo_views->isValid()) {

                $toDelete = $form_topo_views->get('topoFile')->has('delete') && $form_topo_views->get('topoFile')->get('delete')->getData();

                if(!$toDelete && !is_null($topoFile->getTopo()) && !is_null($request->files->get(0)['topoFile']['file'])) {
                    $filename = null;
                    foreach($request->files as $file) {
                        $filename = $file['topoFile']['file']->getClientOriginalName();
                    }

                    if($filename) {
                        $topoFile->setTopoOriginalName($filename);
                    }

                    $entityManager->persist($topoFile);
                }
                else if ($toDelete) {
                    $entityManager->remove($topoFile);
                }

                $entityManager->flush();

                $this->flashMessages->addSuccess('admin.formation.upload.success');
                return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
            }

            $forms_topo_views[$topoFile->getId()] = $form_topo_views->createView();
        }

        if (($form_emargement->isSubmitted() && $form_emargement->isValid())
            || ($form_facture_resto->isSubmitted() && $form_facture_resto->isValid())
            || ($form_justificatif_suivi->isSubmitted() && $form_justificatif_suivi->isValid())) {
            $entityManager->persist($formation);
            $entityManager->flush();
            $this->flashMessages->addSuccess('admin.formation.upload.success');
            return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
        }

        $participationRepository = $entityManager->getRepository(Participation::class);
        $participationsCount = $participationRepository->countQueryBuilder($formation->getId());

        $nbPerPage = 30;
        $npages = ceil($participationsCount / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($nbPerPage == 1 ? 0 : $nbPerPage - 1) / 2)));
        $sup = min($inf + ($nbPerPage - 1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'urlName' => 'admin_formation_show',
            'nb' => $participationsCount,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'extraParams' => array("id" => $formation->getId())
        );

        $participations = $participationRepository->listing($formation->getId(), $page, $nbPerPage);

        $professions = array();
        $participantActionSheetList = [];
        foreach ($participations as $participation) {
            if(!in_array($participation->getParticipant()->getCategory(), $professions)) {
                $professions[] = $participation->getParticipant()->getCategory();
            }

            if($courseManager->hasModule($courseManager->getCourseDetail($participation), "fiche_action_1")) {
                $participantActionSheetRepository = $entityManager->getRepository(ParticipantActionSheet::class);
                $participantActionSheet = $participantActionSheetRepository->findOneBy(array(
                    'participation' => $participation
                ));
                $participantActionSheetList[$participation->getId()] = $participantActionSheet;
            }
        }

        $uploadFileDates = array(
            'emargement' => $formation->getUploadFileDate('emargementFile'),
            'justificatifSuivi' => $formation->getUploadFileDate('justificatifSuiviFile'),
            'factureRestauration' => $formation->getUploadFileDate('factureRestaurationFile'),
        );

        // $displayPdfCoordinatorHonorary = $coordinatorHonoraryService->displayCoordinatorsHonoraryByFormation($formation);
        $displayPdfCoordinatorHonorary = [];
        foreach($formation->getParticipations() as $participation) {
            if(!is_null($participation->getCoordinator())) {
                $displayPdfCoordinatorHonorary[$participation->getCoordinator()->getId()] = true;
            }
        }

        /* A SUPPRIMER LORSQUE TOUS LES CONTRATS ET FACTURE FORMATEUR AURONT ETE RECUPERES ET REDEPOSES */
        $uploadFileDates['factureFormer'] = $formation->getUploadFileDate('factureFormerFile');
        $uploadFileDates['contratFormateur'] = $formation->getUploadFileDate('contratFormateurFile');

        $participation = (new Participation())->setId(0)->setFormation($formation);
        $formation->refreshFinanceSousModeFactures();

        return $this->render('admin/formation/show.html.twig', array(
            'formation' => $formation,
            'participation' => $participation,
            'formateurs' => $formateurs,
            'coordinators' => $coordinators,
            'form_andpc' => $form_andpc,
            'form_zoom' => $form_zoom,
            'form_emarg' => $form_emargement->createView(),
            'form_justificatif_suivi' => $form_justificatif_suivi->createView(),
            'forms_topo' => $forms_topo_views,
            'forms_topo_new' => $form_topo_new_view->createView(),
            'participations' => $participations,
            'nbParticipants' => $participationsCount,
            'pagination' => $pagination,
            'uploadFileDates' => $uploadFileDates,
            'forms_contrat_formateur' => $forms_contrat_formateur_views,
            'forms_facture_formateur' => $forms_facture_formateur_views,
            'forms_facture_coordinator' => $forms_facture_coordinator_views,
            'forms_facture_coordinatorN1' => $forms_facture_coordinator_viewsN1,
            'forms_attestation_honneur' => $forms_attestation_views,
            'forms_attestation_honneurN1' => $forms_attestation_viewsN1,
            'forms_emargement' => $forms_emargement_views,
            'forms_convention' => $forms_convention_views,
            'fichiersFormateur' => $fichiersFormateur,
            'fichiersCoordinator' => $fichiersCoordinator,
            'fichiersCoordinatorN1' => $fichiersCoordinatorN1,
            'fichiersEmargement' => $fichiersEmargement,
            'fichiersConvention' => $fichiersConvention,
            'fichiersTopo' => $fichiersTopo,
            'topoFileNew' => $topoFileNew,
            'displayPdfCoordinatorHonorary' => $displayPdfCoordinatorHonorary,
            'form_facture_resto' => $form_facture_resto->createView(),
            'professions' => $professions,
            'newHonorary' => $formation->getStartDate() >= new \DateTime($this->getParameter('honoraires.migration_date')),
            'oldCourse' => $courseManager->isOldCourse($participation),
            'isBeforeElearningUpdate' => $courseManager->isBeforeElearningUpdate($participation),
            'courseType' => $courseManager->getCourseType($participation),
            'course' => $formation->hasLinkedForm() ? $courseManager->getCourseDetail($participation) : null,
            'courseManager' => $courseManager,
            'participantActionSheetList' => $participantActionSheetList,
            'facturationStateManager' => $facturationStateManager,
        ));
    }

    /**
     * Finds and displays a Formation entity.
     *
     * @param Request   $request
     * @param Formation $formation
     * @param int $page
     */
    #[Route(path: '/{id}/participants/{page}', methods: ['GET', 'POST'], defaults: ['page' => '1'], name: 'admin_formation_participants')]
    public function participants(Request $request, Formation $formation, $page, FormFactoryInterface $formFactory, CourseManager $courseManager, EntityManagerInterface $em): Response
    {
        $this->denyAccessUnlessGranted('view', $formation);

        $participationRepository = $em->getRepository(Participation::class);
        $participationsCount = $participationRepository->countQueryBuilder($formation->getId());
        $nbPerPage = 30;
        $npages = ceil($participationsCount / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($nbPerPage == 1 ? 0 : $nbPerPage - 1) / 2)));
        $sup = min($inf + ($nbPerPage - 1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'urlName' => 'admin_formation_show',
            'nb' => $participationsCount,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'extraParams' => array("id" => $formation->getId())
        );

        $participations = $participationRepository->listing($formation->getId(), $page, $nbPerPage);

        $forms_attestation_views = [];
        $forms_attestation_viewsN1 = [];
        if (!$formation->getProgramme()->isFormatPresentiel()) {
            foreach($formation->getParticipations() as $participation) {
                $form_attestation_views = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);
                $form_attestation_viewsN1 = $this->drawUploadAttestationForm($request, 'attestationHonneurFileN1', $participation, $formFactory);
                $forms_attestation_views[$participation->getId()] = $form_attestation_views->createView();
                $forms_attestation_viewsN1[$participation->getId()] = $form_attestation_viewsN1->createView();
            }
        }

        $participation = (new Participation())->setId(0)->setFormation($formation);

        return $this->render('admin/formation/participants.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'nbParticipants' => $participationsCount,
            'pagination' => $pagination,
            'coordinators' => $formation->getCoordinators(),
            'forms_attestation_honneur' => $forms_attestation_views,
            'forms_attestation_honneurN1' => $forms_attestation_viewsN1,
            'newHonorary' => $formation->getStartDate() >= new \DateTime($this->getParameter('honoraires.migration_date')),
            'oldCourse' => $courseManager->isOldCourse($participation),
            'courseManager' => $courseManager,
            'isBeforeElearningUpdate' => $courseManager->isBeforeElearningUpdate($participation),
        ));
    }

    /**
     * @param Request   $request
     * @param Formation $formation
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/close', methods: ['GET'], name: 'admin_formation_close_formation')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function close(Formation $formation, EmailSender $emailSender, EntityManagerInterface $entityManager): RedirectResponse
    {
        $formation->setClosed(!$formation->getClosed());
        $formation->setClosedAndpc(!$formation->getClosedAndpc());
        $entityManager->persist($formation);
        $entityManager->flush();
        if ($formation->canSendEmail() && $formation->getClosed()) {
            $emailSender->sendAllEmail(EmailSender::MAIL_CLOSE, $formation);
        }
        return $this->redirectToRoute('admin_formation_show', array("id" => $formation->getId()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param EntityManagerInterface $entityManager
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/bill/{state}', methods: ['GET'], name: 'admin_formation_bill')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function bill(Formation $formation, bool $state, EntityManagerInterface $entityManager): RedirectResponse
    {
        $formation->setBilled($state);
        $entityManager->persist($formation);
        $entityManager->flush();
        return $this->redirectToRoute('admin_formation_show', array("id" => $formation->getId()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param EntityManagerInterface $entityManager
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/account/{mid}/{state}', methods: ['GET'], name: 'admin_formation_account')]
    #[IsGranted('ROLE_WEBMASTER_COMPTA')]
    public function account(Formation $formation, bool $mid, bool $state, EntityManagerInterface $entityManager): RedirectResponse
    {
        if ($mid) {
            $formation->setAccountedMidCourse($state);
        } else {
            if ($formation->isPluriannuelle() && $state == 1) {
                $formation->setAccountedMidCourse($state);
            }
            $formation->setAccounted($state);
        }
        $entityManager->persist($formation);
        $entityManager->flush();
        return $this->redirectToRoute('admin_formation_show', array("id" => $formation->getId()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/import', methods: ['POST'], name: 'admin_formation_import_formation')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function import(Request $request, Formation $formation, CsvImporter $csvImporter, ElasticSynchronizer $elasticSynchronizer, FormFactoryInterface $formFactory)
    {
        $sessionKey = 'current_import_data';

        $formAndpc = $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_formation_import_formation', array('id' => $formation->getId())))
            ->add('andpcFile', FileType::class, array(
                'label' => false,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'text/plain', 'text/csv', 'application/octet-stream'
                        ),
                        'mimeTypesMessage' => 'Le fichier choisi n\'est pas un fichier au format CSV'
                    )
                )
            ))
            ->getForm();
        $formAndpc->handleRequest($request);

        $formSubmit = $formFactory->createNamedBuilder('submit')->getForm();
        $formSubmit->handleRequest($request);

        if ($formAndpc->isSubmitted()) {
            if ($formAndpc->isValid()) {
                $file = $formAndpc->get('andpcFile');
                /** @var UploadedFile $fileData */
                $fileData = $file->getData();

                $nbLinesIgnored = 1;
                $datas = $csvImporter->parseCSV($fileData->getRealPath(), $nbLinesIgnored);

                $request->getSession()->set($sessionKey, $datas);

                $diff = $csvImporter->getDiff($datas, $formation, true);

                if (count($diff['errors']) > 0) {
                    $diff['errors'] = array_map("unserialize", array_unique(array_map("serialize", $diff['errors'])));
                    foreach ($diff['errors'] as $error) {
                        $errorData = $error['participant'];
                        $params = ['%name%' => $errorData[CsvImporter::FIELD_FIRSTNAME] . " " . $errorData[CsvImporter::FIELD_LASTNAME]];
                        if (isset($error["message"])) {
                            $params["%error%"] = $error["message"];
                        }
                        $this->flashMessages->addError($error['error'], $params);
                    }
                }
                return $this->render('admin/formation/import.html.twig', array(
                    'formation' => $formation,
                    'diff' => $diff,
                    'formAndpc' => $formAndpc,
                    'formSubmit' => $formSubmit,
                ));
            } else {
                foreach ($formAndpc->getErrors(true) as $key => $error) {
                    $this->flashMessages->addError($error->getMessage());
                }
            }
        }

        if ($formSubmit->isSubmitted()) {

            $datas = $request->getSession()->get($sessionKey);
            $errors = $csvImporter->import($datas, $formation, true);
            if (!isset($errors['error'])) {
                $this->flashMessages->addSuccess('admin.formation.import.success');
                $request->getSession()->remove($sessionKey);
                $elasticSynchronizer->synchronize($formation->getId());
                return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
            } else {
                $errorData = $errors['participant'];
                $params = ['%name%' => $errorData[CsvImporter::FIELD_FIRSTNAME] . " " . $errorData[CsvImporter::FIELD_LASTNAME]];
                if (isset($errors["message"])) {
                    $params["%error%"] = $errors["message"];
                }
                $this->flashMessages->addError($errors['error'], $params);
            }
        }

        return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));

    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export', methods: ['GET'], name: 'admin_formation_export_formation')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function export(Formation $formation, CsvImporter $csvImporter): CsvFileResponse
    {
        $this->denyAccessUnlessGranted('view', $formation);

        $coordinator = $this->getUser()->isCoordinator() ? $this->getUser() : false;
        $data = $csvImporter->export($formation, $coordinator);
        return new CsvFileResponse($data, sprintf("export_andpc_%s_%s.csv", $formation->getProgramme()->getReference(), $formation->getSessionNumber()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/import-zoom', methods: ['POST'], name: 'admin_formation_import_zoom')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function importZoom(Request $request, Formation $formation, CsvImporterZoom $csvImporter, FormFactoryInterface $formFactory)
    {
        $sessionKey = 'current_import_zoom_data';

        $formZoom = $formFactory->createNamedBuilder("form_zoom")
            ->setAction($this->generateUrl('admin_formation_import_zoom', array('id' => $formation->getId())))
            ->add('zoomFile', FileType::class, array(
                'label' => false,
                'attr' => array("class" => "btn-download-file"),
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'text/plain', 'text/csv', 'application/octet-stream'
                        ),
                        'mimeTypesMessage' => 'Le fichier choisi n\'est pas un fichier au format CSV'
                    )
                )
            ))
            ->getForm();
        $formZoom->handleRequest($request);

        $formSubmit = $formFactory->createNamedBuilder('submit')->getForm();
        $formSubmit->handleRequest($request);

        if ($formZoom->isSubmitted()) {
            if ($formZoom->isValid()) {
                $file = $formZoom->get('zoomFile');
                /** @var UploadedFile $fileData */
                $fileData = $file->getData();
                $datas = null;

                $nbLinesIgnored = 4;
                try {
                    $datas = $csvImporter->parseCSV($fileData->getRealPath(), $nbLinesIgnored, ";", true);
                    if (array_keys($datas[0])[0] !== CsvImporterZoom::FIELD_PARTICIPANT) {
                        throw new \Exception();
                    }
                } catch (\Exception $e) {
                    $nbLinesIgnored = 1;
                    try {
                        $datas = $csvImporter->parseCSV($fileData->getRealPath(), $nbLinesIgnored, ";", true);
                    } catch (\Exception $e) {}
                }

                if ($datas && $csvImporter->checkFile($datas)) {
                    $request->getSession()->set($sessionKey, $datas);

                    $diff = $csvImporter->getDiff($datas, $formation, true);

                    return $this->render('admin/formation/import_zoom.html.twig', array(
                        'formation' => $formation,
                        'diff' => $diff,
                        'formZoom' => $formZoom,
                        'formSubmit' => $formSubmit,
                    ));
                }

                $this->flashMessages->addError("admin.formation.import.zoom.invalid", array(
                    "%fields%" => implode('", "', array(CsvImporterZoom::FIELD_RPPS_READABLE, CsvImporterZoom::FIELD_START_READABLE, CsvImporterZoom::FIELD_END_READABLE))
                ));

            } else {
                foreach ($formZoom->getErrors(true) as $key => $error) {
                    $this->flashMessages->addError($error->getMessage());
                }
            }
        }

        if ($formSubmit->isSubmitted()) {

            $datas = $request->getSession()->get($sessionKey);
            $errors = $csvImporter->import($datas, $formation, true);
            if (!isset($errors['error'])) {
                $this->flashMessages->addSuccess('admin.formation.import.zoom.success');
                $request->getSession()->remove($sessionKey);
                return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
            } else {
                $this->flashMessages->addError('admin.formation.import.zoom.error');
            }
        }

        return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));

    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-email-history', methods: ['GET'], name: 'admin_formation_export_emails')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function exportMailerHistory(Formation $formation, CsvBilanExport $csvExport): CsvFileResponse
    {
        $this->denyAccessUnlessGranted('view', $formation);
        $data = $csvExport->exportMailHistoryFormation($formation, $this->getUser());
        return new CsvFileResponse($data, sprintf("export_emails_%s_%s.csv", $formation->getProgramme()->getReference(), $formation->getSessionNumber()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-participations-logs', methods: ['GET'], name: 'admin_formation_export_participation_logs')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function exportParticipationLogs(Formation $formation, CsvBilanExport $csvExport): CsvFileResponse
    {
        $data = $csvExport->exportParticipationLogs($formation);
        return new CsvFileResponse($data, sprintf("export_logs_participations_%s_%s.csv", $formation->getProgramme()->getReference(), $formation->getSessionNumber()));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-participations-logs/{unity}', methods: ['GET'], name: 'admin_formation_export_participation_logs_unity')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function exportParticipationLogsByUnity(Request $request, Formation $formation, CsvBilanExport $csvExport, $unity): CsvFileResponse
    {
        $data = $csvExport->exportParticipationLogsByUnity($formation, $unity, $request->query->has("withIp"));
        return new CsvFileResponse($data, sprintf("export_logs_participations_%s_%s_unite_%s.csv", $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $unity));
    }

    /**
     * @param Request   $request
     * @param Formation $formation
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/close-andpc', methods: ['GET'], name: 'admin_formation_close_andpc_formation')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function closeAndpc(Formation $formation, EntityManagerInterface $entityManager): RedirectResponse
    {
        $formation->setClosedAndpc(true);
        $entityManager->persist($formation);
        $entityManager->flush();
        return $this->redirectToRoute('admin_programme_index');
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize='128M') {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new File(
                array(
                    'mimeTypes' => array(
                        'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesAttestation',
                    'maxSize' => $maxSize
                )
            )
        ));
    }

    public function drawUploadForm(Request $request, Formation $formation, $fieldName, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName, FormType::class, $formation);
        $this->drawUploadFormField($genForm, $fieldName);

        if($this->propertyAccessor->getValue($formation, $fieldName) !== null) {
            $genForm->add('submit', SubmitType::class, array(
                'label' => 'admin.global.send'
            ));
        }

        $form = $genForm->getForm();
        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadFormateurForm(Request $request, FormateurFiles $formateurFiles, $fieldName, $formateurId, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$formateurId, FormType::class, $formateurFiles);
        $this->drawUploadFormField($genForm, $fieldName);

        if($formateurFiles->getId() && $this->propertyAccessor->getValue($formateurFiles, $fieldName) !== null) {
            $genForm->add('submit', SubmitType::class, array(
                'label' => 'admin.global.send'
            ));
        }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadAttestationForm(Request $request, $fieldName, $participation, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participation->getId(), FormType::class, $participation);
        $this->drawUploadFormField($genForm, $fieldName, false, '12M');
        $form = $genForm->getForm();
        $form->handleRequest($request);
        return $form;
    }

    public function drawUploadCoordinatorForm(Request $request, CoordinatorFiles $coordinatorFiles, $fieldName, $coordinatorId, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$coordinatorId, FormType::class, $coordinatorFiles);
        $this->drawUploadFormField($genForm, $fieldName);

        if($coordinatorFiles->getId() && $this->propertyAccessor->getValue($coordinatorFiles, $fieldName) !== null) {
            $genForm->add('submit', SubmitType::class, array(
                'label' => 'admin.global.send'
            ));
        }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadTopoForm(Request $request, TopoFiles $topoFiles, $fieldName, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$topoFiles->getId(), FormType::class, $topoFiles);
        $this->drawUploadFormField($genForm, $fieldName);

            if($topoFiles->getId()) {
                $genForm->add('submit', SubmitType::class, array(
                    'label' => 'admin.global.send'
                ));
            }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadEmargementForm(Request $request, EmargementFile $emargementFile, $fieldName, $financeSousModeId, FormFactoryInterface $formFactory, $key) {
        $nameForm = $fieldName.'_'.$financeSousModeId.'_'.$key;
        $genForm = $formFactory->createNamedBuilder($nameForm, FormType::class, $emargementFile);
        $this->drawUploadFormField($genForm, $fieldName);
        if ($emargementFile->getEmargement()) {
            $genForm->add('submit', SubmitType::class, array(
                'label' => 'admin.global.send'
            ));
        }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadConventionForm(Request $request, ConventionFile $conventionFile, $fieldName, $financeSousModeId, FormFactoryInterface $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$financeSousModeId, FormType::class, $conventionFile);
        $this->drawUploadFormField($genForm, $fieldName);

        if($conventionFile->getId()) {
            $genForm->add('submit', SubmitType::class, array(
                'label' => 'admin.global.send'
            ));
        }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    #[Route(path: '/{id}/file/{fileField}', name: 'admin_formation_file')]
    public function fileDownload(Formation $formation, $fileField, DownloadHandler $downloadHandler)
    {
        $getter = "get" . ucfirst(str_replace("File", "", $fileField));
        $filename = call_user_func(array($formation, $getter));
        return $downloadHandler->downloadObject($formation, $fileField, null, $filename, false);
    }

    #[Route(path: '/{id}/attestation-file/{n1}', name: 'admin_attestation_file', defaults: ['n1' => false])]
    public function attestationFileDownload(Participation $participation, $n1): BinaryFileResponse
    {
        if ($n1) {
            return new BinaryFileResponse($participation->getAttestationHonneurFileN1()->getRealPath());
        }
        return new BinaryFileResponse($participation->getAttestationHonneurFile()->getRealPath());
    }

    #[Route(path: '/{id}/attestation-delete-file/{n1}', name: 'admin_attestation_delete_file', defaults: ['n1' => false])]
    public function attestationDeleteFileDownload(Request $request, Participation $participation, AttestationService $attestationService, EntityManagerInterface $entityManager, bool $n1): RedirectResponse
    {
        $attestationService->deleteAttestation($n1, $participation);

        $entityManager->persist($participation);
        $entityManager->flush();

        $this->flashMessages->addSuccess('admin.participation.attestation_honneur.deleteSucess');

        $redirect = $request->headers->get('referer');

        if ($request->query->has("redirect")) {
            $redirect = $request->query->get("redirect");
        }

        return $this->redirect($redirect);
    }

    /**
     * Send an email to the linked participants
     * @param Formation  $formation
     * @param string         $type
     * @return RedirectResponse
     */
    #[Route(path: '/{id}/email/{type}', name: 'admin_formation_email')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function sendEmail(Formation $formation, $type, EmailSender $emailSender, Sender $sender): RedirectResponse
    {
        $this->denyAccessUnlessGranted('view', $formation);

        if (EmailSender::isExistingType($type)) {
            $email = $emailSender->sendAllEmail($type, $formation);
        } else {
            /** @var BaseEmailParticipation $checker */
            $checker = $sender->getChecker($type);
            $emailSender->sendAllAutomaticParticipationEmail($checker, $formation);
        }

        $this->flashMessages->addSuccess('admin.formation.mail.send.success');
        return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
    }

    /**
     * Deletes a Formation entity.
     *
     * @param Request   $request
     * @param Formation $formation
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_formation_delete')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function delete(Request $request, Formation $formation, EmailSender $emailSender, EntityManagerInterface $em)
    {
        $session = $request->getSession();
        $sessionKey = "formation-delete-redirect-page";
        $form = $this->createDeleteForm($formation)
        ->add('motif', ChoiceType::class, array(
            'required' => true,
            'label' => 'admin.participation.delete.motif',
            'choices'  => array(
                FormationHistory::MOTIF_DATECHANGE => FormationHistory::MOTIF_DATECHANGE,
                FormationHistory::MOTIF_UNEVAILABLE_PLACE => FormationHistory::MOTIF_UNEVAILABLE_PLACE,
                FormationHistory::MOTIF_UNEVAILABLE_SPEAKER => FormationHistory::MOTIF_UNEVAILABLE_SPEAKER,
                FormationHistory::MOTIF_UNEVAILABLE_CR => FormationHistory::MOTIF_UNEVAILABLE_CR,
                FormationHistory::MOTIF_IMPEDIMENT => FormationHistory::MOTIF_IMPEDIMENT,
                FormationHistory::MOTIF_FEW_PART => FormationHistory::MOTIF_FEW_PART,
                FormationHistory::MOTIF_ANDPC_DELETE => FormationHistory::MOTIF_ANDPC_DELETE,
                FormationHistory::MOTIF_OTHER => FormationHistory::MOTIF_OTHER
            ),
            'placeholder' => 'admin.participation.delete.motifEmpty',
        ))
        ->add('commentaire', TextareaType::class, array(
            'required' => false,
            'label' => 'admin.participation.delete.commentaire'
        ));
        $form->handleRequest($request);
        $redirectedPage = 1;
        $programmeId = $formation->getProgramme()->getId();

        if ($request->query->has('page')) {
            $session->set($sessionKey, $request->query->get('page'));
        } else if ($session->has($sessionKey)) {
            $redirectedPage = $session->get($sessionKey);
            $session->remove($sessionKey);
        }

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            try {
                foreach ($formation->getParticipations() as $participation) {
                    $participation->setArchived(true);
                    $participation->addParticipationHistoryUnregister("Annulation session", $data["commentaire"], ParticipationHistory::MANUAL_TYPE);
                    // $em->remove($participation);
                }

                $date = New DateTime('now');

                // différence entre nouvelle date et aujourd'hui
                $dateDiff = date_diff($date, $formation->getStartDate());
                $dateDiff = abs($dateDiff->format('%R%a'));

                if ($dateDiff <= 30) {
                    foreach($formation->getFormateurs() as $formateur) {
                        if (is_null($formateur->getPerson()->getSiret())) {
                            $emailSender->sendFormerAddedOrDeletedEmail($formation, $formateur, false);
                        }
                    }
                }

                $formation->setArchived(true);
                $formation->addFormationHistoryUnregister($data["motif"], $data["commentaire"]);
                // $em->remove($formation);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('formation.delete.constraint_error');
                return $this->redirectToRoute('admin_programme_index', array("page" => $redirectedPage, "open" => $programmeId));
            }
            $this->flashMessages->addSuccess('formation.delete.success');
            return $this->redirectToRoute('admin_programme_index', array("page" => $redirectedPage, "open" => $programmeId));
        }

        return $this->render('admin/formation/delete.html.twig', array(
            'formation' => $formation,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a Formation entity.
     */
    private function createDeleteForm(Formation $formation): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_formation_delete', array('id' => $formation->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/duplicate-check', name: 'admin_formation_duplicate_check', methods: ['POST'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function preventDuplicate(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $repo = $em->getRepository(Formation::class);
        $formations = $repo->findPreventDuplicate($request->request->get('programme'), $request->request->get('sessionNumber'));
        return new JsonResponse(array(
            "duplicated" => count($formations) > 0
        ));
    }

    /**
     * Displays a form to edit an existing commande entity.
     *
     * @param Request                            $request
     * @param Formateur $formateur
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/paidFormateur', methods: ['GET', 'POST'], name: 'paidFormateur')]
    #[IsGranted(new Expression("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')"))]
    public function paidFormateur(Request $request, Formateur $formateur, EntityManagerInterface $entityManager)
    {
        $form = $this->createForm(FormateurType::class, $formateur);
        $form->handleRequest($request);

        $formateur->setIsPaid(!$formateur->isPaid());
        $formateur->setPaidDate(new DateTime());

        $entityManager->flush();
        return $formateur->isPaid() ? $this->json(true) : $this->json(false);
    }

    #[Route(path: '/{id}/{midCourse}/paidCoordinateur', methods: ['GET', 'POST'], name: 'paidCoordinator')]
    #[IsGranted(new Expression("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')"))]
    public function paidCoordinator(Request $request, Coordinator $coordinator, EntityManagerInterface $entityManager, $midCourse = false): JsonResponse
    {
        $form = $this->createForm(CoordinatorType::class, $coordinator);
        $form->handleRequest($request);

        if ($midCourse) {
            $coordinator->setIsPaidMidCourse(!$coordinator->isPaidMidCourse());
            $coordinator->setPaidMidCourseDate(new DateTime());
        } else {
            $coordinator->setIsPaid(!$coordinator->isPaid());
            $coordinator->setPaidDate(new DateTime());
        }

        $entityManager->flush();
        if ($midCourse) {
            return $coordinator->isPaidMidCourse() ? $this->json(true) : $this->json(false);
        }
        return $coordinator->isPaid() ? $this->json(true) : $this->json(false);
    }

    /**
     * Displays a form to edit an existing commande entity.
     *
     * @param Request                            $request
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{formation}/factureFinanceSousMode/{financeSousMode}/{n1}', methods: ['GET', 'POST'], defaults: ['n1' => '0'], name: 'factureFinanceSousMode')]
    #[IsGranted(new Expression("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')"))]
    public function factureFinanceSousMode(Request $request, Formation $formation, FinanceSousMode $financeSousMode, EntityManagerInterface $entityManager, $n1 = null)
    {
        $form = $this->createForm(FormationType::class, $formation);
        $form->handleRequest($request);
        $formation->refreshFinanceSousModeFactures();
        $state = $formation->factureFinanceSousMode($financeSousMode, $n1);
        $formation->setOneFinanceSousModeFactured($formation->thereIsFinanceSousModeFactured());
        $entityManager->flush();
        return $state ? $this->json(true) : $this->json(false);
    }

    /**
     * Displays a form to edit an existing commande entity.
     *
     * @param Request                            $request
     * @param Formation $formation
     * @param EntityManagerInterface $entityManager
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/allIsFactured', methods: ['GET', 'POST'], name: 'allIsFactured')]
    #[IsGranted(new Expression("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')"))]
    public function allIsFactured(Formation $formation, EntityManagerInterface $entityManager)
    {
        $allIsFactured = $formation->allIsFactured();
        $formation->setBilled($allIsFactured);
        $entityManager->persist($formation);
        $entityManager->flush();
        return $allIsFactured ? $this->json(true) : $this->json(false);
    }

    /**
     * Displays a form to edit an existing commande entity.
     */
    #[Route(path: '/{id}/refreshFactureState', name: 'refresh_facture_state', methods: ['GET', 'POST'])]
    #[IsGranted(new Expression("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_WEBMASTER_COMPTA')"))]
    public function refreshFactureState(Formation $formation, EntityManagerInterface $entityManager, FacturationStateManager $facturationStateManager): RedirectResponse
    {
        $facturationStateManager->manageFactureModes($formation);
        $entityManager->flush();
        return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
    }

    /**
     * @param array $coordinatorFiles
     * @param $coordinator
     * @param Formation $formation
     * @param array $fichiersCoordinator
     * @param Request $request
     * @param FormFactoryInterface $formFactory
     * @return array|RedirectResponse
     */
    public function generateFormCoordinatorFiles(
        ?CoordinatorFiles $coordinatorFiles,
        $coordinator,
        Formation $formation,
        array $fichiersCoordinator,
        Request $request,
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        bool $n1 = false)
    {
        if (!$coordinatorFiles) {
            $coordinatorFiles = new CoordinatorFiles();
            $coordinatorFiles->setCoordinator($coordinator);
            $coordinatorFiles->setFormation($formation);
        } else {
            $fichiersCoordinator[$coordinator->getId()] = $coordinatorFiles;
        }
        $fieldName = sprintf('factureCoordinatorFile%s', $n1 ? 'N1': '');
        $form_facture_coordinator_views = $this->drawUploadCoordinatorForm($request, $coordinatorFiles, $fieldName, $coordinator->getId(), $formFactory);

        if ($form_facture_coordinator_views->isSubmitted() && $form_facture_coordinator_views->isValid()) {
            if (!$coordinatorFiles->getFactureCoordinatorFile() && !$coordinatorFiles->getFactureCoordinatorFileN1()) {
                $entityManager->remove($coordinatorFiles);
            } else {
                $entityManager->persist($coordinatorFiles);
            }
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.formation.upload.success');

            return $this->redirectToRoute('admin_formation_show', array('id' => $formation->getId()));
        }
        return array($fichiersCoordinator, $form_facture_coordinator_views);
    }

    public function getFormationBuilder($formationType): FormationBuilderInterface
    {
        if ($formationType == Formation::TYPES[Formation::TYPE_VFC]) {
            return new FormationVfcBuilder();
        }
        if ($formationType == Formation::TYPES[Formation::TYPE_TCS]) {
            return new FormationTcsBuilder();
        }
        return new FormationBuilder();
    }

    /**
     * @param $financeSousMode
     * @param array $fichiersEmargement
     * @param ObjectManager $entityManager
     * @param Formation $formation
     * @return array
     */
    public function loadEmargementFiles($financeSousMode, ObjectManager $entityManager, Formation $formation): array
    {
        $fichiersEmargement = array();

        /** @var $emargementFileRepository $emargementFileRepository */
        $emargementFileRepository = $entityManager->getRepository(EmargementFile::class);
        $emargementFiles = $emargementFileRepository->findBy(array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode
        ));

        foreach ($emargementFiles as $emargementFile) {
            $fichiersEmargement[] = $emargementFile;
        }
        $countFichierEmargement = count($fichiersEmargement);
        $nbEmargementProgrammeNeeded = $formation->getProgramme()->nbEmargementNeeded();
        if ($countFichierEmargement != $nbEmargementProgrammeNeeded) {
            for ($i = $countFichierEmargement; $i < $nbEmargementProgrammeNeeded; $i++) {
                $emargementFile = new EmargementFile();
                $emargementFile->setFinanceSousMode($financeSousMode);
                $emargementFile->setFormation($formation);
                $fichiersEmargement[] = $emargementFile;
            }
        }
        return $fichiersEmargement;
    }

    public function asychrOpeningEmails(Formation $formation) {
        $command = sprintf("eduprat:asychr_opening_emails %s", $formation->getId());

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf("php %s/bin/console %s", $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }
}
