<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Form\FinanceModeType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FinanceModeController
 */
#[Route(path: '/finance-mode')]
#[IsGranted('ROLE_WEBMASTER')]
class FinanceModeController extends Ed<PERSON>ratController
{
    /**
     * Lists all FinanceMode entities.
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_finance_mode_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $financeModes = $em->getRepository(FinanceMode::class)->findBy(array(), array('name' => 'ASC'));
        return $this->render('admin/finance_mode/index.html.twig', array(
            'financeModes' => $financeModes,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_finance_mode_create')]
    public function create(Request $request, EntityManagerInterface $entityManager) {
        $financeMode = new FinanceMode();
        $form = $this->createForm(FinanceModeType::class, $financeMode);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($financeMode);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.financeMode.create.success');
            return $this->redirectToRoute('admin_finance_mode_index');
        }

        return $this->render('admin/finance_mode/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing FinanceMode entity.
     *
     * @param Request $request
     * @param FinanceMode $financeMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_finance_mode_edit')]
    public function edit(Request $request, FinanceMode $financeMode, EntityManagerInterface $entityManager) {
        $form = $this->createForm(FinanceModeType::class, $financeMode);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            $entityManager->persist($financeMode);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.financeMode.edit.success');
            return $this->redirectToRoute('admin_finance_mode_index');
        }

        return $this->render('admin/finance_mode/create.html.twig', array(
            'financeMode' => $financeMode,
            'form' => $form,
        ));
    }

    /**
     * Deletes a FinanceMode entity.
     *
     * @param Request $request
     * @param FinanceMode $financeMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_finance_mode_delete')]
    public function delete(Request $request, FinanceMode $financeMode, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($financeMode);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($financeMode);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('admin.financeMode.delete.constraint_error');
                return $this->redirectToRoute('admin_finance_mode_index');
            }
            $this->flashMessages->addSuccess('admin.financeMode.delete.success');
            return $this->redirectToRoute('admin_finance_mode_index');
        }

        return $this->render('admin/finance_mode/delete.html.twig', array(
            'financeMode' => $financeMode,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a FinanceMode entity.
     */
    private function createDeleteForm(FinanceMode $financeMode): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_finance_mode_delete', array('id' => $financeMode->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }
    /**
     * @param Request $request
     * @param FinanceMode $financeMode
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/children', methods: ['GET'], name: 'admin_finance_mode_children')]
    public function children(FinanceMode $financeMode): JsonResponse
    {
        /** @var FinanceSousMode[] $financeSousModes */
        $financeSousModes = $financeMode->getFinanceSousModes();
        foreach ($financeSousModes as $financeSousMode) {
            $json[] = array('id' => $financeSousMode->getId(), 'name' => $financeSousMode->getName(), 'identifiant' => $financeSousMode->getIdentifiant());
        }
        return new JsonResponse($json);
    }
}
