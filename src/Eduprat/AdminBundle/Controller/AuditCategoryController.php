<?php

namespace Eduprat\AdminBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\AuditCategory;
use Eduprat\DomainBundle\Form\AuditCategoryType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * AuditCategory controller.
 */
#[Route(path: '/audit-category')]
#[IsGranted('ROLE_WEBMASTER')]
class AuditCategoryController extends EdupratController
{
    /**
     * Lists all AuditCategory entities.
     */
    #[Route(path: '/', methods: ['GET'], name: 'admin_audit_category_index')]
    public function index(EntityManagerInterface $em): Response
    {
        $auditCategories = new ArrayCollection($em->getRepository(AuditCategory::class)->findAll());

        return $this->render('admin/audit_category/index.html.twig', array(
            'auditCategories' => $auditCategories,
        ));
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_audit_category_create')]
    public function create(Request $request, EntityManagerInterface $entityManager)
    {
        $auditCategory = new AuditCategory();
        $form = $this->createForm(AuditCategoryType::class, $auditCategory);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($auditCategory);
            $entityManager->flush();

            return $this->redirectToRoute('admin_audit_category_index');
        }

        return $this->render('admin/audit_category/create.html.twig', array(
            'form' => $form,
        ));
    }

    /**
     * Displays a form to edit an existing AuditCategory entity.
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_audit_category_edit')]
    public function edit(Request $request, AuditCategory $auditCategory, EntityManagerInterface $entityManager)
    {
        $form = $this->createForm(AuditCategoryType::class, $auditCategory);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($auditCategory);
            $entityManager->flush();

            $this->flashMessages->addSuccess('audit.category.edit.success');
            return $this->redirectToRoute('admin_audit_category_index');
        }

        return $this->render('admin/audit_category/create.html.twig', array(
            'auditCategory' => $auditCategory,
            'form' => $form,
        ));
    }

    /**
     * Deletes a AuditCategory entity.
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_audit_category_delete')]
    public function delete(Request $request, AuditCategory $auditCategory, EntityManagerInterface $em)
    {
        $form = $this->createDeleteForm($auditCategory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($auditCategory);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('audit.category.delete.constraint_error');
                return $this->redirectToRoute('admin_audit_category_index');
            }
            $this->flashMessages->addSuccess('audit.category.delete.success');
            return $this->redirectToRoute('admin_audit_category_index');
        }

        return $this->render('admin/audit_category/delete.html.twig', array(
            'auditCategory' => $auditCategory,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a AuditCategory entity.
     */
    private function createDeleteForm(AuditCategory $auditCategory): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_audit_category_delete', array('id' => $auditCategory->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

}
