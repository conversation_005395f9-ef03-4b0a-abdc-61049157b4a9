<?php

namespace Eduprat\AdminBundle\Form;

use <PERSON><PERSON><PERSON>\AdminBundle\Validator\Constraints\NotAlreadyUsedPassword;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Validator\Constraints\UserPassword;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotCompromisedPassword;
use Symfony\Component\Validator\Constraints\Regex;

class EditPasswordType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        $builder->add('current_password', PasswordType::class, array(
            'label' => 'user.form.current_password',
            'mapped' => false,
            'always_empty' => false,
            'constraints' => array(new UserPassword(array('groups' => ['Default', 'password_edit']))),
        ));

        $builder->add('plainPassword', RepeatedType::class, array(
            'type' => PasswordType::class,
            'first_options' => array('label' => 'form.new_password'),
            'second_options' => array('label' => 'form.new_password_confirmation'),
            'invalid_message' => 'user.form.password_mismatch',
            'constraints' => array(
                new NotCompromisedPassword(array('groups' => ['Default', 'password_edit'])),
                new NotBlank(array('groups' => ['Default', 'password_edit'])),
                new Regex(array(
                    'groups' => ['Default', 'password_edit'],
                    'pattern' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,}$/',
                    'match' => true,
                    'message' => 'password.edit.constraints',
                )),
                new NotAlreadyUsedPassword(array('groups' => ['Default', 'password_edit']))
            ),
        ));

        $builder->add('save', SubmitType::class, array(
            'label' =>'admin.global.save',
            'attr' => array(
                'class' => 'btn btn-eduprat'
            ),

        ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'email' => null
        ));
    }

}
