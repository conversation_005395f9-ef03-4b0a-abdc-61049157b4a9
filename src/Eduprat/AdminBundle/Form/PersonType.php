<?php

namespace Eduprat\AdminBundle\Form;

use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Services\AdressesService;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotCompromisedPassword;
use Symfony\Component\Validator\Constraints\Regex;
use Vich\UploaderBundle\Form\Type\VichImageType;
use Eduprat\AdminBundle\Entity\Person;

class PersonType extends AbstractType {

    /**
     * @var array|null
     */
    private $ugas;

    public function __construct($ugas)
    {
        $this->ugas = $ugas;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void {

        $isCoordinator = !is_null($options['user']) && $options['user']->isCoordinator();
        $isCoordinatorLbi = !is_null($options['user']) && $options['user']->isCoordinatorLbi();
        $personEdited = $options["data"];

        $builder
            ->add('email', null, array(
                'label' => 'login.email',
            ))
            ->add('firstname', null, array(
                'label' => 'login.firstname',
            ))
            ->add('lastname', null, array(
                'label' => 'login.lastname',
            ));

        if (!$isCoordinator) {
            $builder
                ->add('roles', ChoiceType::class, array(
                    'choices' => array(
                        'user.role.ROLE_WEBMASTER' => 'ROLE_WEBMASTER',
                        'user.role.ROLE_WEBMASTER_COMPTA' => 'ROLE_WEBMASTER_COMPTA',
                        'user.role.ROLE_COORDINATOR' => 'ROLE_COORDINATOR',
                        'user.role.ROLE_COORDINATOR_LBI' => 'ROLE_COORDINATOR_LBI',
                        'user.role.ROLE_SUPERVISOR' => 'ROLE_SUPERVISOR',
                        'user.role.ROLE_SUPERVISOR_FRANCE' => 'ROLE_SUPERVISOR_FRANCE',
                        'user.role.ROLE_FORMER' => 'ROLE_FORMER',
                        'user.role.ROLE_FORMER_PHARMACIE' => 'ROLE_FORMER_PHARMACIE',
                        'user.role.ROLE_ADVISOR' => 'ROLE_ADVISOR',
                    ),
                    'label' => "login.roles"
                ))
                /** CHAMPS SPECIFIQUES AUX ROLES **/
                ->add('job', null, array(
                    'required' => false,
                    'label' => 'admin.user.job',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE'])]
                ))
                ->add('civility', ChoiceType::class, array(
                    'required' => false,
                    'label' => 'admin.user.civility',
                    'attr' => ['data-required' => "true", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE'])],
                    'choices' => ["Docteur" => "Docteur", "M" => "M", "Mme" => "Mme", "Professeur" => "Professeur"],
                    'placeholder' => 'admin.global.select'
                ));
        }

        $builder
            ->add('address', null, array(
                'required' => false,
                'label' => 'admin.user.address',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
            ))
            ->add('address2', null, array(
                'required' => false,
                'label' => 'admin.user.address2',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
            ))
            ->add('zipCode', null, array(
                'required' => false,
                'label' => 'admin.user.zipCode',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
            ))
            ->add('city', null, array(
                'required' => false,
                'label' => 'admin.user.city',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
            ))
            ->add('phone', null, array(
                'required' => false,
                'label' => 'admin.user.phone',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
            ));

        if (!$isCoordinator) {
            $builder
                ->add('crStatus', ChoiceType::class, array(
                    'required' => true,
                    'label' => 'admin.user.crStatus',
                    'attr' => ['data-required' => "true", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])],
                    'choices' => array(
                        "Coordinateur Régional" => Person::CR_STATUS_INDEPENDANT,
                        "Coordinateur de Formation" => Person::CR_STATUS_SALARIE,
                    )
                ))
                ->add('crStatusDate', DateType::class, array(
                    'required' => true,
                    'label' => 'admin.user.crStatusDate',
                    'attr' => ['provider' => 'datepicker', 'class' => 'datepicker', 'data-required' => "true", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])],
                    'format' => 'dd/MM/yyyy',
                    'widget' => 'single_text',
                    'html5' => false,
                ))
                ->add('crIdentifier', null, array(
                    'required' => false,
                    'label' => 'admin.user.crIdentifier',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])],
                ))
                ->add('codeApporteur', null, array(
                    'required' => false,
                    'label' => 'admin.user.codeApporteur',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_ADVISOR'])],
                ))
                ->add('departments', ChoiceType::class, array(
                    'label' => 'admin.user.departments.title',
                    'expanded' => false,
                    'multiple' => true,
                    'required' => false,
                    'choices'  => array_combine(AdressesService::getDepartementsCodeLabel(), AdressesService::getDepartementsLabel()),
                    'placeholder' => 'admin.user.departments',
                    'attr' => ['class' => "select2", 'data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])]
                ))
                ->add('ugas', ChoiceType::class, array(
                    'label' => 'admin.formation.ugas.title',
                    'expanded' => false,
                    'multiple' => true,
                    'required' => false,
                    'choices'  => $this->getUgas(),
                    'placeholder' => 'admin.global.select',
                    'attr' => ['class' => "select2", 'data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])]
                ))
                ->add('companyName', null, array(
                    'required' => false,
                    'label' => 'admin.user.companyName',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
                ))
                ->add('siret', null, array(
                    'required' => false,
                    'label' => 'admin.user.siret',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_SUPERVISOR', 'ROLE_ADVISOR'])]
                ))
                ->add('rpps', null, array(
                    'required' => false,
                    'label' => 'admin.user.rpps',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE'])]
                ))
                ->add('adeli', null, array(
                    'required' => false,
                    'label' => 'admin.user.adeli',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE'])]
                ))
                ->add('edupratFormer', null, array(
                    'required' => false,
                    'label' => 'admin.user.edupratFormer',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']), 'data-hidden-value' => "false"]
                ))
                ->add('unpaidFormer', null, array(
                    'required' => false,
                    'label' => 'admin.user.unpaidFormer',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']), 'data-hidden-value' => "false"]
                ))
                ->add('supervisor', EntityType::class, array(
                    'label' => 'admin.user.supervisor.title',
                    'class' => Person::class,
                    'choice_value' => 'lastname',
                    'choice_label' =>  'invertedFullname',
                    'query_builder' => function(EntityRepository $repository) {
                        $queryBuilder = $repository->createQueryBuilder('p');
                        return $queryBuilder
                            ->where('p.roles like :role')
                            ->setParameter('role', '%ROLE_SUPERVISOR%');
                    },
                    'placeholder' => 'admin.user.supervisor.empty',
                    'attr' => ['data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])]
                ))
                ->add('webmaster', EntityType::class, array(
                    'label' => 'admin.user.webmaster.title',
                    'class' => Person::class,
                    'choice_value' => 'lastname',
                    'choice_label' =>  'invertedFullname',
                    'query_builder' => function(EntityRepository $repository) {
                        $queryBuilder = $repository->createQueryBuilder('p');
                        return $queryBuilder
                            ->where('p.roles like :role')
                            ->setParameter('role', '%"ROLE_WEBMASTER"%');
                    },
                    'placeholder' => 'admin.user.webmaster.empty',
                    'attr' => ['data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI'])]
                ))
                ->add('cms', null, array(
                    'required' => false,
                    'label' => 'admin.user.cms',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR']), 'data-hidden-value' => "false"]
                ))
                ->add('isArchived', null, array(
                    'required' => false,
                    'label' => 'admin.user.archived',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI']), 'data-hidden-value' => "false"]
                ))
                ->add('allowShareAdress', null, array(
                    'required' => false,
                    'label' => 'admin.user.allowShareAdress',
                    'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI']), 'data-hidden-value' => "false"]
                ))
            ;

            if ($personEdited->getId()) {
                $builder->add('coordinatorBinome', EntityType::class, array(
                    'required' => false,
                    'label' => 'admin.user.coordinatorBinome.title',
                    'class' => Person::class,
                    'choice_value' => 'lastname',
                    'choice_label' =>  'invertedFullname',
                    'query_builder' => function(EntityRepository $repository) use ($personEdited) {
                        $qb = $repository->createQueryBuilder('p');
                        $qb->where('p.roles LIKE :role')
                            ->andWhere('p != :current')
                            ->andWhere(
                                $qb->expr()->orX(
                                    'p.coordinatorBinome IS NULL',
                                    'p = :existingBinome'
                                )
                            )
                            ->setParameter('role', '%"ROLE_COORDINATOR"%')
                            ->setParameter('current', $personEdited)
                            ->setParameter('existingBinome', $personEdited->getCoordinatorBinome());

                        return $qb;

                    },
                    'placeholder' => 'admin.user.coordinatorBinome.empty',
                    'attr' => ['data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI']), 'data-required' => "false"]
                    
                ));
            }

            $builder->addEventListener(
                FormEvents::PRE_SET_DATA,
                function(FormEvent $event) {
                    $form = $event->getForm();
                    $data = $event->getData();

                    $required = is_null($data->getId()) ? "true" : "false";

                    if (is_null($data->getId())) {
                        $form->add('plainPassword', RepeatedType::class, array(
                            'type' => PasswordType::class,
                            'invalid_message' => 'form.password_must_match',
                            'options' => array('required' => true),
                            'first_options'  => array('label' => 'login.password', 'attr' => ['data-required' => $required, 'data-role' => json_encode(['ROLE_WEBMASTER', 'ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_SUPERVISOR', 'ROLE_FORMER_PHARMACIE', 'ROLE_ADVISOR'])]),
                            'second_options' => array('label' => 'login.password_again', 'attr' => ['data-required' => !$required, 'data-role' => json_encode(['ROLE_WEBMASTER', 'ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_SUPERVISOR', 'ROLE_FORMER_PHARMACIE', 'ROLE_ADVISOR'])]),
                            'constraints' => array(
                                new NotCompromisedPassword(),
                                new Regex(array(
                                    'pattern' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,}$/',
                                    'match' => true,
                                    'message' => 'password.edit.constraints'
                                ))
                            ),
                        ));
                    }
                }
            );

            // Pour permettre d'avoir un input select non-multiple
            $builder->get('roles')
                ->addModelTransformer(new CallbackTransformer(
                    function ($originalArray) {
                        if(empty($originalArray)){
                            return null;
                        }
                        return $originalArray[0];
                    },
                    function ($submittedArray) {
                        return array($submittedArray);
                    }
                ))
            ;
        }
        
        if (!$isCoordinatorLbi) {
            $builder->add('manualEmailReminder', CheckboxType::class, array(
                "required" => false,
                'label' => 'admin.user.manualEmailReminder',
                'attr' => ['data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR']), 'data-hidden-value' => "false"]
            ));
        }

        $builder
            ->add('avatarFile', VichImageType::class, array(
                'label' => 'admin.user.avatar',
                'required' => false,
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400, 'data-required' => "false", 'data-role' => json_encode(['ROLE_COORDINATOR', 'ROLE_COORDINATOR_LBI', 'ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']), 'data-hidden-value' => "false"),
            ));

    }

    /**
     * @return array
     */
    public function getUgas()
    {
        $ugas = array();

        foreach ($this->ugas as $index => $uga) {
            $ugas[$uga["label"]] = $uga["id"];
        }

        ksort($ugas);

        return $ugas;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\AdminBundle\Entity\Person',
            'validation_groups' => function (FormInterface $form) {
                $data = $form->getData();
                return array_merge($data->getRoles(), ['Default']);
            },
            'user' => null
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_person';
    }

}
