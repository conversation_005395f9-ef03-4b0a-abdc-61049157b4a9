<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FinanceSousModeSearchType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('keyword', TextType::class, array(
                'required' => false,
                'label' => 'admin.financeMode.form.name'
            ))
            ->add('zip', TextType::class, array(
                'required' => false,
                'label' => 'admin.financeSousMode.zipCode'
            ))
            ->add('city', EntityType::class, array(
                'class' => 'Eduprat\DomainBundle\Entity\FinanceSousMode',
                'label'    => 'admin.financeSousMode.city',
                'choice_label' => function($financeSousMode) {
                    return $financeSousMode->getCity();
                },
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $queryBuilder = $er->createQueryBuilder('f')
                        ->where("f.city != ''")
                        ->groupBy('f.city')
                        ->orderBy('f.city', 'ASC');
                    return $queryBuilder;
                },
            ))
            ->add('financeMode', EntityType::class, array(
                'label' => 'admin.programme.financeMode',
                'required' => false,
                'class' => 'Eduprat\DomainBundle\Entity\FinanceMode',
                'choice_label' => 'name',
                'choice_value' => 'name',
                'placeholder' => 'admin.programme.financeMode',
            ))
            ->add('priseEnCharge', ChoiceType::class, array(
                'required' => false,
                'label' => 'admin.formation.priseEnCharge.title',
                'choices'  => $this->getPrisesEnCharges(),
                'placeholder' => 'admin.financeSousMode.priseEnCharge',
            ))
            // ->add('actalians', CheckboxType::class, array(
            //     'label' => 'admin.financeSousMode.actalians',
            //     'required' => false,
            // ))
        ;

    }

         /**
     * @return array
     */
    public static function getPrisesEnCharges()
    {
        return array(
            FinanceSousMode::OPCO_EP => FinanceSousMode::OPCO_EP,
            FinanceSousMode::FIF_PL => FinanceSousMode::FIF_PL,
            FinanceSousMode::FAF_PM => FinanceSousMode::FAF_PM,
            FinanceSousMode::ANDPC => FinanceSousMode::ANDPC,
            FinanceSousMode::PARCOURS_DPC => FinanceSousMode::PARCOURS_DPC,
            FinanceSousMode::PARCOURS_HORS_DPC => FinanceSousMode::PARCOURS_HORS_DPC
        );
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\AdminBundle\Entity\FinanceSousModeSearch',
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_financesousmode_search';
    }

}
