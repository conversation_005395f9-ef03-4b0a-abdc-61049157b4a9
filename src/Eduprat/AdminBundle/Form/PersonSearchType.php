<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Eduprat\AdminBundle\Entity\PersonSearch;

class PersonSearchType extends AbstractBaseType {

    /**
     * @var array|null
     */
    private $ugas;

    public function __construct($ugas)
    {
        $this->ugas = $ugas;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstname', TextType::class, array(
                'required' => false,
                'label' => 'login.firstname'
            ))
            ->add('lastname', TextType::class, array(
                'required' => false,
                'label' => 'login.lastname'
            ))
        ;

        if ($options["displayRoles"]) {
            $builder->add('role', ChoiceType::class, array(
                'choices' => array(
                    'user.role.ROLE_WEBMASTER' => 'ROLE_WEBMASTER',
                    'user.role.ROLE_WEBMASTER_COMPTA' => 'ROLE_WEBMASTER_COMPTA',
                    'user.role.ROLE_COORDINATOR' => 'ROLE_COORDINATOR',
                    'user.role.ROLE_COORDINATOR_LBI' => 'ROLE_COORDINATOR_LBI',
                    'user.role.ROLE_SUPERVISOR' => 'ROLE_SUPERVISOR',
                    'user.role.ROLE_SUPERVISOR_FRANCE' => 'ROLE_SUPERVISOR_FRANCE',
                    'user.role.ROLE_FORMER' => 'ROLE_FORMER',
                    'user.role.ROLE_FORMER_PHARMACIE' => 'ROLE_FORMER_PHARMACIE',
                    'user.role.ROLE_ADVISOR' => 'ROLE_ADVISOR',
                ),
                'required' => false,
                'placeholder' => 'admin.global.select',
                'label' => "login.roles"
            ));
        } else {
            $builder->add('role', HiddenType::class, array(
                'required' => false,
                'label' => false,
                'attr' => array(
                    'readonly' => true,
                ),
            ));
        }

        $builder
            ->add('status', ChoiceType::class, array(
                'required' => false,
                'choices' => array(
                    'Libéral' => 'liberal',
                    'Salarié' => 'salarie',
                ),
                'label' => 'admin.user.status',
                'placeholder' => 'admin.global.select',
            ))
            ->add('linkedWebmaster', ChoiceType::class, array(
                'required' => false,
                'choices' => array(
                    'admin.global.yes' => 'true',
                    'admin.global.no' => 'false',
                ),
                'label' => 'admin.user.webmaster.linked',
                'placeholder' => 'admin.global.select',
            ))
            ->add('ugas', ChoiceType::class, array(
                'label' => 'admin.formation.ugas.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => $this->getUgas(),
                'placeholder' => 'admin.global.select',
            ))
        ;

    }

    /**
     * @return array
     */
    public function getUgas()
    {
        $ugas = array();

        foreach ($this->ugas as $index => $uga) {
            $ugas[$uga["label"]] = $uga["id"];
        }

        ksort($ugas);

        return $ugas;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => PersonSearch::class,
            'displayRoles' => true,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_person_search';
    }

}
