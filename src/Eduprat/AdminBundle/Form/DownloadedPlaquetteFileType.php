<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;
use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class DownloadedPlaquetteFileType extends AbstractBaseType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('originalName', TextType::class, array(
                'required' => true,
            ))
            ->add('titre', TextType::class, array(
                'required' => false,
                'attr' => [
                    'maxlength' => 40,
                ]
            ))
            ->add('formationType', ChoiceType::class, array(
                'required' => true,
                'label'    => 'Type de formation',
                'expanded' => true,
                'multiple' => false,
                'choices' => array(
                    "Classique"=> "classique",
                    "Journée thématique (ex. Cardiologie)"=> "thematique",
                    "Journée éthique (ex. Agressivité)"=> "ethique",
                    "Journée pratique (ex. Dermoscopie)" => "pratique"
                )
            ))
            ->add('logoPartenaire', EntityType::class, array(
                'class' => 'Eduprat\DomainBundle\Entity\LogoPartenaire',
                'label'    => 'admin.logoPartenaire.name',
                'placeholder' => 'admin.global.select',
                'choice_label' => 'logoName',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('l')
                        ->orderBy('l.logoName', 'ASC');
                },
            ))
            ->add('telecharger', SubmitType::class, [
                'label' => 'admin.global.download',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => DownloadedPlaquetteFile::class,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_download_file';
    }
}
