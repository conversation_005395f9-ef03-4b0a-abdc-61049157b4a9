<?php

namespace Eduprat\AdminBundle\Repository;

use Doctrine\ORM\QueryBuilder;
use Alienor\ApiBundle\Repository\PageableAbstractRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Model\EvaluationUserSearch;
use Eduprat\AdminBundle\Entity\PersonSearch;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSearch;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use function Doctrine\ORM\QueryBuilder;

/**
 * PersonRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class PersonRepository extends PageableAbstractRepository implements PasswordUpgraderInterface
{
    public function getBaseQuery()
    {
        $queryBuilder = $this->createQueryBuilder('p')->select('p');

        return $queryBuilder;
    }

    function getQueryBuilder()
    {
        return $this->getBaseQuery();
    }

    function countQueryBuilder()
    {
        return $this->getBaseQuery()->select('count(p)');
    }

    function listing($page, $nombre)
    {
        return $this->getQueryBuilder()
            ->setFirstResult(($page - 1) * $nombre)
            ->setMaxResults($nombre);
    }

    public function countSearchResults(PersonSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->select('COUNT(p.id)')
            ->getQuery()->getSingleScalarResult()
            ;
    }

    public function findSearchResults(PersonSearch $search, $page, $number) {
        return $this->createSearchResultsQueryBuilder($search)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)
            ->getQuery()->getResult();
    }

    public function findAllSearchResults(PersonSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->getQuery()->getResult();
    }


    public function findAllSearchResultsObName(PersonSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->orderBy("p.lastname", "ASC")
            ->addOrderBy("p.firstname", "ASC")
            ->getQuery()->getResult();
    }
    public function countSearchResultsEvaluation(EvaluationUserSearch $search) {
        $qb = $this->createSearchResultsEvaluationQueryBuilder($search);

        $qb->select('COUNT(DISTINCT p.id)');

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function findSearchResultsEvaluation(EvaluationUserSearch $search, $page, $number, $sortBy = null, $order = "ASC") {
        $qb = $this->createSearchResultsEvaluationQueryBuilder($search);

        $pSum = "COALESCE(SUM(c.evaluationParticipantAnswersSum), SUM(f.evaluationParticipantAnswersSum))";
        $pCount = "COALESCE(SUM(c.evaluationParticipantAnswersCount), SUM(f.evaluationParticipantAnswersCount))";
        $fSum = "COALESCE(SUM(c.evaluationFormerAnswersSum), SUM(f.evaluationCoordinatorAnswersSum))";
        $fCount = "COALESCE(SUM(c.evaluationFormerAnswersCount), SUM(f.evaluationCoordinatorAnswersCount))";

        $qb
            ->select("p as entity, p.id, (COUNT(DISTINCT c.id) + COUNT(DISTINCT f.id)) as number, $pSum as participant_sum, $pCount as participant_count, $fSum as former_sum, $fCount as former_count, ($pSum/$pCount) as participant_avg, ($fSum/$fCount) as former_avg, ((($fSum/$fCount) + ($pSum/$pCount)) / 2) as total_avg")
            ->groupBy('p.id')
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number);

        if ($sortBy) {
            $qb->addOrderBy($sortBy, $order);
        } else {
            $qb->addOrderBy("p.lastname", "ASC");
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param PersonSearch $search
     * @return QueryBuilder
     */
    public function createSearchResultsQueryBuilder(PersonSearch $search) {
        $queryBuilder = $this->getQueryBuilder();

        if ($search->getFirstname()) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.firstname', ':firstname'))
                ->setParameter(':firstname', '%'.$search->getFirstname().'%');
        }

        if ($search->getLastname()) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.lastname', ':lastname'))
                ->setParameter(':lastname', '%'.$search->getLastname().'%');
        }

        if ($search->getRole()) {
            $queryBuilder->andWhere('JSON_SEARCH(p.roles, \''. JsonSearch::MODE_ONE.'\', '. ':role) is not null');
            $queryBuilder->setParameter('role', $search->getRole());
        }

        if ($search->getSupervisor()) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('p.supervisor', ':sup'))->setParameter(':sup', $search->getSupervisor()->getId());
        }

        /**
         * Libéral : formateurs : avec SIRET - coordinateurs : Coordinateur Régional
         * Salarié : formateurs : sans SIRET - coordinateurs : Coordinateur de formation
         */
        if ($search->getStatus()) {
            $queryBuilder->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->like('p.roles', ':roleStatusFormer'),
                    $search->getStatus() === "liberal" ? $queryBuilder->expr()->isNotNull('p.siret') : $queryBuilder->expr()->isNull('p.siret'),
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->like('p.roles', ':roleStatusCoordinator'),
                    $queryBuilder->expr()->like('p.crStatus',  ':crStatus'),
                )
            ));
            $queryBuilder->setParameter(':roleStatusFormer', '%"'. Person::ROLE_FORMER .'"%');
            $queryBuilder->setParameter(':roleStatusCoordinator', '%"'. Person::ROLE_COORDINATOR .'"%');
            $queryBuilder->setParameter(':crStatus', $search->getStatus() === "liberal" ? Person::CR_STATUS_INDEPENDANT : Person::CR_STATUS_SALARIE);
        }

        if ($search->getLinkedWebmaster()) {
            if (filter_var($search->getLinkedWebmaster(), FILTER_VALIDATE_BOOL)) {
                $queryBuilder->andWhere("p.webmaster is not null");
            } else {
                $queryBuilder->andWhere("p.webmaster is null");
            }
        }

        if ($search->getUgas()) {
            $conditions = array();
            foreach($search->getUgas() as $index => $uga) {
                $conditions[] = $queryBuilder->expr()->like("p.ugas", ':uga_'.$index);
                $queryBuilder->setParameter(':uga_'.$index, '%' . $uga . '%');
            }

            $orXUga = $queryBuilder->expr()->orX();
            $orXUga->addMultiple($conditions);
            $queryBuilder->andWhere($orXUga);
        }

        if ($search->getYear()) {
            $start = new \DateTime('first day of January ' . $search->getYear());
            $end = clone $start;
            $end->modify('+ 1 year');
            $queryBuilder->innerJoin('p.coordinators', 'c')
                ->innerJoin('c.formation', 'f')
                ->andWhere('f.startDate BETWEEN :start AND :end')
                ->setParameter('start', $start->format('Y-m-d'))
                ->setParameter('end', $end->format('Y-m-d'));
        }

        // On ne garde pas les admins et les utilisateurs sans rôles
        $queryBuilder->andWhere($queryBuilder->expr()->notLike('p.roles', "'%ROLE_SUPER_ADMIN%'"));
        $queryBuilder->andWhere($queryBuilder->expr()->notLike('p.roles', "'[]'"));

        return $queryBuilder;
    }

    /**
     * @param PersonSearch $search
     * @return QueryBuilder
     */
    public function createSearchResultsEvaluationQueryBuilder(EvaluationUserSearch $search) {
        $queryBuilder = $this->getQueryBuilder();

        $queryBuilder->leftJoin('p.coordinators', 'c')
            ->leftJoin('p.formers', 'f');

        if ($search->getFirstname()) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.firstname', ':firstname'))
                ->setParameter(':firstname', '%'.$search->getFirstname().'%');
        }

        if ($search->getLastname()) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.lastname', ':lastname'))
                ->setParameter(':lastname', '%'.$search->getLastname().'%');
        }

        if ($search->getRole()) {
            $queryBuilder->andWhere('JSON_SEARCH(p.roles, \''. JsonSearch::MODE_ONE.'\', '. ':role) is not null');
            $queryBuilder->setParameter('role', $search->getRole());
        } else {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('p.roles', ':former'),
                    $queryBuilder->expr()->like('p.roles', ':coordinator')
                ))
            ;
            $queryBuilder->setParameter(':former', '%\"ROLE_FORMER\"%');
            $queryBuilder->setParameter(':coordinator', '%\"ROLE_COORDINATOR\"%');
        }

        if ($search->getStart()) {
            $queryBuilder->leftJoin('c.programme', 'prc');
            $queryBuilder->leftJoin('f.programme', 'prf');
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->gte('prc.startDate', ':start'),
                    $queryBuilder->expr()->gte('prf.startDate', ':start')
                )
            );
            $queryBuilder->setParameter(':start', $search->getStart());
        }

        if ($search->getEnd()) {
            $search->getEnd()->setTime(23, 59, 59);
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->lte('prc.endDate', ':end'),
                    $queryBuilder->expr()->lte('prf.endDate', ':end')
                )
            );
            $queryBuilder->setParameter(':end', $search->getEnd());
        }

        return $queryBuilder;
    }

    public function findWithParticipantByEmail ($email) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->innerJoin('p.participant', 'pp');
        $queryBuilder->where('p.email LIKE :email')->setParameter('email', $email);
        $queryBuilder->andWhere($queryBuilder->expr()->like('p.roles', "'[]'"));
        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findByEmailRole($email, $role) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->andWhere($queryBuilder->expr()->like('p.email', ':email'))->setParameter(':email', $email);
        $queryBuilder->andWhere('JSON_SEARCH(p.roles, \''. JsonSearch::MODE_ONE.'\', '. ':role) is not null');
        $queryBuilder->setParameter('role', $role);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findByPeriod(\DateTime $start, \DateTime $end)
     {

        $queryBuilder = $this->getQueryBuilder();

        $queryBuilder->leftJoin('p.coordinators', 'c')
            ->leftJoin('p.formers', 'f');

        if ($start) {
            $queryBuilder->leftJoin('c.formation', 'frc');
            $queryBuilder->leftJoin('f.formation', 'frf');
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->gte('frc.querySearchStartDate', ':start'),
                    $queryBuilder->expr()->gte('frf.querySearchStartDate', ':start')
                )
            );
            $queryBuilder->setParameter(':start', $start);
        }

        if ($end) {
            $end->setTime(23, 59, 59);
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->lte('frc.querySearchEndDate', ':end'),
                    $queryBuilder->expr()->lte('frf.querySearchEndDate', ':end')
                )
            );
            $queryBuilder->setParameter(':end', $end);
        }

        return $queryBuilder->getQuery()->getResult();
     }

     public function findCoordinatorIds() {
        $qb = $this->createQueryBuilder('p')->select('p.id')
            ->where('p.roles like :coordinator')
            ->setParameter(':coordinator', '%\"ROLE_COORDINATOR\"%');
        return $qb->getQuery()->getScalarResult();
    }

     public function findCoordinators() {
        $qb = $this->createQueryBuilder('p')
            ->where('p.roles like :coordinator')
            ->setParameter(':coordinator', '%\"' . Person::ROLE_COORDINATOR .  '\"%')
            ->andWhere("p.isArchived = false")
        ->orderBy("p.lastname", "ASC")
        ->addOrderBy("p.firstname", "ASC");
        return $qb->getQuery()->getResult();
    }

    public function findSupervisors() {
        $qb = $this->createQueryBuilder('p')
            ->where('p.roles like :supervisor')
            ->setParameter(':supervisor', '%\"' . Person::ROLE_SUPERVISOR .  '\"%')
            ->andWhere("p.isArchived = false")
        ->orderBy("p.lastname", "ASC")
        ->addOrderBy("p.firstname", "ASC");
        return $qb->getQuery()->getResult();
    }

     public function findAdvisors() {
        $qb = $this->createQueryBuilder('p')
            ->where('p.roles like :advisor')
            ->setParameter(':advisor', '%\"' . Person::ROLE_ADVISOR .  '\"%')
        ->orderBy("p.lastname", "ASC")
        ->addOrderBy("p.firstname", "ASC");
        return $qb->getQuery()->getResult();
    }

     public function findAdvisorByFullname($fullname) {

        $qb = $this->createQueryBuilder('p')
            ->where('p.roles like :advisor')
            ->andWhere("CONCAT(p.lastname, ' ', p.firstname) like :fullname")
            ->setParameter('advisor', '%\"' . Person::ROLE_ADVISOR .  '\"%')
            ->setParameter('fullname', $fullname)
        ;
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findSupervisorCA(Person $person, $year, $n1 = false) {

        $priceField = $n1 ? "price_year_n1" : "price";
        $start = $n1 ? $year-1 . "-01-01" : "$year-01-01";
        $end = $n1 ? $year-1 . "-12-31" : "$year-12-31";

        $whereSupervisor = $person->isSupervisorFr() ? '' : 'WHERE supervisor = :supervisor';
        $rawQuery = '
            SELECT f.id, f.programme_discr, (DATEDIFF(DATE(f.endDate), DATE(f.startDate)) + 1 ) AS diff, (sum(pa.'.$priceField.')) AS ca,
            (SELECT count(*) FROM coordinator c2 WHERE c2.formation = f.id) AS nbcoord
            FROM person pe
            INNER JOIN coordinator co ON pe.id = co.person
            INNER JOIN formation f ON f.id = co.formation
            INNER JOIN participation pa ON pa.formation = f.id
            '.$whereSupervisor.'
            AND f.openingDate BETWEEN :start AND :end
            AND pa.archived = false
            GROUP BY f.id
            HAVING nbcoord = 1
        UNION ALL
            SELECT f.id, f.programme_discr, (DATEDIFF(DATE(f.endDate), DATE(f.startDate)) + 1 ) AS diff, (sum(pa.'.$priceField.')) AS ca,
            (SELECT count(*) FROM coordinator c2 WHERE c2.formation = f.id) AS nbcoord
            from person pe
            INNER JOIN coordinator co ON pe.id = co.person
            INNER JOIN formation f ON f.id = co.formation
            INNER JOIN participation pa ON pa.formation = f.id
            '.$whereSupervisor.'
            AND pa.coordinator = co.id
            AND f.openingDate BETWEEN :start AND :end
            AND pa.archived = false
            GROUP BY f.id
            HAVING nbcoord > 1
        ';

        $statement = $this->getEntityManager()->getConnection()->prepare($rawQuery);
        if ($person->isSupervisor()) $statement->bindValue('supervisor', $person->getId());
        $statement->bindValue('start', $start);
        $statement->bindValue('end', $end);
        $result = $statement->executeQuery();

        $result = $result->fetchAllAssociative();
        $ca = 0;
        foreach ($result as $item) {
            if ($item["programme_discr"] === Formation::TYPE_SEDD) {
                $ca += ($item["ca"] * $item["diff"]);
            } else {
                $ca += $item["ca"];
            }
        }

        return $ca;
   }

    function findCoordinatorsByUga($uga) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->andWhere($queryBuilder->expr()->like('p.roles', ':role'))->setParameter(':role', '%\"' . Person::ROLE_COORDINATOR . '\"%');
        $queryBuilder->andWhere($queryBuilder->expr()->like('p.ugas', ':uga'))->setParameter(':uga', '%' . $uga . '%');
        $queryBuilder->andWhere("p.isArchived = false");
        return $queryBuilder->getQuery()->getResult();
    }

    public function countInitiatorAnalysis($year)
    {
        $start = "$year-01-01";
        $end = "$year-12-31";

        $qb = $this->getQueryBuilder();

        return $qb->select('p.id, pr.categories, pr.exercisesMode, count(DISTINCT f.id) as value')
            ->innerJoin("p.coordinators", "c")
            ->innerJoin("c.formation", "f")
            ->innerJoin("f.programme", "pr")
            ->addGroupBy("p.id")
            ->addGroupBy("pr.categories")
            ->addGroupBy("pr.exercisesMode")
            ->andWhere("f.startDate between :start and :end")
            ->andWhere($qb->expr()->notLike("pr.format", ":elearning"))
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->setParameter('elearning', Programme::FORMAT_ELEARNING)
            ->andWhere("c.initiator = true")
            ->getQuery()
            ->getScalarResult();
    }

    /**
     * @see https://symfony.com/doc/4.4/security/password_migration.html#upgrade-the-password-when-using-doctrine
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->flush();
    }

    public function findByRole($role, $id = null, $ignoreArchived = false)
    {
        $qb = $this->createQueryBuilder('p');
        $qb->where('JSON_SEARCH(p.roles, \''. JsonSearch::MODE_ONE.'\', '. ':role) is not null');
        $qb->setParameter('role', $role);

        if ($id) {
            $qb->andWhere('p.id = :id')
                ->setParameter('id', $id);
        }
        if ($ignoreArchived) {
            $qb->andWhere('p.isArchived = false');
        }
        return $qb->getQuery()->getResult();
    }
}
