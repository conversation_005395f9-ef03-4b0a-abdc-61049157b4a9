<?php

namespace Eduprat\AdminBundle\Services;

use DateTime;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Services\EmailSender;

class FormationClosedQuizCompleted
{

    const START_DATE = '2020-06-01';
    /**
     * @var EntityManager
     */
    private $em;
    /**
     * @var EmailSender
     */
    private $emailSender;

    /**
     * @var ParticipationAccessManager
     */
    private $participationAccessManager;


    /**
     * csvImporter constructor.
     * @param EntityManager $em
     * @param EmailSender   $emailSender
     */
    public function __construct(EntityManagerInterface $em, EmailSender $emailSender, ParticipationAccessManager $participationAccessManager)
    {
        $this->emailSender = $emailSender;
        $this->participationAccessManager = $participationAccessManager;
        $this->em = $em;
        $this->start = New DateTime(self::START_DATE);
    }

    public function sendEmail() {
        $formationRepository = $this->em->getRepository(Formation::class);
        $end = $this->getDateSubWeek(new DateTime('now'));

        $formations = $formationRepository->findFormationClosedByPeriod($this->start, $end);

        foreach ($formations as $formation) {
            $quizCompleted = true;
            $participations = $formation->getParticipations();
            foreach($participations as $participation) {
                if (!$this->participationAccessManager->isBothFormCompleted($participation)) {
                    $quizCompleted = false;
                };
            }

            if ($quizCompleted) {
                $this->emailSender->sendFormationCloseQuizCompletedEmail($formation);
                $formation->setQuizsCompletedsEmailSended(true);
                $this->em->persist($formation);
                $this->em->flush();
            }
        }
    }

    public function getDateSubWeek($date) {
        $date = date('d-m-Y',strtotime("-7 day",mktime(23,59,59,$date->format('m'),$date->format('d'),$date->format('y'))));
        $date = New DateTime($date);
        $date->setTime('23','59','59');
        return $date;
    }
   
}