<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\BaseQuestion;
use Ed<PERSON>rat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\EvaluationProgramme;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;

class EvaluationProgrammeByCoordinatorManager extends EvaluationManagerProgrammeByCoordinator
{
    CONST NB_QUESTION = 7;

    /**
     * @var string
     */
    protected $type = "programme";

    /**
     * EvaluationFormerManager constructor.
     * @param EntityManager $entityManager
     * @param FormFactory $formFactory
     * @param Programme $programme
     * @param \Eduprat\AdminBundle\Entity\Coordinator         $coordinator
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Programme $programme, Coordinator $coordinator) {
        parent::__construct($entityManager, $formFactory, $programme, $coordinator);
    }

    public function getAnswerClass()
    {
        return EvaluationProgramme::class;
    }

    /**
     * @return ArrayCollection
     */
    public function getAnswers(){
        return new ArrayCollection($this->entityManager->getRepository($this->getAnswerClass())->findByProgrammeCoordinator($this->programme, $this->coordinator));
    }

    public function getNbQuestion()
    {
        return self::NB_QUESTION;
    }

    public function getAssociatedAnswer(BaseQuestion $question)
    {
        /** @var EvaluationFormerAnswer $answer */
        $answer = parent::getAssociatedAnswer($question);
        return $answer;
    }

}