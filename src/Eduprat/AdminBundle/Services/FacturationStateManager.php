<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\FactureState;
use Doctrine\ORM\EntityManagerInterface;

class FacturationStateManager {

    private CourseManager $courseManager;
    private EntityManagerInterface $entityManager;

    public function __construct(CourseManager $courseManager, EntityManagerInterface $entityManager)
    {
        $this->courseManager = $courseManager;
        $this->entityManager = $entityManager;
    }

    const DPC_FSM_NAME = "GIP Agence Nationale du DPC - 5720";

    // Fonction d'attribution d'états de facturation d'une formation
    public function manageFactureModes (Formation $formation): void {
        if(!$formation->getFactureState()) {
            $factureState = new FactureState($formation);
            $this->entityManager->persist($factureState);
            $formation->setFactureState($factureState);
        }
        $formation->getFactureState()->setIsWaiting($this->isWaiting($formation));
        $formation->getFactureState()->setIsChargeable($this->isChargeable($formation));
    }

    // Date de cloture dépassée de 1 jour
    // Case globale factu non cochée
    // Au moins un sous mode de financement DPC
    // Au moins une attestation manquante (si elle est obligatoire) OU un parcours DPC non terminé
    public function isWaiting(Formation $formation): bool {
        if ($formation->getBilled()) {
            return false;
        }

        if ($formation->isPluriAnnuelle()) { 
            return $this->isWaitingPluriannuel($formation);
        }
        return 
            $this->isClosingDatePassed($formation) &&
            $formation->isDpc() && // Correspond à la ligne 341 du FormationRepository (exclusion de prise en charge)
            ($this->hasMissingAttestationInDpc($formation) || $this->hasUncompletedCourseInDpc($formation));
    }


    // Date de cloture dépassée de 1 jour
    // Un sous mode de financement HDPC non clôturé OU
    // Les parcours d'un sous mode sont terminés
    public function isChargeable(Formation $formation): bool {
        if ($formation->getBilled()) {
            return false;
        }
        if ($formation->isPluriAnnuelle()) { 
            return $this->isChargeablePluriannuel($formation);
        }
        return 
            $this->isClosingDatePassed($formation) &&
            ($this->hasUncheckedFnsmHdpc($formation) || $this->hasUncheckedFnsmDpcChargeable($formation));
    }

    public function isWaitingPluriannuel(Formation $formation): bool {
            return $this->isWaitingN($formation) || $this->isWaitingN1($formation);
    }

    public function isWaitingN(Formation $formation) {
        // Date de fin de la dernière unité de l'année N dépassée  => $this->isLastUnityOfYearPassed($formation)
        // ET
        //    hasUncompletedCoursePreInDpc => Parcours pré DPC non complété sur un sous mode non facturé
        //    OU
        //    hasMissingAttestationInDpc => Attestation mis parcours manquante (si obligatoire) sur sous mode DPC_FSM_NAME non coché
        return $this->isLastUnityOfYearPassed($formation->getOpeningDate()->format('Y'), $formation) && 
        ($this->hasUncompletedCoursePreInDpc($formation) || $this->hasMissingAttestationInDpc($formation));
    }

    public function isWaitingN1(Formation $formation) {
        // Date de fin de la dernière unité de l'année N dépassée  => $this->isLastUnityOfYearPassed($formation)
        // ET
        //    hasUncompletedCourseInDpc => Parcours post DPC non complété sur un sous mode non facturé
        //    OU
        //    hasMissingAttestationInDpc n1 true => Attestation post parcours manquante (si obligatoire) sur sous mode DPC_FSM_NAME non coché
        return $this->isLastUnityOfYearPassed($formation->getClosingDate()->format('Y'), $formation) && 
        ($this->hasUncompletedCourseInDpc($formation, true) || $this->hasMissingAttestationInDpc($formation, true));
    }

    public function isChargeablePluriannuel(Formation $formation): bool {
            // Date de fin de la dernière unité de l'année N dépassée => $this->isLastUnityOfYearPassed($formation)
            // ET
            //    hasUncheckedFnsmHdpc => un sous mode HorsDpc avec case mis parcours non cochée
            //    OU
            //    hasUncheckedFnsmDpcChargeable => un sous mode Dpc avec tous les parcours pré terminés + attestations mi-parcours importées (si obligatoire)
        
        return $this->isChargeableN($formation) || $this->isChargeableN1($formation);
    }

    public function isChargeableN(Formation $formation): bool {
        return  $this->isLastUnityOfYearPassed($formation->getOpeningDate()->format('Y'), $formation) &&
        ($this->hasUncheckedFnsmHdpc($formation) || $this->hasUncheckedFnsmDpcChargeable($formation));
    }

    public function isChargeableN1(Formation $formation): bool {
        return  $this->isLastUnityOfYearPassed($formation->getClosingDate()->format('Y'), $formation) &&
        ($this->hasUncheckedFnsmHdpc($formation, true) || $this->hasUncheckedFnsmDpcChargeable($formation, true));
    }
        

    // Déjà géré par la requête intiale (pas besoin de développement)
    // public function isBilled(Formation $formation): bool {
    //     return $formation->getOneFinanceSousModeFactured();
    // }

    // Date de clôture dépassée
    public function isClosingDatePassed(Formation $formation): bool
    {
        $closingDateExpired = (new \DateTime())->modify('- 1 day')->setTime(0, 0, 0);
        $closingDate = (clone $formation->getClosingDate())->setTime(0, 0, 0);
        return $closingDate <= $closingDateExpired;
    }

    // Date de fin de dernière unitée de l'année (utilisé pour les pluriannuelles)
    public function isLastDateOfYearClosingDatePassed(Formation $formation): bool
    {
        $closingDateExpired = (new \DateTime())->modify('- 1 day')->setTime(0, 0, 0);
        $year = $formation->getOpeningDate()->format("Y");
        return $formation->getLastUnityDateOfYear($year) <= $closingDateExpired;
    }

    // Dernière unitée de l'année N dépassée
    public function isLastUnityOfYearPassed(string $year, Formation $formation): bool
    {
        $lastUnityDateOfYear = $formation->getLastUnityDateOfYear($year);
        $closingDateExpired = (new \DateTime())->modify('- 1 day')->setTime(0, 0, 0);
        return $lastUnityDateOfYear <= $closingDateExpired;
    }

    // Vérifie si une formation dont le programme possède des heures non connectées
    // et au moins une participation ayant pour sous mode de financement self::DPC_FSM_NAME non facturé
    // et qui n'a pas son attestation sur l'honneur d'uploadée
    public function hasMissingAttestationInDpc(Formation $formation, $n1 = false): bool 
    {
        if ((!$formation->isVfc() || !$formation->isTcs()) && $formation->getProgramme()->thereIsOfflineHours()) {
            foreach ($formation->getParticipations() as $participation) {
                $fnsm = $participation->getFinanceSousMode();
                $attestationMissing = $n1 ? !$participation->getAttestationHonneurN1() : !$participation->getAttestationHonneur();
                if ($fnsm->getIdentifiant() === self::DPC_FSM_NAME && 
                    !$formation->isFacturedFinanceSousMode($fnsm->getId(), $n1) &&
                    $attestationMissing) {
                        return true;
                }
            }
        }
        return false;
    }

    // Vérifie si au moins une participation ayant un sous mode de financement DPC non facturé
    // n'a pas terminé son parcours (nextModule non vide)
    public function hasUncompletedCourseInDpc(Formation $formation, $n1 = false): bool 
    {
        foreach ($formation->getParticipations() as $participation) {
            $fnsm = $participation->getFinanceSousMode();
            $fnsmNotBilled = $n1 ? !$formation->isFacturedFinanceSousMode($fnsm->getId(), true) : !$formation->isFacturedFinanceSousMode($fnsm->getId());
            $courseUncomplete = $participation->getFormation()->isVfc() ? !$participation->getCourseEnded() : $participation->getNextModule();
            if ($fnsm->isDpc() && 
                $fnsmNotBilled &&
                $courseUncomplete) {
                return true;
            }
        }
        return false;
    }

    // Vérifie si au moins une participation ayant un sous mode de financement DPC non facturé
    // n'a pas terminé son parcours pré
    public function hasUncompletedCoursePreInDpc(Formation $formation): bool 
    {
        foreach ($formation->getParticipations() as $participation) {
            $fnsm = $participation->getFinanceSousMode();
            if ($fnsm->isDpc() && 
                !$formation->isFacturedFinanceSousMode($fnsm->getId()) &&
                $this->isDpcCoursePreUncompleted($participation)) {
                return true;
            }
        }
        return false;
    }

    // Vérifie s'il y a un sous mode de financement hors dpc non facturé dans la formation
    // isFacturedFinanceSousMode($fnsm->getId()) == Case mi-parcours pour session pluriannuelle / Case globale pour session mono annuelle
    public function hasUncheckedFnsmHdpc(Formation $formation, $n1 = false): bool
    {
        foreach($formation->getParticipations() as $participation) {
            $fnsm = $participation->getFinanceSousMode();
            if ($fnsm->isHorsDPC() && !$formation->isFacturedFinanceSousMode($fnsm->getId(), $n1)) {
                return true;
            }
        }
        return false;
    }

    // Vérifie si les participants d'au moins un sous mode dpc non facturé
    // ont terminés leur parcours et upload leurs attestations (si requises)
    // (Terminés leur parcours pré et upload attestation mis parcours si la formation est pluriannuelle => $n1 == true)
    public function hasUncheckedFnsmDpcChargeable(Formation $formation, $n1 = false): bool
    {
        $fnsmArray = [];
        foreach ($formation->getParticipations() as $participation) {
            $fnsm = $participation->getFinanceSousMode();
            if (!$formation->isFacturedFinanceSousMode($fnsm->getId(), $n1)) { // Si le sous mode n'est pas déjà facturé
                if (!isset($fnsmArray[$fnsm->getId()])) {
                    $fnsmArray[$fnsm->getId()] = true;
                }
                // Vérifications non nécéssaire si une participation a déjà rendu ce sous mode non facturable
                if ($fnsmArray[$fnsm->getId()]) {
                    $fnsmArray[$fnsm->getId()] = $this->isParticipationChargeable($participation, $n1);
                }
            }
        }
        // Si un sous mode respecte les conditions de facturation alors la formation est potentiellement facturable
        foreach ($fnsmArray as $fnsm) {
            if ($fnsm) {
                return true;
            }
        }

        // Aucun sous mode DPC n'est facturable
        return false;
    }

    // Parcours complété si obligatoire
    // Et attestation upload si obligatoire
    public function isParticipationChargeable(Participation $participation, $n1 = false): bool {
        if ($participation->getFormation()->isPluriAnnuelle()) {
            if ($n1) {
                return !$this->isDpcCourseUncompleted($participation) && !$this->isDpcMissingAttestation($participation, $n1);
            } else {
                return !$this->isDpcCoursePreUncompleted($participation) && !$this->isDpcMissingAttestation($participation);
            }
        }
        return !$this->isDpcCourseUncompleted($participation) && !$this->isDpcMissingAttestation($participation);
    }

    // Parcours obligatoire est non complété
    public function isDpcCourseUncompleted(Participation $participation): bool
    {
        if ($participation->getFinanceSousMode()->isDpc()) {
            return  $participation->getFormation()->isVfc() ? !$participation->getCourseEnded() : $participation->getNextModule() != null;
        }
        return false;
    }

    // Parcours pré obligatoire est non complété
    public function isDpcCoursePreUncompleted(Participation $participation): bool
    {
        // La formation n'est pas Elearning :
        // Récupération du schéma de la course : $courseManager->getCourse($participation);
        // A partir duquel on récupère le dernier module de l'étape 1
        // Afin de savoir si ce module est complété (si le parcours pré est complété)
        if (!$participation->getFormation()->isElearning()) {
            $course = $this->courseManager->getCourse($participation);
            $lastModuleStep1 = last($course[CourseManager::STEP1]);
            return $lastModuleStep1 && !$participation->isStepCompleted($lastModuleStep1["id"]);
        }
        // La formation est Elearning :
        // Le module reunion (participation.isStepCompleted("reunion")) qui correspond au module e-learning doit être complété
        return !$participation->isStepCompleted(CourseManager::STEP2_REUNION_LABEL);
    }

    // Attestation obligatoire manquante
    // getAttestationHonneur() == Attestation mi-parcours pour session pluriannuelle / Attestation globale pour session mono annuelle
    public function isDpcMissingAttestation(Participation $participation, $n1 = false): bool
    {
        $attestationMissing = $n1 ? !$participation->getAttestationHonneurN1() : !$participation->getAttestationHonneur();
        $formation = $participation->getFormation();
        return  (!$formation->isVfc() || !$formation->isTcs()) && // Pas obligatoire pour les VFC
                !$formation->getProgramme()->isFormatPresentiel() && // Pas de vérif si format présentiel
                $formation->getProgramme()->thereIsOfflineHours() && // Obligatoire si heures hors ligne et pas VFC
                $participation->getFinanceSousMode()->getIdentifiant() === self::DPC_FSM_NAME && // si GIP...5720
                $attestationMissing // Attestation manquante
        ;
    }

    // Un sous mode n'est pas coché ($n1 = true => vérifier si N+1 non coché => pluriannuelles only)
    public function hasUncheckedFnsm(Formation $formation, $n1 = false): bool
    {
        foreach($formation->getFinanceSousModes() as $fnsm) {
            if (!$formation->isFacturedFinanceSousMode($fnsm->getId(), $n1)) {
                return true;
            }
        }
        return false;
    }

}
