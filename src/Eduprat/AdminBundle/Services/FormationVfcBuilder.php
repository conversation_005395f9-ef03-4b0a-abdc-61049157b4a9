<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\DomainBundle\Entity\FormationVfc;
use Eduprat\DomainBundle\Entity\Step;
use Eduprat\DomainBundle\Entity\Course;
use Eduprat\DomainBundle\Entity\Module;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\ModuleTimes;
use Eduprat\DomainBundle\Entity\ModuleMinTimes;

class FormationVfcBuilder extends FormationBuilder
{
    public function buildCourse(): Course
    {
        // Step 1
        $video = new Module(CourseManager::STEP1_VIDEO, true, false, 1);
        $vignette = new Module(CourseManager::STEP1_FORM_PRE, true, false, 2);
        $scoring = new Module(CourseManager::STEP1_PRERESTITUTION, true, false, 3);
        $etutorat = new Module(CourseManager::STEP1_ETUTORAT, true, true, 4);
        $step1 = new Step([$video, $vignette, $scoring, $etutorat], 1);

        // Step 2
        $reunion = new Module(CourseManager::STEP2_REUNION, false, false, 1);
        $step2 = new Step([$reunion], 2);

        // Step 3
        $ficheAction = new Module(CourseManager::STEP2_FICHE_ACTION, false, false, 1);
        $synthese = new Module(CourseManager::STEP4_SYNTHESE, false, false, 2);
        $etutorat = new Module(CourseManager::STEP4_ETUTORAT, false, false,  3);
        $satisfaction = new Module(CourseManager::SATISFACTION, false, false, 4);
        $topos = new Module(CourseManager::STEP2_TOPOS, false, false, 5);
        $toolBox = new Module(CourseManager::STEP3_TOOL_BOX, false, false, 6);
        $docPedago = new Module(CourseManager::STEP1_DOC_PEDAGOGIQUE, false, false, 7);
        $end = new Module(CourseManager::STEP4_END, false, false, 8);
        $step3 = new Step([$ficheAction, $synthese, $etutorat, $satisfaction, $topos, $toolBox, $docPedago, $end], 3);

        $steps = [$step1, $step2, $step3];
        $course = new Course($steps);

       return $course;
    }

    public function buildFormation($formationClass, $programme): FormationVfc
    {
        $formation = new $formationClass($this->buildCourse());
        $formation->setProgramme($programme);
        $formation->addModuleTime(new ModuleTimes(Formation::TYPE_VFC)); // TODO: créer une classe ModuleTimeVFC
        $formation->addModuleMinTime(new ModuleMinTimes($programme));
        return $formation;
    }
}
