<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Coordinator;
use Ed<PERSON>rat\DomainBundle\Entity\BaseQuestion;
use Eduprat\DomainBundle\Entity\EvaluationFormerAnswer;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Form\FormFactory;
use Eduprat\DomainBundle\Entity\EvaluationFormerByCoordinator;
use Symfony\Component\Form\FormFactoryInterface;

class EvaluationFormerByCoordinatorManager extends EvaluationManagerCoordinator
{
    CONST NB_QUESTION = 7;

    /**
     * @var string
     */
    protected $type = "former";

    /**
     * @var Person
     */
    private $former;

    /**
     * EvaluationFormerManager constructor.
     * @param EntityManager $entityManager
     * @param FormFactory $formFactory
     * @param Programme $programme
     * @param Person $former
     * @param \Eduprat\AdminBundle\Entity\Coordinator         $coordinator
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Programme $programme, Person $former, Coordinator $coordinator) {
        $this->former = $former;
        parent::__construct($entityManager, $formFactory, $programme, $coordinator);
    }

    public function getAnswerClass()
    {
        return EvaluationFormerByCoordinator::class;
    }

    /**
     * @return ArrayCollection
     */
    public function getAnswers(){
        return new ArrayCollection($this->entityManager->getRepository($this->getAnswerClass())->findByFormerProgrammeCoordinator($this->former, $this->programme, $this->coordinator));
    }

    public function getNbQuestion()
    {
        return self::NB_QUESTION;
    }

    public function getAssociatedAnswer(BaseQuestion $question)
    {
        /** @var EvaluationFormerAnswer $answer */
        $answer = parent::getAssociatedAnswer($question);
        $answer->setFormer($this->former);
        return $answer;
    }

}