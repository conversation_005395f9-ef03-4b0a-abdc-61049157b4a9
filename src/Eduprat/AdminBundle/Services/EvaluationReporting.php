<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\EvaluationGlobalManager;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer;
use Eduprat\DomainBundle\Entity\EvaluationGlobalQuestion;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class EvaluationReporting
{

    /**
     * @var EntityManager
     */
    protected $entityManager;

    /**
     * @var EntityRepository
     */
    protected $repo;

    /**
     * @var Translator
     */
    private $translator;

    /**
     * FormManagerCoordinator constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager, TranslatorInterface $translator)
    {
        $this->entityManager = $entityManager;
        $this->repo = $entityManager->getRepository(EvaluationGlobalAnswer::class);
        $this->translator = $translator;
    }

    /**
     * @param EvaluationGlobalAnswer[] $answers
     * @param array|null $questions
     * @param bool $pdf
     * @param string $key
     * @return array
     */
    public function aggregate(array $answers, array $questions = null, $pdf = false, $key = "label")
    {

        $report = array();
        $titles = array();

        $questions = array_filter($questions, function ($q) {
            return $q !== EvaluationGlobalManager::COMMENT_QUESTION;
        });

        if ($pdf) {
            $questions = array_filter($questions, function ($q) {
                return !in_array($q, EvaluationGlobalManager::RADIO_QUESTIONS);
            });
        }

        foreach ($questions as $question) {
            $report[$question] = array(
                "question" => $question,
                "identifier" => explode("_", $question)[0],
                "label" => EvaluationGlobalManager::getLabel($question, $key),
                "count" => 0,
                "sum" => 0,
                "formers" => array(),
                "countValid" => 0,
            );
        }

        $report = array_values(array_reduce($answers, function ($result, EvaluationGlobalAnswer $answer) use ($questions) {
            $question = $answer->getQuestion();
            if ($questions === null || in_array($question, $questions)) {
                $result[$question]["count"]++;
                if ((int) $answer->getAnswer() > 0) {
                    $result[$question]["countValid"]++;
                }
                $result[$question]["sum"] += (int)$answer->getAnswer();

                if ($question === EvaluationGlobalManager::FORMER_RECOMMANDE_QUESTION) {
                    if (!isset($result[$question]["formers"][$answer->getFormer()->getId()])) {
                        $result[$question]["formers"][$answer->getFormer()->getId()] = array(
                            "former" => $answer->getFormer(),
                            "count" => 0,
                            "sum" => 0
                        );
                    }
                    $result[$question]["formers"][$answer->getFormer()->getId()]["count"]++;
                    $result[$question]["formers"][$answer->getFormer()->getId()]["sum"] += (int) $answer->getAnswer();
                }

            }
            return $result;
        }, $report));

        $previous = null;

        foreach ($report as $index => &$r) {
            $identifier = $r["identifier"];
            if (!in_array($identifier, $titles)) {
                $titles[] = $identifier;
                $r['title'] = $identifier;
            }
            if ($previous !== $identifier) {
                if ($previous !== null) {
                    $report[$index - 1]['close'] = true;
                }
                $previous = $identifier;
            }
            $r["avg"] = $r["countValid"] > 0 ? round($r["sum"] / $r["countValid"], 2) : 0;
            if ($index === (count($report) - 1)) {
                $r['close'] = true;
            }
        }

        return $report;
    }

    public function getSummary(array $reporting, $pdf = false) {
        $summary = array_reduce($reporting, function($result, $r) use ($pdf) {
            $identifier = $r["identifier"];
            $question = $r["question"];
            if ($pdf && in_array($identifier, EvaluationGlobalManager::RADIO_QUESTIONS)) {
                return $result;
            }
            if (!isset($result[$identifier])) {
                $result[$identifier] = array(
                    "count" => 0,
                    "total" => 0,
                    "sum" => 0,

                );
            }
            if (!isset($result[$question])) {
                $result[$question] = array(
                    "count" => 0,
                    "total" => 0,
                    "sum" => 0,

                );
            }
            $result[$identifier]["count"] += $r["countValid"] > 0 ? 1 : 0;
            $result[$identifier]["total"] += $r["countValid"];
            $result[$identifier]["sum"] += $r["avg"];
            $result[$question]["count"] += $r["countValid"] > 0 ? 1 : 0;
            $result[$question]["total"] += $r["countValid"];
            $result[$question]["sum"] += $r["avg"];
            return $result;
        }, array());

        foreach ($summary as &$s) {
            $s["avg"] = $s["count"] > 0 ? round($s["sum"] / $s["count"], 2) : 0;
        }

        return $summary;
    }

    /**
     * @param $answers
     * @param bool $pdf
     * @return array
     */
    public function getFormerReportAnswers($answers, $pdf = false)
    {
        $report = $this->aggregate($answers, EvaluationGlobalManager::FORMER_QUESTIONS, $pdf);

        $expert = array_count_values(array_values(array_map(function (EvaluationGlobalAnswer $answer) {
            return (int)$answer->getAnswer();
        }, array_filter($answers, function (EvaluationGlobalAnswer $answer) {
            return $answer->getQuestion() === EvaluationGlobalManager::FORMER_RECOMMANDE_QUESTION;
        }))));

        if (!isset($expert[0])) {
            $expert[0] = 0;
        }
        if (!isset($expert[1])) {
            $expert[1] = 0;
        }

        return array(
            "questions" => $report,
            "expert" => $expert
        );
    }

    public function returnReportingAndRadar(array $reporting, array $answers, $pdf = false)
    {
        $radarData = $this->getRadarData($reporting);
        $showRadar = count(array_filter($radarData[0], function($r) { return $r["show"]; })) >= 3;
        return array(
            "reporting" => $reporting,
            "answersCount" => $this->getAnswersCount($answers),
            "radarData" => $radarData,
            "showRadar" => $showRadar,
            "summary" => $this->getSummary($reporting, $pdf)
        );
    }

    /**
     * @param Formateur $former
     * @param bool $pdf
     * @return array
     */
    public function getFormerReport(Formateur $former, $pdf = false)
    {
        $answers = $this->repo->findByFormer($former);
        $reporting = $this->getFormerReportAnswers($answers, $pdf);
        $radarData = $this->getRadarData($reporting["questions"]);
        $showRadar = count(array_filter($radarData[0], function($r) { return $r["show"]; })) >= 3;
        if ($pdf) {
            return array(
                "reporting" => $reporting["questions"],
                "radarData" => $radarData,
                "showRadar" => $showRadar,
                "answersCount" => $this->getAnswersCount($answers),
                "summary" => $this->getSummary($reporting["questions"], $pdf),
            );
        }
        return array_merge($reporting, array(
            "radarData" => $radarData,
            "showRadar" => $showRadar,
            "answersCount" => $this->getAnswersCount($answers),
            "summary" => $this->getSummary($reporting["questions"], $pdf),
        ));
    }

    /**
     * @param Person $person
     * @param $year
     * @param bool $pdf
     * @return array
     */
    public function getFormerReportYear(Person $person, $year, $pdf = false)
    {
        $answers = $this->repo->findByFormerYear($person, $year);
        $reporting = $this->getFormerReportAnswers($answers, $pdf);
        if ($pdf) {
            return array(
                "reporting" => $reporting["questions"],
                "answersCount" => $this->getAnswersCount($answers),
                "radarData" => $this->getRadarData($reporting["questions"]),
                "summary" => $this->getSummary($reporting["questions"], $pdf),
            );
        }
        return array_merge($reporting, array(
            "radarData" => $this->getRadarData($reporting["questions"]),
            "answersCount" => $this->getAnswersCount($answers),
            "summary" => $this->getSummary($reporting["questions"], $pdf),
        ));
    }

    /**
     * @param Person $person
     * @param $year
     * @param bool $pdf
     * @return array
     */
    public function getCoordinatorReportYear(Person $person, $year, $pdf = false)
    {
        $answersParticipant = $this->repo->findByCoordinatorYearParticipant($person, $year);
        $answersFormer = $this->repo->findByCoordinatorYearFormer($person, $year);

        $participantReportingAccueil = $this->aggregate($answersParticipant, EvaluationGlobalManager::ACCUEIL_QUESTIONS_PARTICIPANT, $pdf, "report");
        $formerReportingAccueil = $this->aggregate($answersFormer, EvaluationGlobalManager::ACCUEIL_QUESTIONS_FORMER, $pdf, "report");
        $participantReportingConformite = $this->aggregate($answersParticipant, EvaluationGlobalManager::CONFORMITE_QUESTIONS, $pdf, "report");
        $formerReportingConformite = $this->aggregate($answersFormer, EvaluationGlobalManager::CONFORMITE_QUESTIONS, $pdf, "report");

        $radarData = array(array_values(array_map(function($questions) {
            foreach ($questions as $index => $question) {
                if ($index > 0) {
                    $questions[0]["sum"] += $question["sum"];
                    $questions[0]["count"] += $question["count"];
                    unset($questions[$index]);
                }
            }
            return array(
                "axis" => $this->translator->trans($questions[0]["label"]),
                "value" => $questions[0]["count"] ? round($questions[0]["sum"] / $questions[0]["count"], 2) * 20 : null,
            );
        }, array_reduce(array_merge($participantReportingAccueil, $formerReportingAccueil, $participantReportingConformite, $formerReportingConformite), function ($result, $r) {
            if (!isset($result[$r["question"]])) {
                $result[$r["question"]] = array();
            }
            $result[$r["question"]][] = $r;
            return $result;
        }, array()))));

        return array(
            "participantsAccueil" => $participantReportingAccueil,
            "formersAccueil" => $formerReportingAccueil,
            "participantsConformite" => $participantReportingConformite,
            "formersConformite" => $formerReportingConformite,
            "answersCount" => $this->getAnswersCount(array_merge($answersParticipant, $answersFormer)),
            "radarData" => $radarData,
            "summary" => $this->getSummary(array_merge($participantReportingAccueil, $formerReportingAccueil, $participantReportingConformite, $formerReportingConformite), $pdf),
            "participantSummaryAccueil" => $this->getSummary($participantReportingAccueil, $pdf),
            "formerSummaryAccueil" => $this->getSummary($formerReportingAccueil, $pdf),
            "participantSummaryConformite" => $this->getSummary($participantReportingConformite, $pdf),
            "formerSummaryConformite" => $this->getSummary($formerReportingConformite, $pdf),
        );
    }

    /**
     * @param $title
     * @param $year
     * @param $role
     * @param bool $pdf
     * @return array
     */
    public function getTopoByYearAndRole($title, $year, $role, $pdf = false)
    {
        $answers = $this->repo->findByTitleYearRole($title, $year, $role);
        $questions = $role === Person::ROLE_FORMER ? EvaluationGlobalManager::TOPO_QUESTIONS_FORMER : EvaluationGlobalManager::TOPO_QUESTIONS;
        $reporting = $this->aggregate($answers, $questions, $pdf);
        return $this->returnReportingAndRadar($reporting, $answers, $pdf);
    }

    /**
     * @param Programme $programme
     * @param $role
     * @param bool $pdf
     * @return array
     */
    public function getProgrammeReport(Programme $programme, $role, $pdf = false)
    {
        $answers = $this->repo->findByProgrammeRole($programme, $role);
        $questions = EvaluationGlobalManager::getAllQuestionsForRole($programme->getFormations()->first(), $role);
        $identifiers = $questions->map(function (EvaluationGlobalQuestion $question) {
            return $question->getIndex();
        })->toArray();
        $reporting = $this->aggregate($answers, $identifiers, $pdf);
        return array(
            "reporting" => $reporting,
            "answersCount" => $this->getAnswersCount($answers),
            "radarData" => $this->getRadarData(array_filter($reporting, function($r) {
                return !in_array($r["question"], EvaluationGlobalManager::RADIO_QUESTIONS);
            }), true),
            "summary" => $this->getSummary($reporting, $pdf),
        );
    }

    /**
     * @param Formation $formation
     * @param $role
     * @param bool $pdf
     * @return array
     */
    public function getFormationReport(Formation $formation, $role, $pdf = false)
    {
        $answers = $this->repo->findByFormationRole($formation, $role);
        $questions = EvaluationGlobalManager::getAllQuestionsForRole($formation, $role);
        $identifiers = $questions->map(function (EvaluationGlobalQuestion $question) {
            return $question->getIndex();
        })->toArray();
        $reporting = $this->aggregate($answers, $identifiers, $pdf);
        $radarData = $this->getRadarData(array_filter($reporting, function ($r) {
            return !in_array($r["question"], EvaluationGlobalManager::RADIO_QUESTIONS);
        }), true);
        $showRadar = count(array_filter($radarData[0], function($r) { return $r["show"]; })) >= 3;
        return array(
            "reporting" => $reporting,
            "answersCount" => $this->getAnswersCount($answers),
            "radarData" => $radarData,
            "showRadar" => $showRadar,
            "summary" => $this->getSummary($reporting, $pdf),
        );
    }

    /**
     * @param $title
     * @param $year
     */
    public function getTopoListingDetail($title, $year)
    {
        $roles = array("ROLE_PARTICIPANT", "ROLE_FORMER");
        $avgs = array();
        foreach ($roles as $role) {
            $answers = $this->repo->findByTitleYearRole($title, $year, $role);
            $questions = $role === Person::ROLE_FORMER ? EvaluationGlobalManager::TOPO_QUESTIONS_FORMER : EvaluationGlobalManager::TOPO_QUESTIONS;
            $reporting = $this->aggregate($answers, $questions, false);
            $avgs[$role] = round(array_sum(array_map(function($r) {
                return $r["avg"];
            }, $reporting)) / count($reporting), 2);
        }
        $avgs["total"] = round(($avgs["ROLE_PARTICIPANT"] + $avgs["ROLE_FORMER"]) / 2, 2);
        return $avgs;

    }

    /**
     * @param Programme $programme
     * @return array
     */
    public function getProgrammeDetail(Programme $programme)
    {
        $roles = array("ROLE_PARTICIPANT", "ROLE_FORMER");
        $avgs = array();
        foreach ($roles as $role) {
            $answers = $this->repo->findByProgrammeRole($programme, $role);
            $questions = $role === Person::ROLE_FORMER ? EvaluationGlobalManager::TOPO_QUESTIONS_FORMER : EvaluationGlobalManager::TOPO_QUESTIONS;
            $reporting = $this->aggregate($answers, $questions, false);
            $avgs[$role] = array(
                "count" => array_sum(array_map(function($r) {
                    return $r["count"];
                }, $reporting)),
                "sum" => array_sum(array_map(function($r) {
                    return $r["sum"];
                }, $reporting))
            );
        }
        return $avgs;

    }


    /**
     * @param Coordinator $coordinator
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     */
    public function getCoordinatorDetail(Coordinator $coordinator)
    {
        $answersParticipant = $this->repo->findByCoordinatorParticipant($coordinator);
        $answersFormer = $this->repo->findByCoordinatorFormer($coordinator);

        $participantQuestions = array_merge(
            EvaluationGlobalManager::ACCUEIL_QUESTIONS_PARTICIPANT,
            EvaluationGlobalManager::CONFORMITE_QUESTIONS
        );

        $formerQuestions = array_merge(
            EvaluationGlobalManager::ACCUEIL_QUESTIONS_FORMER,
            EvaluationGlobalManager::CONFORMITE_QUESTIONS
        );

        $participantReporting = $this->aggregate($answersParticipant, $participantQuestions, false, "report");
        $formerReporting = $this->aggregate($answersFormer, $formerQuestions, false, "report");

        $avgs[Person::ROLE_PARTICIPANT] = array(
            "count" => array_sum(array_map(function($r) {
                return $r["countValid"];
            }, $participantReporting)),
            "sum" => array_sum(array_map(function($r) {
                return $r["sum"];
            }, $participantReporting))
        );

        $avgs[Person::ROLE_FORMER] = array(
            "count" => array_sum(array_map(function($r) {
                return $r["countValid"];
            }, $formerReporting)),
            "sum" => array_sum(array_map(function($r) {
                return $r["sum"];
            }, $formerReporting))
        );

        return $avgs;
    }


    /**
     * @param Coordinator $coordinator
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     */
    public function getFormerDetail(Formateur $former)
    {
        $answersParticipant = $this->repo->findByFormer($former, Person::ROLE_PARTICIPANT);
        $answersCoordinator = $this->repo->findByFormer($former, Person::ROLE_COORDINATOR);

        $participantReporting = $this->aggregate($answersParticipant, EvaluationGlobalManager::FORMER_QUESTIONS, false, "report");
        $coordinatorReporting = $this->aggregate($answersCoordinator, EvaluationGlobalManager::FORMER_QUESTIONS, false, "report");

        $avgs[Person::ROLE_PARTICIPANT] = array(
            "count" => array_sum(array_map(function($r) {
                return $r["countValid"];
            }, $participantReporting)),
            "sum" => array_sum(array_map(function($r) {
                return $r["sum"];
            }, $participantReporting))
        );

        $avgs[Person::ROLE_COORDINATOR] = array(
            "count" => array_sum(array_map(function($r) {
                return $r["countValid"];
            }, $coordinatorReporting)),
            "sum" => array_sum(array_map(function($r) {
                return $r["sum"];
            }, $coordinatorReporting))
        );

        return $avgs;
    }

    /**
     * @param $answers
     * @return int
     */
    public function getAnswersCount($answers)
    {
        return count(array_unique(array_map(function(EvaluationGlobalAnswer $answer) {
            return $answer->getPerson()->getId();
        }, $answers)));
    }

    /**
     * @param array $reporting
     * @param bool $grouping
     * @param \Closure $getTranslation
     * @return array
     */
    public function getRadarData(array $reporting, $grouping = false, \Closure $getTranslation = null)
    {
        if ($getTranslation === null) {
            $getTranslation = function($d) {
                return $this->translator->trans($d["label"]);
            };
        }

        if ($grouping) {
            $result = array_reduce($reporting, function ($result, $r) {
                if (!isset($result[$r["identifier"]])) {
                    $result[$r["identifier"]] = array();
                }
                $result[$r["identifier"]][] = $r["avg"];
                return $result;
            }, array());

            $data = array();
            foreach ($result as $key => $r) {
                $data[] = array(
                    "axis" => $this->translator->trans("evaluation.global." . $key . ".title"),
                    "value" => round(array_sum($r) / count($r), 2) * 20,
                    "show" => array_sum($r) > 0,
                );
            }
            return array($data);
        }

        return array(array_map(function ($d) use ($getTranslation) {
            return array(
                "axis" => $getTranslation($d),
                "value" => round((float)$d["avg"] * 20),
                "show" => $d["count"] > 0,
            );
        }, $reporting));
    }

    /**
     * Retourne le nombre de votant
     * @param Formation[] $formations
     * @return array
     */
    public function getAnswersCountAndAverage(array $formations): array
    {
        return $this->repo->globalAverageAndCountParticipant($formations);
    }
}
