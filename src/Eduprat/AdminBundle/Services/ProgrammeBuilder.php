<?php

namespace Eduprat\AdminBundle\Services;

use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\ProgrammeVfcOnSite;
use Eduprat\DomainBundle\Entity\ProgrammeVfcVirtual;
use Eduprat\DomainBundle\Entity\ProgrammeTcsOnSite;
use Eduprat\DomainBundle\Entity\ProgrammeTcsVirtual;


class ProgrammeBuilder implements ProgrammeBuilderInterface
{
    const VFC_FORMATION_TYPE = 'VFC';
    const TCS_FORMATION_TYPE = 'TCS';
    const OTHER_FORMATION_TYPE = 'other';

    const PROGRAMME_TYPES = [
        self::VFC_FORMATION_TYPE => [
            PROGRAMME::PRESENCE_SITE => ProgrammeVfcOnSite::class,
            PROGRAMME::PRESENCE_VIRTUELLE => ProgrammeVfcVirtual::class
        ],
        self::TCS_FORMATION_TYPE => [
            PROGRAMME::PRESENCE_SITE => ProgrammeTcsOnSite::class,
            PROGRAMME::PRESENCE_VIRTUELLE => ProgrammeTcsVirtual::class
        ],
        self::OTHER_FORMATION_TYPE => [
            PROGRAMME::PRESENCE_SITE => Programme::class,
            PROGRAMME::PRESENCE_VIRTUELLE => Programme::class,
            PROGRAMME::PRESENCE_ELEARNING => Programme::class
        ]
    ];

    public function buildProgramme($presence): Programme
    {
        return (new Programme())->setPresence($presence);
    }

    public static function isGuidedFormation($programme): bool
    {
        return $programme->getProgrammeDiscr() !== Programme::TYPE_PROGRAMME;
    }

    public static function getKeys(): array{
        $keys = [];
        foreach (self::PROGRAMME_TYPES as $key => $types) {
            $keys[$key] = array_keys($types);
        }
        return $keys;
    }
}
