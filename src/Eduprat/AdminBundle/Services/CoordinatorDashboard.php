<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;

/**
 * Service pour calculer les inscriptions de la semaine d'un coordinateur
 */
class CoordinatorDashboard
{
    private EntityManagerInterface $entityManager;

    private string $parcoursV3MigrationDate;

    public function __construct(EntityManagerInterface $entityManager, $parcoursV3MigrationDate)
    {
        $this->entityManager = $entityManager;
        $this->parcoursV3MigrationDate = $parcoursV3MigrationDate;
    }

    /**
     * Calcule le nombre d'inscriptions de la semaine pour un coordinateur
     * Du samedi 00h00 au vendredi 23h59
     *
     * @param Person $coordinator Le coordinateur
     * @param \DateTime|null $referenceDate Date de référence (par défaut aujourd'hui)
     * @return int Nombre d'inscriptions dans la période
     */
    public function getWeeklyRegistrationsCount(Person $coordinator, ?\DateTime $referenceDate = null): int
    {
        $today = $referenceDate ?? new \DateTime();
        $weekDates = $this->calculateWeekDates($today);

        $participationRepository = $this->entityManager->getRepository(Participation::class);
        return $participationRepository->findParticicipationsForCoordinatorBetweenDates($coordinator, $weekDates['start'], $weekDates['end']);
    }

    /**
     * Calcule le nombre d'inscriptions du mois pour un coordinateur
     * Du 1er du mois 00h00 au dernier jour du mois 23h59
     *
     * @param Person $coordinator Le coordinateur
     * @param \DateTime|null $referenceDate Date de référence (par défaut aujourd'hui)
     * @return int Nombre d'inscriptions dans la période
     */
    public function getMonthlyRegistrationsCount(Person $coordinator, ?\DateTime $referenceDate = null): int
    {
        $referenceDate = $referenceDate ?? new \DateTime();

        $startDate = clone $referenceDate;
        $startDate->modify('first day of this month')->setTime(0, 0, 0);

        $endDate = clone $referenceDate;
        $endDate->modify('last day of this month')->setTime(23, 59, 59);

        $participationRepository = $this->entityManager->getRepository(Participation::class);
        return $participationRepository->findParticicipationsForCoordinatorBetweenDates($coordinator, $startDate, $endDate);
    }

    /**
     * Calcule le nombre de désinscriptions de la semaine pour un coordinateur
     * Du samedi 00h00 au vendredi 23h59
     *
     * @param Person $coordinator Le coordinateur
     * @param \DateTime|null $referenceDate Date de référence (par défaut aujourd'hui)
     * @return int Nombre de désinscriptions dans la période
     */
    public function getWeeklyUnsubscriptionsCount(Person $coordinator, ?\DateTime $referenceDate = null): int
    {
        $today = $referenceDate ?? new \DateTime();
        $weekDates = $this->calculateWeekDates($today);

        $participationHistoryRepository = $this->entityManager->getRepository(ParticipationHistory::class);
        return $participationHistoryRepository->countUnsubscriptionsForCoordinatorBetweenDates($coordinator, $weekDates['start'], $weekDates['end']);
    }

    /**
     * Calcule le nombre d'inscriptions sans coût pour un coordinateur
     * Sur des formations avec openingDate postérieure à aujourd'hui
     *
     * @param Person $coordinator Le coordinateur
     * @return int Nombre d'inscriptions sans coût
     */
    public function getInscriptionsWithoutCosts(Person $coordinator): int
    {
        $participationRepository = $this->entityManager->getRepository(Participation::class);
        return $participationRepository->countInscriptionsWithoutCostsForCoordinator($coordinator);
    }

    /**
     * Récupère les factures manquantes de l'année
     *
     * @param Person $coordinator Le coordinateur
     * @return int Nombre de factures manquantes
     */
    public  function getMissingFiles(Person $coordinator)
    {
        $querySearchStartDate = (new \DateTime('first day of January'))->setTime(0, 0, 0);
        $querySearchEndDate = (new \DateTime('last day of December'))->setTime(23, 59, 59);

        $search = new ComptabiliteSearch();
        $search->coordinator = $coordinator;
        $search->querySearchStartDate = $querySearchStartDate;
        $search->querySearchEndDate = $querySearchEndDate;

        $formationRepository = $this->entityManager->getRepository(Formation::class);
        $formationsWithMissingFiles = $formationRepository->getMissingFilesQuery($search)->getQuery()->getResult();

        $countMissingFiles = 0;

        foreach ($formationsWithMissingFiles as $formation) {
            foreach ($formation->getCoordinators() as $cr) {
                if ($cr->getPerson()->getId() == $coordinator->getId() && count($cr->getParticipations())) {
                    //dd($cr->getCoordinatorFiles()->first());//
                    if ($crFile = $cr->getCoordinatorFiles()->first()) {

                        if (is_null($crFile->getFactureCoordinator())) {
                            $countMissingFiles++;
                        }


                        if ($formation->isPluriAnnuelle() && $formation->shouldDisplayAttestationN1() && is_null($crFile->getFactureCoordinatorN1()))  {
                            $countMissingFiles++;
                        }
                    } else {
                        $countMissingFiles++;
                        if ($formation->isPluriAnnuelle() && $formation->shouldDisplayAttestationN1()) {
                            $countMissingFiles++;
                        }
                    }
                }
            }
        }

        return $countMissingFiles;
    }

    /**
     * Récupère le nombre de modules manquants de l'année
     *
     * @param Person $coordinator Le coordinateur
     * @return int Nombre de modules manquants
     */
    public  function getMissingModules(Person $coordinator, $echeance72Hours = false)
    {
        $querySearchStartDate = (new \DateTime('first day of January'))->setTime(0, 0, 0);
        $querySearchEndDate = (new \DateTime('last day of December'))->setTime(23, 59, 59);

        $search = new ComptabiliteSearch();
        $search->coordinator = $coordinator;
        $search->querySearchStartDate = $querySearchStartDate;
        $search->querySearchEndDate = $querySearchEndDate;

        $participationRepository = $this->entityManager->getRepository(Participation::class);
        $missingModules = $participationRepository->findMissingModules($search);

        if ($echeance72Hours) {
            $countMissingModules72Hours = 0;
            $today = (new \DateTime())->setTime(00, 00, 00);
            $date3Day = (new \DateTime('+3 day'))->setTime(23, 59, 59);
            foreach ($missingModules as $module) {
                if ($module["dateModuleManquantExpiration"] <= $date3Day && $module["dateModuleManquantExpiration"] >= $today) {
                    $countMissingModules72Hours++;
                }
            }
            return $countMissingModules72Hours;
        }

        return count($missingModules);
    }

    /**
     * Récupère le nombre d'attestations manquantes de l'année
     *
     * @param Person $coordinator Le coordinateur
     * @return int Nombre d'attestations manquantes
     */
    public  function getMissingAttestations(Person $coordinator, $echeance2Weeks = false)
    {
        $querySearchStartDate = (new \DateTime('first day of January'))->setTime(0, 0, 0);
        $querySearchEndDate = (new \DateTime('last day of December'))->setTime(23, 59, 59);
        $search = new AttestationComptabiliteSearch();
        $search->coordinator = $coordinator;
        $search->querySearchStartDate = $querySearchStartDate;
        $search->querySearchEndDate = $querySearchEndDate;

        $participationRepository = $this->entityManager->getRepository(Participation::class);
        $missingAttestations = $participationRepository->getMissingAttestationFilesQuery($search, $this->parcoursV3MigrationDate)->getQuery()->getResult();

        $totalCount = 0;
        $twoWeekBefore = (new \DateTime())->setTime(00, 00, 00)->modify('-2 weeks');
        foreach ($missingAttestations as $participationAttestation) {
            if (is_null($participationAttestation->getAttestationHonneur()) && $participationAttestation->getFormation()->isMissingAttestation()) {
                if ($echeance2Weeks) {
                    if ($participationAttestation->getFormation()->getAttestationEcheanceN() < $twoWeekBefore) {
                        $totalCount++;
                    }
                } else {
                    $totalCount++;
                }
            }
            if (is_null($participationAttestation->getAttestationHonneurN1()) && $participationAttestation->getFormation()->isMissingAttestationN1()) {
                if ($echeance2Weeks) {
                    if ($participationAttestation->getFormation()->getAttestationEcheanceN1() < $twoWeekBefore) {
                        $totalCount++;
                    }
                } else {
                    $totalCount++;
                }
            }
        }
        return $totalCount;
    }



    /**
     * Calcule les dates de début et fin de semaine (samedi 00h00 à vendredi 23h59)
     * 
     * @param \DateTime $referenceDate Date de référence
     * @return array Tableau avec les clés 'start' et 'end'
     */
    public function calculateWeekDates(\DateTime $referenceDate): array
    {
        $dayOfWeek = (int)$referenceDate->format('w'); // 0 = dimanche, 1 = lundi, ..., 6 = samedi
        
        if ($dayOfWeek === 6) { // On est samedi
            $startDate = clone $referenceDate;
            $startDate->setTime(0, 0, 0);
            $endDate = clone $referenceDate;
            $endDate->modify('+7 days')->setTime(23, 59, 59);
        } else {
            // Calculer le samedi précédent
            $daysToSubtract = ($dayOfWeek === 0) ? 1 : ($dayOfWeek + 1); // Si dimanche, reculer de 1 jour, sinon reculer de (jour + 1)
            $startDate = clone $referenceDate;
            $startDate->modify("-{$daysToSubtract} days")->setTime(0, 0, 0);
            
            if ($dayOfWeek === 5) { // Si aujourd'hui est vendredi
                $endDate = clone $referenceDate;
                $endDate->setTime(23, 59, 59);
            } else {
                // Calculer le vendredi suivant
                $daysToAdd = (5 - $dayOfWeek + 7) % 7;
                if ($daysToAdd === 0) $daysToAdd = 7;
                $endDate = clone $referenceDate;
                $endDate->modify("+{$daysToAdd} days")->setTime(23, 59, 59);
            }
        }

        return [
            'start' => $startDate,
            'end' => $endDate
        ];
    }

    /**
     * Calcule les dates de début et fin de mois (1er du mois 00h00 au dernier jour du mois 23h59)
     *
     * @param \DateTime $referenceDate Date de référence
     * @return array Tableau avec les clés 'start' et 'end'
     */
    public function calculateMonthDates(\DateTime $referenceDate): array
    {
        // Premier jour du mois à 00h00
        $startDate = clone $referenceDate;
        $startDate->modify('first day of this month')->setTime(0, 0, 0);

        // Dernier jour du mois à 23h59
        $endDate = clone $referenceDate;
        $endDate->modify('last day of this month')->setTime(23, 59, 59);

        return [
            'start' => $startDate,
            'end' => $endDate
        ];
    }


}
