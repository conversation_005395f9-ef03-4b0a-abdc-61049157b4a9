<?php

namespace Eduprat\AdminBundle\Services;

use Ed<PERSON>rat\DomainBundle\Entity\EvaluationProgramme;

class EvaluationProgrammeManager extends EvaluationManagerProgrammeByCoordinator
{

    CONST NB_QUESTION = 7;

    /**
     * @var string
     */
    protected $type = "programme";

    public function getAnswerClass()
    {
        return EvaluationProgramme::class;
    }

    public function getNbQuestion()
    {
        return self::NB_QUESTION;
    }

}