<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Authentication\Token\SwitchUserToken;

readonly class IpPersonSaver
{

    public function __construct(
        private EntityManagerInterface $entityManager,
        private Security $security,
    )
    {
    }

    public function saveIp(string $ip): void
    {
        if (!$this->security->getToken() instanceof SwitchUserToken) {
            /** @var Person $person */
            $person = $this->security->getUser();
            if (!$person->alreadyInIpList($ip)) {
                $person->addIpList($ip);
                $this->entityManager->persist($person);
                $this->entityManager->flush();
            }
        }
    }
}
