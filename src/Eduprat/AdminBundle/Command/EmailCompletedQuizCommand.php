<?php

namespace Eduprat\AdminBundle\Command;

use Ed<PERSON>rat\AdminBundle\Services\FormationClosedQuizCompleted;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:email_formation_and_quiz_completed', description: "Envoi d'un email lorsque la formations est cloturée et que les questionnaires sont complétés")]
class EmailCompletedQuizCommand extends Command
{
    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    public function __construct(FormationClosedQuizCompleted $formationClosedQuizCompleted)
    {
        parent::__construct();
        $this->formationClosedQuizCompleted = $formationClosedQuizCompleted;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {

            $this->formationClosedQuizCompleted->sendEmail();

        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }
}
