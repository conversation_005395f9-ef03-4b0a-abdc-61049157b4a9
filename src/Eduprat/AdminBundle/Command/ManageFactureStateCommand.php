<?php
namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\FacturationStateManager;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:manage-facture-state', description: "Vérification des états chargeable && waiting des formations récémment modifiées")]
class ManageFactureStateCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var FacturationStateManager
     */
    private $facturationStateManager;

    public function __construct(EntityManagerInterface $entityManager, FacturationStateManager $facturationStateManager)
    {
        $this->entityManager = $entityManager;
        $this->facturationStateManager = $facturationStateManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findRecentlyUpdatedFormations();
        $count = 0;
        $formationCount = count($formations);
        foreach ($formations as $formation) {
            $count++;
            $this->facturationStateManager->manageFactureModes($formation);
            if ($count%50 == 0) {
                $this->entityManager->flush();
                dump($count . "/" . $formationCount);
            }
        }
        $this->entityManager->flush();
        dump($count . "/" . $formationCount);
        return 0;
    }
   public function findRecentlyUpdatedFormations(): array
   {
       $nowSub30min = (new \DateTime())->sub(new \DateInterval('PT30M'));
       $formationRepository = $this->entityManager->getRepository(Formation::class);
       $qb = $formationRepository->createQueryBuilder('f');
       return $qb
           ->select('f')
           ->innerJoin('f.participations', 'part')
           ->leftJoin('f.factureState', 'fs')
           ->where(
                $qb->expr()->orX(
                    $qb->expr()->gte('f.updatedAt', ':nowSub30min'),
                    $qb->expr()->gte('part.updatedAt', ':nowSub30min'),
                    $qb->expr()->isNull('fs.id'),
                )
            )->setParameter(':nowSub30min', $nowSub30min)
            ->andWhere('f.archived = :archived')->setParameter('archived', false)
            ->andWhere('f.closed = :closed')->setParameter('closed', false)
            ->getQuery()
            ->getResult()
           ;
   }
}
