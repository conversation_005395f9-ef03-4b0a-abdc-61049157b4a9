<?php
namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Attribute\AsCommand;

#[AsCommand(name: 'eduprat:reinitMissingFiles:pluriannuals')]
class reinitMissingFilesForPluriannualsCommand extends Command
{     
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure()
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findAllPluri();
        $count = 0;
        $formationCount = count($formations);
        foreach($formations as $formation) {
            $formation->updateFileCompleted();
            if ($count % 50 == 0) {
                $this->entityManager->flush();
                dump($count . "/" . $formationCount);
            }
            $count++;
        }
        $this->entityManager->flush();
        dump($count . "/" . $formationCount);
        return 0;
    }

    public function findAllPluri(): array
    {
        $formationRepo = $this->entityManager->getRepository(Formation::class);
        $qb = $formationRepo->createQueryBuilder('f');
        return $qb
            ->select('f')
            ->where($qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'))
            ->getQuery()
            ->getResult()
            ;
    }
}