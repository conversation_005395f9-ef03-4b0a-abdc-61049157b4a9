<?php
namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:ElearningTwoUnityConvertor', description: "Transformation des formations sur 3 unités qui doivent rentrer dans la règle elearningTwoUnity")]
class ElearningTwoUnityConvertorCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addOption('force', null, InputOption::VALUE_NONE)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $programmes  = $this->findAll3UnitiesElearning();
        dump(count($programmes));

        foreach($programmes as $programme) {
            if ($programme->isElearningTwoUnity() && count($programme->getUnities()) == 3) {
               $unityToFormationDelete = $programme->getUnityByPosition(3);
               foreach($programme->getFormations() as $session) {
                    if (count($session->getUnities()) == 3) {
                        $unitySessionToDelete = $session->getUnityByPosition(3);
                        $this->entityManager->remove($unitySessionToDelete);
                    }
                }
                $this->entityManager->remove($unityToFormationDelete);
                $this->entityManager->flush();
            }
        }
        return 0;
    }
   public function findAll3UnitiesElearning(): array
   {
       $repoProgramme = $this->entityManager->getRepository(Programme::class);
       $qb = $repoProgramme->createQueryBuilder('p');
       return $qb
           ->select('p')
           ->innerJoin('p.unities', 'u')
           ->where('p.sessionType = :sessionType')
           ->andWhere('p.presence = :presence')
           ->andWhere('u.method = :method')
           ->setParameter('sessionType', Formation::TYPE_ELEARNING)
           ->setParameter('presence', Programme::PRESENCE_SITE)
           ->setParameter('method', Programme::METHOD_CONTINUE)
           ->getQuery()
           ->getResult()
           ;
   }
}
