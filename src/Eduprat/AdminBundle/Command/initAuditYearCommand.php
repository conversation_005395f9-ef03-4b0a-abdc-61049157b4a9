<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:init_audit_year', description: "Associe l'année des questionnaires utilisés en 2022")]
class initAuditYearCommand extends Command
{
    private $participationRepo;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var CourseManager
     */
    private $courseManager;

    public function __construct(EntityManagerInterface $entityManager, CourseManager $courseManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->courseManager = $courseManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption("force", "f", InputOption::VALUE_NONE, "A passer pour valider les modifications")
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->formationRepo = $this->entityManager->getRepository(Formation::class);

        $queryBuilder = $this->entityManager
            ->createQueryBuilder()
            ->select('f')
            ->from(Formation::class, 'f');

        $formations = $queryBuilder
            ->where($queryBuilder->expr()->gte('f.startDate', ':start'))->setParameter(':start', "2022-01-01 00:00:00")
            ->getQuery()
            ->getResult()
            ;

        /** @var Participation $participation */
        foreach($formations as $formation) {
            if($formation->getForm())
            {
                if($formation->isAudit())
                {
                    if($formation->getAudit()) {
                        $audit1 = $formation->getAudit();
                        $audit1->setYear("2022");
                        if($formation->isVignette()) {
                            if($formation->getAudit2()) {
                                 $audit2 = $formation->getAudit2();
                                 $audit2->setYear("2022");
                             }
                        }
                    }
                } else {
                    $form = $formation->getForm();
                    $form->setYear("2022");
                }
            }
        }

        $this->entityManager->flush();
        return Command::SUCCESS;
    }
}
