<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\AdminBundle\Services\CsvBilanExport;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:csv_export', description: "<PERSON>énère le fichier CSV des coordinateurs ou superviseur")]
class GenerateCSVExportCommand extends Command
{
    /**
     * @var string
     */
    private $projectDir;

    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    public function __construct(string $projectDir, CsvBilanExport $csvBilanExport)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->csvBilanExport = $csvBilanExport;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('type', InputArgument::REQUIRED, 'Type d\'export')
            ->addArgument('start', InputArgument::REQUIRED, 'start')
            ->addArgument('end', InputArgument::REQUIRED, 'end')
            ->addOption('jsonReferenceDate', null, InputOption::VALUE_OPTIONAL, 'jsonReferenceDate')
            ->addOption('supervisor', null, InputOption::VALUE_OPTIONAL, 'Id du superviseur')
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $folder = $input->getOption('supervisor') ? ("sup-" . $input->getOption('supervisor') . "/") : "";

        try {
            // coordinator / supervisor
            $type = $input->getArgument('type');
            $start = new \DateTime($input->getArgument('start'));
            $end = new \DateTime($input->getArgument('end'));

            $baseName = sprintf('eduprat_export_%s_%s_%s', $type, $start->format('Y-m-d'), $end->format('Y-m-d'));

            $fileTmp = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.tmp', $baseName);
            $file = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.csv', $baseName);
            $fileLog = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.log', $baseName);

            if($file && file_exists($file)) {
                unlink($file);
            }

            touch($file);

            if ($type === "coordinator") {
                $this->csvBilanExport->exportCoordinator($fileTmp, $start, $end, $input->getOption('supervisor') ? $input->getOption('supervisor') : null);
            } else if ($type === "supervisor") {
                $this->csvBilanExport->exportSupervisor($fileTmp, $start, $end);
            } else if ($type === "participant") {
                $this->csvBilanExport->exportParticipant($fileTmp, $start, $end);
            } else if ($type === "collaborators") {
                $this->csvBilanExport->exportCollaborator($fileTmp, $start, $end);
            } else if ($type === "satisfaction") {
                $this->csvBilanExport->exportSatisfactionComms($fileTmp, $start, $end);
            } else if ($type === "etutorat") {
                $this->csvBilanExport->exportEtutoratComms($fileTmp, $start, $end);
            } else if ($type === "sessions") {
                $this->csvBilanExport->exportSessions($fileTmp, $start, $end);
            } else if ($type === "sessionsFull") {
                $this->csvBilanExport->exportSessionsFull($fileTmp, $start, $end, $input->getOption('jsonReferenceDate'));
            } else if ($type === "invoices") {
                $this->csvBilanExport->exportInvoices($fileTmp, $start, $end);
            } else if ($type === "detail_session") {
                $this->csvBilanExport->exportInvoicesDetail($fileTmp, $start, $end);
            } else if ($type === "coordinators") {
                $this->csvBilanExport->exportCoordinators($fileTmp, $start, $end);
            } else if ($type === "participations") {
                $this->csvBilanExport->exportParticipationWithoutCoordinator($fileTmp, $start, $end);
            } else if ($type === "former_with_siret") {
                $this->csvBilanExport->exportFormerWithSiret($fileTmp, $start, $end);
            } else if ($type === "former_without_siret") {
                $this->csvBilanExport->exportFormerWithoutSiret($fileTmp, $start, $end);
            } else if ($type === "commissionnement") {
                $this->csvBilanExport->exportCommissionnementGpm($fileTmp, $start, $end);
            } else if ($type === "lead_inscrits") {
                    $this->csvBilanExport->exportLeadInscrits($fileTmp, $start, $end);
            } else if ($type === "finance_sous_mode") {
                $this->csvBilanExport->exportFinanceSousModes($fileTmp, $start, $end, false);
            } else if ($type === "logo_partenaires") {
                $this->csvBilanExport->exportLogoPartenaires($fileTmp, $start, $end, false);
            }

            rename($fileTmp, $file);

        } catch (\Exception $e) {

            $file = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.csv', $baseName);
            if($file && file_exists($file)) {
                unlink($file);
            }

            $fileLog = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.log', $baseName);


            file_put_contents($fileLog, $e->getMessage());
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }
}
