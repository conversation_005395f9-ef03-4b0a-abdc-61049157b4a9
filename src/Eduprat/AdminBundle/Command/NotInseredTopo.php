<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\AdminBundle\Services\FormationNotInseredTopo;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:not_insered_topo', description: "Envoi d'un email aux coordinateurs lorsque topo pas inséré à data de réunion +4j +10j puis tout les 10j")]
class NotInseredTopo extends Command
{
    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    public function __construct(FormationNotInseredTopo $formationNotInseredTopo)
    {
        parent::__construct();
        $this->formationNotInseredTopo = $formationNotInseredTopo;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {

            $this->formationNotInseredTopo->sendEmail();

        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }
}
