<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:comparaisonSIB:check', description: "Vérifie les données de SendInBlue (Brevo) avec la base")]
class ComparaisonSIBBaseCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addArgument('path', null, 'le fichier exporté depuis Brevo/SIB')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $repoParticipant = $this->entityManager->getRepository(Participant::class);
        $repoPerson = $this->entityManager->getRepository(Person::class);
        $path = $input->getArgument('path');

        $file = fopen($path,"r");
        $count = 0;
        while (($line = fgetcsv($file, 1000, ";")) !== FALSE) {
            $participant = $repoParticipant->findOneBy(['email'=> $line[1], 'lastname'=> $line[2], 'firstname'=> $line[3]]);
            $person = $participant ? null : $repoPerson->findOneBy(['email'=> $line[1], 'lastname'=> $line[2], 'firstname'=> $line[3]]);
            if (!$participant && !$person) {
                $count += 1;
                if ($output->isVerbose()) {
                    $output->writeln(sprintf("<comment> Participant / coordinateur non trouvé : %s - %s - %s</comment>", $line[1], $line[2], $line[3]));
                }
            } else  {
                if ($output->isVeryVerbose()) {
                    $output->writeln(sprintf("<comment> PS / coordinateur ok : %s - %s - %s</comment>", $line[1], $line[2], $line[3]));
                }
            }
        }
        $output->writeln("<info>".$count." participant(s) ou coordinateur(s) n'ont pas la bonne correspondace email / nom / prenom</info>");
        return Command::SUCCESS;
    }
}
