<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\AdminBundle\Services\CsvBilanExport;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand('eduprat:csv_export_bis')]
class GenerateCSVExportJsonCommand extends Command
{
    /**
     * @var string
     */
    private $projectDir;

    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    public function __construct(string $projectDir, CsvBilanExport $csvBilanExport)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->csvBilanExport = $csvBilanExport;
    }

    protected function configure(): void
    {
        $this
            ->setDescription("Génère le fichier des sessions en json")
            ->addArgument('start', InputArgument::REQUIRED, 'start')
            ->addArgument('end', InputArgument::REQUIRED, 'end')
            ->addArgument('jsonReferenceDate', InputArgument::REQUIRED, 'jsonReferenceDate')
            ->addOption('supervisor', null, InputOption::VALUE_OPTIONAL, 'Id du superviseur')
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $folder = $input->getOption('supervisor') ? ("sup-" . $input->getOption('supervisor') . "/") : "";

        try {
            // coordinator / supervisor
            $start = new \DateTime($input->getArgument('start'));
            $end = new \DateTime($input->getArgument('end'));

            $baseName = sprintf('eduprat_export_%s_%s_%s', "sessionsFullJson", $start->format('Y-m-d'), $end->format('Y-m-d'));

            $fileTmp = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.tmp', $baseName);
            $file = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.json', $baseName);
            $fileLog = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.log', $baseName);

            if($file && file_exists($file)) {
                unlink($file);
            }

            touch($file);

            file_put_contents($file, $this->csvBilanExport->exportSessionsFullJson($start, $end, $input->getArgument('jsonReferenceDate')));

        } catch (\Exception $e) {

            $file = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.json', $baseName);
            if($file && file_exists($file)) {
                unlink($file);
            }

            $fileLog = $this->projectDir . "/uploads/bilan/".$folder.sprintf('%s.log', $baseName);


            file_put_contents($fileLog, $e->getMessage());
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }
}
