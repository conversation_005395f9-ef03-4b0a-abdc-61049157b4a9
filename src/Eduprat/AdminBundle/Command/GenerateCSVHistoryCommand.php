<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\CsvBilanExport;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand('eduprat:csv_participant_history')]
class GenerateCSVHistoryCommand extends Command
{
    private $router;

    private $personRepo;

    /**
     * @var string
     */
    private $projectDir;

    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(string $projectDir, CsvBilanExport $csvBilanExport, RouterInterface $router, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->csvBilanExport = $csvBilanExport;
        $this->router = $router;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->setDescription("Génère le fichier CSV d'exports des participants")
            ->addArgument('user', InputArgument::REQUIRED, 'User id')
            ->addArgument('params', InputArgument::REQUIRED, 'JSON parameters')
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->personRepo = $this->entityManager->getRepository(Person::class);
        $folder = "";
        $user = $this->personRepo->find($input->getArgument('user'));

        $fileTmp = self::getGeneratedFileTmpPath($user);
        $file = self::getGeneratedFilePath($user);
        $fileLog = self::getGeneratedFileErrorPath($user);

        try {
            $parameters = json_decode($input->getArgument('params'), true);
            if($file && file_exists($file)) {
                unlink($file);
            }

            touch($file);

            $this->csvBilanExport->exportParticipantHistory($fileTmp, $user, $parameters);

            rename($fileTmp, $file);

            var_dump($file);

        } catch (\Exception $e) {
            if($file && file_exists($file)) {
                unlink($file);
            }
            file_put_contents($fileLog, $e->getMessage());
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }

    /**
     * @param $user
     * @return bool
     */
    static public function hasGeneratedFile($user) {
        return self::getGeneratedFilePath($user) && file_exists(self::getGeneratedFilePath($user));
    }

    /**
     * @param $user
     * @return bool
     */
    static public function hasGeneratedFileError($user) {
        return self::getGeneratedFileErrorPath($user) && file_exists(self::getGeneratedFileErrorPath($user));
    }

    /**
     * @param $user
     * @return bool
     */
    static public function generateFileIsFinished($user) {
        if (self::hasGeneratedFile($user)) {
            return filesize(self::getGeneratedFilePath($user)) > 0;
        }
        return false;
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileBasePath() {
        return __DIR__ . sprintf("/../../../../uploads/participation/history/");
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFilePath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_history%s.csv", $user->getId());
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileTmpPath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_history%s.tmp", $user->getId());
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileErrorPath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_history%s.log", $user->getId());
    }

}
