<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\AdminBundle\Services\CsvBilanExport;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:csv', description: "Génère le fichier CSV de suivi de l'année envoyée en paramètre")]
class GenerateCSVCommand extends Command
{
    private $router;

    /**
     * @var string
     */
    private $projectDir;
    /**
     * @var CsvBilanExport
     */
    private $csvBilanExport;

    public function __construct(string $projectDir, CsvBilanExport $csvBilanExport)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
        $this->csvBilanExport = $csvBilanExport;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('year', InputArgument::REQUIRED, 'Année à générer')
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $year = $input->getArgument('year');

            $fileTmp = $this->projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s-tmp.csv', $year);
            $file = $this->projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.csv', $year);
            $fileLog = $this->projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.log', $year);

            if($file && file_exists($file)) {
                unlink($file);
            }

            touch($file);

            $data = $this->csvBilanExport->export($year);

            $output = fopen($fileTmp, 'w+');
            foreach ($data as $row) {
                fputcsv($output, $row, ";");
            }

            rename($fileTmp, $file);

        } catch (\Exception $e) {
            $file = $this->projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.csv', $year);
            if($file && file_exists($file)) {
                unlink($file);
            }

            $fileLog = $this->projectDir . "/uploads/bilan/".sprintf('eduprat_export_%s.log', $year);

            file_put_contents($fileLog, $e->getMessage());
        }
        return Command::SUCCESS;
    }
}
