<?php

namespace Eduprat\AdminBundle\Command;

use Alienor\EmailBundle\Services\EmailAddressBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\MailerHistory;
use Eduprat\DomainBundle\Repository\MailerHistoryRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

#[AsCommand(name: 'alienor:email:alert-errors', description: 'Commande d\'alerte de présence d\'emails en erreurs')]
class AlienorEmailAlertErrorsCommand extends Command
{
    protected MailerHistoryRepository $mailerHistoryRepo;

    protected MailerInterface $mailer;

    protected ParameterBagInterface $parameterBag;
    private EmailAddressBuilder $emailAddressBuilder;

    public function __construct(EntityManagerInterface $objectManager, MailerInterface $mailer, ParameterBagInterface $parameterBag, EmailAddressBuilder $emailAddressBuilder)
    {
        parent::__construct();

        $this->mailerHistoryRepo = $objectManager->getRepository(MailerHistory::class);
        $this->mailer = $mailer;
        $this->parameterBag = $parameterBag;
        $this->emailAddressBuilder = $emailAddressBuilder;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $to = explode(',', $this->parameterBag->get('email_error_alert'));

        $emailsErrors = $this->mailerHistoryRepo->findErrorsForAlert();

        $emailsErrorsCount = count($emailsErrors);
        $subject = sprintf('[EDUPRAT] Rapport erreur : %d email%s en erreur', $emailsErrorsCount, $emailsErrorsCount > 1 ? 's' : '');
        if ($emailsErrorsCount > 0) {
            $listing = implode('', array_map(function(MailerHistory $emailError) {
                $participant = $emailError->getParticipation() ? $emailError->getParticipation()->getParticipant() : null;
                return sprintf(
                    "Alias : %s, Participation : %s, Person : %d, Session : %s <br>",
                    $emailError->getAlias(),
                    $emailError->getParticipation() ? $emailError->getParticipation()->getId() .' '. $participant->getFirstname() . ' '. $participant->getLastname() : '',
                    $emailError->getDestinataire() ? $emailError->getDestinataire()->getId() : '',
                    $emailError->getSession() ? 'id: '.$emailError->getSession()->getId() . ' title: '. $emailError->getSession()->getLowercaseTitle() . ' numéro:'. $emailError->getSession()->getSessionNumber(): '',
                );
            }, $emailsErrors));
            try {
                $this->mailer->send(
                    (new Email())
                        ->to(...$this->emailAddressBuilder->buildAddress($to))
                        ->subject($subject)
                        ->from('<EMAIL>')
                        ->html(
                            "<p>Bonjour,</p>
                    <p>
                    Nombre de mail en erreur : $emailsErrorsCount
                    </p>
                    <p>
                    Liste :<br>
                    $listing
                    </p>
                    ")
                );
            } catch (TransportExceptionInterface $e) {
            }
        }

        if ($output->isVerbose()) {
            if ($emailsErrorsCount > 0) {
                $io->error($subject);
            } else {
                $io->success($subject);
            }
        }
        return 0;
    }
}
