<?php

namespace Eduprat\AdminBundle\Entity;
use Symfony\Component\HttpFoundation\Request;


/**
 * Entité pour moteur de recherche
 */
class AuditSearch
{

    /**
     * @var string
     */
    private $category;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $label;

    /**
     * @var string
     */
    private $year;

    /**
     * @var string
     */
    private $vignetteType;

    /**
     * @var bool
     */
    private $archived = false;

    /**
     * @return string
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param string $category
     * @return AuditSearch
     */
    public function setCategory($category)
    {
        $this->category = $category;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return AuditSearch
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @param string $label
     * @return AuditSearch
     */
    public function setLabel($label)
    {
        $this->label = $label;
        return $this;
    }

    /**
     * @return string
     */
    public function getVignetteType()
    {
        return $this->vignetteType;
    }

    /**
     * @param string $vignetteType
     * @return AuditSearch
     */
    public function setVignetteType($vignetteType)
    {
        $this->vignetteType = $vignetteType;
        return $this;
    }

     /**
     * @return string
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param string $year
     * @return AuditSearch
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * @return bool
     */
    public function getArchived()
    {
        return json_decode($this->archived);
    }

    /**
     * @param bool $archived
     */
    public function setArchived($archived)
    {
        $this->archived = $archived;
    }

    public function toArray() {
        return array(
            'category' => $this->getCategory(),
            'type' => $this->getType(),
            'label' => $this->getLabel(),
            'archived' => $this->getArchived() ? "true" : "false",
            'year' => $this->getYear(),
            'vignetteType' => $this->getVignetteType()
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                $this->$name = $request->query->get($name);
            }
        }
    }

    public function transform() {
        if ($this->getCategory()) {
            $this->setCategory($this->getCategory()->getId());
        }
    }

}