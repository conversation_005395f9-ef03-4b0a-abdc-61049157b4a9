<?php

namespace Eduprat\AdminBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Objectif\ObjectifsSalarie;

#[ORM\Entity]
#[ORM\Table(name: 'coordinator_person')]
class CoordinatorPerson extends Person
{
    #[ORM\OneToMany(targetEntity: ObjectifsSalarie::class, mappedBy: 'person', cascade: ['persist'])]
    private Collection $objectifs;

//    #[ORM\Column(name: 'crStatus', type: Types::STRING, nullable: true)]
//    private ?string $crStatus = null;
//
//    #[ORM\Column(name: 'crStatusDate', type: Types::DATE_MUTABLE, nullable: true)]
//    private ?\DateTimeInterface $crStatusDate = null;
//
//    #[ORM\Column(name: 'crIdentifier', type: Types::STRING, nullable: true)]
//    private ?string $crIdentifier = null;

    public function __construct()
    {
        parent::__construct();
        $this->objectifs = new ArrayCollection();
    }

//    public function setCrStatus(string $crStatus): self
//    {
//        $this->crStatus = $crStatus;
//
//        return $this;
//    }
//
//    public function getCrStatus(): string
//    {
//        return $this->crStatus;
//    }
//
//    public function crIsSalarie(): bool
//    {
//        return $this->crStatus == self::CR_STATUS_SALARIE;
//    }
//
//    public function setCrStatusDate(\DateTimeInterface $crStatusDate): self
//    {
//        $this->crStatusDate = $crStatusDate;
//
//        return $this;
//    }
//
//    public function getCrStatusDate(): \DateTimeInterface
//    {
//        return $this->crStatusDate;
//    }
//
//    public function setCrIdentifier(string $crIdentifier): self
//    {
//        $this->crIdentifier = $crIdentifier;
//
//        return $this;
//    }
//
//    public function getCrIdentifier(): string
//    {
//        return $this->crIdentifier;
//    }
    public function getObjetifCAOf(int $year): ?float
    {
        return $this->loadObjectifOf($year)?->getObjectifCA();
    }

    public function getTauxCumulMargeOf(int $year): ?float
    {
        return $this->loadObjectifOf($year)?->getTauxCumulMarge();
    }

    public function loadObjectifOf(int $year): ?ObjectifsSalarie
    {
        foreach ($this->objectifs as $objectif) {
            if ($objectif->getYear() === $year) {
                return $objectif;
            }
        }
        return null;
    }

    public function getTauxActualise($marges): int
    {
        return Coordinator::getTauxActualise($marges) * 100;
    }
}