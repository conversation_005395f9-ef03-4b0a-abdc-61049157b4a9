<?php

namespace Eduprat\AdminBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Alienor\ElasticBundle\Model\ElasticableInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\ObjectWithTokenInterface;
use Eduprat\DomainBundle\Entity\Participant;
use Scheb\TwoFactorBundle\Model\Email\TwoFactorInterface;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

use Doctrine\Common\Collections\Criteria;
use Eduprat\DomainBundle\Entity\SelectionFormation;

/**
 * Person
 */
#[ORM\Entity(repositoryClass: 'Eduprat\AdminBundle\Repository\PersonRepository')]
#[ORM\InheritanceType('JOINED')]
#[ORM\DiscriminatorColumn(name: 'typePerson', type: 'string')]
#[ORM\DiscriminatorMap(['person' => Person::class, 'coordinator' => CoordinatorPerson::class])]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'person')]
#[Vich\Uploadable]
class Person implements UserInterface, EquatableInterface, ElasticableInterface, PasswordAuthenticatedUserInterface, TwoFactorInterface, ObjectWithTokenInterface
{
    const ROLE_PARTICIPANT = 'ROLE_PARTICIPANT';
    const ROLE_COORDINATOR = 'ROLE_COORDINATOR';
    const ROLE_COORDINATOR_LBI = 'ROLE_COORDINATOR_LBI';
    const ROLE_FORMER = 'ROLE_FORMER';
    const ROLE_FORMER_PHARMACIE = 'ROLE_FORMER_PHARMACIE';
    const ROLE_SUPERVISOR = 'ROLE_SUPERVISOR';
    const ROLE_SUPERVISOR_FRANCE = 'ROLE_SUPERVISOR_FRANCE';
    const ROLE_WEBMASTER = 'ROLE_WEBMASTER';
    const ROLE_WEBMASTER_COMPTA = 'ROLE_WEBMASTER_COMPTA';
    const ROLE_ADVISOR = 'ROLE_ADVISOR';
    const ROLE_MAPPING = array(
        'participant' => self::ROLE_PARTICIPANT,
        'coordinateur' => self::ROLE_COORDINATOR,
        'coordinateur_lbi' => self::ROLE_COORDINATOR_LBI,
        'formateur' => self::ROLE_FORMER,
        'formateur_pharmacie' => self::ROLE_FORMER_PHARMACIE,
        'advisor' => self::ROLE_ADVISOR
    );

    const CR_STATUS_INDEPENDANT = "Indépendant";
    const CR_STATUS_SALARIE = "Salarié";

    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'created_at', type: Types::DATETIME_MUTABLE)]
    protected ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updated_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $updatedAt = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'firstname', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $firstname = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'lastname', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $lastname = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'email', type: Types::STRING, length: 255, nullable: false)]
    #[Assert\Email]
    protected string $email;

    /**
     * @var string
     */
    #[ORM\Column(name: 'salt', type: Types::STRING, length: 128)]
    protected ?string $salt = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'password', type: Types::STRING, length: 255)]
    protected ?string $password = null;

    /**
     * @var Collection<int, PasswordHistory>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\AdminBundle\Entity\PasswordHistory', mappedBy: 'person', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $passwordHistory;

    /**
     * @var string
     */
    #[ORM\Column(name: 'token', type: Types::STRING, length: 32, nullable: true)]
    protected ?string $token = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'token_expire_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $tokenExpireAt = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'is_enabled', type: Types::BOOLEAN)]
    protected ?bool $isEnabled = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'expired_at', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $expiredAt = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'is_locked', type: Types::BOOLEAN)]
    protected ?bool $isLocked = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'roles', type: Types::JSON)]
    protected array $roles = [];

    /**
     * @var Participant
     */
    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', mappedBy: 'user')]
    private ?Participant $participant = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'job', type: Types::STRING, nullable: true)]
    private ?string $job = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address', type: Types::STRING, nullable: true)]
    private ?string $address = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zipCode', type: Types::STRING, nullable: true)]
    private ?string $zipCode = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'departments', type: Types::SIMPLE_ARRAY, nullable: true)]
    private $departments = [];

    /**
     * @var string
     */
    #[ORM\Column(name: 'city', type: Types::STRING, nullable: true)]
    private ?string $city = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'phone', type: Types::STRING, nullable: true)]
    private ?string $phone = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'companyName', type: Types::STRING, nullable: true)]
    private ?string $companyName = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'siret', type: Types::STRING, nullable: true)]
    private ?string $siret = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'coordinatorsPerson', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'supervisor', nullable: true)]
    private ?\Eduprat\AdminBundle\Entity\Person $supervisor = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'coordinatorsPersonWebmaster', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'webmaster', nullable: true)]
    private ?\Eduprat\AdminBundle\Entity\Person $webmaster = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: true)]
    private  ?\Eduprat\AdminBundle\Entity\Person $coordinatorBinome = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'edupratFormer', type: Types::BOOLEAN)]
    private ?bool $edupratFormer = false;

     /**
     * @var boolean
     */
    #[ORM\Column(name: 'unpaidFormer', type: Types::BOOLEAN, nullable: true)]
    protected ?bool $unpaidFormer = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'cv', type: Types::STRING, length: 255, nullable: true)]
    private ?string $cv = null;

    /**
     * @var File
     */
    #[Vich\UploadableField(mapping: 'person_cv', fileNameProperty: "cv")]
    private $cvFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'dli', type: Types::STRING, length: 255, nullable: true)]
    private ?string $dli = null;

    #[Vich\UploadableField(mapping: 'person_dli', fileNameProperty: 'dli')]
    private $dliFile;

    // /**
    //  * @var Collection|Coordinator[]
    //  * @ORM\OneToMany(targetEntity="Eduprat\DomainBundle\Entity\Coordinator", mappedBy="person", cascade={"persist"})
    //  */
    // private $coordinatorProgramme;
    /**
     * @var Collection<int, Formateur>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Formateur', mappedBy: 'person', cascade: ['persist'])]
    private Collection $formers;

    /**
     * @var Collection<int, Coordinator>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', mappedBy: 'person', cascade: ['persist'])]
    private Collection $coordinators;

    /**
     * @var Collection<int, \Eduprat\AdminBundle\Entity\Person>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\AdminBundle\Entity\Person', mappedBy: 'supervisor', cascade: ['persist'])]
    #[ORM\OrderBy(['lastname' => 'ASC'])]
    private Collection $coordinatorsPerson;

    /**
     * @var Collection<int, \Eduprat\AdminBundle\Entity\Person>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\AdminBundle\Entity\Person', mappedBy: 'webmaster', cascade: ['persist'])]
    #[ORM\OrderBy(['lastname' => 'ASC'])]
    private Collection $coordinatorsPersonWebmaster;

    /**
     * @var Collection<int, Participant>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', mappedBy: 'coordinator', cascade: ['persist'])]
    #[ORM\OrderBy(['lastname' => 'ASC'])]
    private Collection $participants;

    /**
     * @var Collection<int, Participant>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', cascade: ['persist'], mappedBy: 'leadReferent')]
    private Collection $participantsLead;

    /**
     * @var Collection<int, Participant>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', cascade: ['persist'], mappedBy: 'advisor')]
    private Collection $participantsLeadAdvisor;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'hasCreatedPassword', type: Types::BOOLEAN)]
    private ?bool $hasCreatedPassword = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'civility', type: Types::STRING, length: 255, nullable: true)]
    private ?string $civility = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpps', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpps = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'adeli', type: Types::STRING, length: 255, nullable: true)]
    private ?string $adeli = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'cms', type: Types::BOOLEAN)]
    private ?bool $cms = false;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'manualEmailReminder', type: Types::BOOLEAN)]
    private ?bool $manualEmailReminder = false;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'allowShareAdress', type: Types::BOOLEAN)]
    private ?bool $allowShareAdress = false;

    /**
     * @var Collection<int, EvaluationGlobalAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer', mappedBy: 'person', cascade: ['persist'])]
    private Collection $evaluationGlobalAnswers;

    /**
     * @var string
     */
    #[ORM\Column(name: 'avatar', type: Types::STRING, length: 255, nullable: true)]
    private ?string $avatar = null;

    /**
     * @var File
     */
    #[Assert\Image(maxWidth: 400, maxHeight: 400)]
    #[Vich\UploadableField(mapping: 'person_avatar', fileNameProperty: 'avatar')]
    private $avatarFile;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'hasDownloadedApp', type: Types::BOOLEAN)]
    private ?bool $hasDownloadedApp = false;

    /**
     * @var array
     */
    #[ORM\Column(name: 'ugas', type: Types::SIMPLE_ARRAY, nullable: true)]
    private $ugas;

    /**
     * @var string
     */
    #[ORM\Column(name: 'crStatus', type: Types::STRING, nullable: true)]
    private ?string $crStatus = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'crStatusDate', type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $crStatusDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'crIdentifier', type: Types::STRING, nullable: true)]
    private ?string $crIdentifier = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'isArchived', type: Types::BOOLEAN)]
    private ?bool $isArchived = null;

    /**
     * @var Collection<int, SelectionFormation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\SelectionFormation', mappedBy: 'person', cascade: ['persist'])]
    private Collection $selectionFormations;

    protected $plainPassword;

    /**
     * @var Collection<int, DownloadedPlaquetteFile>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile', mappedBy: 'person', cascade: ['persist'])]
    private Collection $downloadedPlaquetteFiles;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    private ?string $authCode;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTime $emailAuthCodeCreatedAt = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'ipList', type: Types::JSON)]
    protected $ipList;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'toUpdateSib', type: Types::BOOLEAN, nullable: true)]
    protected ?bool $toUpdateSib = null;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    private ?string $codeApporteur;

    public function __construct() {
        $this->passwordHistory = new ArrayCollection();
        $this->formers = new ArrayCollection();
        $this->coordinatorsPerson = new ArrayCollection();
        $this->coordinatorsPersonWebmaster = new ArrayCollection();
        $this->participants = new ArrayCollection();
        $this->participantsLead = new ArrayCollection();
        $this->participantsLeadAdvisor = new ArrayCollection();
        $this->evaluationGlobalAnswers = new ArrayCollection();
        $this->selectionFormations = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->salt = md5(uniqid('', true));
        $this->token = md5(uniqid('', true));
        $this->isEnabled = false;
        $this->isLocked = false;
        $this->roles = [];
        $this->hasCreatedPassword = false;
        $this->hasDownloadedApp = false;
        $this->isArchived = false;
        $this->downloadedPlaquetteFiles = new ArrayCollection();
        $this->coordinators = new ArrayCollection();
        $this->ipList = [];
    }

    public function __toString()
    {
        return $this->email;
    }

    public function getFullname()
    {
        return $this->firstname . " " . $this->lastname;
    }

    public function getInvertedFullname()
    {
        return $this->lastname . " " . $this->firstname;
    }

    public function getInvertedFullnameTitleCase()
    {
        return strtoupper($this->lastname) . " " . ucwords(strtolower($this->firstname), " -.\t\r\n\f\v");
    }

    public function getInitiales()
    {
        return substr($this->firstname, 0, 1) . ". " . substr($this->lastname, 0, 1) . ".";
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set email
     *
     * @param string $email
     * @return Person
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Get username
     *
     * @return string
     */
    public function getUsername()
    {
        return $this->email;
    }

    /**
     * Set password
     *
     * @param string $password
     * @return Person
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Get password
     *
     * @return string
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setSalt($salt) {
        $this->salt = $salt;
    }

    public function getSalt(): ?string
    {
        return $this->salt;
    }

    public function setToken($token) {
        $this->token = $token;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function eraseCredentials(): void
    {
    }

    /**
     * @param array $roles
     */
    public function setRoles($roles)
    {
        $this->roles = $roles;
        return $this;
    }

    /**
     * @return array
     */
    public function getRoles(): array
    {
        return $this->roles;
    }

    /**
     * @return array
     */
    public function getRole()
    {
        return count($this->getRoles()) ? $this->getRoles()[0] : null;
    }

    /**
     * @return array
     */
    public function getRoleIdentifier()
    {
        if ($this->getRole()) {
            return strtolower(str_replace('ROLE_', '', $this->getRole()));
        }
        return null;
    }

    public function hasRole($role)
    {
        return in_array($role, $this->roles);
    }

    public function isEqualTo(UserInterface $user): bool
    {

        if ($this->password !== $user->getPassword()) {
            return false;
        }

        if ($this->email !== $user->getUsername()) {
            return false;
        }

        return true;
    }

    public function isAccountNonExpired() {
        return is_null($this->expiredAt) || ($this->expiredAt > new \DateTime());
    }

    public function setAccountNonLocked($isLocked) {
        $this->isLocked = $isLocked;

        return $this;
    }

    public function isAccountNonLocked() {
        return (!$this->isLocked);
    }

    public function isCredentialsNonExpired() {
        return true;
    }

    public function setIsEnabled($value) {
        $this->isEnabled = $value;
        return $this;
    }

    public function isEnabled() {
        return $this->isEnabled;
    }

    /**
     * @return mixed
     */
    public function getPlainPassword()
    {
        return $this->plainPassword;
    }

    /**
     * @param mixed $plainPassword
     * @return Person
     */
    public function setPlainPassword($plainPassword)
    {
        $this->plainPassword = $plainPassword;

        return $this;
    }

    /**
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param string $firstname
     * @return Person
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param string $lastname
     * @return Person
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }



    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     * @return Person
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Person
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Get isEnabled
     *
     * @return boolean
     */
    public function getIsEnabled()
    {
        return $this->isEnabled;
    }

    /**
     * Set expiredAt
     *
     * @param \DateTime $expiredAt
     * @return Person
     */
    public function setExpiredAt($expiredAt)
    {
        $this->expiredAt = $expiredAt;

        return $this;
    }

    /**
     * Get expiredAt
     *
     * @return \DateTime
     */
    public function getExpiredAt()
    {
        return $this->expiredAt;
    }

    /**
     * Set isLocked
     *
     * @param boolean $isLocked
     * @return Person
     */
    public function setIsLocked($isLocked)
    {
        $this->isLocked = $isLocked;

        return $this;
    }

    /**
     * Get isLocked
     *
     * @return boolean
     */
    public function getIsLocked()
    {
        return $this->isLocked;
    }

    /**
     * Set participant
     *
     * @param Participant $participant
     * @return Person
     */
    public function setParticipant(Participant $participant = null)
    {
        $this->participant = $participant;

        return $this;
    }

    /**
     * Get participant
     *
     * @return Participant
     */
    public function getParticipant()
    {
        return $this->participant;
    }

    /**
     * Get participant
     *
     * @return bool
     */
    public function hasParticipant()
    {
        return $this->participant !== null;
    }

    /**
     * @return string
     */
    public function getJob()
    {
        return $this->job;
    }

    /**
     * @param string $job
     * @return Person
     */
    public function setJob($job)
    {
        $this->job = $job;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param string $address
     * @return Person
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * @param string $address2
     * @return Person
     */
    public function setAddress2($address2): Person
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * @return string
     */
    public function getZipCode()
    {
        return $this->zipCode;
    }

    /**
     * @param string $zipCode
     * @return Person
     */
    public function setZipCode($zipCode)
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    /**
     * @return array
     */
    public function getDepartments()
    {
        return $this->departments;
    }

    /**
     * @param array $departments
     * @return Person
     */
    public function setDepartments($departments)
    {
        $this->departments = $departments;
        return $this;
    }

    public function getMergedBinomeDepartments()
    {
        return array_unique(array_merge($this->getDepartments(), $this->getCoordinatorBinome()->getDepartments()));
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     * @return Person
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return Person
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * @param string $companyName
     * @return Person
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * @return string
     */
    public function getSiret()
    {
        return $this->siret;
    }

    /**
     * @param string $siret
     * @return Person
     */
    public function setSiret($siret)
    {
        $this->siret = $siret;

        return $this;
    }

    /**
     * @return boolean
     */
    public function isEdupratFormer()
    {
        return $this->edupratFormer;
    }

    /**
     * @param boolean $edupratFormer
     * @return Person
     */
    public function setEdupratFormer($edupratFormer)
    {
        $this->edupratFormer = $edupratFormer;

        return $this;
    }

    /**
     * @return boolean
     */
    public function isUnpaidFormer()
    {
        return $this->unpaidFormer;
    }

    /**
     * @param boolean $unpaidFormer
     * @return Person
     */
    public function setUnpaidFormer($unpaidFormer)
    {
        $this->unpaidFormer = $unpaidFormer;

        return $this;
    }

    /**
     * @return Collection|Formation[]
     */
    public function getFormers()
    {
        return $this->formers;
    }

    /**
     * @param Collection<int, Formateur> $formers
     * @return Person
     */
    public function setFormers($formers)
    {
        $this->formers = $formers;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getTokenExpireAt()
    {
        return $this->tokenExpireAt;
    }

    /**
     * @param \DateTime $tokenExpireAt
     * @return Person
     */
    public function setTokenExpireAt($tokenExpireAt)
    {
        $this->tokenExpireAt = $tokenExpireAt;

        return $this;
    }

    /**
     * @return \Eduprat\AdminBundle\Entity\Person
     */
    public function getSupervisor()
    {
        return $this->supervisor;
    }

    /**
     * @param \Eduprat\AdminBundle\Entity\Person $supervisor
     * @return Person
     */
    public function setSupervisor($supervisor)
    {
        $this->supervisor = $supervisor;

        return $this;
    }

    /**
     * @return \Eduprat\AdminBundle\Entity\Person
     */
    public function getWebmaster()
    {
        return $this->webmaster;
    }

    /**
     * @param \Eduprat\AdminBundle\Entity\Person $webmaster
     * @return Person
     */
    public function setWebmaster($webmaster)
    {
        $this->webmaster = $webmaster;

        return $this;
    }

    public function getCoordinatorBinome(): ?self
    {
        return $this->coordinatorBinome;
    }

    public function setCoordinatorBinome(?self $crBinome): static
    {
        // Désasociation du binôme actuel
        if ($this->coordinatorBinome !== null) {
            $this->coordinatorBinome->associateCoordinatorBinome(null);
            $this->associateCoordinatorBinome(null);
        } 
        
        $this->associateCoordinatorBinome($crBinome);
        if ($crBinome !== null) {
            $crBinome->associateCoordinatorBinome($this);
        }
        return $this;
    }

    public function associateCoordinatorBinome(?self $crBinome): static
    {
       $this->coordinatorBinome = $crBinome;

        return $this;
    }

    /**
     * Get edupratFormer
     *
     * @return boolean
     */
    public function getEdupratFormer()
    {
        return $this->edupratFormer;
    }

    /**
     * @return bool
     */
    public function isFormer()
    {
        return $this->hasRole(self::ROLE_FORMER) || $this->isPharmacieFormer();
    }

    /**
     * @return bool
     */
    public function isPharmacieFormer()
    {
        return $this->hasRole(self::ROLE_FORMER_PHARMACIE);
    }

    /**
     * @return bool
     */
    public function isAdvisor()
    {
        return $this->hasRole(self::ROLE_ADVISOR);
    }

    /**
     * @return bool
     */
    public function isCoordinator()
    {
        return $this->hasRole(self::ROLE_COORDINATOR) || $this->isCoordinatorLbi();
    }

    /**
     * @return bool
     */
    public function isCoordinatorLbi()
    {
        return $this->hasRole(self::ROLE_COORDINATOR_LBI);
    }

    /**
     * @return bool
     */
    public function isSupervisor()
    {
        return $this->hasRole(self::ROLE_SUPERVISOR);
    }

    /**
     * @return bool
     */
    public function isSupervisorFr()
    {
        return $this->hasRole(self::ROLE_SUPERVISOR_FRANCE);
    }

    /**
     * @return bool
     */
    public function isWebmaster()
    {
        return $this->hasRole(self::ROLE_WEBMASTER) || $this->hasRole(self::ROLE_WEBMASTER_COMPTA);
    }

    /**
     * @return bool
     */
    public function isWebmasterCompta()
    {
        return $this->hasRole(self::ROLE_WEBMASTER_COMPTA);
    }

    /**
     * Add coordinators
     *
     * @param Coordinator $coordinator
     *
     * @return Person
     */
    public function addCoordinator(Coordinator $coordinator)
    {
        $this->coordinators[] = $coordinator;

        return $this;
    }

    /**
     * Add coordinators
     *
     * @param Formation $coordinators
     *
     * @return Person
     */
    public function addCoordinators(Formation $coordinators)
    {
        $this->coordinators[] = $coordinators;

        return $this;
    }

    /**
     * Remove coordinators
     *
     * @param Formation $coordinators
     */
    public function removeCoordinators(Formation $coordinators)
    {
        $this->coordinators->removeElement($coordinators);
    }

    /**
     * Get coordinators
     *
     * @return Collection
     */
    public function getCoordinators()
    {
        return $this->coordinators;
    }

    /**
     * Add former
     *
     * @param Formateur $former
     *
     * @return Person
     */
    public function addFormer(Formateur $former)
    {
        $this->formers[] = $former;

        return $this;
    }

    /**
     * Remove former
     *
     * @param Formateur $former
     */
    public function removeFormer(Formateur $former)
    {
        $this->formers->removeElement($former);
    }

    /**
     * Set cv
     *
     * @param string $cv
     *
     * @return Person
     */
    public function setCv($cv)
    {
        $this->cv = $cv;

        return $this;
    }

    /**
     * Get cv
     *
     * @return string
     */
    public function getCv()
    {
        return $this->cv;
    }

    /**
     * @return File
     */
    public function getCvFile()
    {
        return $this->cvFile;
    }

    /**
     * @param File $cvFile
     * @return Person
     */
    public function setCvFile($cvFile)
    {
        $this->cvFile = $cvFile;
        if ($this->cvFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set dli
     *
     * @param string $dli
     *
     * @return Person
     */
    public function setDli($dli)
    {
        $this->dli = $dli;

        return $this;
    }

    /**
     * Get dli
     *
     * @return string
     */
    public function getDli()
    {
        return $this->dli;
    }

    /**
     * @return File
     */
    public function getDliFile()
    {
        return $this->dliFile;
    }

    /**
     * @param File $dliFile
     * @return Person
     */
    public function setDliFile($dliFile)
    {
        $this->dliFile = $dliFile;
        if ($this->dliFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getAvatar()
    {
        return $this->avatar;
    }

    /**
     * @param string $avatar
     * @return Person
     */
    public function setAvatar($avatar)
    {
        $this->avatar = $avatar;
        return $this;
    }

    /**
     * @return File
     */
    public function getAvatarFile()
    {
        return $this->avatarFile;
    }

    /**
     * @param File $avatarFile
     * @return Person
     */
    public function setAvatarFile($avatarFile)
    {
        $this->avatarFile = $avatarFile;
        if ($this->avatarFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * @return Collection|\Eduprat\AdminBundle\Entity\Person[]
     */
    public function getCoordinatorsPerson()
    {
        return $this->coordinatorsPerson;
    }

    /**
     * @param Collection<int, \Eduprat\AdminBundle\Entity\Person> $coordinatorsPerson
     * @return Person
     */
    public function setCoordinatorsPerson($coordinatorsPerson)
    {
        if (is_array($coordinatorsPerson)) {
            $coordinatorsPerson = new ArrayCollection($coordinatorsPerson);
        }
        $this->coordinatorsPerson = $coordinatorsPerson;

        return $this;
    }

    /**
     * @return Collection|Participant[]
     */
    public function getParticipantsLead()
    {
        return $this->participantsLead;
    }

    /**
     * @param Collection|Participant[] $participants
     * @return Person
     * @param Collection<int, Participant> $participantsLead
     */
    public function setParticipantsLead($participantsLead)
    {
        $this->participantsLead = $participantsLead;

        return $this;
    }

    /**
     * @return Collection|Participant[]
     */
    public function getParticipants()
    {
        return $this->participants;
    }

    /**
     * @param Collection<int, Participant> $participants
     * @return Person
     */
    public function setParticipants($participants)
    {
        $this->participants = $participants;

        return $this;
    }

    /**
     * @return boolean
     */
    public function isHasCreatedPassword()
    {
        return $this->hasCreatedPassword;
    }

    /**
     * @param boolean $hasCreatedPassword
     * @return Person
     */
    public function setHasCreatedPassword($hasCreatedPassword)
    {
        $this->hasCreatedPassword = $hasCreatedPassword;

        return $this;
    }

    /**
     * @return bool
     */
    public function isHasDownloadedApp()
    {
        return $this->hasDownloadedApp;
    }

    /**
     * @param bool $hasDownloadedApp
     * @return Person
     */
    public function setHasDownloadedApp($hasDownloadedApp)
    {
        $this->hasDownloadedApp = $hasDownloadedApp;
        return $this;
    }

    /**
     * Set uga
     *
     * @param array $uga
     * @return Person
     */
    public function setUgas($ugas)
    {
        $this->ugas = $ugas;

        return $this;
    }

    /**
     * Get uga
     *
     * @return array
     */
    public function getUgas()
    {
        return $this->ugas;
    }

    /**
     * Set crStatus
     *
     * @param string $crStatus
     * @return Person
     */
    public function setCrStatus($crStatus)
    {
        $this->crStatus = $crStatus;

        return $this;
    }

    /**
     * Get crStatus
     *
     * @return string
     */
    public function getCrStatus()
    {
        return $this->crStatus;
    }

    /**
     * Get crStatus
     *
     * @return string
     */
    public function crIsSalarie()
    {
        return $this->crStatus == $this::CR_STATUS_SALARIE;
    }

    /**
     * Set crStatusDate
     *
     * @param string $crStatusDate
     * @return Person
     */
    public function setCrStatusDate($crStatusDate)
    {
        $this->crStatusDate = $crStatusDate;

        return $this;
    }

    /**
     * Get crStatusDate
     *
     * @return \DateTime
     */
    public function getCrStatusDate()
    {
        return $this->crStatusDate;
    }

    /**
     * Set crIdentifier
     *
     * @param string $crIdentifier
     * @return Person
     */
    public function setCrIdentifier($crIdentifier)
    {
        $this->crIdentifier = $crIdentifier;

        return $this;
    }

    /**
     * Get crIdentifier
     *
     * @return string
     */
    public function getCrIdentifier()
    {
        return $this->crIdentifier;
    }

    public function getSupervisorUgas()
    {
        return array_unique(array_merge(...array_values($this->getCoordinatorsPerson()->map(function(Person $p) {
            return $p->getUgas();
        })->toArray())));
    }

    /**
     * @return string
     */
    public function getCivility()
    {
        return $this->civility;
    }

    /**
     * @param string $civility
     * @return Person
     */
    public function setCivility($civility)
    {
        $this->civility = $civility;

        return $this;
    }

    /**
     * @return boolean
     */
    public function isCms()
    {
        return $this->cms;
    }

    /**
     * @param boolean $cms
     */
    public function setCms($cms)
    {
        $this->cms = $cms;
    }

    /**
     * Set rpps
     *
     * @param string $rpps
     * @return Person
     */
    public function setRpps($rpps)
    {
        $this->rpps = $rpps;

        return $this;
    }

    /**
     * Get rpps
     *
     * @return string
     */
    public function getRpps()
    {
        return $this->rpps;
    }

    /**
     * Set adeli
     *
     * @param string $adeli
     * @return Person
     */
    public function setAdeli($adeli)
    {
        $this->adeli = $adeli;

        return $this;
    }

    /**
     * Get adeli
     *
     * @return string
     */
    public function getAdeli()
    {
        return $this->adeli;
    }

    /**
     * @return bool
     */
    public function isArchived()
    {
        return $this->isArchived;
    }

    /**
     * @param bool $isArchived
     * @return Person
     */
    public function setIsArchived($isArchived)
    {
        $this->isArchived = $isArchived;
        return $this;
    }

    /**
     * Retourne les formations effectuées par le coordinateur dans l'année passée en paramêtre
     * @param null $year
     * @return Collection
     */
    public function getFormationDones(string $year = null, $closed = false, $pluriannuelleOnly = false)
    {
        $programme = $this->getCoordinators()->map(function (Coordinator $c){
                return $c->getFormation();
            })->filter(function ($f) use ($year, $closed) {
                return !is_null($f);
            })->filter(function (Formation $f) use ($year, $closed, $pluriannuelleOnly) {
                $valid = $closed === false || $f->getClosed();
                $pluriannuelleValid = $pluriannuelleOnly ? $f->isPluriAnnuelle() : true;
                return $valid && $pluriannuelleValid && ($year === null || ($f->getOpeningDate()->format('Y') === $year) && !$f->isArchived());
            });

        $criteria = Criteria::create()
            ->orderBy(array("openingDate" => Order::Ascending));

        return $programme->matching($criteria);
    }

    /**
     * Retourne les formations effectuées par le coordinateur dans l'année passée en paramêtre
     * @param string $year
     * @return Collection
     */
    public function getCoordinatorsFormationDones(string $year = null, $closed = false, $pluriannuelleOnly = false)
    {
        return $this->getCoordinators()->filter(function (Coordinator $c) use ($year, $closed, $pluriannuelleOnly) {
            $valid = $closed === false || ($c->getFormation() && $c->getFormation()->getClosed());
            $pluriannuelleValid = $pluriannuelleOnly ? $c->getFormation() && $c->getFormation()->isPluriAnnuelle() : true;
            return $c->getFormation() && $pluriannuelleValid && ($valid && ($year === null || ($c->getFormation()->getOpeningDate()->format('Y') === $year))) && !$c->getFormation()->isArchived();
        });
    }

    public function countFormationDones($year = null) {
        return count($this->getCoordinatorsFormationDones($year));
    }

    /**
     * Somme des honoraires orateur de toutes les formations d'une même année
     */
    public function getCoordinatorsFormationsTotalFormerHonorary($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getFormerHonorary();
        });
    }

    /**
     * Somme des honoraires orateur de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsTotalFormerHonorary($year = null)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsPerson(), function(Person $c) use ($year) {
            return $c->getCoordinatorsFormationsTotalFormerHonorary($year);
        });
    }

    /**
     * Somme des honoraires restaurant de toutes les formations d'une même année
     */
    public function getCoordinatorsFormationsTotalRestaurationHonorary($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getRestaurationHonorary();
        });
    }

    /**
     * Somme des honoraires restaurant de toutes les formations d'une même année
     */
    public function getCoordinatorsFormationsTotalRestaurationHonoraryByCoordinator($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getRestaurationHonoraryByCoordinator($this);
        });
    }

    /**
     * Somme des honoraires restaurant de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsTotalRestaurationHonorary($year = null)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsPerson(), function(Person $c) use ($year) {
            return $c->getCoordinatorsFormationsTotalRestaurationHonorary($year);
        });
    }

    /**
     * Somme des honoraires restaurant de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsTotalRestaurationHonoraryByCoordinator($year = null)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsPerson(), function(Person $c) use ($year) {
            return $c->getCoordinatorsFormationsTotalRestaurationHonoraryByCoordinator($year);
        });
    }

    /**
     * Somme des honoraires coordinateur de toutes les formations d'une même année
     */
    public function getCoordinatorsTotalHonorary($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getCoordinatorHonorary();
        });
    }

    /**
     * @todo delete
     * Somme des honoraires coordinateur de toutes les formations d'une même année
     */
    public function getCoordinatorsTotalHonoraryByCoordinator($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getCoordinatorHonoraryByCoordinator($this);
        });
    }

    /**
     * Somme des cout de formation de toutes les formations d'une même année au prorata du nb de participant
     */
    public function getCoordinatorsTotalCoutFormation($year = null)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsFormationDones($year, true), function(Coordinator $c) {
            return $c->getFormationCost();
        });
    }

    /**
     * Somme des commissions comptabilisées
     */
    public function getAllCoordinatorsCommissionsComptabilisees($year = null): ?float
    {
        return $this->getCoordinatorsCommissionsComptabilisees($year) + $this->getCoordinatorsCommissionsComptabilisees(strval($year-1), true);
    }

    /**
     * Somme des commissions comptabilisées
     */
    public function getCoordinatorsCommissionsComptabilisees(string $year = null, $n1 = false): ?float
    {
        return $this->arrayCollectionSum($this->getCoordinatorsFormationDones($year, false, $n1), function(Coordinator $c) use($n1) {
            return $c->getCalculatedHonorary($n1);
        });
    }

    /**
     * Somme des avances de formation de toutes les formations d'une même année
     */
    public function getCoordinatorsTotalAvancesFormation($year = null)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsFormationDones($year, true), function(Coordinator $c) {
            return $c->getAvancesCost();
        });
    }

    /**
     * Somme des CA secteur de toutes les formations d'une même année
     */
    public function getCoordinatorCaSecteur($year = null)
    {
        return $this->arrayCollectionSum($this->getFormationDones($year), function(Formation $f) {
            return $f->getCaTotal();
        });
    }


    /**
     * Somme des CA secteur de toutes les formations d'une même année
     * + somme des CA n+1 pour les formations pluriannuelles de l'année n-1
     */
    public function getAllCoordinatorCaSecteurByCoordinator(string $year): ?float
    {
        return $this->getCoordinatorCaSecteurByCoordinator($year) + $this->getCoordinatorCaSecteurByCoordinator(strval($year-1), true);
    }


    /**
     * Somme des CA secteur de toutes les formations d'une même année
     */
    public function getCoordinatorCaSecteurByCoordinator(string $year = null, $n1 = false): ?float
    {
        return $this->arrayCollectionSum($this->getFormationDones($year, false, $n1), function(Formation $f) use($n1) {
            if($f->getCoordinators()->count() > 1) {
                return $f->getCaTotalByCoordinator($this, $n1);
            }
            return $f->getCaTotal($n1);
        });
    }

    /**
     * Somme des CA secteur de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsTotalCaSecteur($year)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsPerson(), function(Person $c) use ($year) {
            return $c->getCoordinatorCaSecteur($year);
        });
    }

    /**
     * Somme des CA secteur de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsTotalCaSecteurByCoordinateur($year)
    {
        return $this->arrayCollectionSum($this->getCoordinatorsPerson(), function(Person $c) use ($year) {
            return $c->getCoordinatorCaSecteurByCoordinator($year);
        });
    }

    /**
     * @todo a verifier
     * Somme du nombre de participants de toutes les formations d'une même année
     */
    public function getCoordinatorsFormationsParticipations($year = null)
    {
        return array_reduce(
            $this->getFormationDones($year)->map(function (Formation $f){
                return $f->getParticipations($this);
            })->toArray(),
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    public function getCoordinatorByFormation($formation) {
        foreach($this->getCoordinators() as $coordinator) {
            if($coordinator->getFormation()) {
                if($coordinator->getFormation()->getId() == $formation->getId()) {
                    return $coordinator;
                }
            }
        }
    }

    /**
     * Somme du nombre de participants de toutes les formations d'une même année
     */
    public function getCoordinatorsFormationsParticipationsByCoordinator($year = null)
    {
        return array_reduce(
            $this->getFormationDones($year)->map(function (Formation $f){
                if($f->getCoordinators()->count() > 1) {
                    return $f->getParticipationsByCoordinator($this)->toArray();
                }
                else {
                    return $f->getParticipations()->toArray();
                }
            })->toArray(),
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    /**
     * Somme du nombre de participants de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsParticipations($year = null)
    {
        return array_reduce(
            $this->getCoordinatorsPerson()->map(function (Person $c) use ($year) {
                return $c->getCoordinatorsFormationsParticipations($year);
            })->toArray(),
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    /**
     * Somme du nombre de participants de toutes les formations d'une même année pour chaque coordinateur lié au superviseur
     */
    public function getCoordinatorsParticipationsByCoordinator($year = null)
    {
        return array_reduce(
            $this->getCoordinatorsPerson()->map(function (Person $c) use ($year) {
                return $c->getCoordinatorsFormationsParticipationsByCoordinator($year);
            })->toArray(),
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    /**
     * Ce calcul se fera uniquement sur les formations clôturées et sera remis à zéro tous les ans.
     * Nous calculerons la commission exceptionnelle du coordinateur avec la règle suivante :
     * Cumul des marges = Somme des marges (CA – Coût de formation) du coordinateur
     * Commissions taux de base = Cumul des marges * 0.34
     * Commission actualisée = Cumul des marges * taux actualisé
     * Commission exceptionnelle = Commission actualisé - Commissions taux de base
     */
    public function getCommissionExceptionnelle(string $year, $detail = false)
    {
        if ($this->isCoordinatorLbi()) {
            return array(
                "marges" => 0,
                "avances" => 0,
                "commissionBase" => 0,
                "commissionActualisee" => 0,
                "commissionExceptionnelle" => 0,
                "commissionsComptabilisees" => 0,
            );
        }
        $marges = $this->getAllMargesParticipationsByCoordinator($year);

        $commissionBase = $this->getCommissionBase($marges);
        $tauxActualise = Coordinator::getTauxActualise($marges);
        $commissionActualisee = $this->getCommissionActualisee($marges, $tauxActualise);
        $commissionExceptionnelle = $this->calculateCommissionExceptionnelle($commissionActualisee, $commissionBase);
        if ($detail) {
            $formations = $this->getCoordinatorsFormationDones($year);
            $avances = $this->arrayCollectionSum($formations, function (Coordinator $c) {
                return $c->getAvancesCost();
            });
            $commissionsComptabilisees = $this->arrayCollectionSum($formations, function (Coordinator $c) {
                return $c->getCalculatedHonorary();
            });
            return array(
                "marges" => $marges,
                "avances" => $avances,
                "commissionBase" => $commissionBase,
                "tauxActualise" => $tauxActualise,
                "commissionActualisee" => $commissionActualisee,
                "commissionExceptionnelle" => $commissionExceptionnelle,
                "commissionsComptabilisees" => $commissionsComptabilisees
            );
        }
        return $commissionExceptionnelle;
    }

    public function getCommissionByYear() {
        $years = array();
        /** @var Coordinator $coordinator */
        foreach ($this->getCoordinatorsFormationDones() as $coordinator) {
            $year = $coordinator->getFormation()->getStartDate()->format('Y');
            if (!isset($years[$year])) {
                $years[$year] = array(
                    "year" => $year,
                    "formations" => array(),
                );
            }
            $years[$year]["formations"][] = $coordinator;
        }

        $years = array_map(function($y) {
            return array_merge($y, $this->getCommissionExceptionnelle($y["year"], true));
        }, $years);

        return array_values($years);
    }

    private function arrayCollectionSum(Collection $collection, $getter) {
        return array_reduce(
            $collection->map($getter)->toArray(),
            function($a, $b) { $a += $b; return $a; }
        );
    }

    /**
     * Retourne les Formations effectuées par le coordinateur dans la période passée en paramêtre
     * @param null $year
     * @return Collection
     */
    public function getCoordinatorsFormationDonesByPeriod($start = null, $end = null, $closed = false)
    {
        $coordinators = $this->getCoordinatorsFormation()->filter(function (Coordinator $c) use ($start, $end, $closed) {
            $valid = $closed === false || $c->getFormation()->getClosed();
            return $valid && $c->getFormation() && ($c->getFormation()->getStartDate() > $start && $c->getFormation()->getEndDate() < $end);
        });

        return $coordinators;
    }

    /**
     * @todo delete
     * Récupere tous les Formations d'un coordinateur
     */
    public function getCoordinatorsFormation()
    {
        return $this->getCoordinators();
    }

    /**
     * Retourne les Formations effectué par le coordinateur dans la période passée en paramêtre
     * @param null $year
     * @return Collection
     */
    public function getFormersFormationDonesByPeriod($start = null, $end = null, $closed = false)
    {
        $formers = $this->getFormersFormation()->filter(function (Formateur $f) use ($start, $end, $closed) {
            $valid = $closed === false || $f->getFormation()->getClosed();
            return $valid && $f->getFormation() && ($f->getFormation()->getStartDate() > $start && $f->getFormation()->getEndDate() < $end);
        });

        return $formers;
    }


    /**
     * @todo delete
     * Récupere tous les Formations d'un coordinateur
     */
    public function getFormersFormation()
    {
        return $this->getFormers();
    }

    /**
     * @todo delete
     * Récupere tous les Formations d'un formateur
     */
    public function getFormersFormations()
    {
        return array_reduce(
            $this->getFormers()->map(function (Formateur $f){
                return $f->getFormation();
            })->toArray(),
            function($carry, $item) {
                $carry[] = $item;
                return $carry;
            },
            array()
        );
    }

    /**
     * Récupere tous les Formations d'un coordinateur
     */
    public function getCoordinatorsFormations()
    {
        return array_reduce(
            $this->getCoordinators()->map(function (Coordinator $c){
                return $c->getFormation();
            })->toArray(),
            function($carry, $item) {
                $carry[] = $item;
                return $carry;
            },
            array()
        );
    }

    /**
     * Add evaluationGlobalAnswer
     *
     * @param EvaluationGlobalAnswer $evaluationGlobalAnswer
     *
     * @return Person
     */
    public function addEvaluationGlobalAnswer(EvaluationGlobalAnswer $evaluationGlobalAnswer)
    {
        $this->evaluationGlobalAnswers[] = $evaluationGlobalAnswer;

        return $this;
    }

    /**
     * Remove evaluationGlobalAnswer
     *
     * @param EvaluationGlobalAnswer $evaluationGlobalAnswer
     */
    public function removeEvaluationGlobalAnswer(EvaluationGlobalAnswer $evaluationGlobalAnswer)
    {
        $this->evaluationGlobalAnswers->removeElement($evaluationGlobalAnswer);
    }

    /**
     * Get evaluationGlobalAnswers
     *
     * @return Collection
     */
    public function getEvaluationGlobalAnswers()
    {
        return $this->evaluationGlobalAnswers;
    }

    public function getElasticIndex(): string
    {
        return '';
    }

    /**
     * @return bool
     */
    public function isManualEmailReminder()
    {
        return $this->manualEmailReminder;
    }

    /**
     * @param bool $manualEmailReminder
     * @return Person
     */
    public function setManualEmailReminder($manualEmailReminder)
    {
        $this->manualEmailReminder = $manualEmailReminder;
        return $this;
    }

     /**
     * @return bool
     */
    public function isAllowShareAdress()
    {
        return $this->allowShareAdress;
    }

    /**
     * @param bool $allowShareAdress
     * @return Person
     */
    public function setAllowShareAdress($allowShareAdress)
    {
        $this->allowShareAdress = $allowShareAdress;
        return $this;
    }



    public function getFullAdress()
    {
        return sprintf("%s %s %s %s", $this->address, $this->address2, $this->getZipCode(), $this->getCity());
    }


    public function getStatus() {
        return empty($this->siret) ? "Salarié" : "Libéral";
    }

    public function isSalarie() {
        return empty($this->siret);
    }

    public function isLiberal() {
        return $this->siret !== null;
    }

    public function toArraySib() {
        $person = array();

        $person['PRENOM'] = $this->getFirstname();
        $person['NOM'] = $this->getLastname();
        $person['CODEPOSTAL'] = $this->getZipCode();
        $person['VILLE'] = $this->getCity();
        return $person;
    }

    /**
     * Le prix par défaut d'un formateur salarié est de 570€ et celui d'un libéral est de 400€
     * @return int
     */
    public function getDefaultFormateurCost() {
        if($this->isUnpaidFormer()) {
            return 0;
        }
        return $this->isSalarie() ? 570 : 400;
    }

    /**
     * Get selectionFormations
     *
     * @return Collection
     */
    public function getSelectionFormations()
    {
        return $this->selectionFormations;
    }


    public function getCurrentSelectionFormation()
    {
        return $this->getSelectionFormations()->filter(function(SelectionFormation $s) {
                return !$s->isFavori();
        })->first();
    }

    public function getFavoriSelectionFormation($favori)
    {
        return $this->getSelectionFormations()->filter(function(SelectionFormation $s) use($favori) {
                return $s->getId() == $favori;
        })->first();
    }

    public function getTemporaryFavori()
    {
        return $this->getSelectionFormations()->filter(function(SelectionFormation $s) {
            return $s->isTemporary() && $s->getNomFavori();
        })->first();
    }

    /**
     * @return ArrayCollection
     */
    public function getSelectionFormationsFavori(): ArrayCollection
    {
        return $this->getSelectionFormations()->filter(function(SelectionFormation $s) {
            return $s->isFavori() && !$s->isTemporary();
        });
    }

    /**
     * @return ArrayCollection|Collection|DownloadedPlaquetteFile[]
     */
    public function getDownloadedPlaquetteFiles()
    {
        return $this->downloadedPlaquetteFiles;
    }

    /**
     * @param Collection<int, DownloadedPlaquetteFile> $downloadedPlaquetteFiles
     * @return Person
     */
    public function setDownloadedPlaquetteFiles($downloadedPlaquetteFiles)
    {
        $this->downloadedPlaquetteFiles = $downloadedPlaquetteFiles;
        return $this;
    }

    /**
     * Somme des CA secteur de toutes les formations d'une même année
     * + somme des CA n+1 pour les formations pluriannuelles de l'année n-1
     */
    public function getAllMargesParticipationsByCoordinator(string $year) :float
    {
        return $this->getMargeParticipationsByCoordinator($year) + $this->getMargeParticipationsByCoordinator(strval($year-1), true);
    }

    /**
     * @param $year
     * @return mixed
     */
    public function getMargeParticipationsByCoordinator(string $year, $n1 = false): ?float
    {
        return $this->arrayCollectionSum($this->getCoordinatorsFormationDones($year), function (Coordinator $c) use ($n1) {
            if ($c->getFormation()->isCoordinatorLbi()) {
                return 0;
            }
            return $c->getMarges($n1);
        });
    }

    /**
     * @param float $marges
     * @param float $tauxActualise
     * @return float
     */
    public function getCommissionActualisee(float $marges, float $tauxActualise): float
    {
        return $marges * $tauxActualise;
    }

    /**
     * @param float $commissionActualisee
     * @param $commissionBase
     * @return float
     */
    public function calculateCommissionExceptionnelle(float $commissionActualisee, $commissionBase): float
    {
        return $commissionActualisee - $commissionBase;
    }

    /**
     * @param float $marges
     * @return float
     */
    public function getCommissionBase(float $marges): float
    {
        return $marges * Coordinator::MARGE_RATE;
    }

    public function getUserIdentifier(): string
    {
        return $this->getUsername();
    }

    public function __serialize(): array
    {
        return array(
            $this->id,
            $this->email,
            $this->password,
        );
    }

    public function __unserialize(array $data): void
    {
        list (
            $this->id,
            $this->email,
            $this->password,
            ) = $data;
    }

    public function isEmailAuthEnabled(): bool
    {
        return true;
    }

    public function getEmailAuthRecipient(): string
    {
        return $this->email === 's.martine' ? '<EMAIL>' : $this->email;
    }

    public function getEmailAuthCode(): string
    {
        if (null === $this->authCode) {
            throw new \LogicException('The email authentication code was not set');
        }

        return $this->authCode;
    }

    public function setEmailAuthCode(string $authCode): void
    {
        $this->authCode = $authCode;
    }

    public function getEmailAuthCodeCreatedAt(): ?\DateTime
    {
        return $this->emailAuthCodeCreatedAt;
    }

    public function setEmailAuthCodeCreatedAt(\DateTime $expiresAt): void
    {
        $this->emailAuthCodeCreatedAt = $expiresAt;
    }

    /**
     * @param array $ipList
     */
    public function setIpList($ipList)
    {
        $this->ipList = $ipList;
        return $this;
    }

    /**
     * @return array
     */
    public function getIpList()
    {
        return $this->ipList;
    }

    public function addIpList($ip) {
        $this->ipList[] = $ip;

        return $this;
    }

    public function alreadyInIpList($ip) {
        if ($this->getIpList()) {
            return in_array($ip, $this->getIpList());
        }
        return false;
    }

     /**
     * @return bool
     */
    public function isToUpdateSib()
    {
        return $this->toUpdateSib;
    }

    /**
     * @param bool $toUpdateSib
     * @return Person
     */
    public function setToUpdateSib($toUpdateSib)
    {
        $this->toUpdateSib = $toUpdateSib;
        return $this;
    }

    /**
     * Set codeApporteur
     *
     * @param string $codeApporteur
     * @return Person
     */
    public function setCodeApporteur($codeApporteur)
    {
        $this->codeApporteur = $codeApporteur;

        return $this;
    }

    /**
     * Get codeApporteur
     *
     * @return string
     */
    public function getCodeApporteur()
    {
        return $this->codeApporteur;
    }

    public function resumeCivility() {
        switch ($this->civility) {
            case 'Monsieur':
                return 'M.';
            case 'Madame': 
                return 'MME';
            default:
                return "Dr";
        }
    }
    
}
