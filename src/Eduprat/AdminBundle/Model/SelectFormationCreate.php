<?php

namespace Eduprat\AdminBundle\Model;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Eduprat\AdminBundle\Services\ProgrammeBuilder;

class SelectFormationCreate
{
    private string $formationType;
    private ?string $presence;

    public function getFormationType(): string
    {
        return $this->formationType;
    }

    public function setFormationType(string $formationType): SelectFormationCreate
    {
        $this->formationType = $formationType;
        return $this;
    }

    public function getPresence(): ?string
    {
        return $this->presence;
    }

    public function setPresence(?string $presence): SelectFormationCreate
    {
        $this->presence = $presence;
        return $this;
    }

    #[Assert\Callback]
    public function validate(ExecutionContextInterface $context, $payload)
    {
        if ($this->formationType === ProgrammeBuilder::OTHER_FORMATION_TYPE) {
            return;
        }

        if (!array_key_exists($this->getPresence(), ProgrammeBuilder::PROGRAMME_TYPES[$this->getFormationType()])) {
            $context->buildViolation('Cette présence ne peut être associée à ce type de formation.')
                ->atPath('presence')
                ->addViolation();
        }
    }
}
