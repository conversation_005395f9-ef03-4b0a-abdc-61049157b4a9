<?php

namespace Eduprat\AdminBundle\Security;

use MeteoConcept\HCaptchaBundle\Form\DataTransformer\HCaptchaValueFetcher;
use MeteoConcept\HCaptchaBundle\Service\HCaptchaVerifier;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

readonly class CaptchaDisplayer
{
    private const ERROR_COUNT_SESSION_KEY = 'login_error_count';

    public function __construct(
        private HCaptchaValueFetcher $hCaptchaValueFetcher,
        private HCaptchaVerifier $verifier,
        #[Autowire(env: 'HCAPTCHA_SITE_KEY')]
        private string $hCaptchaSiteKey,
        private RequestStack $requestStack,
    )
    {
    }

    public function getErrorFormCountSession(): int
    {
        return $this->requestStack->getSession()->get(self::ERROR_COUNT_SESSION_KEY) ?? 0;
    }

    public function addError(Request $request): void
    {
        $session = $request->getSession();
        $countError = $session->get(self::ERROR_COUNT_SESSION_KEY);
        // Incrémenter le compteur d'erreurs
        $session->set(self::ERROR_COUNT_SESSION_KEY, $countError + 1, 0);
    }

    /**
     * @throws \Exception
     */
    public function checkCaptchaIfRequired(Request $request): void
    {
        $session = $request->getSession();
        // Vérifier le captcha seulement si le compteur d'erreurs >= 3
        if ($session->get(self::ERROR_COUNT_SESSION_KEY) >= 3) {
            $this->hCaptchaValueFetcher->setSiteKey($this->hCaptchaSiteKey);
            $captcha = $request->request->get('h-captcha-response') ? $this->hCaptchaValueFetcher->reverseTransform('notuse') : null;

            if (is_null($captcha)) {
                throw new CustomUserMessageAuthenticationException('Captcha invalide');
            }

            try {
                if (!$this->verifier->verify($captcha)) {
                    throw new CustomUserMessageAuthenticationException('Captcha invalide');
                }
            } catch (CustomUserMessageAuthenticationException) {
                throw new CustomUserMessageAuthenticationException('Captcha invalide');
            }
        }
    }

    public function resetErrorCount(Request $request): void
    {
        $session = $request->getSession();
        $session->set(self::ERROR_COUNT_SESSION_KEY, 0);
    }
}