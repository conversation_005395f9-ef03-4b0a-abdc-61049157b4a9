<?php

namespace Eduprat\AdminBundle\Security;

use Doctrine\ORM\EntityManagerInterface;

use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Listener\AuthenticationSuccessHandler;
use MeteoConcept\HCaptchaBundle\Form\DataTransformer\HCaptchaValueFetcher;
use MeteoConcept\HCaptchaBundle\Service\HCaptchaVerifier;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Util\TargetPathTrait;

class LoginFormAuthenticator extends AbstractAuthenticator
{
    use TargetPathTrait;

    private EntityRepository $userRepository;

    public function __construct(
        private EntityManagerInterface $entityManager,
        private RouterInterface $router,
        private CsrfTokenManagerInterface $csrfTokenManager,
        private AuthenticationSuccessHandler $successHandler,
        private CaptchaDisplayer $captchaDisplayer,
    )
    {
        $this->userRepository = $entityManager->getRepository(Person::class);
    }

    public function supports(Request $request): bool
    {
        return 'alienor_user_login_check' === $request->attributes->get('_route') && $request->isMethod('POST');
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $this->captchaDisplayer->resetErrorCount($request);

        $targetPath = $this->getTargetPath($request->getSession(), $firewallName);
        if (!$targetPath) {
            $targetPath = $this->router->generate('admin_index');
        }

        return $this->successHandler->onAuthenticationSuccess($request, $token, $targetPath);
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        $this->captchaDisplayer->addError($request);
        return $this->successHandler->onAuthenticationFailure($request, $exception, $this->getLoginUrl());
    }

    protected function getLoginUrl(): string
    {
        return $this->router->generate('alienor_user_login');
    }

    public function authenticate(Request $request): Passport
    {
        $email = $request->request->get('_username');
        $password = $request->request->get('_password');

        $this->captchaDisplayer->checkCaptchaIfRequired($request);

        return new Passport(
            new UserBadge($email, function($userIdentifier) {
                // optionally pass a callback to load the User manually
                try {
                    $user = $this->userRepository->findOneBy(['email' => $userIdentifier]);
                    if (!$user) {
                        throw new UserNotFoundException();
                    }
                    return $user;
                } catch (\Throwable) {
                    throw new UserNotFoundException();
                }
            }),
            new PasswordCredentials($password),
            [
                new CsrfTokenBadge(
                    'authenticate',
                    $request->request->get('_csrf_token')
                ),
                (new RememberMeBadge())->disable(),
            ]
        );
    }
}