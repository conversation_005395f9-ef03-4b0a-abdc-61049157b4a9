<?php

namespace Eduprat\AdminBundle\Listener;

use Eduprat\AdminBundle\Security\CaptchaDisplayer;
use Ed<PERSON>rat\AdminBundle\Security\LoginFormAuthenticator;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\PasswordHistory;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\AccountSwitcher;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\TooManyLoginAttemptsAuthenticationException;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationFailureHandlerInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Component\Security\Http\SecurityRequestAttributes;
use Symfony\Contracts\Translation\TranslatorInterface;

class AuthenticationSuccessHandler implements AuthenticationSuccessHandlerInterface, AuthenticationFailureHandlerInterface
{

    /**
     * @var AccountSwitcher
     */
    private $accountSwitcher;

    /**
     * @var RouterInterface
     */
    private $router;

    /**
     * @var CsrfTokenManagerInterface
     */
    private $csrfTokenManager;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /** @var string */
    private $passwordExpirationInterval;

    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    private LoggerInterface $securityLogger;
    private CaptchaDisplayer $captchaDisplayer;

    /**
     * AuthenticationSuccessHandler constructor.
     * @param AccountSwitcher $accountSwitcher
     * @param RouterInterface $router
     * @param CsrfTokenManagerInterface $csrfTokenManager
     * @param TranslatorInterface $translator
     * @param EntityManagerInterface $entityManager
     * @param TokenStorageInterface $tokenStorage
     * @param LoggerInterface $securityLogger
     * @param $passwordExpirationInterval
     * @param CaptchaDisplayer $captchaDisplayer
     */
    public function __construct(AccountSwitcher $accountSwitcher, RouterInterface $router, CsrfTokenManagerInterface $csrfTokenManager, TranslatorInterface $translator, EntityManagerInterface $entityManager, TokenStorageInterface $tokenStorage, LoggerInterface $securityLogger, $passwordExpirationInterval, CaptchaDisplayer $captchaDisplayer)
    {
        $this->accountSwitcher = $accountSwitcher;
        $this->router = $router;
        $this->csrfTokenManager = $csrfTokenManager;
        $this->translator = $translator;
        $this->entityManager = $entityManager;
        $this->passwordExpirationInterval = $passwordExpirationInterval;
        $this->tokenStorage = $tokenStorage;
        $this->securityLogger = $securityLogger;
        $this->captchaDisplayer = $captchaDisplayer;
    }

    public function onSecurityInteractiveLogin(InteractiveLoginEvent $event): void
    {
        $token = $event->getAuthenticationToken();
        $request = $event->getRequest();
        $this->onAuthenticationSuccess($request, $token);
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, $targetPath = null): ?Response
    {

        /** @var Person $person */
        $person = $token->getUser();

        $this->accountSwitcher->prepareAccountSwitching($person);

        if (!$targetPath || strpos($request->headers->get('referer'), '/gestion/login') === false) {
            $targetPath = $this->router->generate('eduprat_audit_index');
        }

        $lastUserPassword = $this->entityManager->getRepository(PasswordHistory::class)
            ->findOneBy(array("person" => $person), array("createdAt" => "DESC"), 1);

        // Changement de mdp requis si expiré pour les utilisateurs extranet hors formateur
        $requirePasswordChange =
            (
                $person->getRole()
                && !$person->isFormer()
                && !$person->hasRole('ROLE_SUPER_ADMIN')
            )
            && (
                (
                    $lastUserPassword
                    && $lastUserPassword->getCreatedAt()->add(new \DateInterval($this->passwordExpirationInterval)) < new \Datetime()
                )
                || (is_null($lastUserPassword))
            );


        // Si le mdp de l'utilisateur est expiré, on le rédirige vers la page de modification du mot de passe
        if ($requirePasswordChange) {
            $session = $request->getSession();
            $session->getFlashBag()->add("danger", "Votre mot de passe a expiré, vous devez en saisir un nouveau (autre que les dix derniers utilisés)");
            $person->setTokenExpireAt((new \DateTime())->modify('+ 7 day'));
            $this->entityManager->persist($person);
            $this->entityManager->flush();
            $targetPath = $this->router->generate("eduprat_password_reset", array(
                "token" => $person->getToken()
            ));
            $this->tokenStorage->setToken(null);
        }

        if (!$targetPath || strpos($request->headers->get('referer'), 'crm') !== false) {
            $targetPath = $this->router->generate('crm_index');
        }

        $this->securityLogger->info("Connexion réussie : " . $request->request->get('_username'));

        if ($request->isXmlHttpRequest()) {
            $result = array(
                'success'  => true,
                'token'    => $this->csrfTokenManager->getToken('authenticate')->getValue(),
                'redirect' => $targetPath,
                'error_count_login_try' => $this->captchaDisplayer->getErrorFormCountSession(),
            );
            return new JsonResponse($result);
        }

        return new RedirectResponse($targetPath);
    }

    /**
     * onAuthenticationFailure
     *
     * @param Request $request
     * @param AuthenticationException $exception
     * @param $targetPath
     * @return JsonResponse|RedirectResponse
     * <AUTHOR> Sexton <<EMAIL>>
     */
    public function onAuthenticationFailure(Request $request, AuthenticationException $exception, $targetPath = null): RedirectResponse|JsonResponse
    {
        $this->securityLogger->info("Connexion échouée : " . $request->request->get('_username'));

        if ($request->isXmlHttpRequest()) {
            $tooMany = $exception instanceof TooManyLoginAttemptsAuthenticationException;
            $message =  $tooMany ? "security.tooManyLoginAttemps" : "Identifiants invalides";
            $array = array(
                'success' => false,
                'message' => $this->translator->trans($message),
                'token'   => $this->csrfTokenManager->getToken('authenticate')->getValue(),
                'error_count_login_try' => $this->captchaDisplayer->getErrorFormCountSession(),
            );
            return new JsonResponse($array, $tooMany ? Response::HTTP_TOO_MANY_REQUESTS : Response::HTTP_UNAUTHORIZED);
        }

        if (!$targetPath) {
            $targetPath = $this->router->generate('eduprat_audit_login');
        }

        $request->getSession()->set(
            SecurityRequestAttributes::AUTHENTICATION_ERROR,
            $this->translator->trans("Identifiants invalides")
        );

        return new RedirectResponse($targetPath);
    }

}
