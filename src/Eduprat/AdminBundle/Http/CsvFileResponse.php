<?php

namespace Eduprat\AdminBundle\Http;

use Symfony\Component\HttpFoundation\Response;

class CsvFileResponse extends Response
{
    protected $data;
    protected $filename = 'export.csv';

    public function __construct($data = array(), $filename = 'export.csv', $status = 200, $headers = array()) {
        parent::__construct('', $status, $headers);
        $this->setData($data);
        $this->setFilename($filename);
    }

    public function setData(array $data)
    {
        $output = fopen('php://temp', 'r+');
        foreach ($data as $row) {
            array_walk($row, function(&$value, $key) {
                $value = iconv('UTF-8', 'Windows-1252', $value);
            });
            fputcsv($output, $row, ";");
        }
        rewind($output);
        $this->data = '';
        while ($line = fgets($output)) {
            $this->data .= $line;
        }
        $this->data .= fgets($output);

        return $this->update();
    }

    public function getFilename()
    {
        return $this->filename;
    }

    public function setFilename($filename)
    {
        $this->filename = $filename;

        return $this->update();
    }

    protected function update()
    {
        $this->headers->set('Content-Disposition',sprintf('inline; filename="%s"', $this->filename));
        if (!$this->headers->has('Content-Type')) {
            $this->headers->set('Content-Type', 'text/csv');
        }

        return $this->setContent($this->data);
    }
}