<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Ed<PERSON>rat\CrmBundle\Entity\AnalysisConfig;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AnalysisConfigType extends AbstractBaseType {

    public function buildForm(FormBuilderInterface $builder, array $options): void {

        $builder
            ->add('ca', NumberType::class, array(
                'required' => true,
                'label' => 'crm.analysis_config.ca',
                'input' => 'string',
                'scale' => 2
            ))
            ->add('hours', IntegerType::class, array(
                'required' => true,
                'label' => 'crm.analysis_config.hours',
            ));
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => AnalysisConfig::class,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_analysis_config';
    }

}
