<?php

namespace Eduprat\CrmBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Form\ProgrammeType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Eduprat\AdminBundle\Entity\Person;

class AnalysisType extends AbstractBaseType {

    /**
     * @var array|null
     */
    private $ugas;

    public function __construct($ugas)
    {
        $this->ugas = $ugas;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void {

        $builder
            ->add(in_array($options["type"], array("participants", "commissions")) ? 'coordinatorsId' : 'coordinators', EntityType::class, array(
                'class' => Person::class,
                'label'    => 'admin.participant.coordinator',
                'choice_label' => function(Person $person) use ($options) {
                    return $person->getInvertedFullname();
                },
                'choice_value' => function($person) use ($options) {
                    if (in_array($options["type"], array("participants", "commissions"))) {
                        return $person->getId();
                    }
                    return json_encode(array("name" => $person->getInvertedFullname(), "ugas" => $person->getUgas()));
                },
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'placeholder' => 'admin.global.all',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.roles LIKE :roles')
                        ->andWhere("p.isArchived = false")
                        ->setParameter('roles', '%"ROLE_COORDINATOR"%')
                        ->orderBy('p.lastname', 'ASC');

                    if (!in_array($options["type"], array("participants", "commissions"))) {
                        $qb->andWhere('p.ugas is not null');
                    }

                    return $qb;
                },
            ))
            ->add('ugas', ChoiceType::class, array(
                'label' => 'admin.formation.ugas.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => $this->getUgas(),
                'placeholder' => 'admin.global.select',
                'attr' => ['class' => "select2", 'data-required' => "false"]
            ))
            ->add('categories', ChoiceType::class, array(
                'label' => 'admin.formation.categories.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => ProgrammeType::getCategories(),
                'placeholder' => 'admin.formation.categories.empty',
            ))
            ->add('period', ChoiceType::class, array(
                'label' => 'crm.analysis.period',
                'required' => true,
                'choices'  => array(
                    "Année" => "year",
                    "Semestre" => "semester",
                    "Trimestre" => "trimester",
                    "Mois" => "month",
                    "Semaine" => "week",
                )
            ))
            ->add('exerciseMode', ChoiceType::class, array(
                'label' => 'admin.formation.exerciseMode.title',
                'choices'  => ProgrammeType::getExerciseModes()['Monomode'],
                'placeholder' => 'admin.global.all',
            ))
            ->add('presence', ChoiceType::class, array(
                'label' => 'admin.programme.presence',
                'choices'  => ProgrammeType::getPresences(),
                'placeholder' => 'admin.global.all',
            ))
            ->add('filters', ChoiceType::class, array(
                'label' => 'crm.analysis.filters',
                'required' => true,
                'choices'  => array(
                    "Cumulé" => "cumulated",
                    "Divisé" => "divided",
                )
            ))
            ->add('display', ChoiceType::class, array(
                'label' => 'crm.analysis.display',
                'required' => true,
                'choices'  => $options["type"] !== "participants" ? array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinators",
                    "UGA" => "ugas",
                ): array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinatorsId",
                )
            ))
            ->add('display2', ChoiceType::class, array(
                'label' => 'crm.analysis.display2',
                'required' => false,
                'placeholder' => "Aucun",
                'choices'  => $options["type"] !== "participants" ? array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinators",
                    "UGA" => "ugas",
                ): array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinatorsId",
                )
            ))
            ->add('display3', ChoiceType::class, array(
                'label' => 'crm.analysis.display3',
                'required' => false,
                'placeholder' => "Aucun",
                'choices'  => $options["type"] !== "participants" ? array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinators",
                    "UGA" => "ugas",
                ): array(
                    "Catégorie professionnelle" => "categories",
                    "Coordinateur" => "coordinatorsId",
                )
            ))
            ->add('future', CheckboxType::class, array(
                'label' => 'crm.analysis.include_future'
            ))
            ->add('cumulated', CheckboxType::class, array(
                'label' => 'crm.analysis.cumulated',
                'data' => true
            ))
            ;
    }

    /**
     * @return array
     */
    public function getUgas()
    {
        $ugas = array();

        foreach ($this->ugas as $index => $uga) {
            $ugas[$uga["label"]] = $uga["id"];
        }

        ksort($ugas);

        return $ugas;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => null,
            "type" => "ca"
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_analysis';
    }

}
