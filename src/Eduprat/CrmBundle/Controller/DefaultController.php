<?php

namespace Eduprat\CrmBundle\Controller;

use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Controller\EdupratController;
use Symfony\Component\Routing\Attribute\Route;

class DefaultController extends EdupratController
{
    #[Route(path: '/', name: 'crm_index')]
    public function index()
    {
        /** @var Person $user */
        $user = $this->getUser();
        if ($user->isCoordinator() && !$user->isCoordinatorLbi()) {
            return $this->redirectToRoute('admin_crm_comptabilite_files', array('id' => $user->getId(), 'year' => date("Y")));
        }

        return $this->render('crm/default/index.html.twig', array(
            'crm' => true,
        ));
    }

}
