<?php

namespace Eduprat\CrmBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

abstract class AbstractUserController extends AbstractController {

    public function getTemplate($template) {
        $templatesLocation = $this->getParameter('alienor_user.templatesLocationCrm');
        $location = $templatesLocation . ":" . $template;
        if (strpos($templatesLocation, "Bundle") === false) {
            $location = strtolower(str_replace(':', '/', $location));
        }
        return $location;
    }

}
