<?php

namespace Eduprat\CrmBundle\Controller;

use Ed<PERSON><PERSON>\AdminBundle\Security\CaptchaDisplayer;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

class SecurityController extends AbstractUserController {

    #[Route(path: '/login', name: 'alienor_crm_login')]
    public function login(Request $request, CaptchaDisplayer $captchaDisplayer): Response {
        $session = $request->getSession();

        // Récupère l'erreur d'authentification s'il y en a un
        if ($request->attributes->has(SecurityRequestAttributes::AUTHENTICATION_ERROR)) {
            $error = $request->attributes->get(
                SecurityRequestAttributes::AUTHENTICATION_ERROR
            );
        } else {
            $error = $session->get(SecurityRequestAttributes::AUTHENTICATION_ERROR);
            $session->remove(SecurityRequestAttributes::AUTHENTICATION_ERROR);
        }

        return $this->render($this->getTemplate('Security:login.html.twig'), array(
            // dernier nom entré par l'utilisateur
            'last_username' => $session->get(SecurityRequestAttributes::LAST_USERNAME),
            'error' => $error,
            'error_count_login_try' => $captchaDisplayer->getErrorFormCountSession(),
        ));
    }

}
