<?php

namespace Eduprat\CrmBundle\Model;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Model\AbstractSearch;
use Eduprat\CrmBundle\Form\ComptabiliteSearchType;

/**
 * Entité pour moteur de recherche
 */
class ComptabiliteSearch extends AbstractSearch
{
    const SEARCH_FORM = ComptabiliteSearchType::class;

    public ?Person $webmaster = null;
    public ?Person $coordinator = null;
    public ?Person $supervisor = null;
    public ?\DateTime $start = null;
    public ?\DateTime $end = null;

    public ?\DateTime $startInscription = null;
    public ?\DateTime $endInscription = null;

    public ?\DateTime $querySearchStartDate = null;
    public ?\DateTime $querySearchEndDate = null;

    public ?string $partenariat = null;
    public ?string $parcoursMandatory = null;
    public ?string $type = null;

    public function prepareSearch($parameters = array())
    {
        if (isset($parameters["year"])) {
            $year = $parameters["year"];
            if($year && $year !== 'tous') {
                $this->querySearchStartDate = new \DateTime($year.'-01-01');
                $this->querySearchEndDate = new \DateTime($year.'-12-31');
            }
        }

        if ($parameters["user"]->isCoordinator()) {
            $this->coordinator = $parameters["user"];
        } else if ($parameters["user"]->isSupervisor()) {
            $this->supervisor = $parameters["user"];
        }
    }

    public function prepareFormOptions($options = array(), $parameters = array())
    {
        if ($parameters["user"]->isSupervisor()) {
            $options['supervisorId'] = $parameters["user"]->getId();
        }

        if ($parameters["user"]->isSupervisor() || $parameters["user"]->isSupervisorFr() || $parameters["user"]->isWebmaster() || in_array('ROLE_SUPER_ADMIN', $parameters["user"]->getRoles())) {
            $options['displayCoordinatorFilter'] = true;
        }

        if (in_array('ROLE_WEBMASTER', $parameters["user"]->getRoles()) || in_array('ROLE_SUPER_ADMIN', $parameters["user"]->getRoles())) {
            $options['displayWebmasterFilter'] = true;
        }

        return $options;
    }

    public function setUserFilters(Person $person)
    {
        if ($person->isCoordinator()) {
            $this->coordinator = $person;
        }
    }
}
