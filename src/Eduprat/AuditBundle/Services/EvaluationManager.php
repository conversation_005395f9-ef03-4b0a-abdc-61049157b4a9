<?php

namespace Eduprat\AuditBundle\Services;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Form\EvaluationType;
use Ed<PERSON>rat\DomainBundle\Entity\BaseQuestion;
use Ed<PERSON>rat\DomainBundle\Entity\EvaluationFormationAnswer;
use Eduprat\DomainBundle\Entity\EvaluationFormerAnswer;
use Eduprat\DomainBundle\Entity\EvaluationQuestion;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\SurveyAnswer;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;

abstract class EvaluationManager extends FormManager
{

    const NB_QUESTION = 0;

    /**
     * @var ArrayCollection|EvaluationFormationAnswer[]
     */
    private $answers;

    /**
     * @var FormationPresentielle|FormationAudit
     */
    protected $formation;

    /**
     * @var string
     */
    protected $type = "";

    /**
     * AuditManager constructor.
     * @param EntityManager $entityManager
     * @param FormFactory   $formFactory
     * @param Participation $participation
     * @throws \Exception
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Participation $participation)
    {
        parent::__construct($entityManager, $formFactory, $participation);
        $this->answers = $this->getAnswers();
    }

    /**
     * @return ArrayCollection
     */
    public function getAnswers(){
        return new ArrayCollection($this->entityManager->getRepository($this->getAnswerClass())->findByParticipation($this->participation));
    }

    /**
     * @inheritdoc
     */
    public function getQuestionIndex(Participation $participation, $key, array $options)
    {
        return $key + 1;
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function supports(Participation $participation)
    {
        return $participation->getFormation()->acceptForm();
    }

    public function getQuestions()
    {
        $questions = new ArrayCollection();
        for ($i = 1; $i <= $this->getNbQuestion(); $i++) {
            $questions->add(new EvaluationQuestion($this->type, $i));
        }
        return $questions;
    }

    public function getNbQuestion()
    {
        return self::NB_QUESTION;
    }

    /**
     * Retourne true si toutes les questions ont été répondues
     * @return bool
     */
    public function isCompleted()
    {
        return $this->answers->count() === $this->questions->count();
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getAnswerClass()
    {
        throw new \Exception("getAnswerConstructor must be overrided");
    }

    /**
     * Retourne la réponse associée à une question
     * Si elle n'existe pas, une nouvelle réponse est créée
     * @param BaseQuestion $question
     * @return SurveyAnswer
     */
    public function getAssociatedAnswer(BaseQuestion $question)
    {
        $answer = $this->answers->filter(function($answer) use ($question) {
            /** @var SurveyAnswer $answer */
            return $answer->getQuestion() === $question->getIndex();
        })->first();

        if (!$answer) {
            $class = $this->getAnswerClass();
            $answer = (new $class())
                ->setParticipation($this->participation)
                ->setQuestion($question->getIndex());
        }

        return $answer;
    }

    /**
     * Transforme les réponses actuelles pour qu'elles soient restaurées dans le formulaire
     * format : clé => valeur
     * avec clé = concaténation de :  id Participation + index Question
     */
    public function getFormAnswers()
    {
        $formAnswers = array();
        $participation = $this->participation;
        $answers = $this->answers;

        $this->questions->forAll(function($key, BaseQuestion $question) use ($participation, $answers, &$formAnswers) {
            $answers->forAll(function ($aKey, EvaluationFormerAnswer $answer) use ($participation, $question, &$formAnswers) {
                if ($question->getIndex() === $answer->getQuestion()) {
                    $formAnswers[$question->getIndex()] = $answer->getAnswer();
                }
                return true;
            });
            return true;
        });
        return $formAnswers;
    }

    public function getForm($csrfProtection = true)
    {
        return $this->formFactory->create(EvaluationType::class, $this->getFormAnswers(), array(
            "questions" => $this->questions
        ));
    }

}