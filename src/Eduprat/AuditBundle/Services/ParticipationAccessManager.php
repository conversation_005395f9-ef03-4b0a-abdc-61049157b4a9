<?php

namespace Eduprat\AuditBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationWithFormInterface;
use Eduprat\DomainBundle\Entity\ParticipantDateDownload;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Services\ConfigManager;
use Eduprat\DomainBundle\Services\InvoiceManager;

class ParticipationAccessManager
{
    /**
     * @var FormManagerFactory
     */
    private $formManagerFactory;

    /**
     * @var EntityManager
     */
    private $entityManager;
    
    /**
     * @var ConfigManager
     */
    private $configManager;

    /**
     * @var InvoiceManager
     */
    private $invoiceManager;

    /**
     * ParticipationAccessManager constructor.
     * @param FormManagerFactory $formManagerFactory
     * @param EntityManager $entityManager
     */
    public function __construct(FormManagerFactory $formManagerFactory, EntityManagerInterface $entityManager, ConfigManager $configManager) {
        $this->formManagerFactory = $formManagerFactory;
        $this->entityManager = $entityManager;
        $this->configManager = $configManager;
    }

    /**
     * @param Participation $participation
     * @param int $formId
     * @return bool
     */
    public function isFormCompleted(Participation $participation, $formId) {
        if ($participation->getFormation()->isTcs()) {
            return $participation->isStepCompleted(CourseManager::STEP1_ETUTORAT_LABEL);
        }

        if (!$participation->getFormation()->hasLinkedForm()) {
            return false;
        }
        if (($participation->getFormation()->isFormVignette() || $participation->getFormation()->isFormVignetteAudit()) && $participation->getFormation()->hasFormPost()) {
            if ($formId == 2 && is_null($participation->getFormation()->getAudit2())) {
                return false;
            }
        }
        if (!$participation->getFormation()->isFormPresentielle()) {
            $auditManager = $this->formManagerFactory->getAuditManager($participation, $formId, 1, true);
            return $auditManager->isCompleted();
        } else if ($participation->getFormation()->isFormPresentielle()) {
            $surveyManager = $this->formManagerFactory->getSurveyManager($participation, $formId, true);
            $isFormCompleted = $surveyManager->isCompleted();
            if ($participation->getFormation()->getProgramme()->isCertifying() && $isFormCompleted) {
                $isFormCompleted = $surveyManager->hasReachedMinScore();
            }
            return $isFormCompleted;
        }
        return false;
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function isBothFormCompleted(Participation $participation) {
        if (!$participation->getFormation()->hasFormPost()) {
            return $this->isFormCompleted($participation, 1);
        }            
        return $this->isFormCompleted($participation, 1) && $this->isFormCompleted($participation, 2);
    }

    /**
     * @param FormationAudit $formation
     * @return bool
     */
    public function areAllFormCompleted(FormationWithFormInterface $formation) {
        foreach ($formation->getParticipations() as $participation) {
            if (!$this->isBothFormCompleted($participation)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param FormationAudit $formation
     * @return bool
     */
    public function areAllFirstFormCompleted(FormationWithFormInterface $formation) {
        foreach ($formation->getParticipations() as $participation) {
            if (!$this->isFormCompleted($participation, 1)) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param Participation $participation
     * @param Person         $former
     * @return bool
     */
    public function canEvaluateFormer(Participation $participation, Person $former) {
        return $this->isBothFormCompleted($participation) && !$this->formManagerFactory->getEvaluationFormerManager($participation, $former)->isCompleted();
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function canEvaluateFormation(Participation $participation) {
        return $this->isBothFormCompleted($participation) && !$this->formManagerFactory->getEvaluationGlobalManager($participation->getFormation(), $participation->getParticipant()->getUser())->isCompleted();
    }

    /**
     * @param Formation $formation
     * @param Person $person
     * @return bool
     * @internal param Participation $participation
     */
    public function canEvaluateProgramme(Formation $formation, Person $person) {
        $manager = $this->formManagerFactory->getEvaluationGlobalManager($formation, $person, 1);
        // if ($manager->getRole() === "ROLE_PARTICIPANT") {
        //     $completed = false;
        //     // L'évaluation est dispo si l'une des participation à ce programme est complétée
        //     foreach ($manager->getParticipations() as $participation) {
        //         $completed = $completed || $this->isBothFormCompleted($participation);
        //     }
        //     return $completed && !$manager->isCompleted();
        // }
        return !$manager->isCompleted();
    }

    /**
     * @param Formation $formation
     * @param Person $person
     * @return bool
     */
    public function isEvaluationAvailable(Formation $formation, Person $person) {
        $manager = $this->formManagerFactory->getEvaluationGlobalManager($formation, $person, 1);
        if ($manager->getRole() === "ROLE_PARTICIPANT") {
            return $this->isBothFormCompleted($manager->getParticipation())
                && $this->hasPassedMeeting($manager->getParticipation(), true);
        }
        return true;
    }

    /**
     * @param Formation $formation
     * @param Person $person
     * @return bool
     */
    public function isEvaluationCompleted(Formation $formation, Person $person) {
        $manager = $this->formManagerFactory->getEvaluationGlobalManager($formation, $person, 1);
        return $manager->isCompleted();
    }

    /**
     * @param Participation $participation
     * @param integer $formId
     * @return bool
     */
    public function canAnswerForm(Participation $participation, $formId) {
        if (!$participation->getFormation()->hasLinkedForm()) {
            return false;
        }

//        if ($formId == 2 && !$this->isFormCompleted($participation, 1)) {
//            return false;
//        }

//        $isFormCompleted = $this->isFormCompleted($participation, $formId);
        $isFormCompleted = false;
        $isAnswerable = $this->isAnswerable($participation, $formId);

        return !$isFormCompleted && $isAnswerable;
    }

    /**
     * Retourne true si l'utilisateur peut consulter les réponses à son questionnaire
     * @param Participation $participation
     * @param                                            $formId
     * @return bool
     */
    public function canShowAnswers(Participation $participation, $formId) {
        return $this->isFormCompleted($participation, $formId) && !$this->canAnswerForm($participation, $formId);
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function canGetAttestation(Participation $participation) {
        if($participation->getFinanceSousMode()->isHorsDPC()) {
            return $participation->getFormation()->getClosed();
        }
        return $participation->getFormation()->getClosed() && $this->isBothFormCompleted($participation) && $this->hasPassedClosingDate($participation) && $this->hasDownloadedRestitution($participation);
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function canGetTopo(Participation $participation) {
        return $this->isBothFormCompleted($participation) && $this->hasDownloadedRestitution($participation);
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function canGetRestitution(Participation $participation) {
        if (!$participation->getFormation()->hasFormPost()) {
            return false;
        } 
        return $participation->getFormation()->hasLinkedForm() && $this->isFormCompleted($participation, 2);
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function canGetPrerestitution(Participation $participation) {
        // $hasPastStartDateTommorow = (new \DateTime('today midnight'))->modify('+ 1 day') >= $participation->getFormation()->getStartDate();
        return $participation->getFormation()->hasLinkedForm() && $this->isFormCompleted($participation, 1);
    }

    /**
     * Return true si le participant peut évaluer qqch
     * @param Participation $participation
     * @return bool
     */
    public function canEvaluateSomething(Participation $participation) {
        $that = $this;
        return $this->canEvaluateFormation($participation) || $participation->getFormation()->getProgramme()->getFormateursPersons()->map(function($f) use ($that, $participation) {
            /** @var Person $f */
            return $that->canEvaluateFormer($participation, $f);
        })->contains(true);
    }

    public function hasDownloadedSomething(Participation $participation) {
        $downloads = $this->entityManager->getRepository(ParticipantDateDownload::class)->findOneBy(array(
            'formation' => $participation->getFormation(),
            'participant' => $participation->getParticipant()
        ));
        if ($downloads instanceof ParticipantDateDownload) {
            if (count($downloads->getToposDownloadedAt()) > 0 || !is_null($downloads->getRestitutionDownloadedAt()) || !is_null($downloads->getParticipationDownloadedAt())) {
                return true;
            }
        }
        return false;
    }

    public function hasDownloadedRestitution(Participation $participation) {
        $downloads = $this->entityManager->getRepository(ParticipantDateDownload::class)->findOneBy(array(
            'formation' => $participation->getFormation(),
            'participant' => $participation->getParticipant()
        ));
        return $downloads instanceof ParticipantDateDownload && !is_null($downloads->getRestitutionDownloadedAt());
    }

    public function hasDownloadedPrerestitution(Participation $participation) {
        $downloads = $this->entityManager->getRepository(ParticipantDateDownload::class)->findOneBy(array(
            'formation' => $participation->getFormation(),
            'participant' => $participation->getParticipant()
        ));
        return $downloads instanceof ParticipantDateDownload && !is_null($downloads->getPreRestitutionDownloadedAt());
    }

    /**
     * Retourne true si le formulaire est modifiable
     * @param Participation $participation
     * @param int                                        $formId
     * @return bool
     */
    public function isAnswerable(Participation $participation, $formId)
    {
        $now = new \DateTime('today midnight');
        $available = $now >= $this->getFormAvailabilityDate($participation, $formId);
        // Pour les formations Elearning, l'étape 2 est accessible avant la date de dispo dans le cas ou il n'y a pas du tout d'étape présentielle
        if ($formId === 2 && $participation->getFormation()->isElearning() && !$participation->getFormation()->getProgramme()->isElearningTwoUnity()) {
            if ($participation->getFormation()->getApiVariety() !== Formation::VARIETY_MIXED) {
                $available = true;
            }
            return $available && $participation->isElearningPhaseDone();
        }

        if ($formId == 2) {
            $available = $available && new \DateTime('today midnight') <= $this->getFormUnavailabilityDate($participation, $formId);
        }

        return $available;
    }

    /**
     * Retourne true si l'étape présentiel est passée
     * @param Participation $participation
     * @return bool
     */
    public function hasPassedMeeting(Participation $participation, $waitForEndDate = false)
    {
        if ($participation->getFormation()->isElearning()) {
            return $participation->isElearningPhaseDone();
        }
        if ($waitForEndDate && $participation->getFormation()->getEndDate()) {
            return new \DateTime() >= $participation->getFormation()->getEndDate();
        }
        return new \DateTime() >= $participation->getFormation()->getStartDate();
    }

    public function canAccessMeeting(Participation $participation) {
        if ($participation->getFormation()->isElearning()) {
            // $closingDate = clone $participation->getFormation()->getClosingDate();
            // $closingDate->modify("+ 1 day");
            // $available = (new \DateTime()) <= $closingDate;
            return $this->isFormCompleted($participation, 1);
        }
        $closingDate = clone $participation->getFormation()->getClosingDate();
        $closingDate->modify("+ 1 day");
        return $this->isFormCompleted($participation, 1) && ((new \DateTime()) <= $closingDate);
    }

    /**
     * Retourne la date à laquelle le formulaire sera disponible à l'édition
     * @param Participation $participation
     * @param                                            $formId
     * @return bool|\DateTime
     */
    public function getFormAvailabilityDate(Participation $participation, $formId)
    {
        $formation = $participation->getFormation();
        if ($formId === 1) {
            if (!is_null($formation->getFormOpeningDate())) {
               return $formation->getFormOpeningDate();
            }
            return $formation->getOpeningDate();
        } else if ($formId === 2) {
            if ($formation->getProgramme()->isElearning() && $formation->isThreeUnity() && $formation->getUnityByPosition(2)->getClosingDate()) {
                $endDate = clone $formation->getUnityByPosition(2)->getClosingDate();
            } else {
                $endDate = clone $formation->getEndDate();
            }
            $endDate->modify('+ 1 day')->setTime(0, 0, 0);
            return $endDate;
        }
        return false;
    }

    /**
     * Retourne la date ou le formulaire ne sera plus disponible à l'édition
     * @param Participation $participation
     * @param                                            $formId
     * @return bool|\DateTime
     */
    public function getFormUnavailabilityDate(Participation $participation, $formId)
    {
        $formation = $participation->getFormation();
        $closingDate = $formation->getClosingDate();
        if (!is_null($formation->getFormClosingDate())) {
            $closingDate = $formation->getFormClosingDate();
        }

        if ($formation->isPowerpoint()) {
            return $closingDate;
        }

        if ($formId === 1) {
            if ($formation->getProgramme()->isElearning() && $formation->getUnityByPosition(2)) {
                return $formation->getUnityByPosition(2)->getClosingDate();
            }
            if ($formation->getProgramme()->isElearning() && $formation->isOneUnity()) {
                return $formation->getUnityByPosition(1)->getClosingDate();
            }
            // Si le formulaire n'est pas encore répondu, on repousse la date jusqu'à la cloture de la session / la date définie
            if (new \DateTime('today midnight') > $formation->getStartDate()) {
                if (!$this->isFormCompleted($participation, $formId)) {
                    return $closingDate;
                }
            }
            $startDate = clone $formation->getStartDate();
            $startDate->setTime(0, 0, 0);
            return $startDate;
        } else if ($formId === 2) {
            return $closingDate;
        }
        return false;
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function hasPassedClosingDate(Participation $participation)
    {
        return (new \DateTime()) > $participation->getFormation()->getClosingDate();
    }

    /**
     * @return FormManagerFactory
     */
    public function getFormManagerFactory()
    {
        return $this->formManagerFactory;
    }
}