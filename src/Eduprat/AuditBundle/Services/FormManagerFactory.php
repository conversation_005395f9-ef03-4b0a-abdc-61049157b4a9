<?php

namespace Eduprat\AuditBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\AdminBundle\Services\EvaluationFormerByCoordinatorManager;
use Eduprat\AdminBundle\Services\EvaluationCoordinatorByCoordinatorManager;
use Eduprat\AdminBundle\Services\EvaluationProgrammeByCoordinatorManager;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;

class FormManagerFactory
{
    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * @var FormFactory
     */
    private $formFactory;

    /** @var array */
    private $cache;

    /**
     * AuditManager constructor.
     * @param EntityManager                       $entityManager
     * @param FormFactory $formFactory
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory)
    {
        $this->entityManager = $entityManager;
        $this->formFactory = $formFactory;
        $this->cache = array();
    }

    /**
     * @param Participation $participation
     * @param                                            $auditId
     * @param int $patient
     * @param bool $noCache
     * @return AuditManager
     */
    public function getAuditManager(Participation $participation, $auditId, $patient = 1, $noCache = false)
    {
        return $this->getFromCache("audit", array($participation, $auditId, $patient), function() use ($participation, $auditId, $patient) {
            return new AuditManager($this->entityManager, $this->formFactory, $participation, $auditId, $patient);
        }, $noCache);
    }

    /**
     * @param Participation $participation
     * @param                                            $surveyId
     * @param bool $noCache
     * @return SurveyManager
     */
    public function getSurveyManager(Participation $participation, $surveyId, $noCache = false)
    {
        return $this->getFromCache("survey", array($participation, $surveyId), function() use ($participation, $surveyId) {
            return new SurveyManager($this->entityManager, $this->formFactory, $participation, $surveyId);
        }, $noCache);
    }

    /**
     * @param Participation $participation
     * @param Person $former
     * @return EvaluationFormerManager
     */
    public function getEvaluationFormerManager(Participation $participation, Person $former)
    {
        return $this->getFromCache("evaluation_former", array($participation, $former), function() use ($participation, $former) {
            return new EvaluationFormerManager($this->entityManager, $this->formFactory, $participation, $former);
        });
    }

    /**
     * @param Programme $programme
     * @param Person $former
     * @param Coordinator $coordinator
     * @return EvaluationFormerByCoordinatorManager
     */
    public function getEvaluationFormerByCoordinatorManager(Programme $programme, Person $former, Coordinator $coordinator)
    {
        return $this->getFromCache("evaluation_former_by_coordinator", array($programme, $former, $coordinator), function() use ($programme, $former, $coordinator) {
            return new EvaluationFormerByCoordinatorManager($this->entityManager, $this->formFactory, $programme, $former, $coordinator);
        });
    }

    /**
     * @param Programme $programme
     * @param Coordinator $coordinator
     * @return EvaluationCoordinatorByCoordinatorManager
     */
    public function getEvaluationCoordinatorByCoordinatorManager(Programme $programme, Coordinator $coordinator)
    {
        return $this->getFromCache("evaluation_coordinator_by_coordinator", array($programme, $programme, $coordinator), function() use ($programme, $coordinator) {
            return new EvaluationCoordinatorByCoordinatorManager($this->entityManager, $this->formFactory, $programme, $coordinator);
        });
    }

    /**
     * @param Programme $programme
     * @param Coordinator $coordinator
     * @return EvaluationProgrammeByCoordinatorManager
     */
    public function getEvaluationProgrammeByCoordinatorManager(Programme $programme, Coordinator $coordinator)
    {
        return $this->getFromCache("evaluation_programme_by_coordinator", array($programme, $coordinator), function() use ($programme, $coordinator) {
            return new EvaluationProgrammeByCoordinatorManager($this->entityManager, $this->formFactory, $programme, $coordinator);
        });
    }

    /**
     * @param Participation $participation
     * @return EvaluationFormationManager
     */
    public function getEvaluationFormationManager(Participation $participation)
    {
        return $this->getFromCache("evaluation_formation", array($participation), function() use ($participation) {
            return new EvaluationFormationManager($this->entityManager, $this->formFactory, $participation);
        });
    }

    /**
     * @param Formation $formation
     * @param Person $person
     * @param int $page
     * @return EvaluationGlobalManager
     */
    public function getEvaluationGlobalManager(Formation $formation, Person $person, $page = 1)
    {
        return $this->getFromCache("evaluation_global", array($formation, $person, $page), function() use ($formation, $person, $page) {
            return new EvaluationGlobalManager($this->entityManager, $this->formFactory, $formation, $person, $page);
        });
    }

    /**
     * @param Participation $participation
     * @param int $page
     * @return EvaluationGlobalManager
     */
    public function getEvaluationGlobalManagerFromParticipation(Participation $participation, $page = 1)
    {
        $formation = $participation->getFormation();
        $person = $participation->getParticipant()->getUser();
        return $this->getEvaluationGlobalManager($formation, $person, $page);
    }

    /**
     * @param $type
     * @param $args
     * @param $func
     * @param bool $force
     * @return mixed|null
     */
    public function getFromCache($type, $args, $func, $force = false) {
        $key = $this->getCacheKey($type, $args);
        if (!$force && $manager = $this->getCache($key)) {
            return $manager;
        } else {
            $manager = $func();
            $this->setCache($key, $manager);
            return $manager;
        }
    }

    /**
     * @param $type
     * @param $args
     * @return string
     */
    public function getCacheKey($type, $args) {
        return $type . "_" . join("_", array_map(function($arg) {
            return is_object($arg) ? $arg->getId() : $arg;
        }, $args));
    }

    /**
     * @param $key
     * @return mixed|null
     */
    public function getCache($key) {
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }
        return null;
    }

    /**
     * @param $key
     * @param $value
     */
    public function setCache($key, $value) {
        $this->cache[$key] = $value;
    }

    public function clearCache() {
        $this->cache = [];
    }

}