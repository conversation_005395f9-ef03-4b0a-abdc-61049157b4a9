<?php

namespace Eduprat\AuditBundle\Services;

use Symfony\Component\Form\FormInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Form\AuditType;
use Eduprat\DomainBundle\Entity\AuditAnswer;
use Eduprat\DomainBundle\Entity\AuditQuestion;
use Eduprat\DomainBundle\Entity\BaseQuestion;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\PatientDescription;
use Eduprat\DomainBundle\Entity\PatientDescriptionPredefined;
use Eduprat\DomainBundle\Entity\SurveyQuestion;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormFactory;
use Symfony\Component\Form\FormFactoryInterface;

class AuditManager extends FormManager
{

    CONST FIRST_AUDIT = 1;
    CONST SECOND_AUDIT = 2;

    /**
     * @var integer
     */
    private $auditId;

    /**
     * @var integer
     */
    private $patient;

    /**
     * @var ArrayCollection
     */
    private $currentAnswers;

    /**
     * @var int
     */
    private $nbPatients;

    /**
     * @var int
     */
    private $descriptionsCount;

    /**
     * @var FormationAudit
     */
    protected $formation;

    /**
     * @var PatientDescription
     */
    protected $currentDescription;

    /** @var ArrayCollection|AuditAnswer[] */
    protected $allAnswers = null;

    /**
     * AuditManager constructor.
     * @param EntityManager                       $entityManager
     * @param FormFactory $formFactory
     * @param Participation                       $participation
     * @param                                     $auditId
     * @param                                     $patient
     */
    public function __construct(EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, Participation $participation, $auditId, $patient)
    {
        $this->options = array(
            "patient" => $patient
        );
        $this->auditId = $auditId;
        $this->patient = $patient;
        parent::__construct($entityManager, $formFactory, $participation);
        $this->nbPatients = $this->getAudit()->getNbPatients();
        $this->currentAnswers = $participation->getAuditAnswers()->filter(function(AuditAnswer $answer) use ($auditId, $patient) {
            return $answer->getAuditId() === $auditId && $answer->getPatient() === $patient;
        });
        $description = $participation->getPatientsDescriptions()->filter(function (PatientDescription $patientDescription) use ($patient, $auditId) {
            return $patientDescription->getPatient() === $patient && $patientDescription->getAuditId() === $auditId;
        });
        $this->currentDescription = $description->isEmpty() ? null : $description->first();
        $this->descriptionsCount = $participation->getPatientsDescriptions()->count();
    }

    /**
     * @inheritdoc
     */
    public function getQuestionIndex(Participation $participation, $key, array $options)
    {
        return $participation->getId() . $options['patient'] . $key;
    }

    /**
     * @param Participation $participation
     * @return bool
     */
    public function supports(Participation $participation)
    {
        return $participation->getFormation()->isAudit() || ($participation->getFormation() instanceof FormationElearning && $participation->getFormation()->getFormType() != "survey");
    }

    /**
     * @return Collection|AuditQuestion[]
     */
    public function getQuestions()
    {
        if ($this->isDefaultType()) {
            return $this->getAudit()->getQuestions();
        } else {
            return $this->getAudit()->getSurveyQuestions()->filter(function(SurveyQuestion $question) {
                return $question->getPatient() === $this->patient;
            });
        }
    }

    public function isDefaultType() {
        if ($this->formation->isFormVignetteAudit() && $this->auditId == "2") {
            return $this->participation->getFormation()->getAudit2()->isDefaultType();
        }
        return $this->participation->getFormation()->getAudit()->isDefaultType();
    }

    public function isVignetteType() {
        return $this->participation->getFormation()->getAudit()->isVignetteType();
    }

    /**
     * Retourne true si le numéro du patient est valide
     * @return bool
     */
    public function isValidStep()
    {
        return in_array($this->auditId, [self::FIRST_AUDIT, self::SECOND_AUDIT]) && $this->patient <= $this->nbPatients;
    }

    /**
     * Retourne true si le patient actuel est le dernier
     * @return bool
     */
    public function isLastPatient()
    {
        return $this->patient === $this->nbPatients;
    }

    /**
     * @return int
     */
    public function getNbPatients()
    {
        return $this->nbPatients;
    }

    /**
     * @return bool
     */
    public function isLastAudit()
    {
        return $this->auditId === self::SECOND_AUDIT;
    }

    /**
     * Retourne true si toutes les questions ont été répondues
     * @return bool
     */
    public function isCompleted()
    {
        $currentPatient = $this->patient;
        $isCompleted = true;
        for ($patient = 1; $patient <= $this->nbPatients; $patient++) {
            $this->patient = $patient;
            foreach ($this->getQuestions() as $question) {
                if ($question->getRequired() && !$this->getAllAnswers()->contains($this->getpatientAnswer($question, $patient))) {
                    $isCompleted = false;
                }
            }
        }
        $this->patient = $currentPatient;
        return $isCompleted;
    }

    /**
     * Retourne le statut completed pour chacun des patients
     * @return array
     */
    public function isCompletedPerPatient()
    {
        $currentPatient = $this->patient;
        $patients = array();
        for ($patient = 1; $patient <= $this->nbPatients; $patient++) {
            $isCompleted = true;
            $this->patient = $patient;
            foreach ($this->getQuestions() as $question) {
                if ($question->getRequired() && !$this->getAllAnswers()->contains($this->getpatientAnswer($question, $patient))) {
                    $isCompleted = false;
                }
            }
            $patients[] = array("patient" => $patient, "completed" => $isCompleted);
        }
        $this->patient = $currentPatient;
        return $patients;
    }


    /**
     * Retourne la réponse associée à une question
     * Si elle n'existe pas, une nouvelle réponse est créée
     * @param BaseQuestion $question
     * @return AuditAnswer
     */
    public function getAssociatedAnswer(BaseQuestion $question)
    {
        $answer = $this->currentAnswers->filter(function($answer) use ($question) {
            /** @var AuditAnswer $answer */
            return (($this->isDefaultType() && $answer->getQuestion() === $question) || (!$this->isDefaultType() && $answer->getSurveyQuestion() === $question));
        })->first();

        if (!$answer) {
            $answer = (new AuditAnswer())
                ->setParticipation($this->participation)
                ->setAuditId($this->auditId)
                ->setPatient($this->patient);
            if ($this->isDefaultType()) {
                $answer->setQuestion($question);
            } else {
                $answer->setSurveyQuestion($question);
            }
        }

        return $answer;
    }

    public function getpatientAnswer($question, $patient)
    {
        $allAnswers = $this->getAllAnswers();

        $auditId = $this->auditId;
        $answer = $allAnswers->filter(function ($a) use ($question, $patient, $auditId) {
            /** @var AuditAnswer $a */
           return (($this->isDefaultType() && $a->getQuestion() === $question) || (!$this->isDefaultType() && $a->getSurveyQuestion() === $question)) && $a->getPatient() === $patient && $a->getAuditId() === $auditId;
        });
        return $answer->first();
    }

    /**
     * Retourne toutes les réponses de l'audit, indépendamment du patient
     * @return ArrayCollection
     */
    public function getAllAnswers()
    {
        if (is_null($this->allAnswers)) {
            $this->allAnswers = new ArrayCollection($this->entityManager->getRepository(AuditAnswer::class)->findByAudit($this->participation, $this->auditId));
        }
        return $this->allAnswers;
    }

    /**
     * Transforme les réponses actuelles pour qu'elles soient restaurées dans le formulaire
     * format : clé => valeur
     * avec clé = concaténation de :  id Participation + n° patient + index Question
     */
    public function getFormAnswers()
    {
        $answers = array();
        $participation = $this->participation;
        $patient = $this->patient;
        $currentAnswers = $this->currentAnswers;

        $this->questions->forAll(function($key, $question) use ($participation, $patient, $currentAnswers, &$answers) {
            $currentAnswers->forAll(function ($aKey, $answer) use ($participation, $patient, $question, &$answers) {
                /** @var AuditAnswer $answer */
                if ((($this->isDefaultType() && $answer->getQuestion() === $question) || (!$this->isDefaultType() && $answer->getSurveyQuestion() === $question))) {
                    $answers[$question->getIndex()] = $answer->getAnswer();
                }
                return true;
            });
            return true;
        });

        $description = $this->getDescription();

        if (!is_null($description)) {
            $answers['description'] = $description->getDescription();
        }

        return $answers;
    }

    public function getAudit() {
        if ($this->isLastAudit() && ($this->isVignetteType() || $this->participation->getFormation()->isFormVignetteAudit()) && $this->participation->getFormation()->hasFormPost()) {
            return $this->participation->getFormation()->getAudit2();
        }
        return $this->participation->getFormation()->getAudit();
    }

    public function getDescription() {
        return $this->isDefaultType() ? $this->currentDescription : $this->getPredefinedDescription();
    }

    public function setDescription($form, $description) {
        if ($this->isDefaultType()) {
            $this->currentDescription = $this->getAssociatedDescription($form);
            $this->currentDescription->setDescription($description);
        }
    }

    public function getPredefinedDescription() {
        return $this->entityManager->getRepository(PatientDescriptionPredefined::class)->findOneBy(
            array(
                "patient" => $this->patient,
                "audit" => $this->getAudit()
            )
        );
    }

    /**
     * @return FormInterface
     */
    public function getForm($csrfProtection = true)
    {
        $options = array(
            "questions" => $this->questions,
            "type" => $this->getAudit()->getType()
        );
        if (!$this->isDefaultType()) {
            $options["description"] = $this->getPredefinedDescription();
        }
        if (!$csrfProtection) {
            $options["csrf_protection"] = false;
        }
        return $this->formFactory->create(AuditType::class, $this->getFormAnswers(), $options);
    }

    /**
     * @param Form $form
     * @return PatientDescription
     */
    public function getAssociatedDescription(Form $form)
    {
        $description = $this->currentDescription;

        if (!$description) {
            $description = (new PatientDescription())
                ->setParticipation($this->participation)
                ->setAuditId($this->auditId)
                ->setPatient($this->patient);
        }

        if (isset($form->getData()["description"])) {
            $description->setDescription($form->getData()["description"]);
        }

        return $description;
    }

    /**
     * @param array $answers
     */
    public function setAnswers(ArrayCollection $answers){
        $this->currentAnswers = $answers;
    }

    public function save(Form $form)
    {
        if ($this->isDefaultType()) {
            $description = $this->getAssociatedDescription($form);
            $this->entityManager->persist($description);
        }
        parent::save($form);
    }

    public function saveParticipation() {
        $this->entityManager->persist($this->participation);
        $this->entityManager->flush();
    }

}