<?php

namespace Eduprat\AuditBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;

class EvaluationController extends Ed<PERSON>ratController
{
    /**
     * @param Request $request
     * @param Participation $participation
     * @param Person $former
     * @return Response
     */
    #[Route(path: '/evaluation-former/{id}/{former}', name: 'eduprat_evaluation_former')]
    public function evaluationFormer(Request $request, Participation $participation, Person $former, ParticipationAccessManager $accessManager, FormManagerFactory $formManagerFactory)
    {
        $this->denyAccessUnlessGranted('answer', $participation);
        if (!$this->isGranted('ROLE_COORDINATOR') && !$accessManager->canEvaluateFormer($participation, $former)) {
            throw $this->createAccessDeniedException();
        }

        $evaluationFormerManager = $formManagerFactory->getEvaluationFormerManager($participation, $former);

        $form = $evaluationFormerManager->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $evaluationFormerManager->save($form);
            /** @var Person $person */
            foreach ($participation->getFormation()->getProgramme()->getFormateursPersons() as $person) {
                // On cherche le formateur suivant, si son évaluation n'est pas complétée on redirige dessus
                if ($accessManager->canEvaluateFormer($participation, $person)) {
                    return $this->redirectToRoute('eduprat_evaluation_former', array('id' => $participation->getId(), 'former' => $person->getId()));
                }
            }
            // si toutes les evaluations sont complétées, on redirige vers la page d'accueil
            return $this->redirectToRoute('eduprat_audit_index');
        }

        return $this->render('audit/evaluation/former.html.twig', array(
            'participation' => $participation,
            'former' => $former,
            'form' => $form,
            'completed' => $evaluationFormerManager->isCompleted(),
        ));
    }

    /**
     * @param Request $request
     * @param Participation $participation
     * @return Response
     */
    #[Route(path: '/evaluation-formation/{id}', name: 'eduprat_evaluation_formation')]
    public function evaluationFormation(Request $request, Participation $participation, ParticipationAccessManager $accessManager, FormManagerFactory $formManagerFactory)
    {
        $this->denyAccessUnlessGranted('answer', $participation);
        if (!$this->isGranted('ROLE_COORDINATOR') && !$accessManager->canEvaluateFormation($participation)) {
            throw $this->createAccessDeniedException();
        }

        $evaluationFormerManager = $formManagerFactory->getEvaluationFormationManager($participation);

        $form = $evaluationFormerManager->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $evaluationFormerManager->save($form);
            $firstFormer = $participation->getFormation()->getProgramme()->getFormateursPersons()->first();
            if ($accessManager->canEvaluateFormer($participation, $firstFormer)) {
                // On redirige vers le formulaire d'évaluation du premier formateur lié à la formation
                return $this->redirectToRoute('eduprat_evaluation_former', array('id' => $participation->getId(), 'former' => $firstFormer->getId()));
            } else {
                return $this->redirectToRoute('eduprat_audit_index');
            }
        }

        return $this->render('audit/evaluation/formation.html.twig', array(
            'participation' => $participation,
            'form' => $form,
            'completed' => $evaluationFormerManager->isCompleted(),
        ));
    }

    /**
     * @param Participation $participation
     * @return RedirectResponse
     */
    #[Route(path: '/evaluation-redirect/{participation}', name: 'eduprat_evaluation')]
    public function evaluationRedirect(Participation $participation): RedirectResponse {
        return $this->redirectToRoute('eduprat_front_formation_module', array(
           "id" => $participation->getId(),
           "module" => CourseManager::STEP4_RESTITUTION_LABEL,
        ));
    }

    /**
     * @param Participation $participation
     * @return RedirectResponse
     */
    #[Route(path: '/evaluation-redirect-front/{participation}', name: 'eduprat_evaluation_redirect')]
    public function evaluationRedirectFront(Participation $participation): RedirectResponse {
        return $this->redirectToRoute('eduprat_evaluation_global', array(
            "person" => $participation->getParticipant()->getUser()->getId(),
            "formation" => $participation->getFormation()->getId(),
            "page" => 1
        ));
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param Formation $formation
     * @param $page
     * @return Response
     */
    #[Route(path: '/evaluation-global/{person}/{formation}/{page}', name: 'eduprat_evaluation_global', requirements: ['page' => '\d+'], defaults: ['page' => '1'])]
    public function evaluationGlobal(Request $request, EntityManagerInterface $entityManager, Person $person, Formation $formation, int $page, ParticipationAccessManager $accessManager, FormManagerFactory $formManagerFactory, ParticipationLogger $participationLogger, CourseManager $courseManager): Response
    {
        $page = (int) $page;
        if (!$accessManager->canEvaluateProgramme($formation, $person)) {
            throw $this->createAccessDeniedException();
        }

        $evaluationGlobalManager = $formManagerFactory->getEvaluationGlobalManager($formation, $person, $page);

        $form = $evaluationGlobalManager->getForm();

        $logKey = sprintf("p_log_eval_%s_%s", $person->getId(), $formation->getId());
        if ($participation = $evaluationGlobalManager->getParticipation()) {
            $participationLogger->startLog($participation, ParticipationLog::ACTION_FORM_EVAL, $logKey);
        }

        $form->handleRequest($request);
        $course = null;
        if ($participation) {
            $currentModule = CourseManager::SATISFACTION_LABEL;
            $course = $courseManager->getCourseDetail($participation);
            $step = $courseManager->findStepModule($course, $currentModule);
        }
        
        $nbPages = $evaluationGlobalManager->getNbPages($formation, $evaluationGlobalManager->getRole());
        $isLastPage = $page === $nbPages;
        if ($form->isSubmitted() && $form->isValid()) {
            if ($participation && !$participation->minModuleTimeReached($currentModule) && $isLastPage) {
                $this->flashMessages->addError("front.error.minTime");
            } else {
                $evaluationGlobalManager->save($form);
                if ($participation) {
                    $progression = round($page / $nbPages * 100);
                    $participationLogger->endLog($participation, ParticipationLog::ACTION_FORM_EVAL, $logKey, $progression);
                }
                if ($nbPages > $page) {
                    return $this->redirectToRoute("eduprat_evaluation_global", array("person" => $person->getId(), "formation" => $formation->getId(), "page" => ($page + 1)));
                } else {
                    if ($participation && isset($course)) {
                        $courseManager->completeCourseStep($participation, CourseManager::SATISFACTION_LABEL);
                        $entityManager->persist($participation);
                        $entityManager->flush();
                        $this->flashMessages->addSuccess('evaluation.success');
                        $nextModule = $courseManager->getNextModule($course, $currentModule);
                        if ($nextModule && $nextModule["url"]) {
                            return $this->redirect($nextModule["url"]);
                        }
                    }
                    return $this->redirectToRoute('eduprat_audit_index');
                }
            }
        }

        $paramTwig = [
            'page' => $page,
            'manager' => $evaluationGlobalManager,
            'former' => $evaluationGlobalManager->getFormer(),
            'formation' => $formation,
            'person' => $person,
            'form' => $form->createView(),
            'role' => $evaluationGlobalManager->getRole(),
            'completed' => $evaluationGlobalManager->isCompleted(),
            'isLastPage' => $isLastPage,
            'courseManager' => $courseManager,
            'course' => $course,
        ];
        if (isset($step) && isset($currentModule)) {
            $paramTwig["step"] = $step;
            $paramTwig["current_module"] = $currentModule;
        }
        if ($participation) {
            $paramTwig["participation"] = $participation;
            $paramTwig["satisfaction_form"] = true;
        }
        return $this->render('audit/evaluation/global.html.twig', $paramTwig);
    }
}
