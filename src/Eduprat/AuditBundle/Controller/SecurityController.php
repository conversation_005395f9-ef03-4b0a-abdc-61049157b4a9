<?php

namespace Eduprat\AuditBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Http\SecurityRequestAttributes;

class SecurityController extends AbstractController
{
    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/login', name: 'eduprat_audit_login')]
    public function login(Request $request): Response {
        $session = $request->getSession();

        // Récupère l'erreur d'authentification s'il y en a un
        if ($request->attributes->has(SecurityRequestAttributes::AUTHENTICATION_ERROR)) {
            $error = $request->attributes->get(
                SecurityRequestAttributes::AUTHENTICATION_ERROR
            );
        } else {
            $error = $session->get(SecurityRequestAttributes::AUTHENTICATION_ERROR);
            $session->remove(SecurityRequestAttributes::AUTHENTICATION_ERROR);
        }

        return $this->render('audit/security/login.html.twig', array(
            // dernier nom entré par l'utilisateur
            'last_username' => $session->get(SecurityRequestAttributes::LAST_USERNAME),
            'error' => $error,
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/imap', name: 'imap')]
    public function imap()
    {
        $TENANT = $this->getParameter('inscription_client_tenant');
        $CLIENT_ID = $this->getParameter('inscription_client_id');
        $REDIRECT_URI = $this->getParameter('inscription_client_redirect_uri');
        $CLIENT_SECRET = $this->getParameter('inscription_client_secret');
        $SCOPE = "https://outlook.office365.com/IMAP.AccessAsUser.All offline_access";
        /* $uri = 'https://login.microsoftonline.com/'.$TENANT.'/oauth2/v2.0/authorize?
           client_id='.$CLIENT_ID.'
           &response_type=code
           &redirect_uri='.urlencode($REDIRECT_URI).'
           &response_mode=query
           &scope='.$SCOPE.'
           &state=12345';
           var_dump($uri);die; */
        if ($_GET["code"]) {
            $CODE = $_GET["code"];
            $SESSION = $_GET["session_state"];
            $SESSION = $_GET["state"];
            $url= "https://login.microsoftonline.com/$TENANT/oauth2/v2.0/token";

            $param_post_curl = [ 
            'client_id'=>$CLIENT_ID,
            'scope'=>$SCOPE,
            'code'=>$CODE,
            'session_state'=>$SESSION,
            'client_secret'=>$CLIENT_SECRET,
            'redirect_uri'=>$REDIRECT_URI,
            'grant_type'=>'authorization_code' ];

            $ch=curl_init();
            curl_setopt($ch,CURLOPT_URL,$url);
            curl_setopt($ch,CURLOPT_POSTFIELDS, http_build_query($param_post_curl));
            curl_setopt($ch,CURLOPT_POST, 1);
            curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);

            $oResult=curl_exec($ch);
            dd(json_decode($oResult));
        }
    }
}
