<?php

namespace Eduprat\AuditBundle\Security\User;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;

class ParticipantProvider implements UserProviderInterface, PasswordUpgraderInterface
{
    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
     * WebserviceUserProvider constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager) {
        $this->entityManager = $entityManager;
    }

    /**
     * @param string $username
     * @return Person
     */
    public function loadUserByIdentifier(string $username): UserInterface
    {
        /** @var Participant|null $participant */
        $participant = $this->entityManager->getRepository(Participant::class)->findOneByRppsOrAdeliOrLogin($username);

        if ($participant) {
            return $participant->getUser();
        }

        throw new UserNotFoundException(sprintf('Identifiants invalides', $username));
    }

    /**
     * @param UserInterface $user
     * @return Person
     */
    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof Person) {
            throw new UnsupportedUserException(
                sprintf('Instances of "%s" are not supported.', get_class($user))
            );
        }

        return $this->loadUserByIdentifier($user->getUsername());
    }

    /**
     * @param string $class
     * @return bool
     */
    public function supportsClass($class): bool
    {
        return $class === 'Eduprat\AdminBundle\Entity\Person';
    }

    /**
     * @See https://symfony.com/doc/4.4/security/password_migration.html#upgrade-the-password-when-using-a-custom-user-provider
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        $user->setPassword($newHashedPassword);
        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }
}
