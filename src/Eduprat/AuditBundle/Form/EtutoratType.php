<?php

namespace Eduprat\AuditBundle\Form;

use Eduprat\DomainBundle\Entity\Etutorat;
use Ed<PERSON>rat\DomainBundle\Entity\FicheSynthese;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EtutoratType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var Participation $participation */
        $participation = $options["participation"];
        $isSynthese = $options["synthese"];
        $programme = $participation->getFormation()->getProgramme();
        $choices = [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5];
        $class = Etutorat::class;
        $findMethod = "findEtutorat";

        if (!$isSynthese) {
            $builder->add('message', TextareaType::class, array(
                "required" => false,
                "attr" => array("rows" => 6, "placeholder" => "Saisissez votre texte ici")),
            );
        } else {
            $choices = ["Acquis" => 2, "En cours d'acquisition" => 1, "Non acquis" => 0];
            $class = FicheSynthese::class;
            $findMethod = "findFicheSynthese";
        }
        

        foreach ($programme->getConnaissances() as $index => $connaissance) {
            $key = sprintf("connaissances_%s", $connaissance->getId());
            $builder->add($key, ChoiceType::class, array(
                'label' => $connaissance->getName(),
                'disabled' => $options["disabled"],
                "expanded" => true,
                "multiple" => false,
                "choices" => $choices,
                'label_attr' => array('class' => 'radio-inline'),
                'choice_attr' => function() {
                    return ['class' => 'type-note'];
                }
            ));
            $builder->get($key)
                ->setData($participation->$findMethod("connaissance", $connaissance))
                ->addModelTransformer(new CallbackTransformer(
                    function ($etutorat) {
                        return $etutorat ? $etutorat->getAnswer() : null;
                    },
                    function ($input) use ($participation, $connaissance, $class) {
                        return (new $class())
                            ->setParticipation($participation)
                            ->setConnaissance($connaissance)
                            ->setAnswer($input);
                    }
                ))
            ;
        }

        foreach ($programme->getCompetences() as $index => $competence) {
            $key = sprintf("competence_%s", $competence->getId());
            $builder->add($key, ChoiceType::class, array(
                'label' => $competence->getName(),
                'disabled' => $options["disabled"],
                "expanded" => true,
                "multiple" => false,
                "choices" => $choices,
                'label_attr' => array('class' => 'radio-inline'),
                'choice_attr' => function() {
                    return ['class' => 'type-note'];
                }
            ));
            $builder->get($key)
                ->setData($participation->$findMethod("competence", $competence))
                ->addModelTransformer(new CallbackTransformer(
                    function ($etutorat) {
                        return $etutorat ? $etutorat->getAnswer() : null;
                    },
                    function ($input) use ($participation, $competence, $class) {
                        return (new $class())
                            ->setParticipation($participation)
                            ->setCompetence($competence)
                            ->setAnswer($input);
                    }
                ))
            ;
        }

    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            "participation" => null,
            "synthese" => false,
            "disabled" => false
        ));
    }
}
