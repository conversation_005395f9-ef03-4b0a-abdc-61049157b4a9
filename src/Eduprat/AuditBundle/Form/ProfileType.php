<?php

namespace Eduprat\AuditBundle\Form;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON><PERSON>rat\DomainBundle\Entity\Participant;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Validator\Constraints\UserPassword;
use Symfony\Component\Validator\Constraints\NotBlank;

class ProfileType extends AbstractType
{

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('civility', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.civility'
            ))
            ->add('lastname', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.lastname'
            ))
            ->add('firstname', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.firstname'
            ))
            ->add('birthName', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.birthname'
            ))
            ->add('address', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.address'
            ))
            ->add('zipCode', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.zipCode'
            ))
            ->add('city', ChoiceType::class, array(
                'label' => 'admin.participant.city',
                'choices' => $this->getCities($options["em"]),
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
            ))
            ->add('phone', EmailType::class, array(
                'required' => false,
                'label' => 'admin.participant.email'
            ))
            ->add('email', EmailType::class, array(
                'required' => false,
                'label' => 'admin.participant.email'
            ))
            ->add('rpps', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.rpps'
            ))
            ->add('adeli', TextType::class, array(
                'required' => false,
                'label' => 'admin.participant.adeli'
            ))
            ->add('category', ChoiceType::class, array(
                'label' => 'admin.participant.category',
                'choices' => ProgrammeType::getCategories(),
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
            ))
            ->add('speciality', ChoiceType::class, array(
                'label' => 'admin.participant.speciality',
                'choices' => ProgrammeType::getSpecialities(),
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'placeholder' => 'admin.global.all',
            ))
            ->add('notifInscription', null, array(
                'label' => false,
            ))
            ->add('notifSurveyOpen', null, array(
                'label' => false,
            ))
            ->add('notifRemindSurvey', null, array(
                'label' => false,
            ))
            ->add('notifRemindSession', null, array(
                'label' => false,
            ))
            ->add('notifNewSession', null, array(
                'label' => false,
            ))
            ->add('notifSessionChange', null, array(
                'label' => false,
            ));

        if ($options["password"] && $options["user"] && $options["userManager"]) {

            $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($options) {
                $data = $event->getData();
                $form = $event->getForm();

                if (isset($data["password"]) || isset($data["currentPassword"])) {

                    $event->getForm()->add('currentPassword', PasswordType::class, array(
                        'mapped' => false,
                        'required' => false,
                        'label' => 'user.form.current_password',
                        'always_empty' => false,
                        'constraints' => new UserPassword(),
                    ));

                    $event->getForm()->add('password', PasswordType::class, array(
                        'mapped' => false,
                        'required' => false,
                        'label' => 'form.new_password',
                        'constraints' => array(
                            new NotBlank()
                        ),
                    ));

                }

            });

            $builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) use ($options) {
                $form = $event->getForm();

                if ($form->has('password') && $newPassword = $form->get('password')->getData()) {
                    $user = $options["user"];
                    $userManager = $options["userManager"];
                    $user->setPlainPassword($newPassword);
                    $user->setHasCreatedPassword(true);
                    $userManager->updateUser($user);
                }
            });
        }
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            "em" => null,
            "user" => null,
            "userManager" => null,
            "json" => false,
            "password" => false
        ));
    }

    private function getCities(EntityManagerInterface $entityManager)
    {
        $cities = array_column(
            $entityManager->getRepository(Participant::class)
                ->createQueryBuilder('p')
                ->select('p.city')
                ->where("p.city != ''")
                ->groupBy('p.city')
                ->orderBy('p.city', 'ASC')
                ->getQuery()
                ->getScalarResult(),
            "city");
        return array_combine($cities, $cities);
    }

    private function getCategories(EntityManagerInterface $entityManager)
    {
        $categories = array_column(
            $entityManager->getRepository(Participant::class)
                ->createQueryBuilder('p')
                ->select('p.category')
                ->where("p.category != ''")
                ->groupBy('p.category')
                ->orderBy('p.category', 'ASC')
                ->getQuery()
                ->getScalarResult(),
            "category");
        return array_combine($categories, $categories);
    }

    private function getSpecialities(EntityManagerInterface $entityManager)
    {
        $specialities = array_column(
            $entityManager->getRepository(Participant::class)
                ->createQueryBuilder('p')
                ->select('p.speciality')
                ->where("p.speciality != ''")
                ->groupBy('p.speciality')
                ->orderBy('p.speciality', 'ASC')
                ->getQuery()
                ->getScalarResult(),
            "speciality");
        return array_combine($specialities, $specialities);
    }

}
