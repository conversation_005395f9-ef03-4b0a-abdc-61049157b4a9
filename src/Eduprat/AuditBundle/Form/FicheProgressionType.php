<?php

namespace Eduprat\AuditBundle\Form;

use Eduprat\DomainBundle\Entity\FicheProgression;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FicheProgressionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('pointsCles', null, array(
                "required" => true,
                "disabled" => !$options["editable"],
                "label"    => "front.fiche_progression.pointsCles",
                "attr" => array("rows" => 6, "placeholder" => $options["editable"] ? "Saisissez votre texte ici" : null),
            ))
            ->add('ameliorations', null, array(
                "required" => true,
                "disabled" => !$options["editable"],
                "label"    => "front.fiche_progression.ameliorations",
                "attr" => array("rows" => 6, "placeholder" => $options["editable"] ? "Saisissez votre texte ici" : null),
            ))
            ->add('moyens', null, array(
                "required" => true,
                "disabled" => !$options["editable"],
                "label"    => "front.fiche_progression.moyens",
                "attr" => array("rows" => 6, "placeholder" => $options["editable"] ? "Saisissez votre texte ici" : null),
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FicheProgression::class,
            "editable" => true
        ]);
    }
}
