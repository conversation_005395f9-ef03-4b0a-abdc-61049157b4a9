services:
    eduprat.participant_provider:
        class: Eduprat\AuditBundle\Security\User\ParticipantProvider
        arguments: ["@doctrine.orm.entity_manager"]
    eduprat_form_manager_factory:
        class:  Eduprat\AuditBundle\Services\FormManagerFactory
        arguments: [ "@doctrine.orm.entity_manager", "@form.factory" ]
    eduprat_participation_access_manager:
        class: Eduprat\AuditBundle\Services\ParticipationAccessManager
        arguments: ["@eduprat_form_manager_factory", "@doctrine.orm.entity_manager", "@eduprat.config_manager"]
    eduprat.participation_voter:
        class: Eduprat\AuditBundle\Security\ParticipationVoter
        arguments: ['@security.access.decision_manager']
        public: false
        tags:
            - { name: security.voter }
#    eduprat.evaluation.listener:
#        class: Eduprat\AuditBundle\EventListener\EvaluationListener
#        arguments: ["@eduprat.email.sender"]
#        tags:
#            - { name: doctrine.event_listener, event: postPersist }
    Eduprat\AuditBundle\Form\ProfileType:
        class: Eduprat\AuditBundle\Form\ProfileType
        arguments: ["@doctrine.orm.entity_manager"]
