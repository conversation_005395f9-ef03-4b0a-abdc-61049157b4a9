<?php

namespace Eduprat\AuditBundle\EventListener;

use <PERSON><PERSON>rat\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Bundle\SecurityBundle\Security;

class RequestListener implements EventSubscriberInterface
{
    private Security $security;
    private ParticipationLogger $participationLogger;
    private RequestStack $requestStack;

    public function __construct(Security $security, ParticipationLogger $participationLogger, RequestStack $requestStack)
    {
        $this->security = $security;
        $this->participationLogger = $participationLogger;
        $this->requestStack = $requestStack;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            RequestEvent::class => 'onKernelRequest',
        ];
    }

    public function onKernelRequest(RequestEvent $event)
    {
        $request = $event->getRequest();
        /** @var Person|null $user */
        $user = $this->security->getUser();
        if (
            (
                $user === null
                || !$event->isMainRequest()
                || $request->isXmlHttpRequest()
                || $request->attributes->get("_firewall_context") !== "security.firewall.map.context.main"
                || $request->attributes->get("_route") === "participant_end_action"
            )
            && $this->requestStack->getMainRequest()->attributes->get("_route") !== "admin_formation_show"
        )
        {
            // don't do anything if it's not the master request
            return;
        }

        $this->participationLogger->handleRequest($request);

    }
}
