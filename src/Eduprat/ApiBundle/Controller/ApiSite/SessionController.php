<?php

namespace Eduprat\ApiBundle\Controller\ApiSite;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\ApiBundle\Controller\ApiController;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\SessionManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;

class SessionController extends ApiController
{
    /**
     * @return JsonResponse
     */
    #[Route(path: 'places-restantes/{reference}', name: 'api_site_places_restantes')]
    public function placesRestantes(string $reference, SessionManager $sessionManager, EntityManagerInterface $entityManager): JsonResponse
    {
        $programmeRepository = $entityManager->getRepository(Programme::class);
        $reference = mb_convert_encoding($reference, 'ISO-8859-1', 'UTF-8');

        $programme = $programmeRepository->findOneBy(['reference' => $reference]);
        if (!$programme) {
            throw new NotFoundHttpException();
        }
        return $this->json($sessionManager->getPlacesRestantesForFormation($programme, SessionManager::DEFAULT_MIN_PLACES_RESTANTES));
    }
}