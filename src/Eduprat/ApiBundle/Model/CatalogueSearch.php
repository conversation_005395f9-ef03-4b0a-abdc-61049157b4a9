<?php

namespace Eduprat\ApiBundle\Model;

use Ed<PERSON><PERSON>\DomainBundle\Entity\Formation;
use Ed<PERSON><PERSON>\DomainBundle\Entity\Programme;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\GroupSequenceProviderInterface;
use Symfony\Component\Validator\Mapping\ClassMetadata;

class CatalogueSearch implements GroupSequenceProviderInterface
{
    const VARIETY_ELEARNING = 'elearning';
    const VARIETY_MIXED = 'mixed';
    const VARIETY_PRESENTIEL = 'presentiel';
    const VARIETIES = [self::VARIETY_ELEARNING, self::VARIETY_MIXED, self::VARIETY_PRESENTIEL];

    /**
     * @var string
     */
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $reference;

    /**
     * @var string
     */
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $title;

    /**
     * @var string
     */
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $titleContains;

    /**
     * @var string
     */
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $category;

    /**
     * @var string[]
     */
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $thematique;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: ['Eduprat\DomainBundle\Services\AdressesService', 'getRegionsLabel'], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $region;


    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: ['Eduprat\DomainBundle\Services\AdressesService', 'getDepartementsLabel'], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $departement = [];

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: ['Eduprat\DomainBundle\Form\ProgrammeType', 'getCategories'], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $categories;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: ['Eduprat\DomainBundle\Form\ProgrammeType', 'getCategories'], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $professions;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: ['Eduprat\DomainBundle\Form\ProgrammeType', 'getFlatSpecialities'], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $specialities;

    /**
     * @var \DateTime
     */
    #[Assert\Date(groups: ['Values'])]
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $startDate;

    /**
     * @var \DateTime
     */
    #[Assert\Date(groups: ['Values'])]
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $endDate;

    /**
     * @var bool
     */
    #[Assert\Type(type: 'bool', groups: ['Types'])]
    public $expert;

    /**
     * @var bool
     */
    #[Assert\Type(type: 'bool', groups: ['Types'])]
    public $certifying;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, callback: 'getAllowedTypes', groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $type;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, choices: Formation::VARIETIES, groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $variety;

    /**
     * @var string
     */
    #[Assert\Choice(['default', 'elearning'], groups: ['Values'])]
    public $format;

    /**
     * @var string
     */
    #[Assert\Type(type: 'string', groups: ['Types'])]
    public $localization;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, choices: [Formation::STATUS_OPENED, Formation::STATUS_CLOSED, Formation::STATUS_FUTURE, Formation::STATUS_ACCESSIBLE], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $status;

    /**
     * @var string[]
     */
    public $formationIds;

    /** @var string */
    public $formationOrder;

    /** @var integer */
    public $distance;

    /**
     * @var string[]
     */
    #[Assert\Choice(multiple: true, choices: [Programme::PRESENCE_SITE, Programme::PRESENCE_VIRTUELLE, Programme::PRESENCE_ELEARNING], groups: ['Values'])]
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $presence;

    /**
     * @var int[]
     */
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $coordinators = [];

    /**
     * @var int[]
     */
    #[Assert\Type(type: 'array', groups: ['Types'])]
    public $financeModes = [];

    public bool $ignoreAndpcStatusRejeteeOrDeposee = false;

    public function __construct()
    {
        $this->formationOrder = "ASC";
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     */
    public function setTitle($title)
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return string
     */
    public function getTitleContains()
    {
        return $this->titleContains;
    }

    /**
     * @param string $titleContains
     */
    public function setTitleContains($titleContains)
    {
        $this->titleContains = $titleContains;
        return $this;
    }

    /**
     * @return string
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param string $category
     */
    public function setCategory($category)
    {
        $this->category = $category;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getRegion()
    {
        return $this->region;
    }

    /**
     * @param string[] $region
     */
    public function setRegion($region)
    {
        $this->region = $region;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getDepartement(): array
    {
        return $this->departement;
    }

    /**
     * @param string[] $departement
     */
    public function setDepartement(array $departement): CatalogueSearch
    {
        $this->departement = $departement;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getCategories()
    {
        return $this->categories;
    }

    /**
     * @param string[] $categories
     */
    public function setCategories($categories)
    {
        $this->categories = $categories;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getProfessions(): ?array
    {
        return $this->professions;
    }

    /**
     * @param string[] $professions
     */
    public function setProfessions(array $professions): void
    {
        $this->professions = $professions;
    }

    /**
     * @return string[]
     */
    public function getSpecialities()
    {
        return $this->specialities;
    }

    /**
     * @param string[] $specialities
     */
    public function setSpecialities($specialities)
    {
        $this->specialities = $specialities;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStartDate()
    {
        return $this->startDate;
    }

    /**
     * @param \DateTime $startDate
     */
    public function setStartDate($startDate)
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEndDate()
    {
        return $this->endDate;
    }

    /**
     * @param \DateTime $endDate
     */
    public function setEndDate($endDate)
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * @return bool
     */
    public function isExpert()
    {
        return $this->expert;
    }

    /**
     * @param bool $expert
     */
    public function setExpert($expert)
    {
        $this->expert = $expert;
        return $this;
    }

    /**
     * @return bool
     */
    public function isCertifying()
    {
        return $this->certifying;
    }

    /**
     * @param bool $certifying
     */
    public function setCertifying($certifying)
    {
        $this->certifying = $certifying;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string[] $type
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getVariety()
    {
        return $this->variety;
    }

    /**
     * @param string[] $variety
     */
    public function setVariety($variety)
    {
        $this->variety = $variety;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getPresence()
    {
        return $this->presence;
    }

    /**
     * @param string[] $presence
     */
    public function setPresence($presence)
    {
        $this->presence = $presence;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormat()
    {
        return $this->format;
    }

    /**
     * @param string $format
     */
    public function setFormat($format)
    {
        $this->format = $format;
        return $this;
    }

    /**
     * @return string
     */
    public function getLocalization()
    {
        return $this->localization;
    }

    /**
     * @param string $localization
     */
    public function setLocalization($localization)
    {
        $this->localization = $localization;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param string[] $status
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getFormationIds()
    {
        return $this->formationIds;
    }

    /**
     * @param string[] $formationIds
     */
    public function setFormationIds($formationIds)
    {
        $this->formationIds = $formationIds;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormationOrder()
    {
        return $this->formationOrder;
    }

    /**
     * @param string $formationOrder
     */
    public function setFormationOrder($formationOrder)
    {
        $this->formationOrder = $formationOrder;
        return $this;
    }

    /**
     * @return integer
     */
    public function getDistance()
    {
        return $this->distance;
    }

    /**
     * @param integer $distance
     */
    public function setDistance($distance)
    {
        $this->distance = $distance;
        return $this;
    }

    /**
     * @return array
     */
    public static function getAllowedTypes()
    {
        return array_map(function($type) {
            return str_replace("formation_", "", $type);
        }, array_keys(Formation::TYPES));
    }

    /**
     * @return array
     */
    public function getTypeClasses() {
        return array_map(function($type) {
            return Formation::getClassByType("formation_" . $type);
        }, $this->type);
    }

    public function getStatuses() {
        if (!$this->status) {
            return null;
        }
        $statuses = array();
        foreach ($this->status as $status) {
            $statuses[$status] = true;
        }
        return $statuses;
    }

    /**
     * @return string
     */
    public function getReference(): ?string
    {
        return $this->reference;
    }

    /**
     * @param string $reference
     */
    public function setReference(?string $reference): void
    {
        $this->reference = $reference;
    }

    /**
     * @return self
     */
    public function addThematique($thematique): CatalogueSearch
    {
        $this->thematique[] = $thematique;
        return $this;
    }

    /**
     * @return string[]
     */
    public function getThematique(): ?array
    {
        return $this->thematique;
    }

    /**
     * @param string[] $thematique
     */
    public function setThematique(array $thematique): void
    {
        $this->thematique = $thematique;
    }

    /**
     * @return int[]
     */
    public function getCoordinators(): array
    {
        return $this->coordinators;
    }

    /**
     * @param int[] $coordinators
     */
    public function setCoordinators(array $coordinators): CatalogueSearch
    {
        $this->coordinators = $coordinators;
        return $this;
    }

    /**
     * @return int[]
     */
    public function getFinanceModes(): array
    {
        return $this->financeModes;
    }

    /**
     * @param int[] $financeModes
     */
    public function setFinanceModes(array $financeModes): CatalogueSearch
    {
        $this->financeModes = $financeModes;
        return $this;
    }

    /**
     * @param ExecutionContextInterface $context
     * @param $payload
     */
    #[Assert\Callback]
    public function validate(ExecutionContextInterface $context, $payload)
    {
        if ($this->getLocalization()) {
            $regex = "/^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/";
            if (!preg_match($regex, $this->getLocalization())) {
                $context->buildViolation('This value is not valid.')
                    ->setInvalidValue($this->getLocalization())
                    ->atPath('localization')
                    ->addViolation();
            }
        }
    }

    public static function loadValidatorMetadata(ClassMetadata $metadata)
    {
        $metadata->setGroupSequenceProvider(true);
    }

    public function getGroupSequence(): array
    {
        return ['Types', 'Values'];
    }

    public function ignoreAndpcStatusRejeteeOrDeposee(): CatalogueSearch
    {
        $this->ignoreAndpcStatusRejeteeOrDeposee = true;
        return $this;
    }

    public function isIgnoreAndpcStatusRejeteeOrDeposee(): bool
    {
        return $this->ignoreAndpcStatusRejeteeOrDeposee;
    }

}