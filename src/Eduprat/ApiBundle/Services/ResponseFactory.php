<?php

namespace Eduprat\ApiBundle\Services;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class ResponseBuilder
 * Normalize HTTP Responses
 */
class ResponseFactory
{

    /**
     * @param array $datas
     * @return JsonResponse
     */
    public function buildAdded(array $datas) {
        $datas = array_merge($datas, array('status' => 'OK',));
        return new JsonResponse($datas, JsonResponse::HTTP_CREATED);
    }

    /**
     * @param array $datas
     * @return JsonResponse
     */
    public function buildAddedWithErrors(array $datas, array $errors) {
        $datas = array_merge($datas, array('status' => 'OK', 'errors' => $errors));
        return new JsonResponse($datas, JsonResponse::HTTP_ACCEPTED);
    }

    /**
     * @param array $errors
     * @return JsonResponse
     */
    public function buildError($errors = array(), $httpCode = JsonResponse::HTTP_BAD_REQUEST) {
        return new JsonResponse(
            array(
                'status' => 'KO',
                (is_array($errors) ? 'errors' : 'error') => $errors
            ),
            $httpCode
        );
    }

    /**
     * @param array $errors
     * @return JsonResponse
     */
    public function buildUnauthorized(array $errors = array()) {
        return new JsonResponse(
            array(
                'status' => 'KO',
                'errors' => $errors
            ),
            JsonResponse::HTTP_UNAUTHORIZED
        );
    }

    /**
     * @param array $data
     * @return JsonResponse
     */
    public function buildSuccess(array $data = array()) {
        $data = array_merge($data, array('status' => 'OK',));
        return new JsonResponse($data, JsonResponse::HTTP_OK);
    }

    /**
     * @param $data
     * @return JsonResponse
     */
    public function buildSuccessFromJson($data): JsonResponse
    {
        return new JsonResponse($data, Response::HTTP_OK, array(), true);
    }

}