<?php

namespace Eduprat\ApiBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Eduprat\DomainBundle\Repository\FormationRepository;
use Symfony\Component\Asset\Packages;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

class ProgrammeManager
{
    private Packages $packages;
    private UploaderHelper $uploaderHelper;
    private EvaluationReporting $evaluationReporting;

    private FormationRepository $formationRepo;

    public function __construct(Packages $packages, UploaderHelper $uploaderHelper, EvaluationReporting $evaluationReporting, EntityManagerInterface $entityManager)
    {
        $this->packages = $packages;
        $this->uploaderHelper = $uploaderHelper;
        $this->evaluationReporting = $evaluationReporting;
        $this->formationRepo = $entityManager->getRepository(Formation::class);
    }

    public function loadImageLink(Programme $programme): array
    {
        $imgInfos = [];
        if ($programme->getFirstAdditionalInfosPictureFile()) {
            $firstAdditionalInfosPictureFile = $this->packages->getUrl($this->uploaderHelper->asset($programme, 'firstAdditionalInfosPictureFile', Programme::class));
            $imgInfos[] = array(
                "picture" => $firstAdditionalInfosPictureFile,
                "link"    => $programme->getFirstAdditionalInfosPictureLink()
            );
        }

        if ($programme->getSecondAdditionalInfosPictureFile()) {
            $secondAdditionalInfosPictureFile = $this->packages->getUrl($this->uploaderHelper->asset($programme, 'secondAdditionalInfosPictureFile', Programme::class));
            $imgInfos[] = array(
                "picture" => $secondAdditionalInfosPictureFile,
                "link"    => $programme->getSecondAdditionalInfosPictureLink()
            );
        }
        return $imgInfos;
    }

    public function loadPicture(Programme $programme): ?string
    {
        $pictureFile = null;
        if ($programme->getPictureFile()) {
            $pictureFile = $this->packages->getUrl($this->uploaderHelper->asset($programme, 'pictureFile', Programme::class));
        }
        return $pictureFile;
    }

    public function loadGroups(Programme $programme): array
    {
        $groupesList = ProgrammeType::getGroupes();
        $groupes = [];
        if (is_array($programme->getCategories()) || is_object($programme->getCategories()))
        {
            if ($programme->getSpecialities()) {
                foreach ($programme->getCategories() as $categorie) {
                    foreach ($programme->getSpecialities() as $specialite) {
                        if (isset($groupesList[$categorie][$specialite]) && !in_array($groupesList[$categorie][$specialite], $groupes)) {
                            $groupes[] = $groupesList[$categorie][$specialite];
                        }
                    }
                }
            }
        }
        return $groupes;
    }

    public function loadNbAnswersAndAverage(Programme $programme): void
    {
        $formations = $this->formationRepo->findAllByProgrammeNotArchived($programme);
        $infosVote = $this->evaluationReporting->getAnswersCountAndAverage($formations);
        $programme->setExportNbVotants($infosVote['votants']);
        $programme->setExportNote($infosVote['average']);
        $countParticipant = 0;
        array_map(function(Formation $formation) use (&$countParticipant) {
            $countParticipant += $formation->getParticipationsComplete()->count();
        }, $formations);
        $programme->setExportNbParticipants($countParticipant);
    }
}