<?php

namespace Eduprat\ApiBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON><PERSON>rat\DomainBundle\Entity\Indemnisation;
use Ed<PERSON>rat\DomainBundle\Entity\IndemnisationPriseEnCharge;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Repository\IndemnisationRepository;
use Symfony\Component\Asset\Packages;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

class IndemnisationManager
{
    private IndemnisationRepository $indemnisationsRepository;
    private Packages $packages;
    private UploaderHelper $uploaderHelper;


    public function __construct(EntityManagerInterface $entityManager, Packages $packages, UploaderHelper $uploaderHelper)
    {
        $this->indemnisationsRepository = $entityManager->getRepository(Indemnisation::class);
        $this->packages = $packages;
        $this->uploaderHelper = $uploaderHelper;
    }

    public function buildIndemnisations(Programme $programme, $groupes, &$prisesEnCharge) : array
    {
        $allIndemnisations = $this->indemnisationsRepository->findBy(array(
            "format" => Programme::FORMATS_RAW[$programme->getFormat()] ?? null,
            "presence" => $programme->getPresence(),
            "formType" => $programme->getFormType(),
            "year" => $programme->getYear(),
        ));

        foreach ($programme->getPrisesEnCharge() as $priseEnCharge) {
            $priseEnChargePictureFile = null;
            if ($priseEnCharge->getPriseEnChargePictureFile()) {
                $priseEnChargePictureFile = $this->packages->getUrl($this->uploaderHelper->asset($priseEnCharge, 'priseEnChargePictureFile', PriseEnCharge::class));
            }

            $indemnisationGroups = array();
            foreach ($allIndemnisations as $indemnisation) {
                if (in_array($indemnisation->getGroup(), $groupes)) {
                    $p = $indemnisation->getPriseEnCharges()->filter(function(IndemnisationPriseEnCharge $indemnisationPriseEnCharge) use ($priseEnCharge, $indemnisation, $programme) {
                        return $indemnisationPriseEnCharge->getPriseEnCharge() == $priseEnCharge && $indemnisation->getNbHours() == $programme->getDurationTotal() && $indemnisationPriseEnCharge->getPrice() != null;
                    })->first();
                    if ($p) {
                        isset($indemnisationGroups[$indemnisation->getGroup()]) ? : $indemnisationGroups[$indemnisation->getGroup()] = [];
                        array_push($indemnisationGroups[$indemnisation->getGroup()], array(
                            "group" => $indemnisation->getGroup(),
                            "nbHours" => $indemnisation->getNbHours(),
                            "price" => $p->getPrice(),
                            "api" => $indemnisation->isSendToApi()
                        ));
                    }
                }
            }

            $indemnisations = array();
            foreach($indemnisationGroups as $indemnisationGroup) {
                if (count($indemnisationGroup) > 1) {
                    foreach($indemnisationGroup as $i) {
                        if($i["api"]) {
                            $indemnisations[] = $i;
                        }
                    }
                } else {
                    $indemnisations[] = $indemnisationGroup[0];
                }
            }

            $prisesEnCharge[] = array(
                "name" => $priseEnCharge->getName(),
                "id"   => $priseEnCharge->getId(),
                "logo" => $priseEnChargePictureFile,
                "prices" => $indemnisations
            );
        }


        $indemnisationGroups = array();
        foreach ($allIndemnisations as $indemnisation) {
            if (in_array($indemnisation->getGroup(), $groupes) && $programme->getDurationTotal() == $indemnisation->getNbHours()) {

                isset($indemnisationGroups[$indemnisation->getGroup()]) ? : $indemnisationGroups[$indemnisation->getGroup()] = [];
                if($indemnisation->getUniquePriseEnCharge()->getPriseEnCharge()->isDpc()) {
                    array_push($indemnisationGroups[$indemnisation->getGroup()], array(
                        "group" => $indemnisation->getGroup(),
                        "nbHour" => $indemnisation->getNbHours(),
                        "price" => $indemnisation->getIndemnisationParticipant(),
                        "api" => $indemnisation->isSendToApi(),
                    ));
                }
            }
        }

        $indemnisations = array();
        foreach($indemnisationGroups as $indemnisationGroup) {
            if (count($indemnisationGroup) > 1) {
                foreach($indemnisationGroup as $i) {
                    if($i["api"]) {
                        $indemnisations[] = $i;
                    }
                }
            } else {
                $indemnisations[] = $indemnisationGroup[0];
            }
        }

        foreach ($prisesEnCharge as &$item) {
            if (isset($item["prices"])) {
                foreach ($item["prices"] as &$price) {
                    unset($price["api"]);
                }
            }
        }
        foreach ($indemnisations as $key => $indemnisation) {
            unset($indemnisations[$key]["api"]);
        }
        return $indemnisations;
    }
}