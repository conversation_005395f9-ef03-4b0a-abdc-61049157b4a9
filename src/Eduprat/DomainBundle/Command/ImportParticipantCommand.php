<?php

namespace Eduprat\DomainBundle\Command;

use Alienor\EmailBundle\Services\EmailAddressBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\CsvImporter;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;
use Psr\Log\LoggerInterface;
use SendinBlue\Client\ApiException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use SSilence\ImapClient\ImapClientException;
use SSilence\ImapClient\ImapClient as Imap;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Twig\Environment;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Console\Command\LockableTrait;
use Webklex\PHPIMAP\ClientManager;
use Webklex\PHPIMAP\Message;

#[AsCommand(name: 'eduprat:import:participant', description: 'Export du dashboard au format csv')]
class ImportParticipantCommand extends Command
{
    use LockableTrait;

    /**
     * @var EntityManagerInterface
     */
    private $em;

    /**
     * @var CsvImporter
     */
    private $csvImporter;

    /**
     * @var Imap
     */
    private $client;

    /**
     * @var boolean
     */
    private $debug;

    /**
     * @var boolean
     */
    private $megadebug;

    /**
     * @var OutputInterface
     */
    private $output;
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;
    /**
     * @var ParameterBag
     */
    private $parameterBag;
    /**
     * @var MailerInterface
     */
    private $mailer;
    /**
     * @var Environment
     */
    private $templating;

    /**
     * @var LoggerInterface
     */
    private $inscriptionsLogger;
    private EmailAddressBuilder $emailAddressBuilder;

    public function __construct(EntityManagerInterface $entityManager, CsvImporter $csvImporter, ParameterBagInterface $parameterBag, MailerInterface $mailer, Environment $templating, LoggerInterface $inscriptionsLogger, EmailAddressBuilder $emailAddressBuilder)
    {
        parent::__construct();
        $this->em = $entityManager;
        $this->csvImporter = $csvImporter;
        $this->parameterBag = $parameterBag;
        $this->mailer = $mailer;
        $this->templating = $templating;
        $this->inscriptionsLogger = $inscriptionsLogger;
        $this->emailAddressBuilder = $emailAddressBuilder;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addOption('debug', 'd', InputOption::VALUE_NONE, 'Debug')
            ->addOption('megadebug', 'm', InputOption::VALUE_NONE, 'Debug more')
        ;
    }

    protected function initialize(InputInterface $input, OutputInterface $output): void
    {
        parent::initialize($input, $output);
        $this->debug = $input->getOption('debug') || $input->getOption('megadebug');
        $this->megadebug = $input->getOption('megadebug');
        $this->output = $output;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (!$this->lock()) {
            $output->writeln('The command is already running in another process.');
            return 0;
        }

        try{
            $this->connect();
            $folder = $this->client->getFolder($this->parameterBag->get('inscriptions.folder'));
            $countUnreadMessages = $folder->query()->unseen()->count();
            if ($this->debug) {
                $output->writeln(sprintf('Dossier <info>%s</info> : <info>%s</info> emails à traiter', $folder, $countUnreadMessages));
            }
            if ($countUnreadMessages > 0) {
                $this->inscriptionsLogger->info(sprintf('%s emails à traiter', $countUnreadMessages));
            }

            $emails = $folder->query()->unseen()->get();
            if (count($emails)) {

                // $emails = array($emails[0]);
                /** @var Email $email */
                foreach ($emails as $email) {
                    $from = $email->getFrom();
                    $mailbox = $from->toArray()[0]->toArray()["mailbox"];
                    $host = $from->toArray()[0]->toArray()["host"];
                    if (($mailbox === "no-reply" || $mailbox === "andpc") && ($host === "agencedpc.fr" || $host === "andpc.fr")) {
                        $this->createParticipationFromEmail($email);
                    }
                }
            }
        } catch (ImapClientException $error){
            $this->inscriptionsLogger->error(sprintf('Erreur : <error>%s</error>', $error->getInfo()));
            $output->writeln(sprintf('Erreur : <error>%s</error>', $error->getInfo()));
        };
        return 0;
    }

    public function connect(): void
    {
        $url= "https://login.microsoftonline.com/" . $this->parameterBag->get('inscription_client_tenant') . "/oauth2/v2.0/token";

        $param_post_curl = [
            'client_id'=> $this->parameterBag->get('inscription_client_id'),
            'client_secret'=> $this->parameterBag->get('inscription_client_secret'),
            'refresh_token'=> $this->parameterBag->get('inscription_client_refresh_token'),
            'grant_type'=>'refresh_token' ];

        $ch=curl_init();

        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_POSTFIELDS, http_build_query($param_post_curl));
        curl_setopt($ch,CURLOPT_POST, 1);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);
        //ONLY USE CURLOPT_SSL_VERIFYPEER AT FALSE IF YOU ARE IN LOCALHOST !!!
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER, false);// NOT IN LOCALHOST ? ERASE IT !

        $oResult=curl_exec($ch);
        $result = json_decode($oResult, true);
        $username = $this->parameterBag->get('inscriptions.email');
        $password = $this->parameterBag->get('inscriptions.password');
        // $encryption = Imap::ENCRYPT_SSL;
        // $this->client = new Imap($mailbox, $username, $result["access_token"], $encryption);

        $cm = new ClientManager();
        $this->client = $cm->make([
            'host'          => $this->parameterBag->get('inscriptions.host'),
            'port'          => 993,
            'encryption'    => 'ssl',
            'validate_cert' => false,
            'username'      => $this->parameterBag->get('inscriptions.email'),
            'password'      => $result["access_token"],
            'protocol'      => 'imap',
            'authentication' => "oauth"
        ]);
        $this->client->connect();
    }

    public function separator(OutputInterface $output): void
    {
        $output->writeln('------------------------------------');
    }

    public function createParticipationFromEmail(Message $email): void
    {

        $subject = $email->getSubject()->__toString();
        $this->inscriptionsLogger->info(sprintf('Traitement du mail "%s" de %s', $subject, $email->getDate()->__toString()));

        // Remove line breaks and double white spaces
        $emailBody = $email->getHTMLBody();
        $emailBody = html_entity_decode($emailBody);
        $body = str_replace(array("\n\r", "\n", "\r"), ' ', $emailBody);
        $body = preg_replace('/\s+/', ' ', $body);
        $body = preg_replace('/\s+/', ' ', $body);
        $body = preg_replace('/\s+/', ' ', $body);

//        if ($subject == "Réponse à une demande d'inscription par un PS") {
//            $this->handleInscriptionMail($emailBody, $body, $email);
//        }
        if ($subject == "Inscription a une session d'une action" || $subject == "Inscription à une session d’une action") {
            $this->handleActionMail($emailBody, $body, $subject, $email);
        } elseif ($subject == "Désinscription à une session d'une action") {
            $this->handleDesinscription($emailBody, $body, $email);
        }

        if (!$this->debug) {
            $this->archiveMessage($email);
        }
    }

    public function archiveMessage(Message $email): void
    {
        $email->setFlag(['Seen']);
        $this->inscriptionsLogger->info(sprintf('Passage en lu du mail "%s" de %s', $email->getSubject()->__toString(), $email->getDate()->__toString()));
        //$state = $this->client->moveMessage($email->getID(), $this->getContainer()->getParameter('inscriptions.archive'));
    }

    public function handleActionMail($emailBody, $body, $subject, Message $email): void
    {

        $errors = array();
        $newMail = $subject == "Inscription à une session d’une action" ? true : false;

        try {
            preg_match_all('/Madame, Monsieur,   (.*) a été inscrit\(e\) à la session (\d*) du .* au .* de l\'action (.*) référence\((\d*)\).   Voici/', $body, $matches);
            preg_match('/Profession : ([^<]+)/u', $emailBody, $profession);
            preg_match('/Spécialité : ([^<]+)/u', $emailBody, $speciality);
            preg_match('/Discipline complémentaire : (.*)/', $emailBody, $discipline);
            preg_match('/Participation Agence DPC : (.*) €/', $body, $price);
            preg_match('/Courriel : ([^<]+)/u', $emailBody, $emailAddress);
            preg_match('/Adresse postale :\r\n\r\n(.*)\r\n\r\nParticipation/s', $emailBody, $address);
            preg_match('/N° RPPS : ([^<]+)/u', $emailBody, $rpps);
            preg_match('/N° ADELI : ([^<]+)/u', $emailBody, $adeli);
            preg_match('/Téléphone : ([^<]+)/u', $emailBody, $phone);
            preg_match('/<a (.*)/', $emailBody, $validationUrl);
            if ($newMail) {
                preg_match_all('/p>Madame, Monsieur,<\/p> ?<p>(.*) souhaite s’inscrire à la session (.*) du .* au .* de l’action (.*) référence\((\d*)\).<\/p> ?<p>Vous/', $body, $matches);
                preg_match('/Spécialité : ([^<]+)/u', $emailBody, $speciality);
                preg_match('/Frais pédagogiques pris en charge par l’Agence du DPC\* : (.*) €/', $body, $price);
            } else {
                $address = explode("\r\n", $address[1]);
                if (count($address) > 3) {
                    $address[0] = sprintf("%s %s", $address[0], $address[1]);
                    $address[1] = $address[2];
                    $address[2] = $address[3];
                }
            }

            list($civility, $firstname, $lastname) = $this->extractNameInfos($matches[1][0]);


            /** @var Formation $formation */
            $formation = $this->em->getRepository(Formation::class)->findByReference($matches[4][0], $matches[2][0]);

            if (!$formation) {
                throw new \Exception(sprintf("Formation not found : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            if ($formation->isLocked()) {
                throw new \Exception(sprintf("Formation cloturée : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            $speciality = trim(str_replace("\r\n", " ", $speciality[1]));
            if ($speciality === "Infirmier Diplômé d'Etat (IDE)") {
                $speciality = "Infirmier Diplômé d’Etat (IDE)";
            }

            $participation = array(
                "Réponse Ps" => "",
                "Votre Réponse" => "",
                "Mode d'exercice" => "",
                "Civilité" => $civility,
                "Nom" => $lastname,
                "Prénom" => $firstname,
                "Part Orga" => $price[1],
                "Date de naissance" => "",
                "Adresse Pro" => $address != [] ? $address[0] : "",
                "Code Postal Pro" => $address != [] ? $address[1] : "",
                "Ville Pro" => $address != [] ? $address[2] : "",
                "N° téléphone" => $phone != [] ? trim($phone[1]) : "",
                "Nom de naissance" => "",
                "N° RPPS" => count($rpps) > 0 ? trim($rpps[1]) : "",
                "N° ADELI" => count($adeli) > 0 ? trim($adeli[1]) : "",
                "E-mail" => $emailAddress[1],
                "Catégorie Professionnelle" => trim($profession[1]),
                "Spécialité" => $speciality,
                "Date action Ps" => "",
                "Date action Organisme" => "",
            );

            $this->inscriptionsLogger->info(sprintf('Traitement inscription : %s %s - %s session %s n°%s', $lastname, $firstname, $emailAddress[1], $matches[4][0], $matches[2][0]));

            if (!$this->debug) {
                $errors = $this->csvImporter->csvToDatabase(array($participation), $formation, false, true, ParticipationHistory::AUTO_TYPE);

                if ($validationUrl[0]) {
                    $crawler = new Crawler($validationUrl[0]);
                    $href = $crawler->filter('a')->first()->attr('href');
                    try {
                        $content = file_get_contents($href);
                    } catch (\Exception $e) {
                        $this->inscriptionsLogger->info(sprintf('Erreur clic sur lien de validation : %s - %s', $e->getMessage(), $e->getTraceAsString()));
                        $this->output->writeln(sprintf("<info>%s %s</info> Erreur clic sur lien de validation dans la formation <info>%s</info> session <info>%s</info> (<comment>%s</comment>)", $firstname, $lastname, $formation ? $formation->getProgramme()->getReference() : "", $formation ? $formation->getSessionNumber() : "", $formation ? $formation->getId() : ""));
                    }
                }

            } else {
                if ($this->megadebug) {
                    $this->output->writeln(sprintf("%s", json_encode($participation, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)));
                }
                $this->output->writeln(sprintf("<info>%s %s</info> Ajouté dans la formation <info>%s</info> session <info>%s</info> (<comment>%s</comment>)", $firstname, $lastname, $formation ? $formation->getProgramme()->getReference() : "", $formation ? $formation->getSessionNumber() : "", $formation ? $formation->getId() : ""));
            }

            if (count($errors) === 0) {
                $this->inscriptionsLogger->info(sprintf('Participant ajouté à la session %s n°%s', $matches[4][0], $matches[2][0]));
            } else {
                throw new \Exception("");
            }

        } catch (\Exception $exception) {
            $this->inscriptionsLogger->info(sprintf('Erreur lors du traitement : %s - %s - %s', json_encode($errors), $exception->getMessage(), $exception->getTraceAsString()));
            if (!$this->debug) {
                $this->handleInscriptionError($email, $exception, $emailBody);
            } else {
                $this->output->writeln(sprintf("<error>Erreur lors du traitement du mail d'inscription</error>"));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("%s", $emailBody));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getMessage()));
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getTraceAsString()));
            }
        }
    }

    public function handleInscriptionMail($emailBody, $body, Message $email): void
    {

        $errors = array();

        try {

            if (strpos($body, 'a refusé son inscription') !== false) {
                return;
            }

            preg_match_all('/Madame, Monsieur,   (.*) a validé son inscription le .* à la session N° (\d*) du .* au .* de la formation (.*). référence : (\d*)./', $body, $matches);
            preg_match('/Profession : (.*)/', $emailBody, $profession);
            preg_match('/Spécialité : (.*)/', $emailBody, $speciality);
            preg_match('/Discipline complémentaire : (.*)/', $emailBody, $discipline);
            preg_match('/Part organisme : (.*) €/', $body, $price);
            preg_match('/Courriel : (.*)\r\n/', $emailBody, $emailAddress);
            preg_match('/Adresse postale :\r\n \r\n (.*)\r\n (.*)\r\n (.*)\r\n \r\n \r\n Part/s', $emailBody, $address);
            preg_match('/N° RPPS : (.*)/', $emailBody, $rpps);
            preg_match('/N° ADELI : (.*)/', $emailBody, $adeli);
            preg_match('/Téléphone : (.*)/', $emailBody, $phone);

            list($civility, $firstname, $lastname) = $this->extractNameInfos($matches[1][0]);

            $formation = $this->em->getRepository(Formation::class)->findByReference($matches[4][0], $matches[2][0]);

            if (!$formation) {
                throw new \Exception(sprintf("Formation not found : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            if ($formation->isLocked()) {
                throw new \Exception(sprintf("Formation cloturée : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            $speciality = trim(str_replace("\r\n", " ", $speciality[1]));
            if ($speciality === "Infirmier Diplômé d'Etat (IDE)") {
                $speciality = "Infirmier Diplômé d’Etat (IDE)";
            }

            $participation = array(
                "Réponse Ps" => "",
                "Votre Réponse" => "",
                "Mode d'exercice" => "",
                "Civilité" => $civility,
                "Nom" => $lastname,
                "Prénom" => $firstname,
                "Part Orga" => $price[1],
                "Date de naissance" => "",
                "Adresse Pro" => $address[1],
                "Code Postal Pro" => $address[2],
                "Ville Pro" => $address[3],
                "N° téléphone" => trim($phone[1]),
                "Nom de naissance" => "",
                "N° RPPS" => count($rpps) > 0 ? trim($rpps[1]) : "",
                "N° ADELI" => count($adeli) > 0 ? trim($adeli[1]) : "",
                "E-mail" => $emailAddress[1],
                "Catégorie Professionnelle" => trim($profession[1]),
                "Spécialité" => $speciality,
                "Date action Ps" => "",
                "Date action Organisme" => "",
            );

            $this->inscriptionsLogger->info(sprintf('Traitement inscription : %s %s - %s session %s n°%s', $lastname, $firstname, $emailAddress[1], $matches[4][0], $matches[2][0]));

            if (!$this->debug) {
                $errors = $this->csvImporter->csvToDatabase(array($participation), $formation, false, true, ParticipationHistory::AUTO_TYPE);
            } else {
                if ($this->megadebug) {
                    $this->output->writeln(sprintf("%s", json_encode($participation, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)));
                }
                $this->output->writeln(sprintf("<info>%s %s</info> Ajouté dans la formation <info>%s</info> session <info>%s</info> (<comment>%s</comment>)", $firstname, $lastname, $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $formation->getId()));
            }

            if (count($errors) === 0) {
                $this->inscriptionsLogger->info(sprintf('Participant ajouté à la session %s n°%s', $matches[4][0], $matches[2][0]));
            } else {
                throw new \Exception("");
            }

        } catch (\Exception $exception) {
            $this->inscriptionsLogger->info(sprintf('Erreur lors du traitement : %s - %s - %s', json_encode($errors), $exception->getMessage(), $exception->getTraceAsString()));
            if (!$this->debug) {
                $this->handleInscriptionError($email, $exception, $emailBody);
            } else {
                $this->output->writeln(sprintf("<error>Erreur lors du traitement du mail d'inscription</error>"));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("%s", $emailBody));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getMessage()));
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getTraceAsString()));
            }

        }

    }

    public function handleDesinscription($emailBody, $body, Message $email): void
    {
        try {
            // Modification 27/01/2023
            // Les numéros de sessions peuvent contenir tous types de caractères, virgule optionnellement présente juste après dans le mail
            preg_match_all('/Madame, Monsieur,<\/p> <p>(.*) s’est désinscrit de la session n°(.*),? débutant le .* et se terminant le .* de votre action de DPC : (.*), référence : (\d*).<\/p>.*?Cordialement,/', $body, $matches);
            list($civility, $firstname, $lastname) = $this->extractNameInfos($matches[1][0]);
            $matches[2][0] = trim($matches[2][0], ",");
            $formation = $this->em->getRepository(Formation::class)->findByReference($matches[4][0], $matches[2][0]);

            if (!$formation) {
                throw new \Exception(sprintf("Formation not found : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            if ($formation->isLocked()) {
                throw new \Exception(sprintf("Formation cloturée : reference : %s - session : %s", $matches[4][0], $matches[2][0]));
            }

            /** @var Participation $participation */
            $participation = $this->em->getRepository(Participation::class)->findByParticipantNameAndFormation($firstname, $lastname, $formation);

            $this->inscriptionsLogger->info(sprintf('Traitement désincription : %s %s session %s n°%s', $firstname, $lastname, $matches[4][0], $matches[2][0]));

            if ($participation) {
                if (!$this->debug) {
                    $participation->setArchived(true);
                    // $commentaire = reprendre le contenu du mail entre les phrases "Motif précisé par le professionnel de santé" et "Cordialement"
                    $participation->addParticipationHistoryUnregister(ParticipationHistory::MOTIF_AUTO_UNSUB, null, ParticipationHistory::AUTO_TYPE);
                    $this->em->persist($participation);
                    $this->em->flush();
                } else {
                    $this->output->writeln(sprintf("<info>%s %s</info> Supprimé de la formation <info>%s</info> session <info>%s</info> (<comment>%s</comment>)", $firstname, $lastname, $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $formation->getId()));
                }
            } else {
                $this->inscriptionsLogger->info(sprintf('Participation non trouvé : %s %s session %s n°%s', $firstname, $lastname, $matches[4][0], $matches[2][0]));
            }
        } catch (\Exception $exception) {
            if (!$this->debug) {
                $this->handleInscriptionError($email, $exception, $emailBody);
            } else {
                $this->output->writeln(sprintf("<error>Erreur lors du traitement du mail de désinscription</error>"));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("%s", $emailBody));
                $this->output->writeln("-----------------------------------");
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getMessage()));
                $this->output->writeln(sprintf("<error>%s</error>", $exception->getTraceAsString()));
            }
        }
    }

    public function handleInscriptionError(Message $email, \Exception $exception, $emailBody): true
    {
        $alienorEmails = array_filter($this->parameterBag->get('inscriptions.error_email'), function($emailBody) {
            return strpos($emailBody, 'alienor') !== false;
        }, ARRAY_FILTER_USE_KEY);

        $otherEmails = array_filter($this->parameterBag->get('inscriptions.error_email'), function($emailBody) {
            return strpos($emailBody, 'alienor') === false;
        }, ARRAY_FILTER_USE_KEY);

        $isSendInBlue = $exception instanceof ApiException;
        $subject = sprintf('Eduprat - Erreur traitement %semail "%s"', $isSendInBlue ? "vers Sendinblue " : "", $email->getSubject()->__toString());

        foreach ([false, true] as $showError) {
            $message = (new Email())
                ->subject($subject)
                ->from(...$this->emailAddressBuilder->buildAddress($this->parameterBag->get('alienor_user.contact_sender')))
                ->to(...$this->emailAddressBuilder->buildAddress($showError ? $alienorEmails : $otherEmails))
                ->html(
                    $this->templating->render(
                        'admin/mail/inscription_error.html.twig',
                        array(
                            'exception' => $exception,
                            'object' => $email->getSubject()->__toString(),
                            'date' => $email->getDate()->__toString(),
                            'message' => $emailBody,
                            'showError' => $showError
                        )
                    ),
                )
            ;
            try {
                $this->mailer->send($message);
                $this->inscriptionsLogger->info(sprintf('Envoi du mail : %s', $message->getSubject()));
            } catch (TransportExceptionInterface) {
            }
        }
        return true;
    }

    /**
     * @param $matches
     * @return array
     */
    public function extractNameInfos($string): array
    {
        $strings = explode(" ", $string);
        $lastnames = array_filter($strings, function ($word) {
            return mb_strtoupper($word, 'utf-8') == $word;
        });
        $lastnameKeys = array_keys($lastnames);
        $civility = implode(" ", array_slice($strings, 0, min($lastnameKeys)));
        $firstname = implode(" ", array_slice($strings, max($lastnameKeys) + 1, count($strings) - max($lastnameKeys)));
        $lastname = implode(" ", $lastnames);
        if (empty($firstname)) {
            $firstname = explode(' ', $lastname, 2)[1];
            $lastname = explode(" ", $lastname)[0];
        }
        return array($civility, $firstname, $lastname);
    }
}
