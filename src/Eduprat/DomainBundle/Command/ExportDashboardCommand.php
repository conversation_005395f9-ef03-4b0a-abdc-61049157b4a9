<?php

namespace Eduprat\DomainBundle\Command;

use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationCongres;
use Ed<PERSON>rat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Services\StatsCalculator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand('eduprat:export:dashboard')]
class ExportDashboardCommand extends Command
{
    /**
     * @var StatsCalculator
     */
    private $statsCalculator;

    public function __construct(StatsCalculator $statsCalculator)
    {
        parent::__construct();
        $this->statsCalculator = $statsCalculator;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Export du dashboard au format csv')
            ->addArgument('year', InputArgument::OPTIONAL, 'Année à exporter');
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $closed = array("f.closed" => true);
        $current = array("f.closed" => false);
        $year = $input->getArgument('year');
        $s = $this->statsCalculator;

        $this->separator($output);
        $output->writeln('ca total ' . $s->getTotalCa($year));
        $this->separator($output);
        $output->writeln('ca eduprat ' . $s->getTotalCa($year, array(), false, [FormationAudit::class, FormationPresentielle::class]));
        $output->writeln('ca cms ' . $s->getTotalCa($year, array(), true));
        $output->writeln('ca sedd ' . $s->getTotalCa($year, array(), false, [FormationSedd::class]));
        $output->writeln('ca congres ' . $s->getTotalCa($year, array(), false, [FormationCongres::class]));
        $this->separator($output);
        $output->writeln('ca eduprat en cours ' . $s->getTotalCa($year, $current, false, [FormationAudit::class, FormationPresentielle::class]));
        $output->writeln('ca eduprat réalisé ' . $s->getTotalCa($year, $closed, false, [FormationAudit::class, FormationPresentielle::class]));
        $this->separator($output);
        $output->writeln('ca cms en cours ' . $s->getTotalCa($year, $current, true));
        $output->writeln('ca cms réalisé ' . $s->getTotalCa($year, $closed, true));
        $this->separator($output);
        $output->writeln('ca SEDD en cours ' . $s->getTotalCa($year, $current, false, [FormationSedd::class]));
        $output->writeln('ca SEDD réalisé ' . $s->getTotalCa($year, $closed, false, [FormationSedd::class]));
        $this->separator($output);
        $output->writeln('ca Congrés en cours ' . $s->getTotalCa($year, $current, false, [FormationCongres::class]));
        $output->writeln('ca Congrés réalisé ' . $s->getTotalCa($year, $closed, false, [FormationCongres::class]));
        $this->separator($output);
        $output->writeln('nb formations total ' . $s->getFormationCount($year));
        $this->separator($output);
        $output->writeln('nb formations eduprat ' . $s->getFormationCount($year, array(), false, [FormationAudit::class, FormationPresentielle::class]));
        $output->writeln('nb formations cms ' . $s->getFormationCount($year, array(), true));
        $output->writeln('nb formations cms en cours ' . $s->getFormationCount($year, $current, true));
        $output->writeln('nb formations cms réalisée ' . $s->getFormationCount($year, $closed, true));
        $output->writeln('nb formations sedd ' . $s->getFormationCount($year, array(), false, [FormationSedd::class]));
        $output->writeln('nb formations sedd en cours ' . $s->getFormationCount($year, $current, false, [FormationSedd::class]));
        $output->writeln('nb formations sedd réalisé ' . $s->getFormationCount($year, $closed, false, [FormationSedd::class]));
        $output->writeln('nb formations congres ' . $s->getFormationCount($year, array(), false, [FormationCongres::class]));
        $output->writeln('nb formations congres en cours ' . $s->getFormationCount($year, $current, false, [FormationCongres::class]));
        $output->writeln('nb formations congres réalisé ' . $s->getFormationCount($year, array(), false, [FormationCongres::class]));
        $output->writeln('nb formations audit ' . $s->getFormationCount($year, array(), false, [FormationAudit::class]));
        $output->writeln('nb formations audit en cours ' . $s->getFormationCount($year, $current, false, [FormationAudit::class]));
        $output->writeln('nb formations audit réalisé ' . $s->getFormationCount($year, $closed, false, [FormationAudit::class]));
        $output->writeln('nb formations presentielle ' . $s->getFormationCount($year, array(), false, [FormationPresentielle::class]));
        $output->writeln('nb formations presentielle en cours ' . $s->getFormationCount($year, $current, false, [FormationPresentielle::class]));
        $output->writeln('nb formations presentielle réalisé ' . $s->getFormationCount($year, $closed, false, [FormationPresentielle::class]));
        $this->separator($output);
        $output->writeln('nb ps en cours de formation ' . $s->getParticipantCount($year, $current));
        $output->writeln('nb ps formés ' . $s->getParticipantCount($year, $closed));
        $output->writeln('nb moyen stagiaires par formation ' . $s->getAvgParticipantCount($year));
        $output->writeln('nb moyen medecins par formation ' . $s->getAvgParticipantCount($year, array(), true));
        $output->writeln('nb moyen autres par formation ' . $s->getAvgParticipantCount($year, array(), false));
        $this->separator($output);
        $output->writeln('nb medecins en cours de formation ' . $s->getParticipantCount($year, $current, true));
        $output->writeln('nb autres en cours de formation ' . $s->getParticipantCount($year, $current, false));
        $this->separator($output);
        $output->writeln('nb medecins formés ' . $s->getParticipantCount($year, $closed, true));
        $output->writeln('nb autres formés ' . $s->getParticipantCount($year, $closed, false));
        $this->separator($output);
        $output->writeln('nb medecins en cours de formation audit ' . $s->getParticipantCount($year, $current, true, [FormationAudit::class]));
        $output->writeln('nb autres en cours de formation audit ' . $s->getParticipantCount($year, $current, false, [FormationAudit::class]));
        $this->separator($output);
        $output->writeln('nb medecins en cours de formation presentielle ' . $s->getParticipantCount($year, $current, true, [FormationPresentielle::class]));
        $output->writeln('nb autres en cours de formation presentielle ' . $s->getParticipantCount($year, $current, false, [FormationPresentielle::class]));
        $this->separator($output);
        $output->writeln('nb medecins formés audit ' . $s->getParticipantCount($year, $closed, true, [FormationAudit::class]));
        $output->writeln('nb autres formés audit ' . $s->getParticipantCount($year, $closed, false, [FormationAudit::class]));
        $this->separator($output);
        $output->writeln('nb medecins formés presentielle ' . $s->getParticipantCount($year, $closed, true, [FormationPresentielle::class]));
        $output->writeln('nb autres formés presentielle ' . $s->getParticipantCount($year, $closed, false, [FormationPresentielle::class]));
        $this->separator($output);
        $output->writeln('total honoraire cr ' . $s->getTotalHonoraryCr($year));
        $this->separator($output);
        $output->writeln('total honoraire cr previsionnel ' . $s->getTotalHonoraryCr($year, $current));
        $output->writeln('total honoraire cr dû ' . $s->getTotalHonoraryCr($year, $closed));
        $this->separator($output);
        $medecinsCurrentSpeciality = $s->getParticipantCountPerSpeciality($year, $current, true);
        foreach ($medecinsCurrentSpeciality as $key => $item) {
            $output->writeln('nb medecins en cours de formation  - spécialité : '. $key . ' ' . $item);
        }
        return Command::SUCCESS;
    }

    public function separator(OutputInterface $output) {
        $output->writeln('------------------------------------');
    }
}
