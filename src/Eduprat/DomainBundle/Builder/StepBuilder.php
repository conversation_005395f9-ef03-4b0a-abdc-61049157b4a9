<?php

namespace Eduprat\DomainBundle\Builder;

use Ed<PERSON>rat\DomainBundle\Entity\Step;

class StepBuilder
{
    /**
     * @var ModuleBuilder[]
     */
    private array $modules = [];
    private int $position = 1;

    public static function aStep(): StepBuilder
    {
        return new StepBuilder();
    }

    public function withModules(ModuleBuilder ...$modules): StepBuilder
    {
        $this->modules = $modules;
        return $this;
    }

    public function atPosition(int $position): StepBuilder
    {
        $this->position = $position;
        return $this;
    }

    public function build(): Step
    {
        $modules = [];
        foreach ($this->modules as $moduleBuilder) {
            $modules[] = $moduleBuilder->build();
        }
        return new Step($modules, $this->position);
    }
}
