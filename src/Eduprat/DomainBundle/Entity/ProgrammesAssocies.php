<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Eduprat\DomainBundle\Repository\ProgrammesAssociesRepository;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Validator\ProgrammeAssociesContraint;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

/**
 * @ProgrammeAssociesContraint()
 */
#[ORM\Entity(repositoryClass: ProgrammesAssociesRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[UniqueEntity(fields: ['programmeElearning'], message: 'Ce programme est déjà associé à un autre programme.')]
class ProgrammesAssocies
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme')]
    private ?Programme $programmeElearning = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme')]
    private ?Programme $programmeClasseVirtuelle = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme')]
    private ?Programme $programmeSurSite = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProgrammeElearning(): ?Programme
    {
        return $this->programmeElearning;
    }

    public function setProgrammeElearning(?Programme $programmeElearning): ProgrammesAssocies
    {
        $this->programmeElearning = $programmeElearning;
        return $this;
    }

    public function getProgrammeClasseVirtuelle(): ?Programme
    {
        return $this->programmeClasseVirtuelle;
    }

    public function setProgrammeClasseVirtuelle(?Programme $programmeClasseVirtuelle): ProgrammesAssocies
    {
        $this->programmeClasseVirtuelle = $programmeClasseVirtuelle;
        return $this;
    }

    public function getProgrammeSurSite(): ?Programme
    {
        return $this->programmeSurSite;
    }

    public function setProgrammeSurSite(?Programme $programmeSurSite): ProgrammesAssocies
    {
        $this->programmeSurSite = $programmeSurSite;
        return $this;
    }

    public function hasMin2Programme(): bool
    {
        $count = 0;
        if ($this->programmeElearning) {
            $count++;
        }
        if ($this->programmeSurSite) {
            $count++;
        }
        if ($this->programmeClasseVirtuelle) {
            $count++;
        }
        return $count >= 2;
    }

    public function synchro(): void
    {
        if ($this->programmeElearning) {
            $this->programmeElearning->setProgrammesAssocies($this);
        }
        if ($this->programmeSurSite) {
            $this->programmeSurSite->setProgrammesAssocies($this);
        }
        if ($this->programmeClasseVirtuelle) {
            $this->programmeClasseVirtuelle->setProgrammesAssocies($this);
        }
    }
}
