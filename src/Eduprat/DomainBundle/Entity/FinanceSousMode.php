<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;

/**
 * FinanceSousMode
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FinanceSousModeRepository')]
#[ORM\Table(name: 'finance_sous_mode')]
class FinanceSousMode
{

    const OPCO_EP = "OPCO EP";
    const FIF_PL = "FIF PL";
    const FAF_PM = "FAF PM";
    const ANDPC = "ANDPC";
    const PARCOURS_DPC = "Autre - Parcours DPC";
    const PARCOURS_HORS_DPC = "Autre - Parcours HORS DPC";


    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'name', type: Types::STRING, length: 255, nullable: true)]
    private ?string $name = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zipCode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zipCode = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'city', type: Types::STRING, length: 255, nullable: true)]
    private ?string $city = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'identifiant', type: Types::STRING, length: 255, nullable: true)]
    private ?string $identifiant = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'companyName', type: Types::STRING, length: 255, nullable: true)]
    private ?string $companyName = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'siren', type: Types::STRING, length: 255, nullable: true)]
    private ?string $siren = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsLastname', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsLastname = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsFirstname', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsFirstname = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsTitle', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsTitle = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsPhone', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsPhone = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsMail', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsMail = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsAddress', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsAddress = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsAddress2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsAddress2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsZip', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsZip = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpsCity', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpsCity = null;

    /**
     * @var FinanceMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceMode', inversedBy: 'financeSousModes', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'financeMode', nullable: false)]
    private ?FinanceMode $financeMode = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'actalians', type: Types::BOOLEAN, nullable: true)]
    private ?bool $actalians = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'priseEnCharge', type: Types::STRING)]
    private ?string $priseEnCharge = null;

    /**
     * @var Collection<int, Formation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', mappedBy: 'financeSousModes')]
    private Collection $formations;

    /**
     * @var Collection<int, Participation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', mappedBy: 'financeSousMode', cascade: ['persist'])]
    protected Collection $participations;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->participations = new ArrayCollection();
        $this->formations = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     *
     * @return FinanceSousMode
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set address
     *
     * @param string $address
     *
     * @return FinanceSousMode
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * @param string $address2
     * @return FinanceSousMode
     */
    public function setAddress2($address2)
    {
        $this->address2 = $address2;
        return $this;
    }

    /**
     * Set zipCode
     *
     * @param string $zipCode
     *
     * @return FinanceSousMode
     */
    public function setZipCode($zipCode)
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    /**
     * Get zipCode
     *
     * @return string
     */
    public function getZipCode()
    {
        return $this->zipCode;
    }

    /**
     * Set city
     *
     * @param string $city
     *
     * @return FinanceSousMode
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @return string
     */
    public function getIdentifiant()
    {
        return $this->identifiant;
    }

    /**
     * @param string $identifiant
     */
    public function setIdentifiant($identifiant)
    {
        $this->identifiant = $identifiant;

        return $this;
    }

    /**
     * @return string
     */
    public function getCompanyName()
    {
        return $this->companyName;
    }

    /**
     * @param string $companyName
     * @return FinanceSousMode
     */
    public function setCompanyName($companyName)
    {
        $this->companyName = $companyName;
        return $this;
    }

    /**
     * @return string
     */
    public function getSiren()
    {
        return $this->siren;
    }

    /**
     * @param string $siren
     */
    public function setSiren($siren)
    {
        $this->siren = $siren;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsLastname()
    {
        return $this->rpsLastname;
    }

    /**
     * @param string $rpsLastname
     * @return FinanceSousMode
     */
    public function setRpsLastname($rpsLastname)
    {
        $this->rpsLastname = $rpsLastname;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsFirstname()
    {
        return $this->rpsFirstname;
    }

    /**
     * @param string $rpsFirstname
     * @return FinanceSousMode
     */
    public function setRpsFirstname($rpsFirstname)
    {
        $this->rpsFirstname = $rpsFirstname;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsTitle()
    {
        return $this->rpsTitle;
    }

    /**
     * @param string $rpsTitle
     * @return FinanceSousMode
     */
    public function setRpsTitle($rpsTitle)
    {
        $this->rpsTitle = $rpsTitle;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsPhone()
    {
        return $this->rpsPhone;
    }

    /**
     * @param string $rpsPhone
     * @return FinanceSousMode
     */
    public function setRpsPhone($rpsPhone)
    {
        $this->rpsPhone = $rpsPhone;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsMail()
    {
        return $this->rpsMail;
    }

    /**
     * @param string $rpsMail
     * @return FinanceSousMode
     */
    public function setRpsMail($rpsMail)
    {
        $this->rpsMail = $rpsMail;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsAddress()
    {
        return $this->rpsAddress;
    }

    /**
     * @param string $rpsAddress
     * @return FinanceSousMode
     */
    public function setRpsAddress($rpsAddress)
    {
        $this->rpsAddress = $rpsAddress;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsAddress2()
    {
        return $this->rpsAddress2;
    }

    /**
     * @param string $rpsAddress2
     * @return FinanceSousMode
     */
    public function setRpsAddress2($rpsAddress2)
    {
        $this->rpsAddress2 = $rpsAddress2;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsZip()
    {
        return $this->rpsZip;
    }

    /**
     * @param string $rpsZip
     * @return FinanceSousMode
     */
    public function setRpsZip($rpsZip)
    {
        $this->rpsZip = $rpsZip;
        return $this;
    }

    /**
     * @return string
     */
    public function getRpsCity()
    {
        return $this->rpsCity;
    }

    /**
     * @param string $rpsCity
     * @return FinanceSousMode
     */
    public function setRpsCity($rpsCity)
    {
        $this->rpsCity = $rpsCity;
        return $this;
    }


    /**
     * Set financeMode
     *
     * @param FinanceMode $financeMode
     *
     * @return FinanceSousMode
     */
    public function setFinanceMode(FinanceMode $financeMode)
    {
        $this->financeMode = $financeMode;

        return $this;
    }

    /**
     * Get financeMode
     *
     * @return FinanceMode
     */
    public function getFinanceMode()
    {
        return $this->financeMode;
    }

    /**
     * @return bool
     */
    public function isActalians()
    {
        return $this->priseEnCharge == $this::OPCO_EP;
    }

     /**
     * @return bool
     */
    public function isFif()
    {
        return $this->priseEnCharge == $this::FIF_PL;
    }
    
    /**
     * @return bool
     */
    public function isDpc()
    {
        return $this->priseEnCharge == $this::ANDPC ||  $this->priseEnCharge == $this::FIF_PL ||  $this->priseEnCharge == $this::PARCOURS_DPC;
    }

    /**
     * @return bool
     */
    public function isAndpc()
    {
        return $this->priseEnCharge == $this::ANDPC;
    }

    /**
     * @return bool
     */
    public function isHorsDPC() {
        return $this->priseEnCharge == $this::FAF_PM ||  $this->priseEnCharge == $this::PARCOURS_HORS_DPC;
    }

    public function showAttestation() {
        return $this->priseEnCharge != $this::FIF_PL &&  $this->priseEnCharge != $this::PARCOURS_HORS_DPC &&  $this->priseEnCharge != $this::OPCO_EP;
    }

    public static function attestationMandatory() {
        return [
            self::FIF_PL,
            self::PARCOURS_HORS_DPC,
            self::OPCO_EP
        ];
    }

    /**
     * @param bool $actalians
     * @return FinanceSousMode
     */
    public function setActalians($actalians)
    {
        $this->actalians = $actalians;
        return $this;
    }

    /**
     * @return string
     */
    public function getPriseEnCharge()
    {
        return $this->priseEnCharge;
    }

    /**
     * @param string $priseEnCharge
     * @return FinanceSousMode
     */
    public function setPriseEnCharge($priseEnCharge)
    {
        $this->priseEnCharge = $priseEnCharge;
        return $this;
    }

    /**
     * Add formations
     *
     * @param Formation $formations
     * @return FinanceSousMode
     */
    public function addFormation(Formation $formations)
    {
        $this->formations[] = $formations;

        return $this;
    }

    /**
     * Remove formations
     *
     * @param Formation $formations
     */
    public function removeFormation(Formation $formations)
    {
        $this->formations->removeElement($formations);
    }

    /**
     * Get formations
     *
     * @return Collection
     */
    public function getFormations()
    {
        return $this->formations->matching(Criteria::create()->where(Criteria::expr()->eq('archived', false)));
    }

    /**
     * Get participations
     *
     * @return Collection
     */
    public function getParticipations()
    {
        return $this->participations;
    }
}

