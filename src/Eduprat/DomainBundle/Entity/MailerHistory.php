<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * MailerHistory
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\MailerHistoryRepository')]
#[ORM\Table(name: 'mailer_history')]
class MailerHistory
{
    const TYPE_MANUAL = "manual";
    const TYPE_AUTO = "auto";
    public const RESULT_FAILED = 0x1000;
    public const RESULT_SUCCESS = 0x0010;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Participation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', inversedBy: 'mailerHistory', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participation', nullable: true, onDelete: 'CASCADE')]
    private ?Participation $participation = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'subject', type: Types::TEXT, nullable: true)]
    private ?string $subject = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'type', type: Types::STRING, nullable: true)]
    private ?string $type = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'sender', nullable: true, onDelete: 'SET NULL')]
    private ?Person $sender = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'send_event', type: Types::STRING, nullable: true)]
    private ?string $sendEvent;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'destinataire', nullable: true, onDelete: 'SET NULL')]
    private ?Person $destinataire;


    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'session', nullable: true, onDelete: 'SET NULL')]
    private ?Formation $session;

    /**
     * @var string
     */
    #[ORM\Column(name: 'alias', type: Types::STRING, nullable: true)]
    private ?string $alias;


    #[ORM\PreUpdate]
    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set participation
     *
     * @param Participation $participation
     *
     * @return MailerHistory
     */
    public function setParticipation(Participation $participation)
    {
        $this->participation = $participation;

        return $this;
    }

    /**
     * Get participation
     *
     * @return string
     */
    public function getParticipation()
    {
        return $this->participation;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     * @return MailerHistory
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return string
     */
    public function getSubject()
    {
        return $this->subject;
    }

    /**
     * @param string $subject
     * @return MailerHistory
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;
        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return MailerHistory
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return Person
     */
    public function getSender()
    {
        return $this->sender;
    }

    /**
     * @param Person $sender
     * @return MailerHistory
     */
    public function setSender($sender)
    {
        $this->sender = $sender;
        return $this;
    }

    public function getSendEvent(): ?string
    {
        return $this->sendEvent;
    }

    public function setSendEvent(?string $sendEvent): MailerHistory
    {
        $this->sendEvent = $sendEvent;
        return $this;
    }

    public function getDestinataire(): ?Person
    {
        return $this->destinataire;
    }

    public function setDestinataire(?Person $destinataire): MailerHistory
    {
        $this->destinataire = $destinataire;
        return $this;
    }

    public function getSession(): ?Formation
    {
        return $this->session;
    }

    public function setSession(?Formation $session): MailerHistory
    {
        $this->session = $session;
        return $this;
    }

    public function getAlias(): ?string
    {
        return $this->alias;
    }

    public function setAlias(?string $alias): MailerHistory
    {
        $this->alias = $alias;
        return $this;
    }
}

