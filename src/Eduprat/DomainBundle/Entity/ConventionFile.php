<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * ConventionFiles
 */
#[ORM\Entity]
#[ORM\Table(name: 'convention_file')]
class ConventionFile
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'conventionFiles', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var FinanceSousMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceSousMode', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'finance_sous_mode', nullable: false, onDelete: 'CASCADE')]
    private ?FinanceSousMode $financeSousMode = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'convention', type: Types::STRING, length: 255, nullable: true)]
    private ?string $convention = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'convention_original_name', type: Types::STRING, length: 255, nullable: true)]
    private ?string $conventionOriginalName = null;

    /**
     * @var File
     *
     */
    private $conventionFile;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     *
     * @return ConventionFile
     */
    public function setFormation($formation)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * @return FinanceSousMode
     */
    public function getFinanceSousMode()
    {
        return $this->financeSousMode;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     * @return ConventionFile
     */
    public function setFinanceSousMode($financeSousMode)
    {
        $this->financeSousMode = $financeSousMode;
        return $this;
    }

    /**
     * Set topo
     *
     * @param string $convention
     *
     * @return ConventionFile
     */
    public function setConvention($convention)
    {
        $this->convention = $convention;

        return $this;
    }

    /**
     * Get topo
     *
     * @return string
     */
    public function getConvention()
    {
        return $this->convention;
    }

    /**
     * Set topoOriginalName
     *
     * @param string $conventionOriginalName
     *
     * @return ConventionFile
     */
    public function setConventionOriginalName($conventionOriginalName)
    {
        $this->conventionOriginalName = $conventionOriginalName;

        return $this;
    }

    /**
     * Get topoOriginalName
     *
     * @return string
     */
    public function getConventionOriginalName()
    {
        return $this->conventionOriginalName;
    }

    /**
     * @return File
     */
    public function getConventionFile()
    {
        return $this->conventionFile;
    }

    /**
     * @param File $conventionFile
     */
    public function setConventionFile(File $conventionFile = null)
    {
        $this->conventionFile = $conventionFile;
        if ($this->conventionFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Formation
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @return string
     */
    public function getToken() {
        return md5(sprintf('%s%s',
            $this->getId(), $this->getFormation()->getId()
        ));
    }
}

