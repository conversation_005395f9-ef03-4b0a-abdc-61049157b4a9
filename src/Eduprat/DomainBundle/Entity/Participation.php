<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\Criteria;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\TCS\GroupeQuestionTCS;
use Eduprat\DomainBundle\Entity\TCS\ParticipationAnswerTCS;
use Eduprat\DomainBundle\Entity\TCS\QuestionTCS;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * Participation
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ParticipationRepository')]
#[ORM\Table(name: 'participation')]
#[ORM\HasLifecycleCallbacks()]
class Participation implements ObjectWithTokenInterface
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'participations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false)]
    private ?Formation $formation = null;

    /**
     * @var Participant
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', inversedBy: 'participations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participant', nullable: false)]
    private ?Participant $participant = null;

    /**
     * @var FinanceSousMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceSousMode', inversedBy: 'participations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'financeSousMode', nullable: true)]
    private ?FinanceSousMode $financeSousMode = null;

    /**
     * @var Participant
     */
    #[ORM\Column(name: 'price', type: Types::FLOAT, nullable: true)]
    private ?float $price = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'price_year_n1', type: Types::FLOAT)]
    private ?float $priceYearN1 = 0;

    /**
     * @var Collection<int, AuditAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\AuditAnswer', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $auditAnswers;

    /**
     * @var Collection<int, SurveyAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\SurveyAnswer', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $surveyAnswers;

    /**
     * @var Collection<int, EvaluationFormationAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\EvaluationFormationAnswer', mappedBy: 'participation', cascade: ['persist', 'remove'])]
    private Collection $evaluationsFormation;

    /**
     * @var Collection<int, EvaluationFormerAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\EvaluationFormerAnswer', mappedBy: 'participation', cascade: ['persist', 'remove'])]
    private Collection $evaluationsFormer;

    /**
     * @var Collection<int, ParticipationAnswerTCS>
     */
    #[ORM\OneToMany(mappedBy: 'participation', targetEntity: ParticipationAnswerTCS::class, cascade: ['persist', 'remove'], fetch: "LAZY")]
    private Collection $participationTCSAnswers;

    /**
     * @var Collection<int, PatientDescription>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\PatientDescription', mappedBy: 'participation', cascade: ['persist', 'remove'])]
    private Collection $patientsDescriptions;

    /**
     * @var Collection<int, ParticipationHistory>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ParticipationHistory', mappedBy: 'participation', cascade: ['persist', 'remove'])]
    private Collection $participationHistories;

    /**
     * @var Collection<int, ParticipationLog>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ParticipationLog', mappedBy: 'participation', cascade: ['persist', 'remove'])]
    private Collection $participationLogs;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'completedSurvey1', type: Types::BOOLEAN)]
    private ?bool $completedSurvey1 = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'completedSurvey2', type: Types::BOOLEAN)]
    private ?bool $completedSurvey2 = false;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startedAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'finishedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $finishedAt = null;

    /**
     * @var \int
     */
    #[ORM\Column(name: 'totalTime', type: Types::INTEGER, nullable: true)]
    private ?int $totalTime = null;
    private string $totalTimeText = '';


    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startedAtZoom', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startedAtZoom = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'finishedAtZoom', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $finishedAtZoom = null;

    /**
     * @var int
     */
    #[ORM\Column(name: 'totalTimeZoom', type: Types::INTEGER, nullable: true)]
    private ?int $totalTimeZoom = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'ip', type: Types::STRING, length: 255, nullable: true)]
    private ?string $ip = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'auditsCompleted', type: Types::BOOLEAN)]
    private ?bool $auditsCompleted = false;

    /**
      * @var bool
      */
     #[ORM\Column(name: 'validTimeAudit1', type: Types::BOOLEAN)]
     private ?bool $validTimeAudit1 = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'validTimeAudit2', type: Types::BOOLEAN)]
    private ?bool $validTimeAudit2 = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'elearningPhaseDone', type: Types::BOOLEAN)]
    private ?bool $elearningPhaseDone = false;

    /**
     * @var int
     */
    #[ORM\Column(name: 'nbHour', type: Types::INTEGER)]
    private ?int $nbHour = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'budgetCR', type: Types::FLOAT, nullable: true)]
    private ?float $budgetCR = null;

    /**
     * @var Coordinator
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', inversedBy: 'participations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'coordinator', nullable: true, onDelete: 'SET NULL')]
    private ?Coordinator $coordinator = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'exerciseMode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $exerciseMode = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'archived', type: Types::BOOLEAN, length: 255, options: ['default' => false])]
    private ?bool $archived = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\ParticipantActionSheet', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    protected ?ParticipantActionSheet $participantActionSheet = null;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\EtutoratCasClinique', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    protected ?EtutoratCasClinique $etutoratCasClinique = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'uga', type: Types::STRING, length: 255, nullable: true)]
    private ?string $uga = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'completedForm1', type: Types::BOOLEAN)]
    private ?bool $completedForm1 = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'completedForm2', type: Types::BOOLEAN)]
    private ?bool $completedForm2 = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'completedForms', type: Types::BOOLEAN)]
    private ?bool $completedForms = false;

    #[ORM\Column(name: 'course', type: Types::JSON, nullable: false)]
    private ?array $course = array();

    #[ORM\Column(name: 'elearningCourse', type: Types::JSON, nullable: false)]
    private ?array $elearningCourse = array();

    /**
     * @var Collection<int, FicheAction>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FicheAction', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $ficheActions;

    /**
     * @var Collection<int, FicheProgression>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FicheProgression', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $ficheProgressions;

    /**
     * @var Collection<int, Etutorat>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Etutorat', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $etutorats;

    /**
     * @var Collection<int, FicheSynthese>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FicheSynthese', mappedBy: 'participation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $ficheSyntheses;

    /**
     * @var string
     */
    #[ORM\Column(name: 'attestationHonneur', type: Types::STRING, length: 255, nullable: true)]
    private ?string $attestationHonneur = null;

    /**
     * @var File
     */
    #[Vich\UploadableField(mapping: 'participation_attestationHonneur', fileNameProperty: 'attestationHonneur')]
    private $attestationHonneurFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'attestationHonneurN1', type: Types::STRING, length: 255, nullable: true)]
    private ?string $attestationHonneurN1 = null;

    #[Vich\UploadableField(mapping: 'participation_attestationHonneurN1', fileNameProperty: 'attestationHonneurN1')]
    private $attestationHonneurFileN1;

    /**
     * @var string
     */
    #[ORM\Column(name: 'etutoratAttentes', type: Types::TEXT, nullable: true)]
    private ?string $etutoratAttentes = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'etutorat2Message', type: Types::TEXT, nullable: true)]
    private ?string $etutorat2Message = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'etutorat3Message', type: Types::TEXT, nullable: true)]
    private ?string $etutorat3Message = null;

    #[ORM\Column(name: 'nextModule', type: Types::TEXT, nullable: true)]
    protected ?string $nextModule = null;

    #[ORM\Column(name: 'partenariat', type: Types::STRING, nullable: true)]
    private ?string $partenariat = null;

    #[ORM\Column(name: 'ots', type: Types::STRING, nullable: true)]
    private ?string $ots = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'firstPageEtutoCompleted', type: Types::BOOLEAN, nullable: true)]
    private ?bool $firstPageEtutoCompleted = false;

    #[ORM\Column(name: 'isLead', type: Types::BOOLEAN, nullable: true, options: ['default' => false])]
    private ?bool $isLead = false;

    #[ORM\Column(name: 'lastToolFile', type: Types::INTEGER, nullable: true)]
    private ?int $lastToolFile = null;

    #[ORM\Column(name: 'lastDocumentsPedagogiquesFile', type: Types::INTEGER, nullable: true)]
    private ?int $lastDocumentsPedagogiquesFile = null;

    /**
     * Défini si le module identifiant la fin du parcours obligatoire est complété
     */
    #[ORM\Column(name: 'course_ended', type: Types::BOOLEAN, nullable: false)]
    private bool $courseEnded = false;

    #[ORM\Column(name: 'date_module_manquant_expiration', type: 'datetime', nullable: true)]
    private ?\DateTime $dateModuleManquantExpiration = null;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->evaluationsFormation = new ArrayCollection();
        $this->evaluationsFormer = new ArrayCollection();
        $this->participationHistories = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->auditAnswers = new ArrayCollection();
        $this->surveyAnswers = new ArrayCollection();
        $this->participationTCSAnswers = new ArrayCollection();
        $this->patientsDescriptions = new ArrayCollection();
        $this->participationLogs = new ArrayCollection();
        $this->ficheActions = new ArrayCollection();
        $this->ficheSyntheses = new ArrayCollection();
        $this->ficheProgressions = new ArrayCollection();
        $this->etutorats = new ArrayCollection();
        $this->archived = false;
        $this->course = array();
        $this->elearningCourse = array();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return self
     */
    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return Participant
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return Participation
     */
    public function setPrice($price)
    {
        $this->price = $price;

        return $this;
    }

    /**
     * @return float|int
     */
    public function getPriceYearN1()
    {
        return $this->priceYearN1;
    }

    /**
     * @param float|int $priceYearN1
     */
    public function setPriceYearN1($priceYearN1): Participation
    {
        $this->priceYearN1 = $priceYearN1;
        return $this;
    }

    public function setFormation(Formation $formation): Participation
    {
        $this->formation = $formation;

        return $this;
    }

    public function getFormation(): Formation
    {
        return $this->formation;
    }

    /**
     * Set participant
     *
     * @param Participant $participant
     * @return Participation
     */
    public function setParticipant(Participant $participant)
    {
        $this->participant = $participant;

        return $this;
    }

    /**
     * Get participant
     *
     * @return Participant
     */
    public function getParticipant()
    {
        return $this->participant;
    }

    /**
     * @return FinanceSousMode
     */
    public function getFinanceSousMode()
    {
        return $this->financeSousMode;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     */
    public function setFinanceSousMode($financeSousMode): Participation
    {
        $this->financeSousMode = $financeSousMode;
        return $this;
    }

    public function setStartedAt(\DateTime $startedAt): self
    {
        $this->startedAt = $startedAt;
        return $this;
    }

    public function getStartedAt(): ?\DateTimeInterface
    {
        return $this->startedAt;
    }

    public function setFinishedAt(?\DateTime $finishedAt): self
    {
        $this->finishedAt = $finishedAt;
        return $this;
    }

    public function getFinishedAt(): ?\DateTimeInterface
    {
        return $this->finishedAt;
    }

    public function setTotalTime(?int $totalTime): self
    {
        $this->totalTime = $totalTime;
        return $this;
    }

    public function getTotalTime(): ?int
    {
        return $this->totalTime;
    }

    public function setTotalTimeText(string $totalTimeText): self
    {
        $this->totalTimeText = $totalTimeText;
        return $this;
    }

    public function getTotalTimeText(): string
    {
        return $this->totalTimeText;
    }

    public function setStartedAtZoom(?\DateTime $startedAtZoom): self
    {
        $this->startedAtZoom = $startedAtZoom;
        return $this;
    }

    public function getStartedAtZoom(): ?\DateTimeInterface
    {
        return $this->startedAtZoom;
    }

    /**
     * Set finishedAtZoom
     *
     * @param \DateTime|null $finishedAtZoom
     * @return Participation
     */
    public function setFinishedAtZoom(?\DateTime $finishedAtZoom)
    {
        $this->finishedAtZoom = $finishedAtZoom;

        return $this;
    }

    /**
     * Get finishedAtZoom
     *
     * @return \DateTime
     */
    public function getFinishedAtZoom()
    {
        return $this->finishedAtZoom;
    }

    /**
     * Set totalTimeZoom
     *
     * @param int $totalTimeZoom
     * @return Participation
     */
    public function setTotalTimeZoom($totalTimeZoom)
    {
        $this->totalTimeZoom = $totalTimeZoom;

        return $this;
    }

    /**
     * Get totalTimeZoom
     *
     * @return int
     */
    public function getTotalTimeZoom()
    {
        return $this->totalTimeZoom;
    }

    /**
     * Get totalTimeZoom
     *
     * @return int
     */
    public function getTotalTimeZoomMin()
    {
        return $this->totalTimeZoom ? $this->totalTimeZoom / 60 : $this->totalTimeZoom;
    }

    /**
     * Set ip
     *
     * @param string $ip
     * @return Participation
     */
    public function setIp($ip)
    {
        $this->ip = $ip;

        return $this;
    }

    /**
     * Get ip
     *
     * @return string
     */
    public function getIp()
    {
        return $this->ip;
    }

    /**
     * Set exerciseMode
     *
     * @param string $exerciseMode
     * @return Participation
     */
    public function setExerciseMode($exerciseMode)
    {
        $this->exerciseMode = $exerciseMode;

        return $this;
    }

    /**
     * Get exerciseMode
     *
     * @return string
     */
    public function getExerciseMode()
    {
        return $this->exerciseMode;
    }

    public function getAnswers()
    {
        if ($this->getFormation() instanceof FormationAudit || $this->getFormation() instanceof FormationVfc || ($this->getFormation() instanceof FormationPresentielle && $this->getFormation()->getFormType() != "survey")) {
            return $this->getAuditAnswers();
        } else if ($this->getFormation() instanceof FormationPresentielle) {
            return $this->getSurveyAnswers();
        }
        return [];
    }

    /**
     * @return Collection|AuditAnswer[]
     */
    public function getAuditAnswers()
    {
        return $this->auditAnswers;
    }

    /**
     * @param Collection<int, AuditAnswer> $auditAnswers
     * @return Participation
     */
    public function setAuditAnswers($auditAnswers)
    {
        $this->auditAnswers = $auditAnswers;

        return $this;
    }

    /**
     * @return Collection|SurveyAnswer[]
     */
    public function getSurveyAnswers()
    {
        return $this->surveyAnswers;
    }

    /**
     * @param Collection<int, SurveyAnswer> $surveyAnswers
     * @return Participation
     */
    public function setSurveyAnswers($surveyAnswers)
    {
        $this->surveyAnswers = $surveyAnswers;

        return $this;
    }

    /**
     * @return Collection|EvaluationFormationAnswer[]
     */
    public function getEvaluationsFormation()
    {
        return $this->evaluationsFormation;
    }

    /**
     * @param Collection<int, EvaluationFormationAnswer> $evaluationsFormation
     * @return Participation
     */
    public function setEvaluationsFormation($evaluationsFormation)
    {
        $this->evaluationsFormation = $evaluationsFormation;

        return $this;
    }

    /**
     * @return Collection|EvaluationFormerAnswer[]
     */
    public function getEvaluationsFormer()
    {
        return $this->evaluationsFormer;
    }

    /**
     * @param Collection<int, EvaluationFormerAnswer> $evaluationsFormer
     * @return Participation
     */
    public function setEvaluationsFormer($evaluationsFormer)
    {
        $this->evaluationsFormer = $evaluationsFormer;

        return $this;
    }

    /**
     * @return Collection|PatientDescription[]
     */
    public function getPatientsDescriptions()
    {
        return $this->patientsDescriptions;
    }

    /**
     * @param Collection<int, PatientDescription> $patientsDescriptions
     * @return Participation
     */
    public function setPatientsDescriptions($patientsDescriptions)
    {
        $this->patientsDescriptions = $patientsDescriptions;

        return $this;
    }

    public function getToken(): string
    {
        return md5(sprintf('%s%s%s%s',
            $this->getFormation()->getId(), $this->getId(), $this->getParticipant()->getId(), $this->getFormation()->getDiscr()
        ));
    }

    /**
     * @param $auditId
     * @param $patient
     * @return null|string
     */
    public function getDescriptionByPatient($auditId, $patient)
    {
        $answer = $this->getPatientsDescriptions()->filter(function($d) use ($auditId, $patient) {
            /** @var PatientDescription $d */
            return $d->getAuditId() === $auditId && $d->getPatient() === $patient;
        });
        return !$answer->count() ? null : $answer->first()->getDescription();
    }

    /**
     * @param AuditQuestion $question
     * @param                                            $auditId
     * @param                                            $patient
     * @return null|integer
     */
    public function getAuditAnswerByQuestion(AuditQuestion $question, $auditId, $patient)
    {
        $answer = $this->getAuditAnswers()->filter(function($a) use ($auditId, $question, $patient) {
            /** @var AuditAnswer $a */
            return $a->getQuestion() === $question && $a->getAuditId() === $auditId && $a->getPatient() === $patient;
        });
        return !$answer->count() ? null : $answer->first()->getAnswer();
    }

    /**
     * @param SurveyQuestion $question
     * @param int $surveyId
     * @return int|null
     */
    public function getSurveyAnswerByQuestion(SurveyQuestion $question, $surveyId)
    {
        $answer = $this->getSurveyAnswers()->filter(function($a) use ($surveyId, $question) {
            /** @var SurveyAnswer $a */
            return $a->getQuestion() === $question && $a->getSurveyId() === $surveyId;
        });
        return !$answer->count() ? null : $answer->first()->getAnswer();
    }

    /**
     * @param AuditQuestion $question
     * @param                                            $auditId
     * @param                                            $patient
     * @return null|integer
     */
    public function getAuditAnswerByQuestionForSurvey(SurveyQuestion $question, $auditId, $patient)
    {
        $answer = $this->getAuditAnswers()->filter(function($a) use ($auditId, $question, $patient) {
            /** @var AuditAnswer $a */
            return $a->getSurveyQuestion() === $question && $a->getAuditId() === $auditId && $a->getPatient() === $patient;
        });
        return !$answer->count() ? null : $answer->first()->getAnswer();
    }

    /**
     * Get completedForm1
     *
     * @return boolean
     */
    public function getCompletedForm1()
    {
        return $this->completedForm1;
    }

    /**
     * Set completedForm1
     *
     * @param boolean $completedForm1
     * @return Participation
     */
    public function setCompletedForm1($completedForm1)
    {
        $this->completedForm1 = $completedForm1;

        return $this;
    }

    /**
     * Get completedForm2
     *
     * @return boolean
     */
    public function getCompletedForm2()
    {
        return $this->completedForm2;
    }

    /**
     * Set completedForm2
     *
     * @param boolean $completedForm2
     * @return Participation
     */
    public function setCompletedForm2($completedForm2)
    {
        $this->completedForm2 = $completedForm2;

        return $this;
    }

    /**
     * Get completedForm
     *
     * @param $formId
     * @return boolean
     */
    public function getCompletedForm($formId)
    {
        return $this->{"completedForm".$formId};
    }

    /**
     * Get completedForms
     *
     * @return boolean
     */
    public function getCompletedForms()
    {
        return $this->completedForms;
    }

    /**
     * Set completedForms
     *
     * @param boolean $completedForms
     * @return Participation
     */
    public function setCompletedForms($completedForms)
    {
        $this->completedForms = $completedForms;

        return $this;
    }

    /**
     * Get firstPageEtutoCompleted
     *
     * @return boolean
     */
    public function isFirstPageEtutoCompleted()
    {
        return $this->firstPageEtutoCompleted == true;
    }

    /**
     * Set firstPageEtutoCompleted
     *
     * @param boolean $firstPageEtutoCompleted
     * @return Participation
     */
    public function setFirstPageEtutoCompleted($firstPageEtutoCompleted)
    {
        $this->firstPageEtutoCompleted = $firstPageEtutoCompleted;

        return $this;
    }

    /**
     * Get validTimeAudit1
     *
     * @return boolean
     */
    public function getValidTimeAudit1()
    {
        return $this->validTimeAudit1;
    }

    /**
     * Set validTimeAudit1
     *
     * @param boolean $validTimeAudit1
     * @return Participation
     */
    public function setValidTimeAudit1($validTimeAudit1)
    {
        $this->validTimeAudit1 = $validTimeAudit1;

        return $this;
    }

    /**
     * Get validTimeAudit2
     *
     * @return boolean
     */
    public function getValidTimeAudit2()
    {
        return $this->validTimeAudit2;
    }

    /**
     * Set validTimeAudit2
     *
     * @param boolean $validTimeAudit2
     * @return Participation
     */
    public function setValidTimeAudit2($validTimeAudit2)
    {
        $this->validTimeAudit2 = $validTimeAudit2;

        return $this;
    }

    /**
     * @return bool
     */
    public function isElearningPhaseDone()
    {
        return $this->elearningPhaseDone;
    }

    /**
     * @param bool $elearningPhaseDone
     * @return Participation
     */
    public function setElearningPhaseDone($elearningPhaseDone)
    {
        $this->elearningPhaseDone = $elearningPhaseDone;
        return $this;
    }

    /**
     * Set nbHour
     *
     * @param int $nbHour
     * @return Participation
     */
    public function setNbHour($nbHour)
    {
        $this->nbHour = $nbHour;

        return $this;
    }

    /**
     * Get nbHour
     *
     * @return int
     */
    public function getNbHour()
    {
        return $this->nbHour;
    }

    /**
     * Set budgetCR
     *
     * @param float $budgetCR
     * @return Participation
     */
     public function setBudgetCR($budgetCR)
     {
         $this->budgetCR = $budgetCR;

         return $this;
     }

    /**
     * Get budgetCR
     *
     * @return float
     */
    public function getBudgetCR()
    {
        return $this->budgetCR;
    }

    /**
     * Set coordinator
     *
     * @param Coordinator $coordinator
     * @return Participation
     */
    public function setCoordinator(Coordinator $coordinator = null)
    {
        $this->coordinator = $coordinator;

        return $this;
    }

    /**
     * Get coordinator
     *
     * @return Coordinator
     */
    public function getCoordinator()
    {
        return $this->coordinator;
    }

    /**
     * @return bool
     */
    public function isArchived()
    {
        return $this->archived;
    }

    /**
     * Add participationHistory
     *
     * @param ParticipationHistory $participationHistory
     * @return Participation
     */
    public function addParticipationHistory(ParticipationHistory $participationHistory)
    {
        $this->participationHistories[] = $participationHistory;
        $participationHistory->setParticipation($this);
        return $this;
    }

    /**
     * Remove participationHistory
     *
     * @param ParticipationHistory $participationHistory
     */
    public function removeParticipationHistory(ParticipationHistory $participationHistory)
    {
        $this->participationHistories->removeElement($participationHistory);
    }

    /**
     * Get participationHistories
     *
     * @return Collection|ParticipationHistory[] s
     */
    public function getParticipationHistories()
    {
        return $this->participationHistories;
    }

    public function addParticipationHistoryRegister($type = ParticipationHistory::MANUAL_TYPE)
    {
        $this->addParticipationHistory(ParticipationHistory::registerAction($this, $type));
    }

    public function addParticipationHistoryUnregister($motif = null, $commentaire = null, $type = ParticipationHistory::MANUAL_TYPE)
    {
        $this->addParticipationHistory(ParticipationHistory::unregisterAction($this, $motif, $commentaire, $type));
    }

    /**
     * Add participationLog
     *
     * @param ParticipationLog $participationLog
     * @return Participation
     */
    public function addParticipationLog(ParticipationLog $participationLog)
    {
        $this->participationLogs[] = $participationLog;
        $participationLog->setParticipation($this);
        return $this;
    }

    /**
     * Remove participationLog
     *
     * @param ParticipationLog $participationLog
     */
    public function removeParticipationLog(ParticipationLog $participationLog)
    {
        $this->participationLogs->removeElement($participationLog);
    }

    /**
     * Get participationLogs
     *
     * @return Collection|ParticipationLog[] s
     */
    public function getParticipationLogs()
    {
        return $this->participationLogs;
    }

    public function getTotalTimeLogs()
    {
        $time = 0;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                $time += $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
            }
        }
        // Pour les formations elearning on additionne avec le temps passé côté plateforme elearning
        if ($this->getFormation()->isElearningDiscr()) {
            $time += $this->getTotalTime();
        }
        return $time > 0 ? $time : $this->getTotalTime();
    }

    public function getStartedAtLogs()
    {
        $minStart = null;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if ($log->getAction() == ParticipationLog::ACTION_FORM_PRE && (!$minStart || $log->getStartDate() < $minStart)) {
                    $minStart = $log->getStartDate();
                }
            }
        }
        return $minStart ?: $this->getStartedAt();
    }

    public function getStartedAtLogsByStep($step) {
        $step = "step_".$step;
        $minStart = false;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if ($step == CourseManager::STEP4 || $step == CourseManager::STEP3)  {
                    if ((in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP3]) || in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP4])) && (!$minStart || $log->getStartDate() < $minStart)) {
                        $minStart = $log->getStartDate();
                    }
                } else {
                    if ((in_array($log->getAction(), ParticipationLog::STEPS[$step]) || ($this->getFormation()->getProgramme()->isElearningTwoUnity() && $log->getAction() == "elearning")) && (!$minStart || $log->getStartDate() < $minStart)) {
                        $minStart = $log->getStartDate();
                    }
                }
            }
        }
        return $minStart;
    }

    public function getLessonTotalTimeLogs(Lesson $lesson)
    {
        $time = 0;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if ($log->getLesson() == $lesson->getId()) {
                    $time += $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                }
            }
        }

        return $time > 0 ? $time : 0;
    }

    public function getTotalTimeLogsByStep($step, $inSeconds = false, $roundSup = false, $forElearningOneUnity = false, $forResfreshChrono = false, $includeElearningInStep1 = true, $excludeStep2FicheAction = false): string
    {
        $time = 0;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if ($forElearningOneUnity) {
                    $time += $log->getEndDate() && $log->getAction() !== "topos" ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                } else {
                    $initialLogEndDate = $log->getEndDate();
                    if ($this->getParticipationLogs()->last() == $log && $forResfreshChrono) {
                        $log->setEndDate(new \DateTime());
                    }
                    if (($step == CourseManager::STEP4 || $step == CourseManager::STEP3) && $inSeconds)  {
                        if ($log->getStep() === CourseManager::STEP4 || $log->getStep() === CourseManager::STEP3) {
                            $time += $log->getEndDate() && $log->getAction() !== "topos" ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                        }
                    } else {
                        if ($log->getStep() === $step || ($this->getFormation()->getProgramme()->isElearningTwoUnity() && $log->getAction() == "elearning" && $includeElearningInStep1)) {
                            if ($excludeStep2FicheAction && $log->getStep() === CourseManager::STEP2 && $log->getAction() === CourseManager::STEP2_FICHE_ACTION_LABEL) {
                                // La fiche action est exclue du cumul lorsqu'elle est en étape 2 (sinon elle serait prise en compte dans le document de traçabilite)
                            } else {
                                $time += $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                            }
                        }
                    }
                    // if (($step == CourseManager::STEP4 || $step == CourseManager::STEP3) && $inSeconds)  {
                    //     if (in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP3]) || in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP4])) {
                    //         $time += $log->getEndDate() && $log->getAction() !== "topos" ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                    //     }
                    // } else {
                    //     if (in_array($log->getAction(), ParticipationLog::STEPS[$step]) || ($this->getFormation()->getProgramme()->isElearningTwoUnity() && $log->getAction() == "elearning" && $includeElearningInStep1)) {
                    //         $time += $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                    //     }
                    // }
                    $log->setEndDate($initialLogEndDate);
                }
                if ($time > 0 && $roundSup) {
                    $time = 60 * (ceil($time/60));
                }
            }
        }

        if ($inSeconds) {
            return $time;
        }

        $hours = floor($time / 3600) < 10 ? "0" . floor($time / 3600) : floor($time / 3600);
        $minutes = floor(($time / 60) % 60) < 10 ? "0" . floor(($time / 60) % 60) : floor(($time / 60) % 60);
        $seconds = $time % 60 < 10 ? "0" . $time % 60 : $time % 60;
        return "$hours:$minutes:$seconds";
    }

    public function getTimeLogedByModule($moduleId) {
        $time = 0;
        $logs = $this->getLogsByModule($moduleId);
        foreach($logs as $log) {
            $endDate = $log->getEndDate();
            if ($this->getParticipationLogs()->last() == $log) {
                $endDate = new \DateTime();
            }
            $time += $endDate ? ($endDate->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
        }

        return $time;
    }

    public function getLogsByModule($moduleId) {
        return $this->getParticipationLogs()->filter(function($log) use($moduleId) {
            return $log->getAction() == $moduleId;
        });
    }

    public function minModuleTimeReached($moduleId) {
        $modulesMinTime = $this->getFormation()->getModuleMinTimesToArray();
        return $this->getTimeLogedByModule($moduleId) >= $modulesMinTime[$moduleId] * 60;
    }

    public function getFinishedAtLogs()
    {
        $maxEnd = null;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if (!$maxEnd || $log->getEndDate() > $maxEnd) {
                    $maxEnd = $log->getEndDate();
                }
            }
        }
        return $maxEnd ?: $this->getFinishedAt();
    }

    public function getFinishedAtLogsByStep($step) {
        $step = "step_".$step;
        $maxEnd = false;
        if ($this->getParticipationLogs()->count()) {
            foreach($this->getParticipationLogs() as $log) {
                if ($this->getFormation()->getProgramme()->isElearningOneUnity() && (!$maxEnd || $log->getEndDate() > $maxEnd)) {
                    $maxEnd = $log->getEndDate();
                } else {
                    if ($step == CourseManager::STEP4 || $step == CourseManager::STEP3)  {
                        if ((in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP3]) || in_array($log->getAction(), ParticipationLog::STEPS[CourseManager::STEP4])) && (!$maxEnd || $log->getEndDate() > $maxEnd)) {
                            $maxEnd = $log->getEndDate();
                        }
                    } else {
                        if ((in_array($log->getAction(), ParticipationLog::STEPS[$step]) || ($this->getFormation()->getProgramme()->isElearningTwoUnity() && $log->getAction() == "elearning")) && (!$maxEnd || $log->getEndDate() > $maxEnd)) {
                            $maxEnd = $log->getEndDate();
                        }
                    }
                }
            }
        }
        return $maxEnd;
    }

    /**
     * @return bool
     */
    public function isActive() {
        return !$this->archived;
    }

    /**
     * @param bool $archived
     * @return Participation
     */
    public function setArchived($archived)
    {
        $this->archived = $archived;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     * @return Participation
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @param \DateTime $updatedAt
     * @return Participation
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Get coordinator
     *
     * @return Coordinator
     */
    public function getAssociatedCoordinator()
    {
        if (is_null($this->coordinator)) {
            return $this->getFormation()->getCoordinators()->first();
        }
        return $this->coordinator;
    }

    #[ORM\PreUpdate]
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get participantActionSheet
     *
     * @return ParticipantActionSheet
     */
    public function getParticipantActionSheet() {
        return $this->participantActionSheet;
    }

    /**
     * @param ParticipantActionSheet $participantActionSheet
     */
    public function setParticipantActionSheet($participantActionSheet)
    {
        $this->participantActionSheet = $participantActionSheet;
        return $this;
    }

    /**
     * @return EtutoratCasClinique
     */
    public function getEtutoratCasClinique() {
        return $this->etutoratCasClinique;
    }

    /**
     * @param EtutoratCasClinique $etutoratCasClinique
     */
    public function setEtutoratCasClinique($etutoratCasClinique)
    {
        $this->etutoratCasClinique = $etutoratCasClinique;
        return $this;
    }

    /**
     * Set uga
     *
     * @param string $uga
     * @return Participation
     */
    public function setUga($uga)
    {
        $this->uga = $uga;

        return $this;
    }

    /**
     * Get uga
     *
     * @return string
     */
    public function getUga()
    {
        return $this->uga;
    }

    /**
     * @return array
     */
    public function getCourse()
    {
        return $this->course ?? array();
    }

    /**
     * @return array
     */
    public function getElearningCourse()
    {
        return $this->elearningCourse ?? array();
    }

    /**
     * Add ficheAction
     *
     * @param FicheAction $ficheAction
     * @return Participation
     */
    public function addFicheAction(FicheAction $ficheAction)
    {
        $this->ficheActions[] = $ficheAction;
        $ficheAction->setParticipation($this);
        return $this;
    }

    /**
     * Remove ficheAction
     *
     * @param FicheAction $ficheAction
     */
    public function removeFicheAction(FicheAction $ficheAction)
    {
        $this->ficheActions->removeElement($ficheAction);
    }

     /**
     * Remove ficheAction
     *
     */
    public function resetFicheAction()
    {
        $this->ficheActions = new ArrayCollection();
    }

    /**
     * Get ficheActions
     *
     * @return Collection|FicheAction[]
     */
    public function getFicheActions()
    {
        return $this->ficheActions;
    }

    public function getFicheActionById($id)
    {
        foreach($this->ficheActions as $ficheAction) {
            if($ficheAction->getId() == $id) {
                return $ficheAction;
            }
        }
        return false;
    }

    public function getFicheProgressionByPatient($id)
    {
        foreach($this->ficheProgressions as $ficheProgression) {
            if($ficheProgression->getPatient()->getId() == $id) {
                return $ficheProgression;
            }
        }
        return false;
    }

    /**
     * Add etutorat
     *
     * @param Etutorat $etutorat
     * @return Participation
     */
    public function addEtutorat(Etutorat $etutorat)
    {
        $this->etutorats[] = $etutorat;
        $etutorat->setParticipation($this);
        return $this;
    }

    /**
     * Remove etutorat
     *
     * @param Etutorat $etutorat
     */
    public function removeEtutorat(Etutorat $etutorat)
    {
        $this->etutorats->removeElement($etutorat);
    }

    /**
     * Get etutorats
     *
     * @return Collection|Etutorat[]
     */
    public function getEtutorats()
    {
        return $this->etutorats;
    }

    /**
     * @param $class
     * @param $entity
     * @return false|Etutorat
     */
    public function findEtutorat($class, $entity) {
        return $this->etutorats->matching(Criteria::create()->where(Criteria::expr()->eq($class, $entity)))->first();
    }

    /**
     * Add ficheSynthese
     *
     * @param Etutorat $ficheSynthese
     * @return Participation
     */
    public function addFicheSynthese(FicheSynthese $ficheSynthese)
    {
        $this->ficheSyntheses[] = $ficheSynthese;
        $ficheSynthese->setParticipation($this);
        return $this;
    }

    /**
     * Remove ficheSynthese
     *
     * @param FicheSynthese $ficheSynthese
     */
    public function removeFicheSynthese(FicheSynthese $ficheSynthese)
    {
        $this->ficheSyntheses->removeElement($ficheSynthese);
    }

    /**
     * Get ficheSyntheses
     *
     * @return Collection|FicheSynthese[]
     */
    public function getFicheSyntheses()
    {
        return $this->ficheSyntheses;
    }

    /**
     * @param $class
     * @param $entity
     * @return false|FicheSynthese
     */
    public function findFicheSynthese($class, $entity) {
        return $this->ficheSyntheses->matching(Criteria::create()->where(Criteria::expr()->eq($class, $entity)))->first();
    }

    /**
     * Add ficheProgression
     *
     * @param FicheProgression $ficheProgression
     * @return Participation
     */
    public function addFicheProgression(FicheProgression $ficheProgression)
    {
        $this->ficheProgressions[] = $ficheProgression;
        $ficheProgression->setParticipation($this);
        return $this;
    }

    /**
     * Remove ficheProgression
     *
     * @param FicheProgression $ficheProgression
     */
    public function removeFicheProgression(FicheProgression $ficheProgression)
    {
        $this->ficheProgressions->removeElement($ficheProgression);
    }

    /**
     * Get ficheProgressions
     *
     * @return Collection|FicheProgression[]
     */
    public function getFicheProgressions()
    {
        return $this->ficheProgressions;
    }

    /**
     * Set attestationHonneur
     *
     * @param string $attestationHonneur
     * @return Participation
     */
    public function setAttestationHonneur($attestationHonneur)
    {
        $this->attestationHonneur = $attestationHonneur;
        return $this;
    }

    /**
     * Get attestationHonneur
     *
     * @return string
     */
    public function getAttestationHonneur()
    {
        return $this->attestationHonneur;
    }

    public function getAttestationHonneurN1(): ?string
    {
        return $this->attestationHonneurN1;
    }

    public function setAttestationHonneurN1(?string $attestationHonneurN1): Participation
    {
        $this->attestationHonneurN1 = $attestationHonneurN1;
        return $this;
    }

    /**
     * @return File
     */
    public function getAttestationHonneurFile()
    {
        return $this->attestationHonneurFile;
    }

    /**
     * @param File|null $attestationHonneurFile
     * @return Participation
     */
    public function setAttestationHonneurFile(File $attestationHonneurFile = null)
    {
        $this->attestationHonneurFile = $attestationHonneurFile;
        if ($this->attestationHonneurFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    public function getAttestationHonneurFileN1(): ?File
    {
        return $this->attestationHonneurFileN1;
    }

    public function setAttestationHonneurFileN1(?File $attestationHonneurFileN1 = null): Participation
    {
        $this->attestationHonneurFileN1 = $attestationHonneurFileN1;
        if ($this->attestationHonneurFileN1 instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getEtutoratAttentes()
    {
        return $this->etutoratAttentes;
    }

    /**
     * @param string $etutoratAttentes
     * @return Participation
     */
    public function setEtutoratAttentes($etutoratAttentes)
    {
        $this->etutoratAttentes = $etutoratAttentes;
        return $this;
    }

    /**
     * @return string
     */
    public function getEtutorat2Message()
    {
        return $this->etutorat2Message;
    }

    /**
     * @param string $etutorat2Message
     * @return Participation
     */
    public function setEtutorat2Message($etutorat2Message)
    {
        $this->etutorat2Message = $etutorat2Message;
        return $this;
    }

    /**
     * @return string
     */
    public function getEtutorat3Message()
    {
        return $this->etutorat3Message;
    }

    /**
     * @param string $etutorat3Message
     * @return Participation
     */
    public function setEtutorat3Message($etutorat3Message)
    {
        $this->etutorat3Message = $etutorat3Message;
        return $this;
    }

            /**
     * @return string
     */
    public function getNextModule()
    {
        return $this->nextModule;
    }

    /**
     * @param string $nextModule
     */
    public function setNextModule($nextModule): Participation
    {
        $this->nextModule = $nextModule;
        return $this;
    }

    /**
     * @param string $partenariat
     */
    public function getPartenariat() {
        return $this->partenariat;
    }

    /**
     * @param string $partenariat
     * @return Participation
     */
    public function setPartenariat($partenariat) {
        $this->partenariat = $partenariat;
        return $this;
    }

        /**
     * @param string $ots
     */
    public function getOts() {
        return $this->ots;
    }

    /**
     * @param string $ots
     * @return Participation
     */
    public function setOts($ots) {
        $this->ots = $ots;
        return $this;
    }

    public function isLead() {
        return $this->isLead != null;
    }

    /**
     * @param bool $isLead
     */
    public function setIsLead($isLead): Participation
    {
        $this->isLead = $isLead;
        return $this;
    }

    /**
     * Get the value of lastToolFile
     *
     * @return  \int
     */
    public function getLastToolFile()
    {
        return $this->lastToolFile;
    }

    /**
     * Set the value of lastToolFile
     *
     * @param  \int  $lastToolFile
     *
     * @return  self
     */
    public function setLastToolFile($lastToolFile)
    {
        $this->lastToolFile = $lastToolFile;

        return $this;
    }

    /**
     * @return int
     */
    public function getLastDocumentsPedagogiquesFile(): ?int
    {
        return $this->lastDocumentsPedagogiquesFile;
    }

    /**
     * @param int $lastDocumentsPedagogiquesFile
     * @return Participation
     */
    public function setLastDocumentsPedagogiquesFile(int $lastDocumentsPedagogiquesFile): Participation
    {
        $this->lastDocumentsPedagogiquesFile = $lastDocumentsPedagogiquesFile;
        return $this;
    }

    /**
     * @param array $course
     * @return Participation
     */
    public function setCourse(array $course)
    {
        $this->course = $course;
        return $this;
    }

    /**
     * @param array $elearningCourse
     * @return Participation
     */
    public function setElearningCourse(array $elearningCourse)
    {
        $this->elearningCourse = $elearningCourse;
        return $this;
    }

    public function getCourseStep($step) {
        $course = $this->getCourse();
        $key = array_search($step, array_column($course, 'id'));
        if ($key !== false) {
            return $course[$key];
        }
        return null;
    }

    public function getActivityCourse($activity) {
        $lessonCourse = $this->getLessonCourse($activity->getLesson());
        if($lessonCourse && $lessonCourse["activities"]) {
            $key = array_search("a".$activity->getId(), array_column($lessonCourse["activities"], 'id'));
            if ($key !== false) {
                return $lessonCourse["activities"][$key];
            }
        }
        return null;
    }

    public function getLessonCourse($lesson) {
        $elearningCourse = $this->getElearningCourse();
        $key = array_search("l".$lesson->getId(), array_column($elearningCourse, 'id'));
        if ($key !== false) {
            return $elearningCourse[$key];
        }
        return null;
    }

    public function completeCourseStep($step, $time = null) {
        $this->manageEndParticipation($step);
        $course = $this->getCourse();
        $key = array_search($step, array_column($course, 'id'));
        if ($key !== false) {
            $course[$key]["completed"] = true;
            $course[$key]["date"] = (new \DateTime())->format("d/m/Y H:i:s");
            if ($time) {
                $course[$key]["time"] = $time;
            }
        } else {
            $module = array(
                "id"        => $step,
                "completed" => true,
                "date"      => (new \DateTime())->format("d/m/Y H:i:s")
            );
            if ($time) {
                $module["time"] = $time;
            }
            $course[] = $module;
        }
        $this->setCourse($course);
    }

    public function removeCourseStep($step) {
        $course = $this->getCourse();
        $key = array_search($step, array_column($course, 'id'));
        if ($key !== false) {
            unset($course[$key]);
            $this->setCourse(array_values($course));
        }
    }

    public function isStepCompleted($step) {
        $module = $this->getCourseStep($step);
        return $module && $module["completed"];
    }

    public function completeActivity($activity, $time = null) {
        $lesson = $activity->getLesson();
        $elearningCourse = $this->getElearningCourse();
        $key = array_search("l".$lesson->getId(), array_column($elearningCourse, 'id'));
        if ($key !== false) {
            $cle = array_search("a".$activity->getId(), array_column($elearningCourse[$key]["activities"], 'id'));
            if ($cle !== false) {
                $elearningCourse[$key]["activities"][$cle]["completed"] = true;
                $elearningCourse[$key]["activities"][$cle]["date"] = (new \DateTime())->format("d/m/Y H:i:s");
            }
            // if ($time) {
            //     $elearningCourse[$key]["time"] = $time;
            // }
        } else {
            $lessonCourse = array(
                "id"        => "l".$lesson->getId(),
                "completed" => false,
                "date"      => "",
                "activities" => [],
            );

            foreach($lesson->getActivities() as $activite) {
                if ($activite == $activity) {
                    $actityCourse = [
                        "id" => "a".$activite->getId(),
                        "completed" => true,
                        "date" => (new \DateTime())->format("d/m/Y H:i:s")
                    ];
                } else {
                    $actityCourse = [
                        "id" => "a".$activite->getId(),
                        "completed" => false,
                        "date" => ""
                    ];
                }
                array_push($lessonCourse["activities"], $actityCourse);
            }
            // if ($time) {
            //     $module["time"] = $time;
            // }
            $elearningCourse[] = $lessonCourse;
        }
        $this->setElearningCourse($elearningCourse);
    }

    public function completeLesson($lesson, $time = null) {
        $elearningCourse = $this->getElearningCourse();
        $key = array_search("l".$lesson->getId(), array_column($elearningCourse, 'id'));
        if ($key !== false) {
            $elearningCourse[$key]["completed"] = true;
            $elearningCourse[$key]["date"] = (new \DateTime())->format("d/m/Y H:i:s");
        }
        $this->setElearningCourse($elearningCourse);
    }

    /**
     * On conserve l'uga associé au participant au niveau de la participation pour avoir l'historique
     */
    public function saveUga()
    {
        if ($this->getParticipant()->getUga()) {
            $this->uga = $this->getParticipant()->getUga();
        }
    }

    public function isLessonCompleted(Lesson $lesson) {
        if ($lesson->isPresentielle()) {
            if ($lesson->getPosition() == 1) {
                return new \DateTime() >= $this->getFormation()->getEndDate();
            } else {
                $previousLesson = $lesson->getElearning()->getLessonByPosition($lesson->getPosition() -1);
                return new \DateTime() >= $this->getFormation()->getEndDate() && $this->isLessonCompleted($previousLesson);
            }
        }
        $lesson = $this->getLessonCourse($lesson);
        return $lesson && $lesson["completed"];
    }

    public function getLessonCompletionDate(Lesson $lesson) {
        if ($lesson->isPresentielle()) {
            return false;
        }
        $lesson = $this->getLessonCourse($lesson);
        if ($lesson) {
            return $lesson["date"] ? $lesson["date"] : false;
        }
        return false;
    }

    public function isActivityCompleted(Activity $activity) {
        $activity = $this->getActivityCourse($activity);
        return $activity && $activity["completed"];
    }

    public function getActivityCompletionDate(Activity $activity) {
        $activity = $this->getActivityCourse($activity);
        if ($activity) {
            return $activity["date"] ? $activity["date"] : false;
        }
        return false;
    }

    public function getLastActivityCompletedDate(Lesson $lesson) {
        $date = null;
        foreach ($lesson->getActivities() as $activity) {
            $activityCompletionDate = $this->getActivityCompletionDate($activity);
            if ($activityCompletionDate && !$date) {
                $date = $activityCompletionDate;
            }
            if ($activityCompletionDate && (strtotime($date) < strtotime($activityCompletionDate) || $date < $activityCompletionDate)) {
                $date = $activityCompletionDate;
            }
        }
        return $date != null ? \DateTime::createFromFormat('d/m/Y H:i:s', $date) : false;
    }

    public function getNextLesson() {
        if($this->getFormation()->getElearning()) {
            foreach($this->getFormation()->getElearning()->getLessons() as $lesson) {
                if (!$this->isLessonCompleted($lesson)) {
                    return $lesson;
                }
            }
        }
        return false;
    }

    public function getAvancementByUnity(int $unity) {
        if ($unity === 1 && $this->isStepCompleted("etutorat_1")) {
            return 100;
        }

        if ($unity === 2 && $this->isStepCompleted("reunion")) {
            return 100;
        }

        if ($unity === 3 && ($this->isStepCompleted("end") || $this->isStepCompleted("synthese"))) {
            return 100;
        }
        return 0;
    }

    public function getLessonProgression(Lesson $lesson, $percent = true)
    {
        $activityCount = count($lesson->getActivities());
        $completedActivities = 0;
        foreach ($lesson->getActivities() as $activity) {
            if ($this->isActivityCompleted($activity)) {
                $completedActivities++;
            }
        }

        if (!$percent) {
            return $completedActivities . "/" . $activityCount;
        }

        return $completedActivities > 0 ? ($completedActivities * 100) / $activityCount : 0;
    }

    public function hasDpcFinanceSousMode(): bool
    {
        return $this->financeSousMode && $this->financeSousMode->isDpc();
    }

    public function getCourseEnded(): bool
    {
        return $this->courseEnded;
    }

    public function setCourseEnded(bool $courseEnded): Participation
    {
        $this->courseEnded = $courseEnded;
        return $this;
    }

    /**
     * Gestion du champ participationEnded
     */
    public function manageEndParticipation($step): void
    {
        if ($this->getFormation()->hasCourse() && !$this->getCourseEnded()) {
            $endModuleCourse = $this->getFormation()->getCourse()->getEndModule();
            if ($endModuleCourse->getLabel() === $step) {
                $this->setCourseEnded(true);
            }
        }
    }

    /**
     * @return Collection<int, ParticipationAnswerTCS>
     */
    public function getParticipationTCSAnswers(): Collection
    {
        return $this->participationTCSAnswers;
    }

    public function hasAlreadyAnswerTo(QuestionTCS $questionTCS): bool
    {
        return $this->getParticipationTCSAnswers()->filter(function(ParticipationAnswerTCS $answer) use($questionTCS) {
            return $answer->getQuestionTCS()->getId() === $questionTCS->getId();
        })->count() > 0;
    }

    public function getAnswerTo(QuestionTCS $questionTCS) {
        return $this->getParticipationTCSAnswers()->filter(function(ParticipationAnswerTCS $answer) use($questionTCS) {
            return $answer->getQuestionTCS()->getId() === $questionTCS->getId();
        })->first();
    }

    public function hasAnswerToAll(GroupeQuestionTCS $groupeQuestionTCS): bool
    {
        foreach ($groupeQuestionTCS->getQuestionsTCS() as $questionsTCS) {
            if (!$this->hasAlreadyAnswerTo($questionsTCS)) {
                return false;
            }
        }
        return true;
    }

    public function canAnswerTo(QuestionTCS $questionTCS): bool
    {
        $param = new ArrayCollection();
        foreach ($this->getParticipationTCSAnswers() as $participationAnswerTCS) {
            $param->add($participationAnswerTCS->getQuestionTCS());
        }
        return $questionTCS->getGroupeQuestion()->getQuestionnaireTCS()->getNextQuestionNotAnswered(
            $param
        ) === $questionTCS;
    }

    public function getDateModuleManquantExpiration(): ?\DateTime
    {
        return $this->dateModuleManquantExpiration;
    }

    public function calculateEcheanceNextModule(): void
    {
        if (!$this->nextModule) {
            return;
        }
        $this->dateModuleManquantExpiration = $this->formation->calculateEcheanceNextModule($this->nextModule);
    }
}
