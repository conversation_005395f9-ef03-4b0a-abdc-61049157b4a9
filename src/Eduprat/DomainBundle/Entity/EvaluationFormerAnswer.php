<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * EvaluationFormerAnswer
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\EvaluationFormerAnswerRepository')]
#[ORM\Table(name: 'evaluation_former_answer')]
#[ORM\UniqueConstraint(name: 'answer_unique', columns: ['participation', 'former', 'question'])]
class EvaluationFormerAnswer extends BaseAnswer
{

    /**
     * @var integer
     */
    #[ORM\Column(name: 'question', type: Types::INTEGER)]
    private ?int $question = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'answer', type: Types::INTEGER)]
    private ?int $answer = null;

    /**
     * @var Participation
     */
    #[ORM\ManyToOne(targetEntity: 'Ed<PERSON><PERSON>\DomainBundle\Entity\Participation', inversedBy: 'evaluationsFormer', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participation', nullable: false)]
    private ?Participation $participation = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'former', nullable: false)]
    private ?Person $former = null;

    /**
     * Set question
     *
     * @param integer $question
     *
     * @return EvaluationFormerAnswer
     */
    public function setQuestion($question)
    {
        $this->question = $question;

        return $this;
    }

    /**
     * Get question
     *
     * @return integer
     */
    public function getQuestion()
    {
        return $this->question;
    }

    /**
     * Set former
     *
     * @param Person $former
     *
     * @return EvaluationFormerAnswer
     */
    public function setFormer(Person $former)
    {
        $this->former = $former;

        return $this;
    }

    /**
     * Get former
     *
     * @return Person
     */
    public function getFormer()
    {
        return $this->former;
    }

    /**
     * @return int
     */
    public function getAnswer()
    {
        return $this->answer;
    }

    /**
     * @param int $answer
     * @return EvaluationFormerAnswer
     */
    public function setAnswer($answer)
    {
        $this->answer = $answer;

        return $this;
    }

    /**
     * @return Participation
     */
    public function getParticipation()
    {
        return $this->participation;
    }

    /**
     * @param Participation $participation
     * @return EvaluationFormerAnswer
     */
    public function setParticipation(Participation $participation)
    {
        $this->participation = $participation;

        return $this;
    }

}
