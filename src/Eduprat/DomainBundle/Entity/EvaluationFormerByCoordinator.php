<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * EvaluationFormerByCoordinator
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\EvaluationFormerByCoordinatorRepository')]
#[ORM\Table(name: 'evaluation_former_by_coordinator')]
#[ORM\UniqueConstraint(name: 'answer_unique', columns: ['programme', 'former', 'coordinator', 'question'])]
class EvaluationFormerByCoordinator
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
    * Question constructor.
    */
    public function __construct()
    {
        $this->createdAt = new \DateTime();
    }

    /**
    * #ORM\PreUpdate
    */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
    * Get id
    *
    * @return integer
    */
    public function getId()
    {
        return $this->id;
    }

    /**
    * Set createdAt
    *
    * @param \DateTime $createdAt
    *
    * @return EvaluationFormerByCoordinator
    */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
    * Get createdAt
    *
    * @return \DateTime
    */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
    * Set updatedAt
    *
    * @param \DateTime $updatedAt
    *
    * @return EvaluationFormerByCoordinator
    */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
    * Get updatedAt
    *
    * @return \DateTime
    */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @var integer
     */
    #[ORM\Column(name: 'question', type: Types::INTEGER)]
    private ?int $question = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'answer', type: Types::INTEGER)]
    private ?int $answer = null;

    /**
     * @var Coordinator
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'coordinator')]
    private ?Coordinator $coordinator = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'former', nullable: false)]
    private ?Person $former = null;

    /**
     * @var Programme
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'programme')]
    private ?Programme $programme = null;

    /**
     * Set question
     *
     * @param integer $question
     *
     * @return EvaluationFormerByCoordinator
     */
    public function setQuestion($question)
    {
        $this->question = $question;

        return $this;
    }

    /**
     * Get question
     *
     * @return integer
     */
    public function getQuestion()
    {
        return $this->question;
    }

    /**
     * Set coordinator
     *
     * @param Coordinator $coordinator
     *
     * @return EvaluationFormerByCoordinator
     */
    public function setCoordinator(Coordinator $coordinator)
    {
        $this->coordinator = $coordinator;

        return $this;
    }
 
    /**
     * Get coordinator
     *
     * @return Person
     */
    public function getCoordinator()
    {
        return $this->coordinator;
    }

    /**
     * Set programme
     *
     * @param Programme $programme
     *
     * @return EvaluationFormerByProgramme
     */
    public function setProgramme(Programme $programme)
    {
        $this->programme = $programme;

        return $this;
    }
  
     /**
      * Get programme
      *
      * @return Programme
      */
    public function getProgramme()
    {
        return $this->programme;
    }

    /**
     * @return int
     */
    public function getAnswer()
    {
        return $this->answer;
    }

    /**
     * @param int $answer
     * @return EvaluationFormerByCoordinator
     */
    public function setAnswer($answer)
    {
        $this->answer = $answer;

        return $this;
    }

    /**
     * Set former
     *
     * @param Person $former
     *
     * @return EvaluationFormerAnswer
     */
    public function setFormer(Person $former)
    {
        $this->former = $former;

        return $this;
    }
 
    /**
     * Get former
     *
     * @return Person
     */
    public function getFormer()
    {
        return $this->former;
    }
}
