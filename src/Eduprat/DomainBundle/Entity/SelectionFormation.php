<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Repository\SelectionFormationRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: SelectionFormationRepository::class)]
#[ORM\Table(name: 'selection_formation')]
#[ORM\UniqueConstraint(name: 'name_unique', columns: ['person', 'nomFavori'])]
class SelectionFormation
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'participants', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'person', nullable: true)]
    private ?Person $person = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'searchFields', type: Types::JSON, nullable: true)]
    private $searchFields;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'isFavori', type: Types::BOOLEAN)]
    private bool $isFavori = false;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'is_temporary', type: Types::BOOLEAN)]
    private bool $isTemporary = true;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'date_ajout_favori', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTime $dateAjoutFavori;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'exclusions', type: Types::JSON)]
    private array $exclusions = [];

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'nomFavori', type: Types::STRING, nullable: true)]
    private ?string $nomFavori;

    /**
     * @var SelectionFormation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\SelectionFormation', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'parent_favori', nullable: true, onDelete: 'SET NULL')]
    private ?\Eduprat\DomainBundle\Entity\SelectionFormation $parentFavori = null;

    /**
     * @param Person $person
     * @param array $searchFields
     */
    public function __construct(Person $person, array $searchFields, ?array $exclusions)
    {
        $this->person = $person;
        $this->searchFields = $searchFields;
        $this->exclusions = $exclusions;
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSearchFields(): ?array
    {
        return array_filter($this->searchFields, function ($searchFields) {
            return count($searchFields);
        });
    }

    public function getCountSearchFields()
    {
        return count($this->searchFields);
    }

    public function setSearchFields(?array $searchFields): self
    {
        $this->searchFields = $searchFields;

        return $this;
    }

    /**
     * @param array $searchFields
     * @return $this
     */
    public function addSearch(array $searchFields, array $exclusions): self
    {
        $this->searchFields[] = $searchFields;
        $this->exclusions[] = $exclusions;
        return $this;
    }

    /**
     * @param array $searchFields
     * @return $this
     */
    public function replaceLastSearch(array $searchFields, array $exclusions): self
    {
        $this->searchFields[count($this->searchFields) - 1] = $searchFields;
        $this->exclusions[count($this->searchFields) - 1] = $exclusions;
        return $this;
    }

    public function completeOtherRequestsExclusions($numRequest, $exclusion) {
        for ($i = 0; $i <= count($this->exclusions)-1; $i++) {
            if ($i > $numRequest) {
                $this->exclusions[$i][] = $exclusion;
            }
        }
    } 

    public function clearLastSearch(): self
    {
        return $this->replaceLastSearch([], []);
    }

    /**
     * Fonction qui remplace une requete dans une recherche
     * @param $numRequest Numéro de la requete à remplacer
     * @param array $searchFields champs + valeur de la requete
     * @param array|null $exclusions id de session à exclure
     * @return void
     */
    public function replaceSearchNum($numRequest, ?array $searchFields, ?array $exclusions)
    {
        if ($searchFields !== null) {
            $this->searchFields[$numRequest] = $searchFields;
        }
        $this->exclusions[$numRequest] = $exclusions;
    }

    public function isFavori(): ?bool
    {
        return $this->isFavori;
    }

    public function setIsFavori(bool $isFavori): self
    {
        $this->isFavori = $isFavori;
        if ($isFavori && !$this->dateAjoutFavori) {
            $this->dateAjoutFavori = new \DateTime();
        }
        return $this;
    }

    public function isTemporary(): bool
    {
        return $this->isTemporary;
    }

    public function setIsTemporary(bool $isTemporary): self
    {
        $this->isTemporary = $isTemporary;
        return $this;
    }

    /**
     * @return \DateTime|null
     */
    public function getDateAjoutFavori(): ?\DateTime
    {
        return $this->dateAjoutFavori;
    }

    /**
     * @param \DateTime|null $dateAjoutFavori
     * @return SelectionFormation
     */
    public function setDateAjoutFavori(?\DateTime $dateAjoutFavori): SelectionFormation
    {
        $this->dateAjoutFavori = $dateAjoutFavori;
        return $this;
    }

    public function getExclusions(): ?array
    {
        return $this->exclusions;
    }

    public function setExclusions(array $exclusions): self
    {
        $this->exclusions = $exclusions;

        return $this;
    }

    public function getPerson(): ?Person
    {
        return $this->person;
    }

    public function setPerson(?Person $person): self
    {
        $this->person = $person;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getNomFavori(): ?string
    {
        return $this->nomFavori;
    }

    /**
     * @param string|null $nomFavori
     * @return SelectionFormation
     */
    public function setNomFavori(?string $nomFavori): SelectionFormation
    {
        $this->nomFavori = $nomFavori;
        return $this;
    }

    public function getParentFavori(): ?SelectionFormation
    {
        return $this->parentFavori;
    }

    public function setParentFavori(?SelectionFormation $parentFavori): self
    {
        $this->parentFavori = $parentFavori;

        return $this;
    }


    /**
     * @return array|null
     */
    public function getLastExclusion() : ?array
    {
        if (!count($this->exclusions)) {
            return null;
        }
        return $this->exclusions[count($this->exclusions) - 1];
    }

    public function getExclusionNum($num): ?array
    {
        return $this->exclusions[$num];
    }

    public function getLastSearchFields(): ?array
    {
        if (!count($this->searchFields)) {
            return null;
        }
        return $this->searchFields[count($this->searchFields) - 1];
    }

    public function isLastSelectionEmpty(): bool
    {
        $lastExclusion = $this->getLastExclusion();
        $lastSearchField = $this->getLastSearchFields();
        return empty($lastExclusion) && ($lastSearchField === null || $this->isSearchEmpty($lastSearchField));
    }

    private function isSearchEmpty(array $lastSearchField): bool
    {
        foreach ($lastSearchField as $lsf) {
            if (is_array($lsf)) {
                if (!empty($lsf)) {
                    return false;
                }
            } elseif ($lsf) {
                return false;
            }
        }
        return true;
    }

    public function convertToFavori($name)
    {
        $this->isFavori = true;
        $this->nomFavori = $name;
        return $this;
    }

    public function cloneInfoParentFavori(SelectionFormation $parentSf): self
    {
        $this->isTemporary= true;
        $this->isFavori = $parentSf->isFavori();
        $this->dateAjoutFavori = new \DateTime();
        $this->nomFavori = 'clone - ' . $parentSf->getNomFavori();
        $this->parentFavori = $parentSf;
        return $this;
    }
    public function mergeAsSave(SelectionFormation $childSelectionFormation): self
    {
        $this->searchFields = $childSelectionFormation->getSearchFields();
        $this->exclusions = $childSelectionFormation->getExclusions();
        $this->dateAjoutFavori = new \DateTime();
        return $this;
    }

    public function resetTemporary(): self
    {
        $this->isTemporary = false;
        $this->parentFavori = null;
        return $this;
    }

    #[Assert\Callback]
    public function checkName(ExecutionContextInterface $context)
    {
        $re = '/^[0-9A-z :\-_]+$/m';

        if ($this->nomFavori && !preg_match($re, $this->nomFavori)) {
            $context->buildViolation("seuls les lettres, chiffres et caractères spéciaux sont autorisés : -_.")
                ->atPath('nomFavori')
                ->addViolation();
        }
    }
}
