<?php

namespace Eduprat\DomainBundle\Entity\Objectif;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\CoordinatorPerson;
use Eduprat\DomainBundle\Form\ObjectifsSalarieDTO;

#[ORM\Entity]
#[ORM\UniqueConstraint(columns: ['person_id', 'year'])]
class ObjectifsSalarie
{
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    public function __construct(
        #[ORM\ManyToOne(targetEntity: CoordinatorPerson::class, inversedBy: 'objectifsSalarie', cascade: ['persist'])]
        private CoordinatorPerson $person,
        #[ORM\Column(name: 'year', type: Types::INTEGER)]
        private int $year,
        #[ORM\Column(name: 'objectif_ca', type: Types::FLOAT)]
        private float $objectifCA,
        #[ORM\Column(name: 'taux_cumul_marge', type: Types::FLOAT)]
        private float $tauxCumulMarge,
    )
    {
    }

    public static function create(ObjectifsSalarieDTO $objectifsSalarieDTO): ObjectifsSalarie
    {
        return new self(
            $objectifsSalarieDTO->person,
            $objectifsSalarieDTO->year,
            $objectifsSalarieDTO->objectifCA,
            $objectifsSalarieDTO->tauxCumulMarge,
        );
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function getObjectifCA(): float
    {
        return $this->objectifCA;
    }

    public function setObjectifCA(float $objectifCA): void
    {
        $this->objectifCA = $objectifCA;
    }

    public function getTauxCumulMarge(): float
    {
        return $this->tauxCumulMarge;
    }

    public function setTauxCumulMarge(float $tauxCumulMarge): void
    {
        $this->tauxCumulMarge = $tauxCumulMarge;
    }
}