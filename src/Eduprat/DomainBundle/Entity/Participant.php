<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Alienor\ElasticBundle\Model\ElasticableInterface;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Form\ProgrammeType;

/**
 * Participant
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ParticipantRepository')]
#[ORM\Table(name: 'participant')]
#[ORM\Index(name: 'opti_participantSearch_crm', columns: ['createdAt', 'is_prospect', 'zipCode', 'city'])]
#[ORM\Index(name: 'opti2_participantSearch_crm', columns: ['lastname', 'firstname', 'email', 'rpps'])]
class Participant implements ElasticableInterface
{

    const CATEGORY_MEDECIN = "Médecin";

    const ELASTIC_INDEX = "participant";

    const STATUS_ACTIF = "Actif";
    const STATUS_ACTIF_REMPLACANT = "Actif Remplaçant";
    const STATUS_RETRAITE_ACTIF = "Retraité actif";
    const STATUS_RETRAITE_INNACTIF = "Retraité inactif";
    const STATUS_DECEDE = "Décédé";
    const STATUS_AUTRE_INNACTIF = "Autre inactif";

    const LEAD_ON_VALUE = "Actif";
    const LEAD_OFF_VALUE = "Inactif";

    const TYPE_GPM = "GPM";
    const TYPE_MSOIGNER = "M Soigner";
    const TYPE_MFM = "MFM";
    const TYPE_PODOLOGUE = "Union des podologues";
    const TYPE_SITE_INTERNET = "Site internet";

    const PART_GPM = "GPM";
    const PART_MSOIGNER = "M Soigner";
    const PART_MFM = "MFM";
    const PART_PODOLOGUE = "Union des podologues";
    const PART_SITE_INTERNET = "Site internet";
    const PART_LBI = "LBI";
    const PART_PHARMAZON = "Pharmazon";

    const OTS_CPTS = "CPTS";
    const OTS_MSP = "MSP";
    const OTS_CDS = "CDS";
    const OTS_CH = "CH";
    const OTS_CLINIQUE = "Clinique";
    const OTS_ASSOCIATION = "Association";


    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'civility', type: Types::STRING, length: 255, nullable: true)]
    private ?string $civility = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'lastname', type: Types::STRING, length: 255)]
    private ?string $lastname = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'firstname', type: Types::STRING, length: 255)]
    private ?string $firstname = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'birthDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $birthDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zipCode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zipCode = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'region', type: Types::STRING, length: 255, nullable: true)]
    private ?string $region = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'city', type: Types::STRING, length: 255, nullable: true)]
    private ?string $city = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'phone', type: Types::STRING, length: 255, nullable: true)]
    private ?string $phone = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'mobile', type: Types::STRING, length: 255, nullable: true)]
    private ?string $mobile = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'birthName', type: Types::STRING, length: 255, nullable: true)]
    private ?string $birthName = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'rpps', type: Types::STRING, length: 255, nullable: true)]
    private ?string $rpps = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'adeli', type: Types::STRING, length: 255, nullable: true)]
    private ?string $adeli = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'login', type: Types::STRING, length: 255, nullable: true)]
    private ?string $login = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'email', type: Types::STRING, length: 255, nullable: true)]
    private ?string $email = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'category', type: Types::STRING, length: 255, nullable: true)]
    private ?string $category = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'speciality', type: Types::STRING, length: 255, nullable: true)]
    private ?string $speciality = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'uga', type: Types::STRING, length: 255, nullable: true)]
    private ?string $uga = null;

    /**
     * @var Collection<int, Participation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', mappedBy: 'participant', cascade: ['remove'])]
    private Collection $participations;

    /**
     * @var Collection<int, ParticipantDateDownload>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ParticipantDateDownload', mappedBy: 'participant', cascade: ['remove'])]
    private Collection $downloadDates;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * @var Person
     */
    #[ORM\OneToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'participant', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(name: 'user_id', nullable: true)]
    private ?Person $user = null;


    #[ORM\Column(name: 'latitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $latitude = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'longitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $longitude = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'gdprAgreement', type: Types::BOOLEAN, nullable: true)]
    private ?bool $gdprAgreement = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'gdprAgreementPost', type: Types::BOOLEAN, nullable: true)]
    private ?bool $gdprAgreementPost = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'gdprAgreementCall', type: Types::BOOLEAN, nullable: true)]
    private ?bool $gdprAgreementCall = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'gdprAgreementHistory', type: Types::JSON, nullable: true)]
    private $gdprAgreementHistory;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'hasDoneTutorial', type: Types::BOOLEAN, nullable: true)]
    private ?bool $hasDoneTutorial = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifInscription', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifInscription = true;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifSurveyOpen', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifSurveyOpen = true;
    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifRemindSurvey', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifRemindSurvey = true;
    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifRemindSession', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifRemindSession = true;
    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifNewSession', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifNewSession = true;
    /**
     * @var bool
     */
    #[ORM\Column(name: 'notifSessionChange', type: Types::BOOLEAN, nullable: true)]
    private ?bool $notifSessionChange = true;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'is_prospect', type: Types::BOOLEAN)]
    protected ?bool $isProspect = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: Types::STRING, nullable: true)]
    protected ?string $status = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'participants', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'coordinator', nullable: true)]
    private ?Person $coordinator = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'comments', type: Types::TEXT, nullable: true)]
    protected ?string $comments = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'advisor', nullable: true)]
    private ?Person $advisor = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'leadReferent', nullable: true)]
    private ?Person $leadReferent = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'leadCreationDate', type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $leadCreationDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadContactDate', type: Types::STRING, nullable: true)]
    private ?string $leadContactDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadStatus', type: Types::STRING, nullable: true)]
    private ?string $leadStatus = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadState', type: Types::STRING, nullable: true)]
    private ?string $leadState = "A traiter";

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadType', type: Types::STRING, nullable: true)]
    private ?string $leadType = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'partenariat', type: Types::STRING, nullable: true)]
    private ?string $partenariat = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'ots', type: Types::STRING, nullable: true)]
    private ?string $ots = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadComment', type: Types::TEXT, nullable: true)]
    private ?string $leadComment = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadCommentEduprat', type: Types::STRING, nullable: true)]
    private ?string $leadCommentEduprat = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'gpmMemberNumber', type: Types::STRING, nullable: true)]
    private ?string $gpmMemberNumber = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'codeApporteur', type: Types::STRING, nullable: true)]
    private ?string $codeApporteur = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'exerciceMode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $exerciceMode = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'toUpdateSib', type: Types::BOOLEAN, nullable: true)]
    protected ?bool $toUpdateSib = null;
    
    /**
     * @var bool
     */
    #[ORM\Column(name: 'noMailing', type: Types::BOOLEAN)]
    private ?bool $noMailing = false;

    /**
     * @var Collection<int, LeadHistory>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\LeadHistory', mappedBy: 'participant', cascade: ['remove'])]
    #[ORM\OrderBy(['leadCreationDate' => 'DESC'])]
    private Collection $leadHistories;

    /**
     * Participant constructor.
     */
    public function __construct()
    {
        $this->participations = new ArrayCollection();
        $this->leadHistories = new ArrayCollection();
        $this->downloadDates = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->gdprAgreement = null;
        $this->gdprAgreementPost = null;
        $this->gdprAgreementCall = null;
        $this->gdprAgreementHistory = null;
        $this->isProspect = true;
        $this->notifInscription = true;
        $this->notifSurveyOpen = true;
        $this->notifRemindSurvey = true;
        $this->notifRemindSession = true;
        $this->notifNewSession = true;
        $this->notifSessionChange = true;
        $this->status = self::STATUS_ACTIF;
    }

    public function __toArray($complete = false)
    {
        $person = array();

        $person['firstname'] = $this->getFirstname();
        $person['lastname'] = $this->getLastname();
        $person['email'] = $this->getEmail();
        $person['createdAt'] = $this->getCreatedAt()->format('d/m/Y');
        $person['adeli'] = $this->getAdeli();
        $person['rpps'] = $this->getRpps();
        $person['zipCode'] = $this->getZipCode();

        if(!is_null($this->getUser())) {
            $person['created'] = $this->getUser()->isHasCreatedPassword();
        }
        else {
            $person['created'] = false;
        }

        if ($complete) {
            $person['civility'] = $this->getCivility();
            $person['address'] = $this->getAddress();
            $person['city'] = $this->getCity();
            $person['region'] = '';
            $person['phone'] = $this->getPhone();
            $person['mobile'] = $this->getMobile();
            $person['birthname'] = $this->getBirthName();
            $person['category'] = $this->getCategory();
            $person['speciality'] = $this->getSpeciality();
            $person['uga'] = $this->getUga();

            $coordinators = [];
            foreach ($this->getParticipations() as $participation) {
                foreach ($participation->getFormation()->getCoordinators() as $coordinator) {
                    $coordinators[$coordinator->getPerson()->getId()] = $coordinator->getPerson()->getFirstname().' '.$coordinator->getPerson()->getLastname();
                }
            }
            $person['coordinator'] = implode(', ', $coordinators);

            $person['gdprAgreementNotify'] = null;
            $person['gdprAgreementEmailExport'] = $this->isGdprAgreement();
            $person['gdprAgreementEmailExportDate'] = null;
            $person['gdprAgreementEmailExportHistory'] = null;
            $person['gdprAgreementPostExport'] = $this->isGdprAgreementPost();
            $person['gdprAgreementPostExportDate'] = null;
            $person['gdprAgreementPostExportHistory'] = null;
            $person['gdprAgreementCallExport'] = $this->isGdprAgreementCall();
            $person['gdprAgreementCallExportDate'] = null;
            $person['gdprAgreementCallExportHistory'] = null;
        }

        $person['leadStatus'] = $this->getLeadStatus() ? $this->getLeadStatus() : "Non lead";
        $person['partenariat'] = $this->getPartenariat();
        $person['ots'] = $this->getOts();

        return $person;
    }

    public function __toArrayAnalysis()
    {
        $person = array();

        $person['uga'] = $this->getUga();
        $person['lastname'] = $this->getLastname();
        $person['firstname'] = $this->getFirstname();
        $person['speciality'] = $this->getSpeciality();
        $person['address'] = $this->getAddress();
        $person['zipCode'] = $this->getZipCode();
        $person['city'] = $this->getCity();
        $person['email'] = $this->getEmail();
        $person['phone'] = $this->getPhone();
        $person['mobile'] = $this->getMobile();
        $person['rpps'] = $this->getRpps();
        $person['adeli'] = $this->getAdeli();

        return $person;
    }

    public function __toArrayLead()
    {
        $person = array();
        $person['region'] = $this->getRegion();
        $person['lastname'] = $this->getLastname();
        $person['firstname'] = $this->getFirstname();
        $person['leadCreationDate'] = $this->getLeadCreationDate() ? $this->getLeadCreationDate()->format('d/m/Y') : null;
        $person['leadType'] = $this->getLeadType();
        $person['status'] = $this->getLeadStatus();
        $person['advisor'] = $this->getAdvisor() ? $this->getAdvisor()->getInvertedFullname() : null;
        $person['leadReferent'] = $this->getLeadReferent() ? $this->getLeadReferent()->getInvertedFullname() : null;
        $person['category'] = $this->getCategory();
        $person['speciality'] = $this->getSpeciality();
        $person['leadContactDate'] = $this->getLeadContactDate();
        $person['leadComment'] = $this->getLeadComment();
        $person['leadState'] = $this->getLeadState() ? $this->getLeadState() : "A traiter";
        $person['leadCommentEduprat'] = $this->getLeadCommentEduprat();
        $person['adressePost'] = $this->getAddress();
        $person['zipCode'] = $this->getZipCode();
        $person['city'] = $this->getCity();
        $person['email'] = $this->getEmail();
        $person['phone'] = $this->getPhone();
        $person['type'] = $this->isProspect() ? "Prospect" : "Participant";
        $person['exerciseMode'] = $this->getExerciceMode();
        $person['inscriptions'] = count($this->getLeadParticipations($this->getLeadCreationDate() ? $this->getLeadCreationDate() : new \DateTime(), new DateTime('last day of December')));
        $person['endParcours'] = $this->getParticipationsCompletedCount($this->getLeadCreationDate() ? $this->getLeadCreationDate() : null, new DateTime('last day of December'));
        $person['rpps'] = $this->getRpps();


        return $person;
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set civility
     *
     * @param string $civility
     * @return Participant
     */
    public function setCivility($civility)
    {
        $this->civility = $civility;

        return $this;
    }

    /**
     * Get civility
     *
     * @return string
     */
    public function getCivility()
    {
        return $this->civility;
    }

    public function resumeCivility() {
        switch ($this->civility) {
            case 'Monsieur':
                return 'M.';
            case 'Madame': 
                return 'MME';
            default:
                return "Dr";
        }
    }

    /**
     * Set lastname
     *
     * @param string $lastname
     * @return Participant
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * Get lastname
     *
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * Set firstname
     *
     * @param string $firstname
     * @return Participant
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * Get firstname
     *
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * Set birthDate
     *
     * @param \DateTime $birthDate
     * @return Participant
     */
    public function setBirthDate($birthDate)
    {
        $this->birthDate = $birthDate;

        return $this;
    }

    /**
     * Get birthDate
     *
     * @return \DateTime
     */
    public function getBirthDate()
    {
        return $this->birthDate;
    }

    /**
     * Set address
     *
     * @param string $address
     * @return Participant
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set address2
     *
     * @param string $address2
     * @return Participant
     */
    public function setAddress2($address2)
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Get address2
     *
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Set zipCode
     *
     * @param string $zipCode
     * @return Participant
     */
    public function setZipCode($zipCode)
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    /**
     * Get zipCode
     *
     * @return string
     */
    public function getZipCode()
    {
        return $this->zipCode;
    }

    /**
     * Set region
     *
     * @param string $region
     * @return Participant
     */
    public function setRegion($region)
    {
        $this->region = $region;

        return $this;
    }

    /**
     * Get region
     *
     * @return string
     */
    public function getRegion()
    {
        return $this->region;
    }

    /**
     * Set city
     *
     * @param string $city
     * @return Participant
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set phone
     *
     * @param string $phone
     * @return Participant
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Get phone
     *
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * Set mobile
     *
     * @param string $mobile
     * @return Participant
     */
    public function setMobile($mobile)
    {
        $this->mobile = $mobile;

        return $this;
    }

    /**
     * Get mobile
     *
     * @return string
     */
    public function getMobile()
    {
        return $this->mobile;
    }

    /**
     * Set birthName
     *
     * @param string $birthName
     * @return Participant
     */
    public function setBirthName($birthName)
    {
        $this->birthName = $birthName;

        return $this;
    }

    /**
     * Get birthName
     *
     * @return string
     */
    public function getBirthName()
    {
        return $this->birthName;
    }

    /**
     * Set rpps
     *
     * @param string $rpps
     * @return Participant
     */
    public function setRpps($rpps)
    {
        $this->rpps = $rpps;

        return $this;
    }

    /**
     * Get rpps
     *
     * @return string
     */
    public function getRpps()
    {
        return $this->rpps;
    }

    /**
     * Set adeli
     *
     * @param string $adeli
     * @return Participant
     */
    public function setAdeli($adeli)
    {
        $this->adeli = $adeli;

        return $this;
    }

    /**
     * Get adeli
     *
     * @return string
     */
    public function getAdeli()
    {
        return $this->adeli;
    }

    /**
     * @return string
     */
    public function getLogin()
    {
        return $this->login;
    }

    /**
     * @param string $login
     * @return Participant
     */
    public function setLogin($login)
    {
        $this->login = $login;
        return $this;
    }

    /**
     * Set email
     *
     * @param string $email
     * @return Participant
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set category
     *
     * @param string $category
     * @return Participant
     */
    public function setCategory($category)
    {
        $this->category = $category;

        return $this;
    }

    /**
     * Get category
     *
     * @return string
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * Set speciality
     *
     * @param string $speciality
     * @return Participant
     */
    public function setSpeciality($speciality)
    {
        $this->speciality = $speciality;

        return $this;
    }

    /**
     * Get speciality
     *
     * @return string
     */
    public function getSpeciality()
    {
        return $this->speciality;
    }

    /**
     * Set uga
     *
     * @param string $uga
     * @return Participant
     */
    public function setUga($uga)
    {
        $this->uga = $uga;

        return $this;
    }

    /**
     * Get uga
     *
     * @return string
     */
    public function getUga()
    {
        return $this->uga;
    }

    public function setIsProspect($value) {
        $this->isProspect = $value;
        return $this;
    }

    public function isProspect() {
        return $this->isProspect;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     * @return Participant
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Participant
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set user
     *
     * @param Person $user
     * @return Participant
     */
    public function setUser(Person $user = null)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return Person
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Add participations
     *
     * @param Participation $participations
     * @return Participant
     */
    public function addParticipation(Participation $participations)
    {
        $this->participations[] = $participations;

        return $this;
    }

    /**
     * Remove participations
     *
     * @param Participation $participations
     */
    public function removeParticipation(Participation $participations)
    {
        $this->participations->removeElement($participations);
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getParticipations()
    {
        if (!$this->participations) {
            return $this->participations;
        }
        return $this->participations->matching(Criteria::create()->where(Criteria::expr()->eq('archived', false)));
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getLeadParticipations($startDate = null, $endDate = null)
    {
        if (!$this->participations) {
            return $this->participations;
        }
        $leadParticipations = $this->getParticipations()->matching(Criteria::create()->where(Criteria::expr()->eq('isLead', true)));
        if ($startDate && $endDate) {
            $leadParticipations = $leadParticipations->filter(function ($leadParticipation) use ($startDate, $endDate) {
                return $leadParticipation->getCreatedAt() >= $startDate && $leadParticipation->getCreatedAt() <= $endDate;
            });
        }
        return $leadParticipations;
    }

    /**
     * @return Participation[]|Collection
     */
    public function getAllParticipations()
    {
        return $this->participations;
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getParticipationsCompletedCount($startDate = null, $endDate = null)
    {
        $count = 0;
        if (!$this->participations) {
            return $count;
        }
        $leadParticipations = $this->getParticipations();
        if ($startDate && $endDate) {
            $leadParticipations = $leadParticipations->filter(function ($leadParticipation) use ($startDate, $endDate) {
                return $leadParticipation->getCreatedAt() >= $startDate && $leadParticipation->getCreatedAt() <= $endDate;
            });
        }
        foreach($leadParticipations as $participation) {
            $count = $participation->isStepCompleted('end') ? $count + 1 : $count;
        }
       return $count;
    }

    /**
     *
     * @return Integer
     */
    public function getNextParticipations()
    {
        $count = 0;
        $now = New DateTime('now');

        foreach($this->participations as $participation) {
            if ($participation->getFormation()->getOpeningDate() > $now && !$participation->isArchived()) {
                $count = $count + 1;
            }
        }
        return $count;
    }

    /**
     *
     * @return Integer
     */
    public function getClosedParticipations()
    {
        $count = 0;

        foreach($this->participations as $participation) {
            if ($participation->getFormation()->getClosed() && !$participation->isArchived()) {
                $count = $count + 1;
            }
        }
        return $count;
    }

    /**
     *
     * @return Integer
     */
    public function getUnClosedExpiredParticipations()
    {
        $count = 0;
        $now = new DateTime('now');

        foreach($this->participations as $participation) {
            if (!$participation->getFormation()->getClosed() && $participation->getFormation()->getClosingDate() < $now && !$participation->isArchived()) {
                $count = $count + 1;
            }
        }
        return $count;
    }

    public function getLastParticipation() {
        $lastParticipation = null;
        foreach($this->getParticipations() as $participation) {
            if ($lastParticipation == null || $participation->getFormation()->getStartDate() > $lastParticipation->getFormation()->getStartDate()) {
                $lastParticipation = $participation;
            }
        }
        return $lastParticipation;
    }

    /**
     * Add leadHistory
     *
     * @param LeadHistory $leadHistory
     * @return Participant
     */
    public function addLeadHistory(LeadHistory $leadHistory)
    {
        $this->leadHistories[] = $leadHistory;

        return $this;
    }

    /**
     * Remove leadHistory
     *
     * @param LeadHistory $leadHistory
     */
    public function removeLeadHistory(LeadHistory $leadHistory)
    {
        $this->leadHistories->removeElement($leadHistory);
    }

    /**
     * Get leadHistories
     *
     * @return LeadHistory[]|Collection
     */
    public function getLeadHistories()
    {
        return $this->leadHistories;
    }

    public function getLeadHistoriesBetweenDates($startDate = null, $endDate = null)
    {
        $leadHistories = $this->getLeadHistories();
        if ($startDate && $endDate) {
            $leadHistories = $leadHistories->filter(function ($leadHistory) use ($startDate, $endDate) {
                return $leadHistory->getLeadCreationDate() >= $startDate && $leadHistory->getLeadCreationDate() <= $endDate;
            });
        } elseif ($startDate) {
            $leadHistories = $leadHistories->filter(function ($leadHistory) use ($startDate) {
                return $leadHistory->getLeadCreationDate() >= $startDate;
            });
        } elseif ($endDate) {
            $leadHistories = $leadHistories->filter(function ($leadHistory) use ($endDate) {
                return $leadHistory->getLeadCreationDate <= $endDate;
            });
        }

        return $leadHistories;
    }

    /**
     * Add downloadDates
     *
     * @param ParticipantDateDownload $downloadDates
     * @return Participant
     */
    public function addDownloadDates(ParticipantDateDownload $downloadDates)
    {
        $this->downloadDates[] = $downloadDates;

        return $this;
    }

    /**
     * Remove downloadDates
     *
     * @param ParticipantDateDownload $downloadDates
     */
    public function removeDownloadDates(ParticipantDateDownload $downloadDates)
    {
        $this->downloadDates->removeElement($downloadDates);
    }

    /**
     * Get downloadDates
     *
     * @return Collection
     */
    public function getDownloadDates()
    {
        return $this->downloadDates;
    }

    /**
     * @return string
     */
    public function getFullname()
    {
        return $this->firstname . ' ' . $this->lastname;
    }

    /**
     * @return string
     */
    public function getInvertedFullname()
    {
        return $this->lastname . ' ' . $this->firstname;
    }

    /**
     * @return string
     */
    public function getLatitude()
    {
        return $this->latitude;
    }

    /**
     * @param string $latitude
     * @return Participant
     */
    public function setLatitude($latitude)
    {
        $this->latitude = $latitude;
        return $this;
    }

    /**
     * @return bool
     */
    public function isNotifInscription()
    {
        return $this->notifInscription;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifInscription($notifInscription)
    {
        $this->notifInscription = $notifInscription;
        return $this;
    }


    /**
     * @return bool
     */
    public function isNotifSurveyOpen()
    {
        return $this->notifSurveyOpen;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifSurveyOpen($notifSurveyOpen)
    {
        $this->notifSurveyOpen = $notifSurveyOpen;
        return $this;
    }


    /**
     * @return bool
     */
    public function isNotifRemindSurvey()
    {
        return $this->notifRemindSurvey;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifRemindSurvey($notifRemindSurvey)
    {
        $this->notifRemindSurvey = $notifRemindSurvey;
        return $this;
    }


    /**
     * @return bool
     */
    public function isNotifRemindSession()
    {
        return $this->notifRemindSession;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifRemindSession($notifRemindSession)
    {
        $this->notifRemindSession = $notifRemindSession;
        return $this;
    }


    /**
     * @return bool
     */
    public function isNotifNewSession()
    {
        return $this->notifNewSession;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifNewSession($notifNewSession)
    {
        $this->notifNewSession = $notifNewSession;
        return $this;
    }


    /**
     * @return bool
     */
    public function isNotifSessionChange()
    {
        return $this->notifSessionChange;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setNotifSessionChange($notifSessionChange)
    {
        $this->notifSessionChange = $notifSessionChange;
        return $this;
    }



    /**
     * @return bool
     */
    public function isGdprAgreement()
    {
        return $this->gdprAgreement;
    }

    /**
     * @param bool $gdprAgreement
     * @return Participant
     */
    public function setGdprAgreement($gdprAgreement)
    {
        $this->gdprAgreement = $gdprAgreement;
        return $this;
    }

    /**
     * @return bool
     */
    public function isGdprAgreementPost()
    {
        return $this->gdprAgreementPost;
    }

    /**
     * @param bool $gdprAgreementPost
     * @return Participant
     */
    public function setGdprAgreementPost($gdprAgreementPost)
    {
        $this->gdprAgreementPost = $gdprAgreementPost;
        return $this;
    }

    /**
     * @return bool
     */
    public function isGdprAgreementCall()
    {
        return $this->gdprAgreementCall;
    }

    /**
     * @param bool $gdprAgreementCall
     * @return Participant
     */
    public function setGdprAgreementCall($gdprAgreementCall)
    {
        $this->gdprAgreementCall = $gdprAgreementCall;
        return $this;
    }

    /**
     * @return array
     */
    public function getGdprAgreementHistory()
    {
        return $this->gdprAgreementHistory;
    }

    /**
     * @param array $gdprAgreementHistory
     * @return Participant
     */
    public function setGdprAgreementHistory($gdprAgreementHistory)
    {
        $this->gdprAgreementHistory = $gdprAgreementHistory;
        return $this;
    }

    /**
     * @return string
     */
    public function getLongitude()
    {
        return $this->longitude;
    }

    /**
     * @param string $longitude
     * @return Participant
     */
    public function setLongitude($longitude)
    {
        $this->longitude = $longitude;
        return $this;
    }

    /**
     * @return bool
     */
    public function isHasDoneTutorial()
    {
        return $this->hasDoneTutorial;
    }

    /**
     * @param bool $hasDoneTutorial
     * @return Participant
     */
    public function setHasDoneTutorial($hasDoneTutorial)
    {
        $this->hasDoneTutorial = $hasDoneTutorial;
        return $this;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param string $status
     * @return Participant
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    public function isActif() {
        return $this->status === self::STATUS_ACTIF || $this->status === self::STATUS_ACTIF_REMPLACANT || $this->status === self::STATUS_ACTIF_REMPLACANT;
    }

    /**
     * @return Person
     */
    public function getCoordinator()
    {
        return $this->coordinator;
    }

    public function hasCoordinator()
    {
        return $this->coordinator && !$this->coordinator->isArchived();
    }

    public function hasLeadReferent()
    {
        return $this->leadReferent && !$this->leadReferent->isArchived();
    }

    /**
     * @param Person $coordinator
     * @return Participant
     */
    public function setCoordinator($coordinator)
    {
        $this->coordinator = $coordinator;

        return $this;
    }

    /**
     * @return string
     */
    public function getComments()
    {
        return $this->comments;
    }

    /**
     * @param string $comments
     * @return Participant
     */
    public function setComments($comments)
    {
        $this->comments = $comments;
        return $this;
    }

    /**
     * @return boolean
     */
    public function isNoMailing()
    {
        return $this->noMailing;
    }

    /**
     * @param boolean $noMailing
     * @return Formation
     */
    public function setNoMailing($noMailing)
    {
        $this->noMailing = $noMailing;

        return $this;
    }

    // START LEAD BLOCK

    /**
     * @param string $leadStatus
     */
    public function getLeadStatus() {
        return $this->leadStatus;
    }

    /**
     * @param string $leadStatus
     * @return Participant
     */
    public function setLeadStatus($leadStatus) {
        $this->leadStatus = $leadStatus;
        return $this;
    }

    /**
     * @param string $leadState
     */
    public function getLeadState() {
        return $this->leadState;
    }

    /**
     * @param string $leadState
     * @return Participant
     */
    public function setLeadState($leadState) {
        $this->leadState = $leadState;
        return $this;
    }

    /**
     * @param string $leadType
     */
    public function getLeadType() {
        return $this->leadType;
    }

    /**
     * @param string $leadType
     * @return Participant
     */
    public function setLeadType($leadType) {
        $this->leadType = $leadType;
        return $this;
    }

    /**
     * @param string $partenariat
     */
    public function getPartenariat() {
        return $this->partenariat;
    }

    /**
     * @param string $partenariat
     * @return Participant
     */
    public function setPartenariat($partenariat) {
        $this->partenariat = $partenariat;
        return $this;
    }

    /**
     * @param string $leadComment
     */
    public function getLeadComment() {
        return $this->leadComment;
    }

    /**
     * @param string $leadComment
     * @return Participant
     */
    public function setLeadComment($leadComment) {
        $this->leadComment = $leadComment;
        return $this;
    }

    /**
     * @param string $leadCommentEduprat
     */
    public function getLeadCommentEduprat() {
        return $this->leadCommentEduprat;
    }

    /**
     * @param string $leadCommentEduprat
     * @return Participant
     */
    public function setLeadCommentEduprat($leadCommentEduprat) {
        $this->leadCommentEduprat = $leadCommentEduprat;
        return $this;
    }

    /**
     * @param string $gpmMemberNumber
     */
    public function getGpmMemberNumber() {
        return $this->gpmMemberNumber;
    }

    /**
     * @param string $gpmMemberNumber
     * @return Participant
     */
    public function setGpmMemberNumber($gpmMemberNumber) {
        $this->gpmMemberNumber = $gpmMemberNumber;
        return $this;
    }

        /**
     * Set codeApporteur
     *
     * @param string $codeApporteur
     * @return Participant
     */
    public function setCodeApporteur($codeApporteur)
    {
        $this->codeApporteur = $codeApporteur;

        return $this;
    }

    /**
     * Get codeApporteur
     *
     * @return string
     */
    public function getCodeApporteur()
    {
        return $this->codeApporteur;
    }


    /**
     * @param string $ots
     */
    public function getOts() {
        return $this->ots;
    }

    /**
     * @param string $ots
     * @return Participant
     */
    public function setOts($ots) {
        $this->ots = $ots;
        return $this;
    }

    /**
     * Set leadCreationDate
     *
     * @param \DateTime $leadCreationDate
     * @return Participant
     */
    public function setLeadCreationDate(\DateTime $leadCreationDate = null)
    {
        $this->leadCreationDate = $leadCreationDate;
        return $this;
    }

    /**
     * Get leadCreationDate
     *
     * @return \DateTime
     */
    public function getLeadCreationDate()
    {
        return $this->leadCreationDate;
    }

    /**
     * Set leadContactDate
     *
     * @param string|null $leadContactDate
     * @return Participant
     */
    public function setLeadContactDate(string $leadContactDate = null)
    {
        $this->leadContactDate = $leadContactDate;
        return $this;
    }

    /**
     * Get leadContactDate
     *
     * @return string
     */
    public function getLeadContactDate()
    {
        return $this->leadContactDate;
    }

    /**
     * @return Person
     */
    public function getAdvisor()
    {
        return $this->advisor;
    }

    /**
     * @param Person $advisor
     * @return Participant
     */
    public function setAdvisor($advisor)
    {
        $this->advisor = $advisor;
        return $this;
    }

    /**
     * @return Person
     */
    public function getLeadReferent()
    {
        return $this->leadReferent;
    }

    /**
     * @param Person $leadReferent
     * @return Participant
     */
    public function setLeadReferent($leadReferent)
    {
        $this->leadReferent = $leadReferent;
        return $this;
    }

     /**
     * Set exerciceMode
     *
     * @param string $exerciceMode
     * @return Participant
     */
    public function setExerciceMode($exerciceMode)
    {
        $this->exerciceMode = $exerciceMode;
        return $this;
    }

    /**
     * Get exerciceMode
     *
     * @return string
     */
    public function getExerciceMode()
    {
        return $this->exerciceMode;
    }

    public function isLead() {
        return $this->leadStatus != null;
    }

    public function isLeadOn() {
        return $this->leadStatus === self::LEAD_ON_VALUE;
    }

    public function isLeadOff() {
        return $this->leadStatus === self::LEAD_OFF_VALUE;
    }

    public function isLeadGpm() {
        return $this->leadType === self::TYPE_GPM;
    }

    public function isLeadMSoigner() {
        return $this->leadType === self::TYPE_MSOIGNER;
    }

    public function isLeadMfm() {
        return $this->leadType === self::TYPE_MFM;
    }

    public function isLeadPodologue() {
        return $this->leadType === self::TYPE_PODOLOGUE;
    }

    public function isLeadSiteInternet() {
        return $this->leadType === self::TYPE_SITE_INTERNET;
    }

     /**
     * @return bool
     */
    public function isToUpdateSib()
    {
        return $this->toUpdateSib;
    }

    /**
     * @param bool $toUpdateSib
     * @return Person
     */
    public function setToUpdateSib($toUpdateSib)
    {
        $this->toUpdateSib = $toUpdateSib;
        return $this;
    }

    // public function hasAdvisor()
    // {
    //     return $this->advisor && !$this->advisor->isArchived();
    // }


    // EN LEAD BLOCK


    /**
     * On peut créer un compte pour le participant uniquement s'il n'en a pas déjà un, si il a un email et au moins un code rpps ou adeli
     * @return bool
     */
    public function canCreateAccount() {
        return is_null($this->getUser()) && !is_null($this->email);
    }

    public function isMedecin() {
        return $this->category === self::CATEGORY_MEDECIN;
    }

    public function hasNoIdentifier() {
        return is_null($this->adeli) && is_null($this->rpps);
    }

    public function getIdentifier() {
        return $this->getRpps() ?? $this->getAdeli() ?? $this->getLogin();
    }

    public function getElasticIndex(): string
    {
        return self::ELASTIC_INDEX;
    }

    public function toArraySib()
    {
        $coordinators = [];
        $exerciceMode = [];
        $i = 0;

        $participations = $this->getParticipations();
        if ($participations) {
            foreach ($participations as $participation) {
                $exerciceMode[$i] = $participation->getExerciseMode();
                foreach ($participation->getFormation()->getCoordinators() as $coordinator) {
                    $coordinators[$i] = $coordinator->getPerson()->getFirstname().' '.$coordinator->getPerson()->getLastname();
                    $i++;
                }
            }
        }

        $coordinator = isset($coordinators[0]) ? $coordinators[0] : "";
        $exerciceMode = isset($exerciceMode[0]) ? $exerciceMode[0] : "";
        $person = array();

        $sms = $this->isGdprAgreementCall() == false ? $this->isGdprAgreementCall() : true;
        $mail = ($this->isGdprAgreement() == true || $this->isGdprAgreement() == null) && $this->isActif() ? true : false;

        $person['PRENOM'] = $this->getFirstname();
        $person['NOM'] = $this->getLastname();
        $person['CODEPOSTAL'] = $this->getZipCode();
        // $person['VILLE'] = $this->getCity();
        $person['CATEGORIE'] = $this->getCategory();
        $person['SPECIALITE'] = $this->getSpeciality();
        $person['COORDINATEUR'] = $coordinator;
        $person['CONSENTEMENT_EMAIL'] = $mail;
        // $person['CONSENTEMENT_COURRIER'] = $this->isGdprAgreementPost() ? $this->isGdprAgreementPost() : true;
        $person['CONSENTEMENT_APPEL'] = $sms;
        $person['MODE_EXERCICE'] = $exerciceMode;

        return $person;
    }

    /**
     * Retourne les participations effectuées par le participant dans l'année passée en paramêtre
     * @param null $year
     * @return Collection|Participation[]
     */
    public function getParticipationsDone($year = null, $closed = false)
    {
        return $this->getParticipations()->filter(function (Participation $p) use ($year, $closed) {
            $valid = $closed === false || $p->getFormation()->getClosed();
            $yearValid = ($year === null || ($p->getFormation()->getStartDate()->format('Y') === (string) $year));
            return !$p->isArchived() && $valid && $yearValid;
        });
    }

    public function getFormationsStats($csv = false)
    {
        $currentYear = (int) (new \DateTime())->format("Y");
        $startYear = $csv ? 2017 : $currentYear - 2;
        $years = range($startYear, $currentYear);
        $stats = array();
        foreach ($years as $year) {
            $stats[$year] = array(
                "hours" => 0,
                "formations" => 0,
                "presences" => array()
            );
            foreach (ProgrammeType::getPresences() as $presence) {
                $stats[$year]["presences"][$presence] = 0;
            }
            foreach ($this->getParticipationsDone($year, false) as $participation) {
                $stats[$year]["hours"] += $participation->getNbHour();
                $stats[$year]["formations"]++;
                $presence = $participation->getFormation()->getProgramme()->getPresence();
                if ($presence) {
                    $stats[$year]["presences"][$presence]++;
                }
            }
        }
        return $stats;
    }

    public function getFirstFormationDate()
    {
        $iterator = $this->getParticipations()->getIterator();
        $iterator->uasort(function (Participation $a, Participation $b) {
            return $a->getFormation()->getStartDate()->getTimestamp() <=> $b->getFormation()->getStartDate()->getTimestamp();
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));

        return $participations->count() ? $participations->first()->getFormation()->getStartDate() : $this->getCreatedAt();
    }
}
