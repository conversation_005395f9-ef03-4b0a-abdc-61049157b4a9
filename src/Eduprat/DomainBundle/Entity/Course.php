<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Course
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\CourseRepository')]
#[ORM\Table(name: 'course')]
class Course
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formation
     */
    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', mappedBy: 'course')]
    private ?Formation $formation = null;

    /**
     * @var Collection<int, Step>
     */
    #[ORM\OneToMany(targetEntity: 'Edup<PERSON>\DomainBundle\Entity\Step', mappedBy: 'course', orphanRemoval: true, cascade: ['persist', 'remove'])]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'ASC'])]
    private Collection $steps;

    /**
     * Formation constructor.
     */
    public function __construct(array $steps)
    {
        $this->steps = new ArrayCollection();
        foreach($steps as $step) {
            $this->addStep($step);
        }
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Add step
     *
     * @param Step $step
     * @return Course
     */
    public function addStep(Step $step)
    {
        $step->setCourse($this);
        $this->steps[] = $step;
        return $this;
    }

    /**
     * Remove step
     *
     * @param Step $step
     */
    public function removeStep(Step $step)
    {
        $this->steps->removeElement($step);
    }

    /**
     * Get steps
     *
     * @return Collection
     */
    public function getSteps()
    {
        return $this->steps;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     * @return Course
     */
    public function setFormation(Formation $formation)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    public function getEndModule(): ?Module
    {
        foreach ($this->getSteps() as $stepCourse) {
            foreach ($stepCourse->getModules() as $module) {
                if ($module->isEndModule()) {
                    return $module;
                }
            }
        }
        return null;
    }

    public function toArray(): array
    {
        $tmp = [];
        foreach ($this->steps as $key => $step) {
            $step_name = 'step_'. ($key + 1);
            $tmp[$step_name] = ['modules' => []];
            foreach ($step->getModules() as $module) {
                $tmp[$step_name]['modules'][] = $module->toArray();
            }
        }
        return $tmp;
    }
}
