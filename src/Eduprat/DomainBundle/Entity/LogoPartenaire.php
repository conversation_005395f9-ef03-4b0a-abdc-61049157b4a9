<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * LogoPartenaire
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\LogoPartenaireRepository')]
#[ORM\Table(name: 'logo_partenaire')]
class LogoPartenaire
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;


    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'logoName', type: Types::STRING)]
    private ?string $logoName = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'filename', type: Types::STRING)]
    private ?string $filename = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'extension', type: Types::STRING)]
    private ?string $extension = null;


    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person')]
    #[ORM\JoinColumn(nullable: true)]
    private ?\Eduprat\AdminBundle\Entity\Person $personUploader = null;

    /**
     * LogoPartenaire constructor.
     */
    public function __construct() {
        $this->createdAt = new \DateTime();
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return LogoPartenaire
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * @return string
     */
    public function getLogoName()
    {
        return $this->logoName;
    }

    /**
     * @param string $logoName
     * @return LogoPartenaire
     */
    public function setLogoName($logoName)
    {
        $this->logoName = $logoName;
        return $this;
    }

    /**
     * @return string
     */
    public function getFilename()
    {
        return $this->filename;
    }

    /**
     * @param string $filename
     * @return LogoPartenaire
     */
    public function setFilename($filename)
    {
        $this->filename = $filename;
        return $this;
    }

    /**
     * @return string
     */
    public function getExtension()
    {
        return $this->extension;
    }

    /**
     * @param string $extension
     * @return LogoPartenaire
     */
    public function setExtension($extension)
    {
        $this->extension = $extension;
        return $this;
    }

    private $file;

    // Temporary store the file name
    private $tempFilename;

    public function getFile()
    {
        return $this->file;
    }

    public function setFile(UploadedFile $file = null)
    {
        $this->file = $file;

        // Replacing a file ? Check if we already have a file for this entity
        if (null !== $this->extension)
        {
            // Save file extension so we can remove it later
            $this->tempFilename = $this->extension;

            // Reset values
            $this->extension = null;
            $this->logoName = null;
            $this->filename = null;
        }
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function preUpload()
    {
        // If no file is set, do nothing
        if (null === $this->file)
        {
            return;
        }

        // The file name is the entity's ID
        // We also need to store the file extension
        $this->extension = $this->file->guessExtension();

        $this->generateFilename();
    }

    #[ORM\PostPersist]
    #[ORM\PostUpdate]
    public function upload()
    {
        // If no file is set, do nothing
        if (null === $this->file)
        {
            return;
        }

        // A file is present, remove it
        if (null !== $this->tempFilename)
        {
            $oldFile = $this->getUploadRootDir().'/'.$this->id.'.'.$this->tempFilename;
            if ($oldFile && file_exists($oldFile))
            {
                unlink($oldFile);
            }
        }


        $this->file->move(
            $this->getUploadRootDir(),
            $this->filename.'.'.$this->extension
        );
    }

    public function generateFilename() {
        $this->filename = sprintf( '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff ),
            mt_rand( 0, 0xffff ),
            mt_rand( 0, 0x0fff ) | 0x4000,
            mt_rand( 0, 0x3fff ) | 0x8000,
            mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff )
        );
    }

    #[ORM\PreRemove]
    public function preRemoveUpload()
    {
        $this->tempFilename = $this->getUploadRootDir().'/'.$this->filename.'.'.$this->extension;
    }

    #[ORM\PostRemove]
    public function removeUpload()
    {
        if ($this->tempFilename && file_exists($this->tempFilename))
        {
            unlink($this->tempFilename);
        }
    }

    public function getUploadDir()
    {
        return 'uploads/logoPartenaires/';
    }

    protected function getUploadRootDir()
    {
        return __DIR__.'/../../../../public/'.$this->getUploadDir();
    }

    public function getUrl()
    {
        return $this->filename.'.'.$this->extension;
    }

    public function getRelativeUrl() {
        return sprintf("/%s%s", $this->getUploadDir(), $this->getUrl());
    }

    public function removeLinks() {
        $this->logoName = "removedFile";
    }

    public function getPersonUploader(): ?Person
    {
        return $this->personUploader;
    }

    public function setPersonUploader(?Person $personUploader): void
    {
        $this->personUploader = $personUploader;
    }
}

