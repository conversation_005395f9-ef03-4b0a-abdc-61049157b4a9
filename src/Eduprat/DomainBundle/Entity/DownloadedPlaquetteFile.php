<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * DownloadedPlaquetteFile
 */
#[ORM\Entity]
#[ORM\Table(name: 'downloaded_plaquette_file')]
class DownloadedPlaquetteFile
{
    const TYPE_INVITATION = 'invitation';
    const TYPE_PROGRAMME = 'programme';

    const TYPE_FLYER = 'flyer';

    // Ajouter idPerson le %s puis le filename dans le second
    const PATHS_DIR = [
        self::TYPE_INVITATION => '/pdf/plaquettes/plaquettes/%s/%s',
        self::TYPE_PROGRAMME => '/pdf/plaquettes/programme/%s/%s',
        self::TYPE_FLYER => '/pdf/plaquettes/flyers/%s/%s',
    ];

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'original_name', type: Types::STRING, length: 255, nullable: true)]
    private ?string $originalName = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'creationDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $creationDate = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'person', nullable: false)]
    private ?Person $person = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'direct_link', type: Types::STRING, length: 255)]
    private ?string $directLink = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'type', type: Types::STRING, length: 255)]
    private ?string $type = null;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'titre', type: Types::STRING, length: 255, nullable: true)]
    private ?string $titre = null;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'formationType', type: Types::STRING, length: 255, nullable: true)]
    private ?string $formationType = "classique";

    /**
     * @var LogoPartenaire
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\LogoPartenaire', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'logoPartenaire', nullable: true)]
    private ?LogoPartenaire $logoPartenaire = null;

    /**
     *
     */
    public function __construct()
    {
        $this->creationDate = new \DateTime();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getOriginalName(): ?string
    {
        return $this->originalName;
    }

    /**
     * @param string $originalName
     * @return DownloadedPlaquetteFile
     */
    public function setOriginalName(string $originalName): DownloadedPlaquetteFile
    {
        $this->originalName = $originalName;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCreationDate(): ?\DateTime
    {
        return $this->creationDate;
    }

    /**
     * @param \DateTime $creationDate
     * @return DownloadedPlaquetteFile
     */
    public function setCreationDate(\DateTime $creationDate): DownloadedPlaquetteFile
    {
        $this->creationDate = $creationDate;
        return $this;
    }

    /**
     * @return Person
     */
    public function getPerson(): Person
    {
        return $this->person;
    }

    /**
     * @param Person $person
     * @return DownloadedPlaquetteFile
     */
    public function setPerson(Person $person): DownloadedPlaquetteFile
    {
        $this->person = $person;
        return $this;
    }

    public function getLogoPartenaire(): ?LogoPartenaire
    {
        return $this->logoPartenaire;
    }

    public function setLogoPartenaire(?LogoPartenaire $logoPartenaire): void
    {
        $this->logoPartenaire = $logoPartenaire;
    }

    /**
     * @return string
     */
    public function getDirectLink(): ?string
    {
        return $this->directLink;
    }

    /**
     * @param string $directLink
     * @return DownloadedPlaquetteFile
     */
    public function setDirectLink(string $directLink): DownloadedPlaquetteFile
    {
        $this->directLink = $directLink;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return DownloadedPlaquetteFile
     */
    public function setType(string $type): DownloadedPlaquetteFile
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return ?string
     */
    public function getTitre(): ?string
    {
        return $this->titre;
    }

    /**
     * @param string $titre
     * @return DownloadedPlaquetteFile
     */
    public function setTitre(string $titre): DownloadedPlaquetteFile
    {
        $this->titre = $titre;
        return $this;
    }

     /**
     * @return ?string
     */
    public function getFormationType(): ?string
    {
        return $this->formationType;
    }

    /**
     * @param string $formationType
     * @return DownloadedPlaquetteFile
     */
    public function setFormationType(string $formationType): DownloadedPlaquetteFile
    {
        $this->formationType = $formationType;
        return $this;
    }
}