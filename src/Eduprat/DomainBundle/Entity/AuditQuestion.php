<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * AuditQuestion
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\AuditQuestionRepository')]
#[ORM\Table(name: 'audit_question')]
class AuditQuestion extends BaseQuestion
{

    /**
     * @var Audit
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Audit', inversedBy: 'questions', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'audit', nullable: true)]
    private ?Audit $audit = null;

    /**
     * @var AuditQuestionCategory
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\AuditQuestionCategory', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'categoryQuestion', nullable: true)]
    private ?AuditQuestionCategory $categoryQuestion = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'interpretationTrueTrue', type: Types::TEXT, nullable: true)]
    private ?string $interpretationTrueTrue = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'interpretationTrueFalse', type: Types::TEXT, nullable: true)]
    private ?string $interpretationTrueFalse = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'interpretationFalseFalse', type: Types::TEXT, nullable: true)]
    private ?string $interpretationFalseFalse = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'interpretationFalseTrue', type: Types::TEXT, nullable: true)]
    private ?string $interpretationFalseTrue = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'interpretation', type: Types::TEXT, nullable: true)]
    private ?string $interpretation = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'answer', type: Types::INTEGER)]
    private ?int $answer = null;

    /**
     * @var Collection<int, AuditAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\AuditAnswer', mappedBy: 'question', cascade: ['persist'])]
    private Collection $answers;

    /**
     * @var Collection<int, Image>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Image', mappedBy: 'auditQuestion', cascade: ['persist'], orphanRemoval: true)]
    private Collection $images;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'position', type: Types::INTEGER, nullable: true)]
    private ?int $position = null;

    /**
     * @var integer
     */
    private $displayedAnswer;

    /**
     * Set categoryQuestion
     *
     * @param AuditQuestionCategory $categoryQuestion
     * @return AuditQuestion
     */
    public function setCategoryQuestion($categoryQuestion)
    {
        $this->categoryQuestion = $categoryQuestion;

        return $this;
    }

    /**
     * Get category
     *
     * @return AuditQuestionCategory
     */
    public function getCategoryQuestion()
    {
        return $this->categoryQuestion;
    }

    /**
     * Set interpretationTrueTrue
     *
     * @param string $interpretationTrueTrue
     * @return AuditQuestion
     */
    public function setInterpretationTrueTrue($interpretationTrueTrue)
    {
        $this->interpretationTrueTrue = $interpretationTrueTrue;

        return $this;
    }

    /**
     * Get interpretationTrueTrue
     *
     * @return string 
     */
    public function getInterpretationTrueTrue()
    {
        return $this->interpretationTrueTrue;
    }

    /**
     * Set interpretationTrueFalse
     *
     * @param string $interpretationTrueFalse
     * @return AuditQuestion
     */
    public function setInterpretationTrueFalse($interpretationTrueFalse)
    {
        $this->interpretationTrueFalse = $interpretationTrueFalse;

        return $this;
    }

    /**
     * Get interpretationTrueFalse
     *
     * @return string 
     */
    public function getInterpretationTrueFalse()
    {
        return $this->interpretationTrueFalse;
    }

    /**
     * Set interpretationFalseFalse
     *
     * @param string $interpretationFalseFalse
     * @return AuditQuestion
     */
    public function setInterpretationFalseFalse($interpretationFalseFalse)
    {
        $this->interpretationFalseFalse = $interpretationFalseFalse;

        return $this;
    }

    /**
     * Get interpretationFalseFalse
     *
     * @return string 
     */
    public function getInterpretationFalseFalse()
    {
        return $this->interpretationFalseFalse;
    }

    /**
     * Set interpretationFalseTrue
     *
     * @param string $interpretationFalseTrue
     * @return AuditQuestion
     */
    public function setInterpretationFalseTrue($interpretationFalseTrue)
    {
        $this->interpretationFalseTrue = $interpretationFalseTrue;

        return $this;
    }

    /**
     * Get interpretationFalseTrue
     *
     * @return string 
     */
    public function getInterpretationFalseTrue()
    {
        return $this->interpretationFalseTrue;
    }

    /**
     * Set audit
     *
     * @param Audit $audit
     * @return AuditQuestion
     */
    public function setAudit($audit = null)
    {
        $this->audit = $audit;

        return $this;
    }

    /**
     * Get audit
     *
     * @return Audit
     */
    public function getAudit()
    {
        return $this->audit;
    }

    /**
     * Add answers
     *
     * @param AuditAnswer $answers
     * @return AuditQuestion
     */
    public function addAnswer(AuditAnswer $answers)
    {
        $this->answers[] = $answers;

        return $this;
    }

    /**
     * Remove answers
     *
     * @param AuditAnswer $answers
     */
    public function removeAnswer(AuditAnswer $answers)
    {
        $this->answers->removeElement($answers);
    }

    /**
     * Get answers
     *
     * @return Collection
     */
    public function getAnswers()
    {
        return $this->answers;
    }

    /**
     * Add images
     *
     * @param Image $images
     * @return AuditQuestion
     */
    public function addImage(Image $images)
    {
        $images->setAuditQuestion($this);
        $this->images[] = $images;

        return $this;
    }

    /**
     * Remove images
     *
     * @param Image $images
     */
    public function removeImage(Image $images)
    {
        $images->removeLinks();
    }

    /**
     * Get images
     *
     * @return Collection
     */
    public function getImages()
    {
        return $this->images;
    }

    /**
     * @return string
     */
    public function getInterpretation()
    {
        return $this->interpretation;
    }

    /**
     * @param string $interpretation
     * @return AuditQuestion
     */
    public function setInterpretation($interpretation)
    {
        $this->interpretation = $interpretation;

        return $this;
    }

    /**
     * @return int
     */
    public function getAnswer()
    {
        return $this->answer;
    }

    /**
     * @param int $answer
     * @return AuditQuestion
     */
    public function setAnswer($answer)
    {
        $this->answer = $answer;

        return $this;
    }

    /**
     * @return int
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * @param int $position
     * @return AuditQuestion
     */
    public function setPosition($position)
    {
        $this->position = $position;
        return $this;
    }

    /**
     * @return int
     */
    public function getDisplayedAnswer()
    {
        return $this->displayedAnswer;
    }

    /**
     * @param int $displayedAnswer
     * @return AuditQuestion
     */
    public function setDisplayedAnswer($displayedAnswer)
    {
        $this->displayedAnswer = $displayedAnswer;
        return $this;
    }
    public function __construct()
    {
        parent::__construct();
        $this->answers = new ArrayCollection();
        $this->images = new ArrayCollection();
    }

}
