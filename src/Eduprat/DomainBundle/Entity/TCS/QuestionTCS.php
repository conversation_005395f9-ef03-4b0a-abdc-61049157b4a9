<?php

namespace Eduprat\DomainBundle\Entity\TCS;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Eduprat\DomainBundle\DTO\QuestionTCSCreateDto;
use Eduprat\DomainBundle\DTO\QuestionTCSEditDto;
use Ed<PERSON>rat\DomainBundle\DTO\ReponseTCSDtoInterface;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\SurveyQuestionCategory;
use Eduprat\DomainBundle\Repository\TCS\QuestionTCSRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuestionTCSRepository::class)]
class QuestionTCS
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    /**
     * @var Collection<int, ExpertTCSAnswer>
     */
    #[ORM\OneToMany(targetEntity: ExpertTCSAnswer::class, mappedBy: 'question', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $expertsAnswers;

    /**
     * @var Collection<int, ParticipationAnswerTCS>
     */
    #[ORM\OneToMany(targetEntity: ParticipationAnswerTCS::class, mappedBy: 'questionTCS', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $participationAnswersTCS;
    private ?int $cloneOf;

    public function __construct(
        #[ORM\Column(type: Types::TEXT)]
        private string        $libelleSiVousPensiez,
        #[ORM\Column(type: Types::TEXT)]
        private string        $libelleEtQuAlorsVousTrouvez,
        #[ORM\ManyToOne(targetEntity: GroupeQuestionTCS::class, inversedBy: 'questionsTCS', cascade: ['persist'])]
        #[ORM\JoinColumn(name: 'groupe_question')]
        private GroupeQuestionTCS $groupeQuestion,
        #[ORM\ManyToOne(targetEntity: SurveyQuestionCategory::class)]
        #[ORM\JoinColumn(name: 'category')]
        private SurveyQuestionCategory $category,
        #[ORM\OneToMany(targetEntity: ReponseTCS::class, mappedBy: 'questionTCS', cascade: ['persist', 'remove'], orphanRemoval: true)]
        private Collection $reponses = new ArrayCollection(),
    )
    {
        $this->expertsAnswers = new ArrayCollection();
        $this->participationAnswersTCS = new ArrayCollection();
    }

    public static function fromDTO(QuestionTCSCreateDto $questionTCDto): QuestionTCS
    {
        $questionTCS = new self(
            $questionTCDto->libelleSiVousPensiez,
            $questionTCDto->libelleEtQuAlorsVousTrouvez,
            $questionTCDto->groupeQuestion,
            $questionTCDto->category,
        );
        foreach ($questionTCDto->reponses as $reponse) {
            $reponse->questionTCS = $questionTCS;
            $questionTCS->addReponse(ReponseTCS::fromDTO($reponse));
        }
        return $questionTCS;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLibelleSiVousPensiez(): string
    {
        return $this->libelleSiVousPensiez;
    }

    public function getLibelleEtQuAlorsVousTrouvez(): string
    {
        return $this->libelleEtQuAlorsVousTrouvez;
    }

    public function getGroupeQuestion(): GroupeQuestionTCS
    {
        return $this->groupeQuestion;
    }

    public function getCategory(): SurveyQuestionCategory
    {
        return $this->category;
    }

    public function getReponses(): Collection
    {
        return $this->reponses;
    }

    public function addReponse(ReponseTCS $reponse): self
    {
        if (!$this->reponses->contains($reponse)) {
            $this->reponses[] = $reponse;
        }

        return $this;
    }

    private function removeReponse(ReponseTCS $reponseTCS): void
    {
        if (!$this->reponses->contains($reponseTCS)) {
            return;
        }
        $this->reponses->removeElement($reponseTCS);
    }
    public function loadDataFromEditDto(QuestionTCSEditDto $questionTCDto): void
    {
        $this->libelleSiVousPensiez = $questionTCDto->libelleSiVousPensiez;
        $this->libelleEtQuAlorsVousTrouvez = $questionTCDto->libelleEtQuAlorsVousTrouvez;
        $this->category = $questionTCDto->category;

        foreach ($questionTCDto->reponses as $reponse) {
            if ($reponse->id === null) {
                $reponse->questionTCS = $this;
                $this->addReponse(ReponseTCS::fromDTO($reponse));
            } else {
                $reponseTCS = $this->getReponses()->filter(fn (ReponseTCS $reponseTCS) => $reponseTCS->getId() === $reponse->id)->first();
                $reponseTCS?->setReponse($reponse->reponse);
            }
        }

        // Suppression des reponses qui ne sont plus dans la liste
        $ids = array_map(function (ReponseTCSDtoInterface $reponse) {
            return $reponse->id;
        }, $questionTCDto->reponses);
        foreach ($this->getReponses() as $reponseTCS) {
            if (!in_array($reponseTCS->getId(), $ids) && $reponseTCS->getId() !== null) {
                $this->removeReponse($reponseTCS);
            }
        }
    }

    public function duplicate(GroupeQuestionTCS $groupeQuestionTCS): QuestionTCS
    {
        $questionTCS = new self(
            $this->libelleSiVousPensiez,
            $this->libelleEtQuAlorsVousTrouvez,
            $groupeQuestionTCS,
            $this->category,
        );
        $questionTCS->setCloneOf($this->getId());
        /** @var ReponseTCS $reponse */
        foreach ($this->reponses as $reponse) {
            $questionTCS->addReponse($reponse->duplicate($questionTCS));
        }
        return $questionTCS;
    }

    /**
     * @return Collection<int, ExpertTCSAnswer>
     */
    public function getExpertsAnswers(): Collection
    {
        return $this->expertsAnswers;
    }

    public function countAnswer(ReponseTCS $reponseTCS): int
    {
        return $this->expertsAnswers->filter(fn (ExpertTCSAnswer $expertTCSAnswer) => $expertTCSAnswer->getReponseTCS()->getId() === $reponseTCS->getId())->count();
    }

    public function countParticipantAnswer(ReponseTCS $reponseTCS): int
    {
        return $this->participationAnswersTCS->filter(fn (ParticipationAnswerTCS $participationAnswerTCS) => $participationAnswerTCS->getReponseTCS()->getId() === $reponseTCS->getId())->count();
    }

    public function displayAnswerChecked(ReponseTCS $reponseTCS): bool
    {
        return $this->expertsAnswers->filter(fn (ExpertTCSAnswer $expertTCSAnswer) => $expertTCSAnswer->getReponseTCS()->getId() === $reponseTCS->getId())->count() > 0;
    }

    public function displayAnswerCheckedParticipant(ReponseTCS $reponseTCS): bool
    {
        return $this->participationAnswersTCS->filter(fn (ParticipationAnswerTCS $participationAnswerTCS) => $participationAnswerTCS->getReponseTCS()->getId() === $reponseTCS->getId())->count() > 0;
    }

    public function getExpertsAnswersGroupByReponse(): array
    {
        $expertsAnswersGroupByReponse = [];
        foreach ($this->expertsAnswers as $expertAnswer) {
            if (!array_key_exists($expertAnswer->getReponseTCS()->getId(), $expertsAnswersGroupByReponse)) {
                $expertsAnswersGroupByReponse[$expertAnswer->getReponseTCS()->getId()] = [];
            }
            $expertsAnswersGroupByReponse[$expertAnswer->getReponseTCS()->getId()][] = $expertAnswer;
        }

        usort($expertsAnswersGroupByReponse, function ($a, $b) {
            return count($b) <=> count($a);
        });
        return $expertsAnswersGroupByReponse;
    }

    /**
     * @return Collection<int, ParticipationAnswerTCS>
     */
    public function getParticipationAnswersTCS(): Collection
    {
        return $this->participationAnswersTCS;
    }

    public function isAnsweredByParticipation(Participation $participation): bool
    {
        return $this->participationAnswersTCS->filter(
            fn (ParticipationAnswerTCS $participationAnswerTCS) => $participationAnswerTCS->getParticipation()->getId() === $participation->getId()
        )->count() > 0;
    }

    public function getAGoodAnswer(): ?ReponseTCS
    {
        return $this->getReponses()->filter(function(ReponseTCS $reponseTCS) {
            return $reponseTCS->isBestAnswer();
        })->first();
    }

    public function getMajortyCount(): int
    {
        $goodAnswer = $this->getAGoodAnswer();
        return $goodAnswer ? $goodAnswer->getNbVoteExpert() : 0;
    }

    public function getCloneOf(): ?int
    {
        return $this->cloneOf;
    }

    private function setCloneOf(?int $getId)
    {
        $this->cloneOf = $getId;
    }
}
