<?php

namespace Eduprat\DomainBundle\Entity\TCS;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Eduprat\DomainBundle\DTO\GroupeQuestionTCSCreateDto;
use Ed<PERSON>rat\DomainBundle\DTO\GroupeQuestionTCSEditDto;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Repository\TCS\TCSRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use Symfony\Component\Filesystem\Filesystem;

#[ORM\Entity(repositoryClass: TCSRepository::class)]
class GroupeQuestionTCS
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    /**
     * @var Collection<int, QuestionTCS>
     */
    #[ORM\OneToMany(mappedBy: 'groupeQuestion', targetEntity: QuestionTCS::class, cascade: ['persist', 'remove'])]
    private Collection $questionsTCS;
    #[ORM\Column(name: 'description_image', type: Types::TEXT, nullable: true)]
    private ?string $descriptionImage = null;
    #[Vich\UploadableField(mapping: 'question_tcs_picture', fileNameProperty: "descriptionImage")]
    private ?File $descriptionImageFile = null;
    #[ORM\Column(name: 'synthese_educative_image', type: Types::TEXT, nullable: true)]
    private ?string $syntheseEducativeImage = null;
    #[Vich\UploadableField(mapping: 'question_tcs_picture', fileNameProperty: "syntheseEducativeImage")]
    private ?File $syntheseEducativeImageFile = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    /**
     * @var Collection<int, ParticipationGroupeQuestionTCS>
     */
    #[ORM\OneToMany(mappedBy: 'groupeQuestionTCS', targetEntity: ParticipationGroupeQuestionTCS::class, cascade: ['persist', 'remove'])]
    private Collection $participationsGroupeQuestionTCS;

    public function __construct(
        #[ORM\ManyToOne(targetEntity: QuestionnaireTCS::class, cascade: ['persist'], inversedBy: 'groupeQuestionsTCS')]
        private QuestionnaireTCS $questionnaireTCS,
        #[ORM\Column(type: Types::TEXT)]
        private string $description,
        #[ORM\Column(type: Types::TEXT, nullable: true)]
        private ?string $syntheseEducative,
    )
    {
        $this->questionsTCS = new ArrayCollection();
        $this->participationsGroupeQuestionTCS = new ArrayCollection();
    }

    public static function fromDTO(GroupeQuestionTCSCreateDto $groupeQuestionTCSDto)
    {
        $res = new self(
            $groupeQuestionTCSDto->questionnaireTC,
            $groupeQuestionTCSDto->description,
            $groupeQuestionTCSDto->syntheseEducative,
        );
        $res->setDescriptionImage($groupeQuestionTCSDto->descriptionImage);
        $res->setDescriptionImageFile($groupeQuestionTCSDto->descriptionImageFile);
        $res->setSyntheseEducativeImage($groupeQuestionTCSDto->syntheseEducativeImage);
        $res->setSyntheseEducativeImageFile($groupeQuestionTCSDto->syntheseEducativeImageFile);

        return $res;
    }

    public function fromEditDto(GroupeQuestionTCSEditDto $groupeQuestionTCSDto)
    {
        $this->description = $groupeQuestionTCSDto->description;
        $this->syntheseEducative = $groupeQuestionTCSDto->syntheseEducative;
        $this->setDescriptionImage($groupeQuestionTCSDto->descriptionImage);
        $this->setDescriptionImageFile($groupeQuestionTCSDto->descriptionImageFile);
        $this->setSyntheseEducativeImage($groupeQuestionTCSDto->syntheseEducativeImage);
        $this->setSyntheseEducativeImageFile($groupeQuestionTCSDto->syntheseEducativeImageFile);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getDescriptionImage(): ?string
    {
        return $this->descriptionImage;
    }

    public function getSyntheseEducative(): ?string
    {
        return $this->syntheseEducative;
    }

    public function getSyntheseEducativeImage(): ?string
    {
        return $this->syntheseEducativeImage;
    }

    public function getQuestionnaireTCS(): QuestionnaireTCS
    {
        return $this->questionnaireTCS;
    }

    /**
     * @return Collection<int, QuestionTCS>
     */
    public function getQuestionsTCS(): Collection
    {
        return $this->questionsTCS;
    }

    public function getDescriptionImageFile(): ?File
    {
        return $this->descriptionImageFile ?? null;
    }

    public function getSyntheseEducativeImageFile(): ?File
    {
        return $this->syntheseEducativeImageFile ?? null;
    }

    public function setDescriptionImage(?string $descriptionImage): void
    {
        $this->descriptionImage = $descriptionImage;
    }

    public function setDescriptionImageFile(?File $descriptionImageFile = null): void
    {
        $this->descriptionImageFile = $descriptionImageFile;
        if (null !== $descriptionImageFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updatedAt = new \DateTimeImmutable();
        }
    }

    public function setSyntheseEducativeImage($syntheseEducativeImage): void
    {
        $this->syntheseEducativeImage = $syntheseEducativeImage;
    }

    public function setSyntheseEducativeImageFile(?File $syntheseEducativeImageFile): void
    {
        $this->syntheseEducativeImageFile = $syntheseEducativeImageFile;
        if (null !== $syntheseEducativeImageFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updatedAt = new \DateTimeImmutable();
        }
    }

    public function duplicate(QuestionnaireTCS $questionnaireTCS): GroupeQuestionTCS
    {
        $groupeQuestionTCS = new self(
            $questionnaireTCS,
            $this->description,
            $this->syntheseEducative,
        );
        /** @var QuestionTCS $questionTCS */
        foreach ($this->questionsTCS as $questionTCS) {
            $groupeQuestionTCS->addQuestionTCS($questionTCS->duplicate($groupeQuestionTCS));
        }
        $groupeQuestionTCS->duplicateIfExistDescriptionImage($this);
        $groupeQuestionTCS->duplicateIfExistSyntheseEducativeImage($this);
        return $groupeQuestionTCS;
    }

    private function addQuestionTCS(QuestionTCS $question): void
    {
        $this->questionsTCS->add($question);
    }

    public function setQuestionnaireTCS(QuestionnaireTCS $questionnaireTCS): void
    {
        $this->questionnaireTCS = $questionnaireTCS;
    }

    public function answerGroupComplete(Participation $participation): bool
    {
        /** @var QuestionTCS $question */
        foreach ($this->getQuestionsTCS() as $question) {
            if ($question->getParticipationAnswersTCS()->filter(function(ParticipationAnswerTCS $answer) use($participation) {
                return $answer->getParticipation()->getId() === $participation->getId();
            })->count() === 0) {
                return false;
            }
        }
        return true;
    }

    public function getNumeroQuestion(QuestionTCS $currentQuestion): string
    {
        foreach ($this->questionsTCS as $key => $question) {
            if ($question->getId() === $currentQuestion->getId()) {
                return sprintf('%d', $key + 1);
            }
        }
        throw new \Exception('Question not found');
    }

    public function isLastQuestion(QuestionTCS $currentQuestion): bool
    {
        return $this->questionsTCS->last()->getId() === $currentQuestion->getId();
    }

    public function setDescription(?string $description)
    {
        $this->description = $description;
    }

    public function setSyntheseEducative(?string $syntheseEducative)
    {
        $this->syntheseEducative = $syntheseEducative;
    }

    public function hasSynthese(): bool
    {
        return $this->syntheseEducative !== null || $this->syntheseEducativeImage !== null;
    }
    public function duplicateIfExistDescriptionImage(GroupeQuestionTCS $originGroupeQuestionTCS): void
    {
        if ($originGroupeQuestionTCS->getDescriptionImageFile()) {
            $pictureFile = explode('.', $originGroupeQuestionTCS->getDescriptionImage())[0];
            $pictureNewFile = (substr($pictureFile, 0, -9)) . rand ( 100000000 , 999999999 );
            $filesystem = new Filesystem();
            if ($filesystem->exists($originGroupeQuestionTCS->getDescriptionImageFile()->getPathname())) {
                $filesystem->copy($originGroupeQuestionTCS->getDescriptionImageFile()->getPathname(), str_replace($pictureFile, $pictureNewFile, $originGroupeQuestionTCS->getDescriptionImageFile()->getPathname()));
                $newDescriptionImageFile = new File(str_replace($pictureFile, $pictureNewFile, $originGroupeQuestionTCS->getDescriptionImageFile()->getPathname()));
                $this->setDescriptionImageFile($newDescriptionImageFile);
                $this->setDescriptionImage($newDescriptionImageFile->getFileName());
            }
        }
    }

    public function duplicateIfExistSyntheseEducativeImage(GroupeQuestionTCS $originGroupeQuestionTCS): void
    {
        if ($originGroupeQuestionTCS->getSyntheseEducativeImageFile()) {
            $pictureFile = explode('.', $originGroupeQuestionTCS->getSyntheseEducativeImage())[0];
            $pictureNewFile = (substr($pictureFile, 0, -9)) . rand ( 100000000 , 999999999 );
            $filesystem = new Filesystem();
            if ($filesystem->exists($originGroupeQuestionTCS->getSyntheseEducativeImageFile()->getPathname())) {
                $filesystem->copy($originGroupeQuestionTCS->getSyntheseEducativeImageFile()->getPathname(), str_replace($pictureFile, $pictureNewFile, $originGroupeQuestionTCS->getSyntheseEducativeImageFile()->getPathname()));
                $newSyntheseEducativeImageFile = new File(str_replace($pictureFile, $pictureNewFile, $originGroupeQuestionTCS->getSyntheseEducativeImageFile()->getPathname()));
                $this->setSyntheseEducativeImageFile($newSyntheseEducativeImageFile);
                $this->setSyntheseEducativeImage($newSyntheseEducativeImageFile->getFileName());
            }
        }
    }

    public function hasParticipationSyntheseEducativeComplete(Participation $participation): bool
    {
        /** @var ParticipationGroupeQuestionTCS $participationsGroupeQuestionTCS */
        foreach ($this->participationsGroupeQuestionTCS as $participationsGroupeQuestionTCS) {
            if ($participationsGroupeQuestionTCS->getParticipation()->getId() === $participation->getId()) {
                return $participationsGroupeQuestionTCS->hasAnswer();
            }
        }
        return false;
    }
}
