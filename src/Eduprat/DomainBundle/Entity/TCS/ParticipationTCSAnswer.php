<?php

namespace Eduprat\DomainBundle\Entity\TCS;

use Doctrine\DBAL\Types\Types;
use Eduprat\DomainBundle\Repository\TCS\ParticipationTCSAnswerRepository;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\Participation;

#[ORM\Entity(repositoryClass: ParticipationTCSAnswerRepository::class)]
class ParticipationTCSAnswer
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Participation::class, cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participation', nullable: false)]
    protected Participation $participation;

    #[ORM\ManyToOne(targetEntity: ReponseTCS::class, cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'reponse_tcs', nullable: false)]
    protected ReponseTCS $reponseTCS;

    #[ORM\Column(type: Types::TEXT)]
    protected string $justification;

    public function getId(): ?int
    {
        return $this->id;
    }
}
