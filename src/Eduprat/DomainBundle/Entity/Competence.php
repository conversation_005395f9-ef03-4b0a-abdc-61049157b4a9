<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Competence
 */
#[ORM\Entity]
#[ORM\Table(name: 'competence')]
class Competence
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'name', type: Types::STRING, length: 255)]
    private ?string $name = null;

    /**
     * @var Programme
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme', inversedBy: 'competences', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'programme', nullable: true, onDelete: 'CASCADE')]
    private ?Programme $programme = null;


    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return AuditCategory
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string 
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set programme
     *
     * @param Programme $programme
     * @return Competence
     */
    public function setProgramme(Programme $programme = null)
    {
        $this->programme = $programme;

        return $this;
    }

    /**
     * Get programme
     *
     * @return Programme
     */
    public function getProgramme()
    {
        return $this->programme;
    }
}

