<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * AuditAnswer
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\AuditAnswerRepository')]
#[ORM\Table(name: 'audit_answer')]
#[ORM\UniqueConstraint(name: 'answer_unique', columns: ['participation', 'auditId', 'patient', 'question'])]
class AuditAnswer extends BaseAnswer
{

    /**
     * @var integer
     */
    #[ORM\Column(name: 'auditId', type: Types::INTEGER)]
    private ?int $auditId = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'patient', type: Types::INTEGER)]
    private ?int $patient = null;

    /**
     * @var AuditQuestion
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\AuditQuestion', inversedBy: 'answers', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'question', nullable: true)]
    private ?AuditQuestion $question = null;

    /**
     * @var SurveyQuestion
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\SurveyQuestion', inversedBy: 'auditAnswers', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'surveyQuestion', nullable: true)]
    private ?SurveyQuestion $surveyQuestion = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'answer', type: Types::TEXT)]
    private ?string $answer = null;

    /**
     * @var Participation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', inversedBy: 'auditAnswers', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participation', nullable: false)]
    private ?Participation $participation = null;

    /**
     * Set auditId
     *
     * @param integer $auditId
     * @return AuditAnswer
     */
    public function setAuditId($auditId)
    {
        $this->auditId = $auditId;

        return $this;
    }

    /**
     * Get auditId
     *
     * @return integer 
     */
    public function getAuditId()
    {
        return $this->auditId;
    }

    /**
     * Set question
     *
     * @param AuditQuestion $question
     * @return AuditAnswer
     */
    public function setQuestion(AuditQuestion $question)
    {
        $this->question = $question;

        return $this;
    }

    /**
     * Get question
     *
     * @return AuditQuestion
     */
    public function getQuestion()
    {
        return $this->question;
    }

    /**
     * @return int
     */
    public function getPatient()
    {
        return $this->patient;
    }

    /**
     * @param int $patient
     * @return AuditAnswer
     */
    public function setPatient($patient)
    {
        $this->patient = $patient;

        return $this;
    }

    /**
     * @return int
     */
    public function getAnswer()
    {
        if ($this->getSurveyQuestion() && $this->getSurveyQuestion()->getType() === SurveyQuestion::TYPE_CHOICE) {
            return json_decode($this->answer, true);
        }
        return $this->answer;
    }

    /**
     * @param int $answer
     * @return AuditAnswer
     */
    public function setAnswer($answer)
    {
        if ($this->getSurveyQuestion() && $this->getSurveyQuestion()->getType() === SurveyQuestion::TYPE_CHOICE) {
            $answer = json_encode($answer);
        }
        $this->answer = $answer;
        return $this;
    }

    /**
     * Set participation
     *
     * @param Participation $participation
     *
     * @return BaseAnswer
     */
    public function setParticipation(Participation $participation)
    {
        $this->participation = $participation;

        return $this;
    }

    /**
     * Get participation
     *
     * @return Participation
     */
    public function getParticipation()
    {
        return $this->participation;
    }

    /**
     * @return SurveyQuestion
     */
    public function getSurveyQuestion()
    {
        return $this->surveyQuestion;
    }

    /**
     * @param SurveyQuestion $surveyQuestion
     * @return AuditAnswer
     */
    public function setSurveyQuestion($surveyQuestion)
    {
        $this->surveyQuestion = $surveyQuestion;
        return $this;
    }

}
