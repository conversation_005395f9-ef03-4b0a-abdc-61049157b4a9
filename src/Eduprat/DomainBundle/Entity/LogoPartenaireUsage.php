<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * LogoPartenaireUsage
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\LogoPartenaireUsageRepository')]
#[ORM\Table(name: 'logo_partenaire_usage')]
class LogoPartenaireUsage
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person')]
    #[ORM\JoinColumn(name: 'coordinator', nullable: false)]
    private ?Person $coordinator = null;

    /**
     * @var LogoPartenaire
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\LogoPartenaire')]
    #[ORM\JoinColumn(name: 'logo_partenaire', nullable: false)]
    private ?LogoPartenaire $logoPartenaire = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation')]
    #[ORM\JoinColumn(name: 'formation', nullable: false)]
    private ?Formation $formation = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'titre', type: Types::STRING, length: 255)]
    private ?string $titre = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * LogoPartenaireUsage constructor.
     */
    public function __construct(Person $coordinator, LogoPartenaire $logoPartenaire, Formation $formation, string $titre, \DateTimeInterface $createdAt = null)
    {
        $this->coordinator = $coordinator;
        $this->logoPartenaire = $logoPartenaire;
        $this->formation = $formation;
        $this->titre = $titre;
        $this->createdAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Get coordinator
     *
     * @return Person
     */
    public function getCoordinator(): ?Person
    {
        return $this->coordinator;
    }

    /**
     * Set coordinator
     *
     * @param Person $coordinator
     * @return LogoPartenaireUsage
     */
    public function setCoordinator(Person $coordinator): self
    {
        $this->coordinator = $coordinator;
        return $this;
    }

    /**
     * Get logoPartenaire
     *
     * @return LogoPartenaire
     */
    public function getLogoPartenaire(): ?LogoPartenaire
    {
        return $this->logoPartenaire;
    }

    /**
     * Set logoPartenaire
     *
     * @param LogoPartenaire $logoPartenaire
     * @return LogoPartenaireUsage
     */
    public function setLogoPartenaire(LogoPartenaire $logoPartenaire): self
    {
        $this->logoPartenaire = $logoPartenaire;
        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation(): ?Formation
    {
        return $this->formation;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     * @return LogoPartenaireUsage
     */
    public function setFormation(Formation $formation): self
    {
        $this->formation = $formation;
        return $this;
    }

    /**
     * Get titre
     *
     * @return string
     */
    public function getTitre(): ?string
    {
        return $this->titre;
    }

    /**
     * Set titre
     *
     * @param string $titre
     * @return LogoPartenaireUsage
     */
    public function setTitre(string $titre): self
    {
        $this->titre = $titre;
        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTimeInterface
     */
    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    /**
     * Set createdAt
     *
     * @param \DateTimeInterface $createdAt
     * @return LogoPartenaireUsage
     */
    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }
}
