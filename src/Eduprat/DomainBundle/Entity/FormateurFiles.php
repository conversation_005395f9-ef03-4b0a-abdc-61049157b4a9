<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * FormateurFiles
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FormateurFilesRepository')]
#[ORM\Table(name: 'formateur_files')]
class FormateurFiles
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formateur
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formateur', inversedBy: 'formateurFiles', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formateur', nullable: false)]
    private ?Formateur $formateur = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'formateurFiles', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contrat', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contrat = null;

    /**
     * @var File
     */
    private $contratFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'facture', type: Types::STRING, length: 255, nullable: true)]
    private ?string $facture = null;

    /**
     * @var File
     */
    private $factureFile;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set formateur
     *
     * @param Formateur $formateur
     */
    public function setFormateur($formateur): FormateurFiles
    {
        $this->formateur = $formateur;

        return $this;
    }

    /**
     * Get formateur
     *
     * @return Formateur
     */
    public function getFormateur()
    {
        return $this->formateur;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     *
     */
    public function setFormation($formation): FormateurFiles
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    public function setContrat($contrat): FormateurFiles
    {
        $this->contrat = $contrat;

        return $this;
    }

    /**
     * Get contrat
     *
     * @return string
     */
    public function getContrat()
    {
        return $this->contrat;
    }

    /**
     * @return File
     */
    public function getContratFile()
    {
        return $this->contratFile;
    }

    public function setContratFile(File $contratFile = null): FormateurFiles
    {
        $this->contratFile = $contratFile;
        if ($this->contratFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    public function setFacture($facture): FormateurFiles
    {
        $this->facture = $facture;

        return $this;
    }

    /**
     * Get facture
     *
     * @return string
     */
    public function getFacture()
    {
        return $this->facture;
    }

    /**
     * @return File
     */
    public function getFactureFile()
    {
        return $this->factureFile;
    }

    public function setFactureFile(File $factureFile = null): FormateurFiles
    {
        $this->factureFile = $factureFile;
        if ($this->factureFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    public function setUpdatedAt($updatedAt): FormateurFiles
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

}

