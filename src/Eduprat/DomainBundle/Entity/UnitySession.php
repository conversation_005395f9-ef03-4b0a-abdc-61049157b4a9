<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\Formation;

/**
 * UnitySession
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\UnitySessionRepository')]
#[ORM\Table(name: 'unity_session')]
class UnitySession
{
    
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'openingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $openingDate = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'closingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $closingDate = null;
    
    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'unities', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false)]
    private ?Formation $formation = null;

    /**
     * @var Collection<int, UnitySessionDate>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\UnitySessionDate', mappedBy: 'unitySession', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $unitySessionDates;

    /**
     * Formation constructor.
     */
    public function __construct()
    {
        $this->unitySessionDates = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * Set openingDate
     *
     * @param \DateTime $openingDate
     * @return UnitySession
     */
    public function setOpeningDate($openingDate)
    {
        $this->openingDate = $openingDate;

        return $this;
    }

    /**
     * Get openingDate
     *
     * @return \DateTime
     */
    public function getOpeningDate()
    {
        return $this->openingDate;
    }

    /**
     * Set closingDate
     *
     * @param \DateTime $closingDate
     * @return UnitySession
     */
    public function setClosingDate($closingDate)
    {
        $this->closingDate = $closingDate;
        return $this;
    }

    /**
     * Get closingDate
     *
     * @return \DateTime
     */
    public function getClosingDate()
    {
        return $this->closingDate;
    }

    /**
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * @param Formation $formation
     * @return $this
     */
    public function setFormation($formation)
    {
        $this->formation = $formation;
        return $this;
    }    

    /**
     * Add unitySessionDate
     *
     * @param UnitySessionDate $unitySessionDate
     * @return UnitySession
     */
    public function addUnitySessionDate(UnitySessionDate $unitySessionDate)
    {
        $this->unitySessionDates[] = $unitySessionDate;
        $unitySessionDate->setUnitySession($this);

        return $this;
    }

    /**
     * Remove unitySessionDate
     *
     * @param UnitySessionDate $unitySessionDate
     */
    public function removeUnitySessionDate(UnitySessionDate $unitySessionDate)
    {
        $this->unitySessionDates->removeElement($unitySessionDate);
    }

    /**
     * Get unitySessionDates
     *
     * @return Collection
     */
    public function getUnitySessionDates()
    {
        return $this->unitySessionDates;
    }

    public function calculateStartDate(): ?\DateTime
    {
        if (!$this->unitySessionDates || !$this->unitySessionDates->count()) {
            return $this->openingDate;
        }
        return $this->unitySessionDates->first()->getStartDate();
    }

    /**
     * Get last unitySessionDate
     *
     * @return ?UnitySessionDate
     */
    public function getLastUnitySessionDate(): ?UnitySessionDate
    {
        if (!$this->unitySessionDates || !$this->unitySessionDates->count()) {
            return null;
        }
        return array_reduce($this->unitySessionDates->toArray(), function($accumulator, $value) {
            return $accumulator->getEndDate() < $value->getEndDate() ? $value : $accumulator;
        }, $this->unitySessionDates->first());
    }

    public function synchroDate(): void
    {
        if ($this->getOpeningDate() == null || $this->getClosingDate() == null) {
            $unitySessionStartDate = null;
            $unitySessionClosingDate = null;
            foreach ($this->getUnitySessionDates() as $unitySessionDate) {
                if ($unitySessionStartDate == null || ($unitySessionDate->getStartDate() && $unitySessionDate->getStartDate() < $unitySessionStartDate)) {
                    $unitySessionStartDate = $unitySessionDate->getStartDate();
                }
                if ($unitySessionClosingDate == null || ($unitySessionDate->getEndDate() && $unitySessionDate->getEndDate() > $unitySessionClosingDate)) {
                    $unitySessionClosingDate = $unitySessionDate->getEndDate();
                }
            }
            $this->setOpeningDate($unitySessionStartDate);
            $this->setClosingDate($unitySessionClosingDate);
        }
    }

    public function displayableDates(array $daysConvert, array $mountConvert): array
    {
        $dates = [];
        $unityDates = $this->getUnitySessionDates();
        if ($unityDates) {
            foreach ($unityDates as $unityDate) {
                $startHour = $unityDate->getStartDate()->format("H")[0] == "0" ? $unityDate->getStartDate()->format("g\\Hi") : $unityDate->getStartDate()->format("H\\Hi");
                $endHour = $unityDate->getEndDate()->format("H")[0] == "0" ? $unityDate->getEndDate()->format("g\\Hi") : $unityDate->getEndDate()->format("H\\Hi");
                $timestamp = strtotime($unityDate->getStartDate()->format("Y-m-d"));
                $dates[] = ["date" => $daysConvert[date('D', $timestamp)] . " " . $unityDate->getStartDate()->format("d") . " " . $mountConvert[$unityDate->getStartDate()->format("m")], "hours" => $startHour . " - " . $endHour];
            }
        }
        return $dates;
    }
}
