<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * ParticipantDateDownload
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ParticipantDateDownloadRepository')]
#[ORM\Table(name: 'participant_date_download')]
class ParticipantDateDownload
{
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', inversedBy: 'downloadDates')]
    #[ORM\JoinColumn(name: 'participant', nullable: false)]
    private ?Participant $participant = null;

    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation')]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    #[ORM\Column(name: 'participation', type: Types::STRING, nullable: true)]
    protected ?string $participation = null;

    #[ORM\Column(name: 'participationDownloadedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $participationDownloadedAt = null;

    #[ORM\Column(name: 'participationHorary', type: Types::STRING, nullable: true)]
    protected ?string $participationHorary = null;

    #[ORM\Column(name: 'participationHoraryDownloadedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $participationHoraryDownloadedAt = null;

    #[ORM\Column(name: 'topos', type: Types::JSON, nullable: true)]
    protected array $topos = [];

    #[ORM\Column(name: 'toposDownloadedAt', type: Types::JSON, nullable: true)]
    protected array $toposDownloadedAt = [];

    #[ORM\Column(name: 'restitution', type: Types::STRING, nullable: true)]
    protected ?string $restitution = null;

    #[ORM\Column(name: 'restitutionDownloadedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $restitutionDownloadedAt = null;

    #[ORM\Column(name: 'preRestitution', type: Types::STRING, nullable: true)]
    protected ?string $preRestitution = null;

    #[ORM\Column(name: 'preRestitutionDownloadedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $preRestitutionDownloadedAt = null;


    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set participant
     *
     * @param Participant $participant
     * @return ParticipantDateDownload
     */
    public function setParticipant(Participant $participant = null)
    {
        $this->participant = $participant;

        return $this;
    }

    /**
     * Get participant
     *
     * @return Participant
     */
    public function getParticipant()
    {
        return $this->participant;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     * @return ParticipantDateDownload
     */
    public function setFormation(Formation $formation)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * Set participation
     *
     * @param string $participation
     *
     * @return ParticipantDateDownload
     */
    public function setParticipation($participation)
    {
        $this->participation = $participation;

        return $this;
    }

    /**
     * Get participation
     *
     * @return string
     */
    public function getParticipation()
    {
        return $this->participation;
    }

    public function setParticipationDownloadedAt(\DateTime $participationDownloadedAt): self
    {
        $this->participationDownloadedAt = $participationDownloadedAt;

        return $this;
    }

    public function getParticipationDownloadedAt(): ?\DateTimeInterface
    {
        return $this->participationDownloadedAt;
    }

    /**
     * @param array $topos
     */
    public function setTopos($topos)
    {
        $this->topos = $topos;
    }

    /**
     * @return array
     */
    public function getTopos()
    {
        return $this->topos;
    }

    public function setToposDownloadedAt(array $toposDownloadedAt = [])
    {
        $this->toposDownloadedAt = $toposDownloadedAt;
    }

    public function getToposDownloadedAt(): array
    {
        return $this->toposDownloadedAt;
    }

    /**
     * Set restitution
     *
     * @param string $restitution
     *
     * @return ParticipantDateDownload
     */
    public function setRestitution($restitution)
    {
        $this->restitution = $restitution;

        return $this;
    }

    /**
     * Get restitution
     *
     * @return string
     */
    public function getRestitution()
    {
        return $this->restitution;
    }

    /**
     * Set restitutionDownloadedAt
     *
     * @param \DateTime $restitutionDownloadedAt
     * @return ParticipantDateDownload
     */
    public function setRestitutionDownloadedAt(\DateTime $restitutionDownloadedAt)
    {
        $this->restitutionDownloadedAt = $restitutionDownloadedAt;

        return $this;
    }

    public function getRestitutionDownloadedAt(): ?\DateTimeInterface
    {
        return $this->restitutionDownloadedAt;
    }

    /**
     * @return string
     */
    public function getParticipationHorary()
    {
        return $this->participationHorary;
    }

    /**
     * @param string $participationHorary
     * @return ParticipantDateDownload
     */
    public function setParticipationHorary($participationHorary)
    {
        $this->participationHorary = $participationHorary;
        return $this;
    }

    /**
     * @return string
     */
    public function getParticipationHoraryDownloadedAt()
    {
        return $this->participationHoraryDownloadedAt;
    }

    /**
     * @param string $participationHoraryDownloadedAt
     * @return ParticipantDateDownload
     */
    public function setParticipationHoraryDownloadedAt($participationHoraryDownloadedAt)
    {
        $this->participationHoraryDownloadedAt = $participationHoraryDownloadedAt;
        return $this;
    }

    /**
     * @return string
     */
    public function getPreRestitution()
    {
        return $this->preRestitution;
    }

    /**
     * @param string $preRestitution
     * @return ParticipantDateDownload
     */
    public function setPreRestitution($preRestitution)
    {
        $this->preRestitution = $preRestitution;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getPreRestitutionDownloadedAt()
    {
        return $this->preRestitutionDownloadedAt;
    }

    /**
     * @param \DateTime $preRestitutionDownloadedAt
     * @return ParticipantDateDownload
     */
    public function setPreRestitutionDownloadedAt($preRestitutionDownloadedAt)
    {
        $this->preRestitutionDownloadedAt = $preRestitutionDownloadedAt;
        return $this;
    }

}
