<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * CoordinatorFiles
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\CoordinatorFilesRepository')]
#[ORM\Table(name: 'coordinator_files')]
class CoordinatorFiles
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', inversedBy: 'coordinatorFiles', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'coordinator', nullable: false)]
    private ?Coordinator $coordinator = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'coordinatorFiles', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'factureCoordinator', type: Types::STRING, length: 255, nullable: true)]
    private ?string $factureCoordinator = null;

    /**
     * @var ?string
     */
    #[ORM\Column(name: 'factureCoordinatorN1', type: Types::STRING, length: 255, nullable: true)]
    private ?string $factureCoordinatorN1 = null;

    /**
     * @var File
     */
    private $factureCoordinatorFile;

    private $factureCoordinatorFileN1;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAtN1', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAtN1 = null;

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set coordinator
     *
     * @param Coordinator $coordinator
     *
     * @return CoordinatorFiles
     */
    public function setCoordinator($coordinator)
    {
        $this->coordinator = $coordinator;

        return $this;
    }

    /**
     * Get coordinator
     *
     * @return Coordinator
     */
    public function getCoordinator()
    {
        return $this->coordinator;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     *
     * @return CoordinatorFiles
     */
    public function setFormation($formation)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * Set factureCoordinator
     *
     * @param string $factureCoordinator
     *
     * @return CoordinatorFiles
     */
    public function setFactureCoordinator($factureCoordinator)
    {
        $this->factureCoordinator = $factureCoordinator;

        return $this;
    }

    /**
     * Get factureCoordinator
     *
     * @return string
     */
    public function getFactureCoordinator()
    {
        return $this->factureCoordinator;
    }

    /**
     * @return File
     */
    public function getFactureCoordinatorFile()
    {
        return $this->factureCoordinatorFile;
    }

    public function getFactureCoordinatorN1(): ?string
    {
        return $this->factureCoordinatorN1;
    }

    public function setFactureCoordinatorN1(?string $factureCoordinatorN1): CoordinatorFiles
    {
        $this->factureCoordinatorN1 = $factureCoordinatorN1;
        return $this;
    }

    /**
     * @param ?File $factureCoordinatorFile
     */
    public function setFactureCoordinatorFile(?File $factureCoordinatorFile = null)
    {
        $this->factureCoordinatorFile = $factureCoordinatorFile;
        if ($this->factureCoordinatorFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    public function getFactureCoordinatorFileN1()
    {
        return $this->factureCoordinatorFileN1;
    }

    /**
     * @param ?File $factureCoordinatorFileN1
     * @return CoordinatorFiles
     */
    public function setFactureCoordinatorFileN1(?File $factureCoordinatorFileN1): CoordinatorFiles
    {
        $this->factureCoordinatorFileN1 = $factureCoordinatorFileN1;
        if ($this->factureCoordinatorFileN1 instanceof UploadedFile) {
            $this->setPreUpdateN1();
        }
        return $this;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return self
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    public function setPreUpdateN1()
    {
        $this->updatedAtN1 = new \DateTime();
    }

    public function getUpdatedAtN1(): ?\DateTime
    {
        return $this->updatedAtN1;
    }
}

