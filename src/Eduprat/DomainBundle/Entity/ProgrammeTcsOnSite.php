<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\ProgrammeTcs;

/**
 * ProgrammeTcsOnSite
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ProgrammeTcsOnSiteRepository')]
#[ORM\Table(name: 'programme_tcs_onsite')]
class ProgrammeTcsOnSite extends ProgrammeTcs implements FormationWithFormInterface
{
    /**
     * Formation constructor.
     */
    public function __construct(UnityFormation $unityFormation1, UnityFormation $unityFormation2, UnityFormation $unityFormation3)
    {
        parent::__construct();
        $this->setPresence(Programme::PRESENCE_SITE);
        $this->addUnity($unityFormation1);
        $this->addUnity($unityFormation2);
        $this->addUnity($unityFormation3);
    }

    public function getProgrammeDiscr() {
        return self::TYPE_TCS_ON_SITE;
    }
}
