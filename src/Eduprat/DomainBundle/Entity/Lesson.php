<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * lesson
 */
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'lesson')]
class Lesson
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Elearning
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Elearning', inversedBy: 'lessons', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'elearning', nullable: true)]
    private ?Elearning $elearning = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'label', type: Types::STRING, length: 255)]
    private ?string $label = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'position', type: Types::INTEGER, nullable: true)]
    private ?int $position = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'minDuration', type: Types::FLOAT, nullable: true)]
    private ?float $minDuration = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'declaredDuration', type: Types::FLOAT, nullable: true)]
    private ?float $declaredDuration = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'isPresentielle', type: Types::BOOLEAN)]
    private ?bool $isPresentielle = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * @var Collection<int, Activity>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Activity', mappedBy: 'lesson', orphanRemoval: true, cascade: ['persist', 'remove'])]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'ASC'])]
    private Collection $activities;

    /**
     * Lesson constructor.
     */
    public function __construct()
    {
        $this->activities = new ArrayCollection();
        $this->createdAt = new \DateTime();
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set label
     *
     * @param string $label
     * @return Lesson
     */
    public function setLabel($label)
    {
        $this->label = $label;

        return $this;
    }

    /**
     * Get label
     *
     * @return string 
     */
    public function getLabel()
    {
        return $this->label;
    }

    /**
     * @return int
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * @param int $position
     * @return Lesson
     */
    public function setPosition($position)
    {
        $this->position = $position;
        return $this;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     * @return Lesson
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime 
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set minDuration
     *
     * @param \DateTime $minDuration
     * @return Lesson
     */
    public function setMinDuration($minDuration)
    {
        $this->minDuration = $minDuration;

        return $this;
    }

    /**
     * Get minDuration
     *
     * @return \DateTime 
     */
    public function getMinDuration()
    {
        return $this->minDuration;
    }

    /**
     * Set declaredDuration
     *
     * @param \DateTime $declaredDuration
     * @return Lesson
     */
    public function setDeclaredDuration($declaredDuration)
    {
        $this->declaredDuration = $declaredDuration;

        return $this;
    }

    /**
     * Get declaredDuration
     *
     * @return \DateTime 
     */
    public function getDeclaredDuration()
    {
        return $this->declaredDuration;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Lesson
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime 
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return Lesson
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * Set elearning
     *
     * @param Elearning $elearning
     * @return Lesson
     */
    public function setElearning(Elearning $elearning = null)
    {
        $this->elearning = $elearning;

        return $this;
    }

    /**
     * Get elearning
     *
     * @return Elearning
     */
    public function getElearning()
    {
        return $this->elearning;
    }

    public function setIsPresentielle($value) {
        $this->isPresentielle = $value;
        return $this;
    }

    public function isPresentielle() {
        return $this->isPresentielle;
    }

    /**
     * Add activity
     *
     * @param Activity $activity
     * @return Lesson
     */
    public function addActivity(Activity $activity)
    {
        $activity->setLesson($this);
        $this->activities[] = $activity;
        return $this;
    }

    /**
     * Remove activity
     *
     * @param Activity $activity
     */
    public function removeActivity(Activity $activity)
    {
        $this->activities->removeElement($activity);
    }

    /**
     * Get activities
     *
     * @return Collection
     */
    public function getActivities()
    {
        return $this->activities;
    }

      /**
     * Get activities
     *
     * @return Collection
     */
    public function getActivityByPosition($position)
    {
        return $this->getActivities()->filter(function (Activity $activity) use ($position) {
            return $activity->getPosition() == $position;
        })->first();
    }

    public function isLastLesson() {
        return $this->getElearning()->getLessonByPosition($this->getPosition() + 1) == false;
    }
    
     /**
     * Get activities
     *
     * @return Collection
     */
    public function getLastActivity()
    {
        return $this->getActivities()->last();
    }

    /**
     * Get activities
     *
     * @return Collection
     */
    public function getActivityById($id)
    {
        return $this->getActivities()->filter(function (Activity $activity) use ($id) {
            return $activity->getId() == $id;
        })->first();
    }
}
