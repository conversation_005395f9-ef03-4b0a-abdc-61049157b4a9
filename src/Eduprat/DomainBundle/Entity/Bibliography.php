<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * bibliography
 */
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'bibliography')]
class Bibliography
{
    
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Activity
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Activity', inversedBy: 'bibliographies', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'activity', nullable: true)]
    private ?Activity $activity = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'biblioLabel', type: Types::STRING, length: 255, nullable: true)]
    private ?string $biblioLabel = null;

     /**
     * @var string
     */
    #[ORM\Column(name: 'biblioUrl', type: Types::STRING, length: 255, nullable: true)]
    private ?string $biblioUrl = null;

    /**
     * Set activity
     *
     * @param Activity $activity
     *
     * @return Bibliography
     */
    public function setActivity(Activity $activity = null)
    {
        $this->activity = $activity;

        return $this;
    }

    /**
     * Get activity
     *
     * @return Activity
     */
    public function getActivity()
    {
        return $this->activity;
    }

    /**
     * Set biblioLabel
     *
     * @param string $biblioLabel
     * @return Activity
     */
    public function setBiblioLabel($biblioLabel)
    {
        $this->biblioLabel = $biblioLabel;

        return $this;
    }

    /**
     * Get biblioLabel
     *
     * @return string 
     */
    public function getBiblioLabel()
    {
        return $this->biblioLabel;
    }

    /**
     * Set biblioUrl
     *
     * @param string $biblioUrl
     * @return Activity
     */
    public function setBiblioUrl($biblioUrl)
    {
        $this->biblioUrl = $biblioUrl;

        return $this;
    }

    /**
     * Get biblioUrl
     *
     * @return string 
     */
    public function getBiblioUrl()
    {
        return $this->biblioUrl;
    }
}