<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * IndemnisationPriseEnCharge
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\IndemnisationPriseEnChargeRepository')]
#[ORM\Table(name: 'indemnisation_prise_en_charge')]
#[ORM\UniqueConstraint(name: 'unique', columns: ['indemnisation', 'priseEnCharge'])]
class IndemnisationPriseEnCharge
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Indemnisation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Indemnisation', inversedBy: 'prisesEnCharges', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'indemnisation', nullable: false)]
    private ?Indemnisation $indemnisation = null;

    /**
     *
     * @var PriseEnCharge
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\PriseEnCharge', inversedBy: 'indemnisations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'priseEnCharge', nullable: false, onDelete: 'CASCADE')]
    private ?PriseEnCharge $priseEnCharge = null;

    /**
     * @var float|null|string
     */
    #[ORM\Column(name: 'price', type: Types::FLOAT, nullable: true)]
    protected ?float $price = null;

    /**
     * @param Indemnisation $indemnisation
     * @param PriseEnCharge $priseEnCharge
     */
    public function __construct(Indemnisation $indemnisation, PriseEnCharge $priseEnCharge)
    {
        $this->indemnisation = $indemnisation;
        $this->priseEnCharge = $priseEnCharge;
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return Indemnisation
     */
    public function getIndemnisation(): Indemnisation
    {
        return $this->indemnisation;
    }

    /**
     * @param Indemnisation $indemnisation
     * @return IndemnisationPriseEnCharge
     */
    public function setIndemnisation(Indemnisation $indemnisation): IndemnisationPriseEnCharge
    {
        $this->indemnisation = $indemnisation;
        return $this;
    }

    /**
     * @return PriseEnCharge
     */
    public function getPriseEnCharge(): PriseEnCharge
    {
        return $this->priseEnCharge;
    }

    /**
     * @param PriseEnCharge $priseEnCharge
     * @return IndemnisationPriseEnCharge
     */
    public function setPriseEnCharge(PriseEnCharge $priseEnCharge): IndemnisationPriseEnCharge
    {
        $this->priseEnCharge = $priseEnCharge;
        return $this;
    }

    /**
     * @return float|null|string
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * @param float|null $price
     * @return IndemnisationPriseEnCharge
     */
    public function setPrice(?float $price): IndemnisationPriseEnCharge
    {
        $this->price = $price;
        return $this;
    }

}
