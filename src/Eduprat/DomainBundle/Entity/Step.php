<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * step
 */
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'step')]
class Step
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Course
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Course', inversedBy: 'steps', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'course', nullable: true)]
    private ?Course $course = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'position', type: Types::INTEGER, nullable: true)]
    private ?int $position = null;

    /**
     * @var Collection<int, Module>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Module', mappedBy: 'step', orphanRemoval: true, cascade: ['persist', 'remove'])]
    #[ORM\OrderBy(['position' => 'ASC', 'id' => 'ASC'])]
    private Collection $modules;

    public function __construct($modules, $position)
    {
        $this->modules = new ArrayCollection();
        foreach($modules as $module) {
            $this->addModule($module);
        }
        $this->position = $position;
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * @param int $position
     * @return Step
     */
    public function setPosition($position)
    {
        $this->position = $position;
        return $this;
    }


    /**
     * Set course
     *
     * @param Course $course
     * @return Step
     */
    public function setCourse(Course $course = null)
    {
        $this->course = $course;

        return $this;
    }

    /**
     * Get course
     *
     * @return Course
     */
    public function getCourse()
    {
        return $this->course;
    }

    /**
     * Add module
     *
     * @param Module $module
     * @return Step
     */
    public function addModule(Module $module)
    {
        $module->setStep($this);
        $this->modules[] = $module;
        return $this;
    }

    /**
     * Remove module
     *
     * @param Module $module
     */
    public function removeModule(Module $module)
    {
        $this->modules->removeElement($module);
    }

    /**
     * Get modules
     *
     * @return Collection
     */
    public function getModules()
    {
        return $this->modules;
    }

}
