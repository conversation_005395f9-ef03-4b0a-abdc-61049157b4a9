<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AuditBundle\Services\CourseManager;

/**
 * FormationVfc
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FormationVfcRepository')]
#[ORM\Table(name: 'formation_vfc')]
class FormationVfc extends Formation implements FormationWithFormInterface
{

    const TYPE_CLASSIC = "classic";
    const TYPE_PREDEFINED = "predefined";

    /**
     * @var Audit
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Audit', inversedBy: 'formations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'audit', nullable: true)]
    private ?Audit $audit = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'auditLabel1', type: Types::STRING, length: 255, nullable: true)]
    private ?string $auditLabel1 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'auditLabel2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $auditLabel2 = null;

    /**
     * Formation constructor.
     */
    public function __construct(Course $course = null)
    {
        parent::__construct();
        $this->setCourse($course);
    }

    /**
     * Set audit
     *
     * @param Audit $audit
     * @return FormationVfc
     */
    public function setAudit($audit)
    {
        $this->audit = $audit;

        return $this;
    }

    /**
     * Get audit
     *
     */
    public function getAudit(): ?Audit
    {
        return $this->audit;
    }

    /**
     * Set auditLabel1
     *
     * @param string $auditLabel1
     * @return FormationVfc
     */
    public function setAuditLabel1($auditLabel1)
    {
        $this->auditLabel1 = $auditLabel1;

        return $this;
    }

    /**
     * Get auditLabel1
     *
     * @return string 
     */
    public function getAuditLabel1()
    {
        return $this->auditLabel1;
    }

    /**
     * Set auditLabel2
     *
     * @param string $auditLabel2
     * @return FormationVfc
     */
    public function setAuditLabel2($auditLabel2)
    {
        $this->auditLabel2 = $auditLabel2;

        return $this;
    }

    /**
     * Get auditLabel2
     *
     * @return string 
     */
    public function getAuditLabel2()
    {
        return $this->auditLabel2;
    }

    /**
     * @param int $auditId
     * @return string
     */
    public function getAuditLabel($auditId)
    {
        switch ($auditId) {
            case 1:
                return $this->auditLabel1;
            break;
            case 2:
                return $this->auditLabel2;
            break;
            default:
                return "";
        }
    }

    public function getSubject() {
        if ($this->hasLinkedForm()) {
            return $this->getAudit()->getCategory()->getName();
        }
        return parent::getSubject();
    }

    /**
     * @return string
     */
    public function getDiscr()
    {
        return self::TYPE_VFC;
    }

    public function hasFormPost(): bool
    {
        return false;
    }

    public function canHavePreRestitutionsForDownload(): bool
    {
        return true;
    }

    public function canDisplayDetailDateAndPlaceReunion(): bool
    {
        return true;
    }

    public function calculateEcheanceNextModule(?string $nextModule = null): \DateTime
    {
        if (in_array(CourseManager::staticGetModuleStep($nextModule), [CourseManager::STEP1_LABEL, CourseManager::STEP2_LABEL])) {
            return $this->getEndDate();
        }
        return $this->getClosingDate();
    }
}
