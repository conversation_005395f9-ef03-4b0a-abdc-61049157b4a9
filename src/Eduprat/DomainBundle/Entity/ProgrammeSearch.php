<?php

namespace Eduprat\DomainBundle\Entity;

use Symfony\Component\HttpFoundation\Request;

/**
 * Entité pour moteur de recherche
 * @package Eduprat\DomainBundle\Entity
 */
class ProgrammeSearch
{
    /**
     * @var string
     */
    private $key;

    /**
     * @var string
     */
    private $title;

    /**
     * @var string
     */
    private $reference;

    /**
     * @var string
     */
    private $format;

    /**
     * @var string
     */
    private $nbSession;

    /**
     * @var \DateTime
     */
    private $start;

    /**
     * @var \DateTime
     */
    private $end;

    /**
     * @var string
     */
    private $closed;

    /**
     * @var bool
     */
    private $opened;

    /**
     * @var string
     */
    private $formerWithSiret;

    /**
     * @var bool
     */
    private $formerWithoutSiret;

    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $financeMode;

    /**
     * @var string
     */
    private $city;

    /**
     * @var string
     */
    private $zip;

    /**
     * @var string
     */
    private $withForm;

    /** @var bool */
    private $withoutForm;

    /** @var bool */
    private $withTopo;

    /** @var int */
    private $year;

    /** @var string */
    private $presence;

    /** @var array */
    private $specialities;

    /** @var array */
    private $categories;

    /** @var int */
    private $supervisor;

    /**
     * @var string
     */
    private $formatByDurations;

    /** @var string */
    private $andpcStatus;

    /**
     * @var string
     */
    private $sessionType;

    /**
     * @var string
     */
    private $formType;

    /**
     * @return string
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * @param string $key
     */
    public function setKey($key)
    {
        $this->key = $key;
        return $this;
    }

    /**
     * Set title
     *
     * @param string $title
     * @return Programme
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

     /**
     * Set reference
     *
     * @param string $reference
     * @return Programme
     */
    public function setReference($reference)
    {
        $this->reference = $reference;

        return $this;
    }

    /**
     * Get reference
     *
     * @return string
     */
    public function getReference()
    {
        return $this->reference;
    }

    /**
     * Set format
     *
     * @param string $format
     * @return Programme
     */
    public function setFormat($format)
    {
        $this->format = $format;

        return $this;
    }

    /**
     * Get format
     *
     * @return string
     */
    public function getFormat()
    {
        return $this->format;
    }

    /**
     * @return string
     */
    public function getNbSession()
    {
        return $this->nbSession;
    }

    /**
     * @param string $nbSession
     */
    public function setNbSession($nbSession)
    {
        $this->nbSession = $nbSession;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStart()
    {
        return $this->start;
    }

    /**
     * @param \DateTime $start
     */
    public function setStart($start)
    {
        $this->start = $start;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEnd()
    {
        return $this->end;
    }

    /**
     * @param \DateTime $end
     */
    public function setEnd($end)
    {
        $this->end = $end;
        return $this;
    }

    /**
     * @return boolean
     */
    public function getClosed()
    {
        return $this->closed;
    }

    /**
     * @param string $closed
     */
    public function setClosed($closed)
    {
        $this->closed = $closed;
        return $this;
    }

    /**
     * @return boolean
     */
    public function getOpened()
    {
        return (boolean) $this->opened;
    }

    /**
     * @param boolean $opened
     */
    public function setOpened($opened)
    {
        $this->opened = $opened;
        return $this;
    }

    /**
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return ProgrammeSearch
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * @return string
     */
    public function getPresence()
    {
        return $this->presence;
    }

    /**
     * @param string $presence
     * @return ProgrammeSearch
     */
    public function setPresence($presence)
    {
        $this->presence = $presence;
        return $this;
    }

    /**
     * @return array
     */
    public function getSpecialities()
    {
        return $this->specialities;
    }

    /**
     * @param string $specialities
     * @return ProgrammeSearch
     */
    public function setSpecialities($specialities)
    {
        $this->specialities = $specialities;
        return $this;
    }

    /**
     * @return array
     */
    public function getCategories()
    {
        return $this->categories;
    }

    /**
     * @param string $categories
     * @return ProgrammeSearch
     */
    public function setCategories($categories)
    {
        $this->categories = $categories;
        return $this;
    }

    /**
     * @return string
     */
    public function isFormerWithSiret()
    {
        return $this->formerWithSiret;
    }

    /**
     * @param string $formerWithSiret
     * @return ProgrammeSearch
     */
    public function setFormerWithSiret($formerWithSiret)
    {
        $this->formerWithSiret = $formerWithSiret;
        return $this;
    }

    /**
     * @return bool
     */
    public function isFormerWithoutSiret()
    {
        return (boolean) $this->formerWithoutSiret;
    }

    /**
     * @param bool $formerWithoutSiret
     * @return ProgrammeSearch
     */
    public function setFormerWithoutSiret($formerWithoutSiret)
    {
        $this->formerWithoutSiret = $formerWithoutSiret;
        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return ProgrammeSearch
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getFinanceMode()
    {
        return $this->financeMode;
    }

    /**
     * @param string $financeMode
     * @return ProgrammeSearch
     */
    public function setFinanceMode($financeMode)
    {
        $this->financeMode = $financeMode;
        return $this;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     * @return ProgrammeSearch
     */
    public function setCity($city)
    {
        $this->city = $city;
        return $this;
    }

    /**
     * @return string
     */
    public function getZip()
    {
        return $this->zip;
    }

    /**
     * @param string $zip
     * @return ProgrammeSearch
     */
    public function setZip($zip)
    {
        $this->zip = $zip;
        return $this;
    }

    /**
     * @return string
     */
    public function getWithForm()
    {
        return $this->withForm;
    }

    /**
     * @param string $withForm
     * @return ProgrammeSearch
     */
    public function setWithForm($withForm)
    {
        $this->withForm = $withForm;
        return $this;
    }
    /**
     * @return string
     */
    public function getWithTopo()
    {
        return $this->withTopo;
    }

    /**
     * @param string $withTopo
     * @return ProgrammeSearch
     */
    public function setWithTopo($withTopo)
    {
        $this->withTopo = $withTopo;
        return $this;
    }

    /**
     * @return bool
     */
    public function getWithoutForm()
    {
        return (boolean) $this->withoutForm;
    }

    /**
     * @param bool $withoutForm
     * @return ProgrammeSearch
     */
    public function setWithoutForm($withoutForm)
    {
        $this->withoutForm = $withoutForm;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormatByDurations()
    {
        return $this->formatByDurations;
    }

    /**
     * @param string $formatByDurations
     * @return ProgrammeSearch
     */
    public function setFormatByDurations($formatByDurations)
    {
        $this->formatByDurations = $formatByDurations;
        return $this;
    }

    /**
     * @return string
     */
    public function getSessionType()
    {
        return $this->sessionType;
    }

    /**
     * @param string $sessionType
     * @return ProgrammeSearch
     */
    public function setSessionType($sessionType)
    {
        $this->sessionType = $sessionType;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormType()
    {
        return $this->formType;
    }

    /**
     * @param string $formType
     * @return ProgrammeSearch
     */
    public function setFormType($formType)
    {
        $this->formType = $formType;
        return $this;
    }

    public function getAndpcStatus()
    {
        return $this->andpcStatus;
    }

    /**
     * @param string $andpcStatus
     * @return ProgrammeSearch
     */
    public function setAndpcStatus($andpcStatus)
    {
        $this->andpcStatus = $andpcStatus;
        return $this;
    }

    /**
     * @return int
     */
    public function getSupervisor()
    {
        return $this->supervisor;
    }

    /**
     * @param int $supervisor
     * @return ProgrammeSearch
     */
    public function setSupervisor($supervisor)
    {
        $this->supervisor = $supervisor;
        return $this;
    }

    public function isValid() {
        return !empty($this->getParams());
    }

    public function toArray() {
        return array(
            'key' => $this->getKey(),
            'reference' => $this->getReference(),
            'title' => $this->getTitle(),
            'format' => $this->getFormat(),
            'nbSession' => $this->getNbSession(),
            'start' => $this->getStart() !== null ? $this->getStart()->format('Y-m-d') : null,
            'end' => $this->getEnd() !== null ? $this->getEnd()->format('Y-m-d') : null,
            'year' => $this->getYear() !== null ? $this->getYear() : null,
            'presence' => $this->getPresence() !== null ? $this->getPresence() : null,
            'categories' => $this->getCategories() !== null ? $this->getCategories() : null,
            'specialities' => $this->getSpecialities() !== null ? $this->getSpecialities() : null,
            'closed' => $this->getClosed(),
            'formerWithSiret' => $this->isFormerWithSiret(),
            'type' => $this->getType(),
            'financeMode' => $this->getFinanceMode(),
            'formatByDurations' => $this->getFormatByDurations(),
            'sessionType' => $this->getSessionType(),
            'formType' => $this->getFormType(),
            'andpcStatus' => $this->getAndpcStatus(),
            'city' => $this->getCity(),
            'zip' => $this->getZip(),
            'withForm' => $this->getWithForm(),
            'withTopo' => $this->getWithTopo(),
            'supervisor' => $this->getSupervisor(),
        );
    }

    public function getParams() {
        $params = [];
        foreach ($this->toArray() as $name => $value) {
            if ($value) {
                $params[$name] = $value;
            }
        }
        return $params;
    }

    public function handleRequest(Request $request) {
        foreach ($this->toArray() as $name => $value) {
            if ($request->query->has($name)) {
                if (in_array($name, array('start', 'end'))) {
                    $this->$name = new \DateTime($request->query->get($name));
                }
                else {
                    if (in_array($name, array('categories', 'specialities'))) {
                        $this->$name = $request->query->all($name);
                    }
                    else {
                        $this->$name = $request->query->get($name);
                    }
                }
            }
        }
    }
}