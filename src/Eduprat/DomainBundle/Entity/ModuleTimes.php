<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * ModuleTimes
 */
#[ORM\Entity]
#[ORM\Table(name: 'module_times')]
class ModuleTimes
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'video_presession', type: Types::INTEGER)]
    private ?int $video_presession = 3;

    #[ORM\Column(name: 'documents_pedagogiques', type: Types::INTEGER)]
    private ?int $documents_pedagogiques = 3;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'form_presession', type: Types::INTEGER)]
    private ?int $form_presession = 30;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'prerestitution', type: Types::INTEGER)]
    private ?int $prerestitution = 20;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'etutorat_1', type: Types::INTEGER)]
    private ?int $etutorat_1 = 5;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'fiche_action_1', type: Types::INTEGER)]
    private ?int $fiche_action_1 = 0;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'video_postsession', type: Types::INTEGER)]
    private ?int $video_postsession = 2;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'topos', type: Types::INTEGER)]
    private ?int $topos = 0;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'form_postsession', type: Types::INTEGER)]
    private ?int $form_postsession = 20;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'tool_box', type: Types::INTEGER)]
    private ?int $tool_box = 2;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'etutorat_2', type: Types::INTEGER)]
    private ?int $etutorat_2 = 2;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'restitution', type: Types::INTEGER)]
    private ?int $restitution = 20;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'fiche_action_2', type: Types::INTEGER)]
    private ?int $fiche_action_2 = 10;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'synthese', type: Types::INTEGER)]
    private ?int $synthese = 2;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'progression', type: Types::INTEGER)]
    private ?int $progression = 0;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'etutorat_3', type: Types::INTEGER)]
    private ?int $etutorat_3 = 2;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'end', type: Types::INTEGER)]
    private ?int $end = 0;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'form_evaluation', type: Types::INTEGER)]
    private ?int $form_evaluation = 0;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'moduleTimes', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false)]
    private ?Formation $formation = null;

    /**
     * Formation constructor.
     */
    public function __construct($formationType = null)
    {
        if ($formationType === Formation::TYPE_VFC || $formationType === Formation::TYPE_TCS) {
            $this->video_presession = 0;
            $this->documents_pedagogiques = 0;
            $this->form_presession = 50;
            $this->prerestitution = 5;
            $this->etutorat_1 = 5;
            $this->fiche_action_1 = 0;
            $this->video_postsession = 0;
            $this->topos = 0;
            $this->form_postsession = 0;
            $this->tool_box = 0;
            $this->etutorat_2 = 0;
            $this->restitution = 0;
            $this->fiche_action_2 = 0;
            $this->synthese = 0;
            $this->progression = 0;
            $this->etutorat_3 = 0;
            $this->end = 0;
            $this->form_evaluation = 0;
        }
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

     /**
     * Get the value of video_presession
     *
     * @return  integer
     */
    public function getVideoPresession()
    {
        return $this->video_presession;
    }

    /**
     * Set the value of video_presession
     *
     * @param  integer  $video_presession
     *
     * @return  self
     */
    public function setVideoPresession($video_presession)
    {
        $this->video_presession = $video_presession;

        return $this;
    }

    /**
     * Get the value of documents_pedagogiques
     *
     * @return  integer
     */
    public function getDocumentsPedagogiques()
    {
        return $this->documents_pedagogiques;
    }

    /**
     * Set the value of documents_pedagogiques
     *
     * @param  integer  $documents_pedagogiques
     *
     * @return  self
     */
    public function setDocumentsPedagogiques($documents_pedagogiques)
    {
        $this->documents_pedagogiques = $documents_pedagogiques;

        return $this;
    }

    /**
     * Get the value of form_presession
     *
     * @return  integer
     */
    public function getFormPresession()
    {
        return $this->form_presession;
    }

    /**
     * Set the value of form_presession
     *
     * @param  integer  $form_presession
     *
     * @return  self
     */
    public function setFormPresession($form_presession)
    {
        $this->form_presession = $form_presession;

        return $this;
    }

    /**
     * Get the value of prerestitution
     *
     * @return  integer
     */
    public function getPrerestitution()
    {
        return $this->prerestitution;
    }

    /**
     * Set the value of prerestitution
     *
     * @param  integer  $prerestitution
     *
     * @return  self
     */
    public function setPrerestitution($prerestitution)
    {
        $this->prerestitution = $prerestitution;

        return $this;
    }

    /**
     * Get the value of etutorat_1
     *
     * @return  integer
     */
    public function getEtutorat1()
    {
        return $this->etutorat_1;
    }

    /**
     * Set the value of etutorat_1
     *
     * @param  integer  $etutorat_1
     *
     * @return  self
     */
    public function setEtutorat1($etutorat_1)
    {
        $this->etutorat_1 = $etutorat_1;

        return $this;
    }

    /**
     * Get the value of fiche_action_1
     *
     * @return  integer
     */
    public function getFicheAction1()
    {
        return $this->fiche_action_1;
    }

    /**
     * Set the value of fiche_action_1
     *
     * @param  integer  $fiche_action_1
     *
     * @return  self
     */
    public function setFicheAction1($fiche_action_1)
    {
        $this->fiche_action_1 = $fiche_action_1;

        return $this;
    }

    /**
     * Get the value of video_postsession
     *
     * @return  integer
     */
    public function getVideoPostsession()
    {
        return $this->video_postsession;
    }

    /**
     * Set the value of video_postsession
     *
     * @param  integer  $video_postsession
     *
     * @return  self
     */
    public function setVideoPostsession($video_postsession)
    {
        $this->video_postsession = $video_postsession;

        return $this;
    }

    /**
     * Get the value of topos
     *
     * @return  integer
     */
    public function getTopos()
    {
        return $this->topos;
    }

    /**
     * Set the value of topos
     *
     * @param  integer  $topos
     *
     * @return  self
     */
    public function setTopos($topos)
    {
        $this->topos = $topos;

        return $this;
    }

    /**
     * Get the value of form_postsession
     *
     * @return  integer
     */
    public function getFormPostsession()
    {
        return $this->form_postsession;
    }

    /**
     * Set the value of form_postsession
     *
     * @param  integer  $form_postsession
     *
     * @return  self
     */
    public function setFormPostsession($form_postsession)
    {
        $this->form_postsession = $form_postsession;

        return $this;
    }

    /**
     * Get the value of tool_box
     *
     * @return  integer
     */
    public function getToolBox()
    {
        return $this->tool_box;
    }

    /**
     * Set the value of tool_box
     *
     * @param  integer  $tool_box
     *
     * @return  self
     */
    public function setToolBox($tool_box)
    {
        $this->tool_box = $tool_box;

        return $this;
    }

    /**
     * Get the value of etutorat_2
     *
     * @return  integer
     */
    public function getEtutorat2()
    {
        return $this->etutorat_2;
    }

    /**
     * Set the value of etutorat_2
     *
     * @param  integer  $etutorat_2
     *
     * @return  self
     */
    public function setEtutorat2($etutorat_2)
    {
        $this->etutorat_2 = $etutorat_2;

        return $this;
    }

    /**
     * Get the value of restitution
     *
     * @return  integer
     */
    public function getRestitution()
    {
        return $this->restitution;
    }

    /**
     * Set the value of restitution
     *
     * @param  integer  $restitution
     *
     * @return  self
     */
    public function setRestitution($restitution)
    {
        $this->restitution = $restitution;

        return $this;
    }

    /**
     * Get the value of fiche_action_2
     *
     * @return  integer
     */
    public function getFicheAction2()
    {
        return $this->fiche_action_2;
    }

    /**
     * Set the value of fiche_action_2
     *
     * @param  integer  $fiche_action_2
     *
     * @return  self
     */
    public function setFicheAction2($fiche_action_2)
    {
        $this->fiche_action_2 = $fiche_action_2;

        return $this;
    }

    /**
     * Get the value of synthese
     *
     * @return  integer
     */
    public function getSynthese()
    {
        return $this->synthese;
    }

    /**
     * Set the value of synthese
     *
     * @param  integer  $synthese
     *
     * @return  self
     */
    public function setSynthese($synthese)
    {
        $this->synthese = $synthese;

        return $this;
    }

    /**
     * Get the value of progression
     *
     * @return  integer
     */
    public function getProgression()
    {
        return $this->progression;
    }

    /**
     * Set the value of progression
     *
     * @param  integer  $progression
     *
     * @return  self
     */
    public function setProgression($progression)
    {
        $this->progression = $progression;

        return $this;
    }

    /**
     * Get the value of etutorat_3
     *
     * @return  integer
     */
    public function getEtutorat3()
    {
        return $this->etutorat_3;
    }

    /**
     * Set the value of etutorat_3
     *
     * @param  integer  $etutorat_3
     *
     * @return  self
     */
    public function setEtutorat3($etutorat_3)
    {
        $this->etutorat_3 = $etutorat_3;

        return $this;
    }

    /**
     * Get the value of end
     *
     * @return  integer
     */
    public function getEnd()
    {
        return $this->end;
    }

    /**
     * Set the value of end
     *
     * @param  integer  $end
     *
     * @return  self
     */
    public function setEnd($end)
    {
        $this->end = $end;

        return $this;
    }

    /**
     * Get the value of form_evaluation
     *
     * @return  integer
     */
    public function getFormEvaluation()
    {
        return $this->form_evaluation;
    }

    /**
     * Set the value of form_evaluation
     *
     * @param  integer  $form_evaluation
     *
     * @return  self
     */
    public function setFormEvaluation($form_evaluation)
    {
        $this->form_evaluation = $form_evaluation;

        return $this;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     * @return ModuleTimes
     */
    public function setFormation(Formation $formation = null)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }
}

