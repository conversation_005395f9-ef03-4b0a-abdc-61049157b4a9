<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * FactureState
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FactureStateRepository')]
#[ORM\Table(name: 'facture_state')]
class FactureState
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formation
     */
    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'factureState', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'isWaiting', type: Types::BOOLEAN, nullable: true)]
    private ?bool $isWaiting = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'isChargeable', type: Types::BOOLEAN, nullable: true)]
    private ?bool $isChargeable = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * Formation constructor.
     */
    public function __construct(Formation $formation)
    {
        $this->createdAt = new \DateTime();
        $this->setFormation($formation);
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set formation
     *
     * @param Formation $formation
     * @return FactureState
     */
    public function setFormation(Formation $formation = null)
    {
        $this->formation = $formation;

        return $this;
    }

    /**
     * Get formation
     *
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * @return bool
     */
    public function isWaiting()
    {
        return $this->isWaiting;
    }

    /**
     * @param bool $isWaiting
     * @return FactureState
     */
    public function setIsWaiting($isWaiting)
    {
        $this->isWaiting = $isWaiting;
        return $this;
    }

    /**
     * @return bool
     */
    public function isChargeable()
    {
        return $this->isChargeable;
    }

    /**
     * @param bool $isChargeable
     * @return FactureState
     */
    public function setIsChargeable($isChargeable)
    {
        $this->isChargeable = $isChargeable;
        return $this;
    }

    #[ORM\PreUpdate]
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }
}