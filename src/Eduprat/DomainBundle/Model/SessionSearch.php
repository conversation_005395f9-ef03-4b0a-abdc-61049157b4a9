<?php

namespace Eduprat\DomainBundle\Model;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Model\AbstractSearch;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Form\SessionSearchType;

/**
 * Entité pour moteur de recherche
 * @package Eduprat\DomainBundle\Entity
 */
class SessionSearch extends AbstractSearch {

    public const SEARCH_FORM = SessionSearchType::class;

    const FIELDS_REPLACER = array(
        "city"
    );

    public ?string $key = null;
    public ?string $nbSession = null;
    public ?\DateTime $start = null;
    public ?\DateTime $end = null;

    public ?\DateTime $querySearchStartDate = null;
    public ?\DateTime $querySearchEndDate = null;
    public ?\DateTime $startCreationDate = null;
    public ?\DateTime $endCreationDate = null;
    public ?\DateTime $startUpdateDate = null;
    public ?\DateTime $endUpdateDate = null;
    public ?string $closed = null;
    public ?string $archived = null;
    public ?string $type = null;
    public ?FinanceMode $financeMode = null;
    public ?FinanceSousMode $financeSousMode = null;
    public ?string $city = null;
    public ?string $zip = null;
    public ?string $withForm = null;
    public ?int $year = null;
    public ?Person $supervisor = null;
    public ?Programme $programme = null;
    public ?string $withTopo = null;
    public ?string $withParticipant = null;
    public ?string $presence = null;
    public ?string $withFormer = null;
    public ?string $billingStatus = null;
    public ?string $fileStatus = null;
    public ?string $accounted = null;
    public ?string $status = null;
    public ?string $partenariat = null;
    public ?string $ots = null;
    public function prepareFormOptions($options = array(), $parameters = array())
    {
        $coordinatorOrSupervisorId = array();
        if($parameters["user"]->isCoordinator()) {
            $coordinatorOrSupervisorId['coordinatorId'] = $parameters["user"]->getId();
        } else if($parameters["user"]->isSupervisor()) {
            $coordinatorOrSupervisorId['supervisorId'] = $parameters["user"]->getId();
        }
        $options['coordinatorOrSupervisorId'] = $coordinatorOrSupervisorId;
        return $options;
    }

    public function prepareSearch($parameters = array())
    {
        $year = $parameters["year"];
        $programme = $parameters["programme"];
        if($year && $year !== 'tous') {
            $this->querySearchStartDate = new \DateTime($year.'-01-01');
            $this->querySearchEndDate = new \DateTime($year.'-12-31');
        }

        /** @var Programme $programme */
        if($programme && $programme !== null) {
            $this->programme = $programme;
        }
    }

}
