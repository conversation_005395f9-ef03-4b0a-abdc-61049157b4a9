<?php

namespace Eduprat\DomainBundle\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PostFlushEventArgs;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Activity;
use Eduprat\DomainBundle\Entity\CoordinatorFiles;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\FormateurFiles;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\UnityFormation;
use Eduprat\DomainBundle\Services\SendinBlueManager;
use DateTime;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::postPersist)]
#[AsDoctrineListener(event: Events::postUpdate)]
#[AsDoctrineListener(event: Events::postFlush)]
class DoctrineEventsSubscriber
{
    /**
     * @var AdressesService
     */
    private $adressesService;

    /**
     * @var SendinBlueManager
     */
    private $sendinBlueManager;

    protected $personUpdated = null;
    protected $formationUpdateFileCompleted = array();

    protected $entityChangeSetParticipantPreUpdate = array();

    /**
     * DoctrineEventsSubscriber constructor.
     * @param AdressesService $adressesService
     * @param SendinBlueManager $sendinBlueManager
     */
    public function __construct(AdressesService $adressesService, SendinBlueManager $sendinBlueManager)
    {
        $this->adressesService = $adressesService;
        $this->sendinBlueManager = $sendinBlueManager;
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $entity = $args->getObject();
        if ($entity instanceof Formation) {
            $this->updateFormation($entity);
            $this->formationUpdateFileCompleted[] = $entity;
        }
        if ($entity instanceof FormateurFiles || $entity instanceof CoordinatorFiles || $entity instanceof Participation) {
            $this->formationUpdateFileCompleted[] = $entity->getFormation();
        }
        if ($entity instanceof Activity and $entity->isPrezi()) {
            $entity->setPreziUrl(str_replace("view", "p/embed", $entity->getPreziUrl()));
        }
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $this->postPersistOrPostUpdate($args);
    }

    public function postUpdate(PostUpdateEventArgs $args): void
    {
        $this->postPersistOrPostUpdate($args);
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $entity = $args->getObject();
        if ($entity instanceof UnityFormation) {
            $fields = $args->getEntityChangeSet();
            if (array_key_exists("minTimeConnected", $fields)) {
                $programme = $entity->getProgramme();
                $u1 = $programme->getUnityByPosition(1);
                $u3 = $programme->getUnityByPosition(3);
                $u1MinTime = false;
                $u3MinTime = false;
                if ($u1 && $u1->isElearning()) {
                    $u1MinTime = $u1->getMinTimeConnected() ? $u1->getMinTimeConnected() : 0;
                }
                if ($u3 && $u3->isElearning()) {
                    $u3MinTime = $u3->getMinTimeConnected() ? $u3->getMinTimeConnected() : 0;
                }
                foreach ($programme->getFormations() as $formation) {
                    $formationModuleMinTimes = $formation->getModuleMinTimes()->first();
                    $formationModuleMinTimes->setFormPresession($u1MinTime);
                    $formationModuleMinTimes->setFormPostsession($u3MinTime);
                }
            }
        }
        // Mise à jour position Programme
        if ($entity instanceof Formation) {
            $fields = $args->getEntityChangeSet();
            $entity->updateFileCompleted();
            if (
                array_key_exists("address", $fields)
                || array_key_exists("address2", $fields)
                || array_key_exists("city", $fields)
                || array_key_exists("zipCode", $fields)
            ) {
                $this->updateFormation($entity);
            }
        } elseif ($entity instanceof Programme) {
            $fields = $args->getEntityChangeSet();
            // Si on modifie une formation et qu'elle n'a qu'un mode d'exercice, on le modifie pour toutes les participations associées #18208
            if (array_key_exists("exercisesMode", $fields) && $entity->getExercisesMode() && count($entity->getExercisesMode()) === 1) {
                /** @var Participation $participation */
                foreach ($entity->getParticipations() as $participation) {
                    $participation->setExerciseMode($entity->getExercisesMode()[0]);
                    $args->getObjectManager()->persist($participation);
                }
            }
        } elseif ($entity instanceof Person) {
            $fields = $args->getEntityChangeSet();
            if (array_key_exists("siret", $fields) && in_array(null, array_values($fields["siret"]), true)) {
                $this->personUpdated = $entity;
            }
            if (array_key_exists("siret", $fields) && $entity->isFormer()) {
                /** @var Formation $formersFormation */
                foreach ($entity->getFormersFormations() as $formersFormation) {
                    $this->formationUpdateFileCompleted[] = $formersFormation;
                }
            }
            if ($entity->isFormer()) {
                /** @var Formation $formersFormation */
                foreach ($entity->getFormersFormations() as $formersFormation) {
                    $this->updateFormationUpdatedAt($formersFormation);
                }
            }
            if ((array_key_exists("crStatus", $fields) || array_key_exists("crStatusDate", $fields)) && $entity->isCoordinator()) {
                /** @var Formation $coordinatorsFormation */
                foreach ($entity->getCoordinatorsFormations() as $coordinatorsFormation) {
                    $this->formationUpdateFileCompleted[] = $coordinatorsFormation;
                }
            }
        } else if ($entity instanceof FormateurFiles || $entity instanceof CoordinatorFiles) {
            $this->formationUpdateFileCompleted[] = $entity->getFormation();
        } else if (($entity instanceof Participation) && array_key_exists("coordinator", $args->getEntityChangeSet())) {
            $this->formationUpdateFileCompleted[] = $entity->getFormation();
        } else if ($entity instanceof Participant && array_key_exists("email", $args->getEntityChangeSet()) && $entity->getUser() && $entity->getUser()->getRole() === null) {
            $entity->getUser()->setEmail($entity->getEmail());
            $args->getObjectManager()->persist($entity->getUser());
        } else if ($entity instanceof Activity and $entity->isPrezi()) {
            $entity->setPreziUrl(str_replace("view", "p/embed", $entity->getPreziUrl()));
        }

        if ($entity instanceof Participant) {
            $this->entityChangeSetParticipantPreUpdate = $args->getEntityChangeSet();
        }
    }

    public function postFlush(PostFlushEventArgs $args): void
    {
        if ($this->personUpdated && $this->personUpdated instanceof Person) {
            $this->updateFormateurCost($this->personUpdated, $args->getObjectManager());
            $this->personUpdated = null;
        }
        if (count($this->formationUpdateFileCompleted)) {
            /** @var Formation $formation */
            foreach ($this->formationUpdateFileCompleted as $formation) {
                if ($formation instanceof Formation) {
                    $formation->updateFileCompleted();
                    $args->getObjectManager()->persist($formation);
                }
            }
            $this->formationUpdateFileCompleted = array();
            $args->getObjectManager()->flush();
        }
    }

    public function updateFormation(Formation $formation): void
    {
        $formation->updateRegionName();

        if (!is_null($formation->getZipCode())) {
            $address = sprintf("%s %s %s %s", $formation->getAddress(), $formation->getAddress2(), ltrim($formation->getZipCode(), '0'), $formation->getCity());
            $result = $this->adressesService->getGeocodeAdresse($address);

            // Si non trouvé, recherche avec adresse 2 uniquement
            if (count($result["adresse"]) === 0) {
                $address = sprintf("%s %s %s", $formation->getAddress2(), ltrim($formation->getZipCode(), '0'), $formation->getCity());
                $result = $this->adressesService->getGeocodeAdresse($address);
            }

            // Si toujours pas trouvé, recherche avec adresse 1 uniquement
            if (count($result["adresse"]) === 0) {
                $address = sprintf("%s %s %s", $formation->getAddress(), ltrim($formation->getZipCode(), '0'), $formation->getCity());
                $result = $this->adressesService->getGeocodeAdresse($address);
            }

            if (count($result["adresse"])) {
                $coordonnees = explode(",", $result["adresse"][0]["coordonnees"]);
                $lat = $coordonnees[0];
                $lon = $coordonnees[1];
                $formation->setLatitude($lat);
                $formation->setLongitude($lon);
            }
        }
    }

    protected function updateFormationUpdatedAt(?Formation $formation): void
    {
        if ($formation) {
            $formation->setUpdatedAt(new \DateTime());
        }
    }

    public function updateFormateurCost(Person $person, EntityManagerInterface $entityManager): void
    {
        $formateurs = $entityManager->getRepository(Formateur::class)->findFutureFormationForPerson($person);
        if ($formateurs) {
            foreach ($formateurs as $former) {
                $former->setHonorary($person->getDefaultFormateurCost());
                $entityManager->persist($former);
            }
            $this->personUpdated = null;
            // Le flush ici créé une boucle infini -> il a été déplacé dans le controller
            //$entityManager->flush();
        }
    }

    public function postPersistOrPostUpdate(PostPersistEventArgs|PostUpdateEventArgs $args): void
    {
        $this->updateToUpdateSibFieldIfNotAlreadyDone($args);
    }

    public function hasModifiedParticipantInPreupdateWithoutToUpdateSib(): bool
    {
        return !isset($this->entityChangeSetParticipantPreUpdate['toUpdateSib']);
    }

    /**
     * @param PostUpdateEventArgs|PostPersistEventArgs $args
     * @return void
     */
    public function updateToUpdateSibFieldIfNotAlreadyDone(PostUpdateEventArgs|PostPersistEventArgs $args): void
    {
        $entity = $args->getObject();
        if ($entity instanceof Participant
            || ($entity instanceof Person && $entity->isCoordinator())
        ) {
            if ($args instanceof PostUpdateEventArgs
                && !$this->hasModifiedParticipantInPreupdateWithoutToUpdateSib()
            ) {
                return;
            }
        }

        if ($entity instanceof Participant) {
            $entity->setToUpdateSib(true);
        }
        if ($entity instanceof Person && $entity->isCoordinator()) {
            $entity->setToUpdateSib(true);
        }
    }
}
