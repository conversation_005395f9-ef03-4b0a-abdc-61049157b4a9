<?php

namespace Eduprat\DomainBundle\Services\Email;

use Carbon\Carbon;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;

/**
 *
 * Audit et Présentiel => Délai : J+1 de la date de fin de réunion
 *
 * Envoyé seulement si le questionnaire post-session n'est pas déjà complété
 *
 * Class EmailFormationJP28
 * @package Eduprat\DomainBundle\Services\Email
 */
class EmailFormationJP28EmptyCoordinator extends EmailFormationJP28
{

    const ALIAS = 'formation_jp28_coordinators';
    const TEMPLATE = "domain/email/formation_jp28.html.twig";
    const NB_MAX_EMAIL_BEFORE_ALERT = 40;

    public function loadItems(): array
    {
        $startDate = Carbon::now();
        $formations = $this->entityManager->getRepository(Formation::class)->findByUnityOpeningDate($startDate, array(
            FormationAudit::class,
            FormationPresentielle::class,
            FormationVignette::class,
            FormationVignetteAudit::class,
            FormationElearning::class,
        ), true);

        $items = array();

        /** @var Formation $formation */
        foreach ($formations as $formation) {
            $persons = array();
            /** @var Person $person */
            foreach ($formation->getCoordinatorsPerson()->toArray() as $person) {
                if (!in_array($person->getId(), $persons)) {
                    $persons[] = $person->getId();
                    $items[] = (new Participation())->setFormation($formation)->setParticipant((new Participant())->setUser($person)->setAdeli("XXXXXXXXXX"));
                }
            }
        }

        return $items;
    }

    /**
     * @param Participation $item
     */
    public function shouldSend($item): bool
    {
        $startDate = Carbon::now();
        return !$item->getFormation()->isNoMailing() && !$item->getParticipant()->isNoMailing() && $item->getFormation()->hasLinkedForm() && $item->getFormation()->isFormPost28d() && $item->getFormation()->thirdUnityOpeningDate() == $startDate->setTime('0','0','0');
    }

    /**
     * @param Participation $item
     */
    public function getRecipient($item): ?string
    {
        return $item?->getParticipant()?->getUser()?->getEmail();
    }

    /**
     * @param Participation $item
     */
    public function getSubject($item): string
    {
        return "(EDUPRAT Formations) – Accès à votre parcours post-formation";
    }

    /**
     * @param Participation $item
     */
    public function getViewVars($item): array
    {
        return array(
            "evaluationCompleted" => true,
            "item" => $item,
            "emptyForCoordinator" => true
        );
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }

    public function getTemplate($item): string
    {
        return self::TEMPLATE;
    }
}