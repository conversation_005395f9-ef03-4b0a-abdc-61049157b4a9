<?php

namespace Eduprat\DomainBundle\Services\Email;

use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Routing\RouterInterface;

class EmailRelanceEvaluationJP1 extends BaseEmailParticipation
{
    const ALIAS = 'formation_relance_evaluation_jp1';
    const TEMPLATE = "domain/email/evaluation_relance_jp1.html.twig";
    const NB_MAX_EMAIL_BEFORE_ALERT = 500;

    /**
     * @var ParticipationAccessManager
     */
    protected $participationAccessManager;

    public function __construct(ParticipationAccessManager $participationAccessManager, EntityManagerInterface $entityManager, RouterInterface $router, FormManagerFactory $formManagerFactory, $senderEmail = null, $template = null, $subject = null)
    {
        parent::__construct($entityManager, $router, $formManagerFactory, $senderEmail, $template, $subject);
        $this->participationAccessManager = $participationAccessManager;
    }

    public function loadItems(): array
    {
        $date = Carbon::now()->sub(1, 'days');
        return $this->getRepository()->findByFormClosingDateElearning($date);
    }

    /**
     * @param Participation $item
     */
    public function shouldSend($item): bool
    {
        $available = $this->participationAccessManager->isEvaluationAvailable($item->getFormation(), $item->getParticipant()->getUser());
        $completed = $this->participationAccessManager->isEvaluationCompleted($item->getFormation(), $item->getParticipant()->getUser());
        return parent::shouldSend($item) && $available && !$completed;
    }

    /**
     * @param Participation $item
     */
    public function getSubject($item): string
    {
        $coordinator = $item && $item->getCoordinator() && $item->getCoordinator()->getPerson() ? $item->getCoordinator()->getPerson()->getFullname() . ' ' : '';
        return sprintf("%s(EDUPRAT Formations) - Complétez le questionnaire d'évaluation de la formation « %s »", $coordinator, $item->getFormation()->getProgramme()->getTitle());
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }

    public function getTemplate($item): string
    {
        return self::TEMPLATE;
    }
}