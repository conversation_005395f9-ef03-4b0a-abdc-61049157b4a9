<?php

namespace Eduprat\DomainBundle\Services\Email;

use Carbon\Carbon;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\Participation;

/**
 *
 * Présentiel          : Délai : J+3 de la date de fin de réunion
 * Audit Classique     : Délai : J-7 de la date de clotûre
 * Audit Cas clinique  : Délai : J-3 de la date de clotûre
 * Vignette            : Délai : J-3 de la date de clotûre
 * Elearning           : Délai : J-5 et J-2 de la date de clotûre
 *
 * Envoyé seulement si le questionnaire post-session n'est pas déjà complété
 *
 * Class EmailFormationJP1
 * @package Eduprat\DomainBundle\Services\Email
 */
class EmailFormationJP1Relance extends EmailFormationJP1
{
    const ALIAS = 'formation_jp1_relance';
    const TEMPLATE = "domain/email/formation_jp1.html.twig";

    const NB_MAX_EMAIL_BEFORE_ALERT = 500 * 7;

    public function loadItems(): array
    {
        $dateSub3Days = Carbon::now()->sub(3, 'days');
        $dateAdd2Days = Carbon::now()->add(2, 'days');
        $dateAdd3Days = Carbon::now()->add(3, 'days');
        $dateAdd5Days = Carbon::now()->add(5, 'days');
        $dateAdd7Days = Carbon::now()->add(7, 'days');

        return array_filter(array_merge(
            $this->getRepository()->findByDateAndClass("f.endDate", $dateSub3Days, array(FormationPresentielle::class), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd7Days, array(FormationAudit::TYPE_CLASSIC), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd3Days, array(FormationAudit::TYPE_PREDEFINED), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd3Days, array(FormationVignette::class), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd3Days, array(FormationElearning::class), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd2Days, array(FormationElearning::class), false, true),
            $this->getRepository()->findByDateAndClass("f.formClosingDate", $dateAdd5Days, array(FormationElearning::class), false, true),
        ), function ($item) {
            return $this->check3DaysElearningTwoUnity($item);
        });
    }

    /**
     * @param Participation $item
     */
    public function shouldSend($item): bool
    {
        return $this->parentShouldSend($item) && ($item->getFinanceSousMode()->isActalians() || $item->getFinanceSousMode()->isDpc()) && $item->getNextModule() && $item->getFormation()->getFormClosingDate() >= new \DateTime('today midnight');
    }

    public function check3DaysElearningTwoUnity(Participation $item) {
        if ($item->getFormation()->isElearning()) {
            $dateAdd3Days = Carbon::now()->add(3, 'days');
            if ($item->getFormation()->getProgramme()->isElearningTwoUnity()) {
                return $item->getFormation()->getFormClosingDate()->format('Y/m/d') ==  $dateAdd3Days->format('Y/m/d'); // 3 jours
            } else {
                return $item->getFormation()->getFormClosingDate()->format('Y/m/d') !== $dateAdd3Days->format('Y/m/d'); // 2 et 5 jours
            }
        }
        return true;
    }

    /**
     * @param Participation $item
     */
    public function getSubject($item): string
    {
        $coordinator = $item && $item->getCoordinator() && $item->getCoordinator()->getPerson() ? $item->getCoordinator()->getPerson()->getFullname() . ' ' : '';
        return sprintf("%s[Relance] (EDUPRAT Formations) - Accès à votre parcours post-formation", $coordinator);
    }

    /**
     * @param Participation $item
     */
    public function getViewVars($item): array
    {
        return array_merge(parent::getViewVars($item), array(
            "relance" => true
        ));
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }

    public function getTemplate($item): string
    {
        return self::TEMPLATE;
    }

    /**
     * @param Participation $item
     */
    public function parentShouldSend(Participation $item): bool
    {
        return parent::shouldSend($item);
    }
}