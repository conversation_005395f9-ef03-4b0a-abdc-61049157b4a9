<?php

namespace Eduprat\DomainBundle\Services\Email;

use Doctrine\ORM\EntityRepository;
use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\PasswordHistory;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Symfony\Component\Routing\RouterInterface;

class EmailExpiredPassword extends BaseEmailParticipation
{
    const ALIAS = 'expired_password';
    const TEMPLATE = "domain/email/expired_password.html.twig";
    const NB_MAX_EMAIL_BEFORE_ALERT = 500;

    /**
     * @var EntityManagerInterface
     */
    protected $entityManager;

    /**
     * @var string
     */
    private $passwordExpirationInterval;

    public function __construct(EntityManagerInterface $entityManager, RouterInterface $router, FormManagerFactory $formManagerFactory, $senderEmail, $passwordExpirationInterval)
    {
        parent::__construct($entityManager, $router, $formManagerFactory, $senderEmail, self::TEMPLATE, "EDUPRAT Formations - Votre mot de passe va bientôt expirer");
        $this->passwordExpirationInterval = $passwordExpirationInterval;
        $this->entityManager = $entityManager;
    }

    public function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(PasswordHistory::class);
    }

    public function loadItems(): array
    {
        $start = Carbon::now()->add(7, 'day')->sub(new \DateInterval($this->passwordExpirationInterval))->setTime(0, 0 ,0);
        $end = (clone $start)->add(1, 'day');

        return $this->getRepository()->createQueryBuilder("ph")
            ->leftJoin(
                PasswordHistory::class,
                'ph2',
                'WITH',
                'ph.person = ph2.person AND ph.createdAt < ph2.createdAt'
            )
            ->where( 'ph2.createdAt IS NULL' )
            ->andWhere("ph.createdAt BETWEEN :start and :end")
            ->setParameter("start" , $start)
            ->setParameter("end" , $end)
            ->orderBy("ph.createdAt", "DESC")
            ->getQuery()->getResult();
    }

    /**
     * @param PasswordHistory $item
     */
    public function shouldSend($item): bool
    {
        return !is_null($item->getPerson()->getRole()) && !$item->getPerson()->isFormer() && !$item->getPerson()->isAdvisor();
    }

    /**
     * @param PasswordHistory $item
     */
    public function getRecipient($item): ?string
    {
        return $item?->getPerson()?->getEmail();
    }

    /**
     * @param PasswordHistory $item
     */
    function afterEmailSent($item): void
    {
        $date = Carbon::now()->add(7, 'days');
        $item->getPerson()->setTokenExpireAt($date);
        $this->entityManager->persist($item);
        $this->entityManager->flush();
    }

    /**
     * @param PasswordHistory $item
     */
    public function getViewVars($item): array
    {
        $item->getPerson()->setToken(md5($item->getPerson()->getSalt().uniqid()));
        return array(
            "person" => $item->getPerson()
        );
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }
}
