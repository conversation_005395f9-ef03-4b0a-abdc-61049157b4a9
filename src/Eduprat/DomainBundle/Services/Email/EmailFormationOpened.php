<?php

namespace Eduprat\DomainBundle\Services\Email;

use Carbon\Carbon;
use Eduprat\DomainBundle\Entity\Participation;

/**
 *
 * Tous types de formation => Délai : Date d'ouvertue du questionnaire
 *
 * Envoyé seulement si le questionnaire pré-session n'est pas déjà complété
 *
 * Class EmailFormationOpened
 * @package Eduprat\DomainBundle\Services\Email
 */
class EmailFormationOpened extends BaseEmailParticipation
{
    const ALIAS = 'formation_opened';
    const TEMPLATE = "domain/email/formation_opened.html.twig";
    const NB_MAX_EMAIL_BEFORE_ALERT = 500;

    public function loadItems(): array
    {
        $startDate = Carbon::now()->add(15, 'days');
        return $this->getRepository()->findByFormOpeningDate($startDate, true);
    }

    /**
     * @param Participation $item
     */
    public function shouldSend($item): bool
    {
        return parent::shouldSend($item) && !$this->isCompleted($item, 1);
    }

    /**
     * @param Participation $item
     */
    public function getSubject($item): string
    {
        $coordinatorName = $item->getCoordinator() ? $item->getCoordinator()->getPerson()->getFullname() . " " : "";
        return sprintf("%s(EDUPRAT Formations) – Accès à votre formation", $coordinatorName);
    }

    static public function getAlias(): string
    {
        return self::ALIAS;
    }

    public function getTemplate($item): string
    {
        return self::TEMPLATE;
    }
}