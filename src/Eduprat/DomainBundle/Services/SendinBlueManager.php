<?php

namespace Eduprat\DomainBundle\Services;

use Brevo\Client\ApiException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Brevo\Client\Api\ContactsApi;
use GuzzleHttp\Client;
use Brevo\Client\Configuration;
use Brevo\Client\Model\CreateContact;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SendinBlueManager
{

    /** @var SMTPApi */
    private $apiInstance;

    /** @var EntityManagerInterface */
    private $em;

    /** @var AdressesService */
    private $adresseService;

    /** @var SpecialiteService */
    private $specialitesService;

    /** @var EmailSender */
    private $emailSender;

    /** @var string */
    private $participantListId;

    /** @var string */
    private $coordinateurListId;
    private ParameterBagInterface $parameterBag;

    /**
     * SendInBlueRequester constructor.
     * @param EntityManagerInterface $entityManager
     * @param $apiKey
     * @param $participantListId
     * @param $coordinateurListId
     */
    public function __construct($apiKey,
                                $participantListId,
                                $coordinateurListId,
                                AdressesService $adresseService,
                                EntityManagerInterface $entityManager,
                                SpecialitesService $specialitesService,
                                EmailSender $emailSender,
                                ParameterBagInterface $parameterBag
    ) {
        $this->apiInstance = new ContactsApi(
            new Client(),
            Configuration::getDefaultConfiguration()->setApiKey('api-key', $apiKey)
        );
        $this->participantListId = $participantListId;
        $this->coordinateurListId = $coordinateurListId;
        $this->adresseService = $adresseService;
        $this->specialitesService = $specialitesService;
        $this->em = $entityManager;
        $this->emailSender = $emailSender;
        $this->parameterBag = $parameterBag;
    }


    public function createContact($identifier, $attributes, $idList, $blackListedSms, $blackListedMail): void
    {
        if ($identifier && strpos($identifier, "@")) {
            $identifier = str_replace("\xc2\xa0", '', $identifier);
            $identifier = str_replace(",", ".", $identifier);
            $contact = new CreateContact();
            $contact->setListIds([(int) $idList]);
            $contact->setUpdateEnabled(true);
            $contact->setEmail($identifier);
            $contact->setAttributes($attributes);
            // $contact->setSmsBlacklisted(!$blackListedSms);
            $contact->setEmailBlacklisted(!$blackListedMail);
            $this->apiInstance->createContact($contact);
	    } 
    }

    /**
     * @param $identifier
     * @param $attributes
     * @throws ApiException
     */
    public function updateContact($identifier, $attributes, $idList, $blackListedSms = true, $blackListedMail =  true): void
    {
        $this->createContact($identifier, $attributes, $idList, $blackListedSms, $blackListedMail);
    }

    public function updateParticipant(Participant $participant): void
    {
        if (!$this->parameterBag->get('sib_call_api') || $participant->isProspect()) {
            return;
        }
        $arrayToSib = $participant->toArraySib();
        $arrayToSib["REGION"] = $this->adresseService->getRegionCodeSib($participant->getZipCode());
        $arrayToSib["SPECIALITE"] = $this->specialitesService->getSpecialiteSib($participant->getSpeciality());
        $arrayToSib["CATEGORIE"] = $this->specialitesService->getCategorieSib($participant->getCategory());
        if ($arrayToSib["MODE_EXERCICE"] === "Libéral") {
            $arrayToSib["MODE_EXERCICE"] = 1;
        } elseif ($arrayToSib["MODE_EXERCICE"] === "Salarié") {
            $arrayToSib["MODE_EXERCICE"] = 2;
        } else {
            $arrayToSib["MODE_EXERCICE"] = "";
        }
        try {
            // throw new Exception('Test erreur');
            $this->updateContact($participant->getEmail(), $arrayToSib , $this->participantListId, $arrayToSib["CONSENTEMENT_APPEL"], $arrayToSib["CONSENTEMENT_EMAIL"]);
        } catch (\Exception $e) {
            $this->emailSender->sendSibErrorImportParticipant(["email" => $participant->getEmail(), "infos" => $arrayToSib]);
        }
    }

    public function updateCoordinateur(Person $coordinateur): void
    {
        if (!$this->parameterBag->get('sib_call_api')) {
            return;
        }
        $coordinateurToArray = $this->regionStringToId($coordinateur);
        try {
            // throw new Exception('Test erreur');
            $this->updateContact($coordinateur->getEmail(), $coordinateurToArray, $this->coordinateurListId);
        } catch (\Exception $e) {
            $this->emailSender->sendSibErrorImportCoordinator(["email" => $coordinateur->getEmail(), "infos" => $coordinateurToArray]);
        }
    }

    public function regionStringToId(Person $coordinateur): array
    {

        $departments = $coordinateur->getDepartments();

        $coordinateurToArray = $coordinateur->toArraySib();

        for ($i = 0; $i < count($departments); $i++) {
            $deparCode = $this->adresseService->getDepartementCode($departments[$i]);
            $departmentField = "COORDINATEUR_DPT".strval($i + 1);

            $coordinateurToArray[$departmentField] = $deparCode;
        }

        return $coordinateurToArray;
    }

    public function desinscription($content): bool
    {
        $email = $content->email;
        $participantRepository = $this->em->getRepository(Participant::class);
        $participant = $participantRepository->findBy(['email' => $email]);

        if($participant) {
            $participant = $participant[0];
            $participant->setGdprAgreement(false);
            $this->em->persist($participant);
            $this->em->flush();
            return true;
        }
        return false;
    }

}