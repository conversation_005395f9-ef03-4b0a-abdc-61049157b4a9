<?php

namespace Eduprat\DomainBundle\Services;

use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Repository\ProgrammeRepository;
use Eduprat\DomainBundle\Repository\FormationRepository;
use Eduprat\DomainBundle\Repository\FormationAuditRepository;
use Eduprat\DomainBundle\Repository\FormationPresentielleRepository;
use Eduprat\DomainBundle\Repository\FormationSeddRepository;
use Eduprat\DomainBundle\Repository\FormationCongresRepository;
use Eduprat\DomainBundle\Repository\ParticipationRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationCongres;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;

class StatsCalculator
{
    /** @var EntityManager */
    private $entityManager;

    /** @var EntityRepository|ProgrammeRepository  */
    private $programmeRepo;

    /** @var EntityRepository|FormationRepository  */
    private $formationRepo;

    /** @var EntityRepository|FormationAuditRepository  */
    private $formationAuditRepo;

    /** @var EntityRepository|FormationPresentielleRepository  */
    private $formationPresentielleRepo;

    /** @var EntityRepository|FormationSeddRepository  */
    private $formationSeddRepo;

    /** @var EntityRepository|FormationCongresRepository  */
    private $formationCongresRepo;

    /** @var EntityRepository|ParticipationRepository  */
    private $participationRepo;

    /**
     * InvoiceManager constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->programmeRepo = $this->entityManager->getRepository(Programme::class);
        $this->participationRepo = $this->entityManager->getRepository(Participation::class);
        $this->formationRepo = $this->entityManager->getRepository(Formation::class);
        $this->formationAuditRepo = $this->entityManager->getRepository(FormationAudit::class);
        $this->formationPresentielleRepo = $this->entityManager->getRepository(FormationPresentielle::class);
        $this->formationSeddRepo = $this->entityManager->getRepository(FormationSedd::class);
        $this->formationCongresRepo = $this->entityManager->getRepository(FormationCongres::class);
    }

    /**
     * Fait la somme des éléments d'une collection
     * @param ArrayCollection $collection
     * @param                                              $getter
     * @return mixed
     */
    static function arrayCollectionSum(ArrayCollection $collection, $getter) {
        $sum = array_reduce(
            $collection->map($getter)->toArray(),
            function($a, $b) { $a += $b; return $a; },
            0
        );
        return $sum;
    }

    /**
     * Fusionne les tableaux d'une collection
     * @param ArrayCollection $collection
     * @return ArrayCollection
     */
    static function flattenCollection(ArrayCollection $collection) {
        return new ArrayCollection(array_reduce(
            $collection->toArray(),
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        ));
    }

    /**
     * Groupe les éléments d'une collection selon les résultats du getter
     * @param ArrayCollection $collection
     * @param                                              $getter
     * @return array
     */
    static function groupBy(ArrayCollection $collection, $getter) {
        $collections = array_reduce($collection->toArray(), function($a, $b) use ($getter){
            $value = $getter($b);
            if (!isset($a[$value])) { $a[$value] = array(); }
            $a[$value][] = $b;
            return $a;
        }, []);
        return array_map(function($a) { return new ArrayCollection($a);}, $collections);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param bool  $cms
     * @param array $classes
     * @return ArrayCollection
     */
    public function findFormations($year = null, array $options = [], $cms = null, array $classes = []) {
        $formations = $this->formationRepo->findComplex($year, $options, $cms, $classes);
        return new ArrayCollection($formations);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $expired
     * @return ArrayCollection
     */
    public function findProgrammes($year = null, array $options = [], $expired = null) {
        $programmes = $this->programmeRepo->findComplex($year, $options, $expired);
        return new ArrayCollection($programmes);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $medecins
     * @param array $classes
     * @return ArrayCollection|Collection
     */
    public function findParticipations($year = null, $medecins = null, array $options = [],  array $classes = []) {
        $participations = $this->participationRepo->findComplex($year, $medecins, $options, $classes);
        return  new ArrayCollection($participations);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param bool  $cms
     * @param array $classes
     * @return mixed
     */
    public function getTotalCa($year = null, array $options = [], $cms = null, array $classes = []) {
        $formations = $this->findFormations($year, $options, $cms, $classes);
        return $this->arrayCollectionSum($formations, function(Formation $f) {
            return $f->getCaTotal();
        });
    }

    /**
     * @param null  $year
     * @param array $options
     * @param bool  $cms
     * @param array $classes
     * @return int
     */
    public function getFormationCount($year = null, array $options = [], $cms = null, array $classes = []) {
        $formations = $this->findFormations($year, $options, $cms, $classes);
        return $formations->count();
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $cms
     * @param array $classes
     * @return array
     */
    public function getFormationCountPerSubject($year = null, array $options = [], $cms = null, array $classes = []) {
        $formations = $this->getFormationsBySubject($year, $options, $cms, $classes);
        array_walk($formations, function(ArrayCollection &$value, $key) {
            $value = $value->count();
        });
        return $formations;
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $medecins
     * @param array $classes
     * @return int
     */
    public function getParticipantCount($year = null, array $options = [], $medecins = null, array $classes = []) {
        $participations = $this->findParticipations($year, $medecins, $options, $classes);
        return $participations->count();
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $medecins
     * @param array $classes
     * @return array
     */
    public function getParticipantCountPerSpeciality($year = null, array $options = [], $medecins = null, array $classes = []) {
        $participations = $this->getParticipantsBySpeciality($year, $medecins, $options, $classes);
        array_walk($participations, function(ArrayCollection &$value, $key) {
            $value = $value->count();
        });
        return $participations;
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $medecins
     * @param array $classes
     * @return float
     */
    public function getAvgParticipantCount($year = null, array $options = [], $medecins = null, array $classes = []) {
        $participations = $this->findParticipations($year, $medecins, $options, $classes);
        return round($participations->count() / max(1, $this->countFormationFromParticipations($participations)), 2);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $medecins
     * @param array $classes
     * @return array
     */
    public function getAvgParticipantCountPerCategory($year = null, array $options = [], $medecins = null, array $classes = []) {
        $participations = $this->getParticipantgetCategory($year, $medecins, $options, $classes);
        array_walk($participations, function(ArrayCollection &$value, $key) {
            $value = (round($value->count() / max(1, $this->countFormationFromParticipations($value)), 2));
        });
        return $participations;
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $expired
     * @return mixed
     */
    public function getTotalHonoraryCr($year = null, array $options = [], $expired = null) {
        // $formations = $this->findProgrammes($year, $options, $expired);
        $formations = $this->findFormations($year, $options, $expired);
        return round($this->arrayCollectionSum($formations, function(Formation $f) { return $f->getCoordinatorHonorary();}), 2);
    }

    /**
     * @param null  $year
     * @param array $options
     * @param null  $cms
     * @param array $classes
     * @return array
     */
    public function getFormationsBySubject($year = null, array $options = [], $cms = null, array $classes = []) {
        $formations = $this->findFormations($year, $options, $cms, $classes);
        return $this->groupBy($formations, function(Formation $f) {
            return $f->getProgramme()->getTitle();
        });
    }

    /**
     * @param null  $year
     * @param null  $medecins
     * @param array $options
     * @param array $classes
     * @return array
     */
    public function getParticipantsBySpeciality($year = null, $medecins = null, array $options = [], array $classes = []) {
        $participations = $this->findParticipations($year, $medecins, $options, $classes);
        return $this->groupBy($participations, function(Participation $p) {
            return $p->getParticipant()->getSpeciality();
        });
    }

    /**
     * @param null  $year
     * @param null  $medecins
     * @param array $options
     * @param array $classes
     * @return array
     */
    public function getParticipantgetCategory($year = null, $medecins = null, array $options = [], array $classes = []) {
        $participations = $this->findParticipations($year, $medecins, $options, $classes);
        return $this->groupBy($participations, function(Participation $p) {
            return $p->getParticipant()->getCategory();
        });
    }

    /**
     * @param ArrayCollection $participations
     * @return int
     */
    public function countFormationFromParticipations(ArrayCollection $participations) {
        return sizeof($this->groupBy($participations, function(Participation $p) {
            return $p->getFormation()->getId();
        }));
    }

}
