<?php

namespace Eduprat\DomainBundle\Services;

use Alienor\ElasticBundle\Services\ElasticSearch\Transporter;
use Elastica\Request;

class AdressesService {

    const REGIONS = array(
        "82" => "Auvergne-Rhône-Alpes",
        "26" => "Bourgogne-Franche-Comté",
        "53" => "Bretagne",
        "24" => "Centre-Val de Loire",
        "94" => "Corse",
        "21" => "Grand Est",
        "22" => "Hauts-de-France",
        "11" => "Île-de-France",
        "25" => "Normandie",
        "72" => "Nouvelle-Aquitaine",
        "73" => "Occitanie",
        "52" => "Pays de la Loire",
        "93" => "Provence-Alpes-Côte d'Azur",
        "1"  => "Guadeloupe",
        "2"  => "Martinique",
        "3"  => "Guyane",
        "4"  => "La Réunion",
        "6"  => "Mayotte",
    );

    const DEPARTEMENTS = array(
        "01"  => array("region" => "82", "code" => "01", "label" => "Ain"),
        "02"  => array("region" => "22", "code" => "02", "label" => "Aisne"),
        "03"  => array("region" => "82", "code" => "03", "label" => "Allier"),
        "04"  => array("region" => "93", "code" => "04", "label" => "Alpes-de-Haute-Provence"),
        "05"  => array("region" => "93", "code" => "05", "label" => "Hautes-Alpes"),
        "06"  => array("region" => "93", "code" => "06", "label" => "Alpes-Maritimes"),
        "07"  => array("region" => "82", "code" => "07", "label" => "Ardèche"),
        "08"  => array("region" => "21", "code" => "08", "label" => "Ardennes"),
        "09"  => array("region" => "73", "code" => "09", "label" => "Ariège"),
        "10"  => array("region" => "21", "code" => "10", "label" => "Aube"),
        "11"  => array("region" => "73", "code" => "11", "label" => "Aude"),
        "12"  => array("region" => "73", "code" => "12", "label" => "Aveyron"),
        "13"  => array("region" => "93", "code" => "13", "label" => "Bouches-du-Rhône"),
        "14"  => array("region" => "25", "code" => "14", "label" => "Calvados"),
        "15"  => array("region" => "82", "code" => "15", "label" => "Cantal"),
        "16"  => array("region" => "72", "code" => "16", "label" => "Charente"),
        "17"  => array("region" => "72", "code" => "17", "label" => "Charente-Maritime"),
        "18"  => array("region" => "24", "code" => "18", "label" => "Cher"),
        "19"  => array("region" => "72", "code" => "19", "label" => "Corrèze"),
        // Pour la Corse on accepte 2A/2B ou 200/201, Ajaccio a gardé son code postal en 20000
        "2A"  => array("region" => "94", "code" => "2A", "label" => "Corse-du-Sud"),
        "2B"  => array("region" => "94", "code" => "2B", "label" => "Haute-Corse"),
        "200" => array("region" => "94", "code" => "2A", "label" => "Corse-du-Sud"),
        "201" => array("region" => "94", "code" => "2B", "label" => "Corse-du-Sud"),
        "202" => array("region" => "94", "code" => "202", "label" => "Haute-Corse"),
        "206" => array("region" => "94", "code" => "2B", "label" => "Haute-Corse"),
        "21"  => array("region" => "26", "code" => "21", "label" => "Côte-d'Or"),
        "22"  => array("region" => "53", "code" => "22", "label" => "Côtes-d'Armor"),
        "23"  => array("region" => "72", "code" => "23", "label" => "Creuse"),
        "24"  => array("region" => "72", "code" => "24", "label" => "Dordogne"),
        "25"  => array("region" => "26", "code" => "25", "label" => "Doubs"),
        "26"  => array("region" => "82", "code" => "26", "label" => "Drôme"),
        "27"  => array("region" => "25", "code" => "27", "label" => "Eure"),
        "28"  => array("region" => "24", "code" => "28", "label" => "Eure-et-Loir"),
        "29"  => array("region" => "53", "code" => "29", "label" => "Finistère"),
        "30"  => array("region" => "73", "code" => "30", "label" => "Gard"),
        "31"  => array("region" => "73", "code" => "31", "label" => "Haute-Garonne"),
        "32"  => array("region" => "73", "code" => "32", "label" => "Gers"),
        "33"  => array("region" => "72", "code" => "33", "label" => "Gironde"),
        "34"  => array("region" => "73", "code" => "34", "label" => "Hérault"),
        "35"  => array("region" => "53", "code" => "35", "label" => "Ile-et-Vilaine"),
        "36"  => array("region" => "24", "code" => "36", "label" => "Indre"),
        "37"  => array("region" => "24", "code" => "37", "label" => "Indre-et-Loire"),
        "38"  => array("region" => "82", "code" => "38", "label" => "Isère"),
        "39"  => array("region" => "26", "code" => "39", "label" => "Jura"),
        "40"  => array("region" => "72", "code" => "40", "label" => "Landes"),
        "41"  => array("region" => "24", "code" => "41", "label" => "Loir-et-Cher"),
        "42"  => array("region" => "82", "code" => "42", "label" => "Loire"),
        "43"  => array("region" => "82", "code" => "43", "label" => "Haute-Loire"),
        "44"  => array("region" => "52", "code" => "44", "label" => "Loire-Atlantique"),
        "45"  => array("region" => "24", "code" => "45", "label" => "Loiret"),
        "46"  => array("region" => "73", "code" => "46", "label" => "Lot"),
        "47"  => array("region" => "72", "code" => "47", "label" => "Lot-et-Garonne"),
        "48"  => array("region" => "73", "code" => "48", "label" => "Lozère"),
        "49"  => array("region" => "52", "code" => "49", "label" => "Maine-et-Loire"),
        "50"  => array("region" => "25", "code" => "50", "label" => "Manche"),
        "51"  => array("region" => "21", "code" => "51", "label" => "Marne"),
        "52"  => array("region" => "21", "code" => "52", "label" => "Haute-Marne"),
        "53"  => array("region" => "52", "code" => "53", "label" => "Mayenne"),
        "54"  => array("region" => "21", "code" => "54", "label" => "Meurthe-et-Moselle"),
        "55"  => array("region" => "21", "code" => "55", "label" => "Meuse"),
        "56"  => array("region" => "53", "code" => "56", "label" => "Morbihan"),
        "57"  => array("region" => "21", "code" => "57", "label" => "Moselle"),
        "58"  => array("region" => "26", "code" => "58", "label" => "Nièvre"),
        "59"  => array("region" => "22", "code" => "59", "label" => "Nord"),
        "60"  => array("region" => "22", "code" => "60", "label" => "Oise"),
        "61"  => array("region" => "25", "code" => "61", "label" => "Orne"),
        "62"  => array("region" => "22", "code" => "62", "label" => "Pas-de-Calais"),
        "63"  => array("region" => "82", "code" => "63", "label" => "Puy-de-Dôme"),
        "64"  => array("region" => "72", "code" => "64", "label" => "Pyrénées-Atlantiques"),
        "65"  => array("region" => "73", "code" => "65", "label" => "Hautes-Pyrénées"),
        "66"  => array("region" => "73", "code" => "66", "label" => "Pyrénées-Orientales"),
        "67"  => array("region" => "21", "code" => "67", "label" => "Bas-Rhin"),
        "68"  => array("region" => "21", "code" => "68", "label" => "Haut-Rhin"),
        "69"  => array("region" => "82", "code" => "69", "label" => "Rhône"),
        "70"  => array("region" => "26", "code" => "70", "label" => "Haute-Saône"),
        "71"  => array("region" => "26", "code" => "71", "label" => "Saône-et-Loire"),
        "72"  => array("region" => "52", "code" => "72", "label" => "Sarthe"),
        "73"  => array("region" => "82", "code" => "73", "label" => "Savoie"),
        "74"  => array("region" => "82", "code" => "74", "label" => "Haute-Savoie"),
        "75"  => array("region" => "11", "code" => "75", "label" => "Paris"),
        "76"  => array("region" => "25", "code" => "76", "label" => "Seine-Maritime"),
        "77"  => array("region" => "11", "code" => "77", "label" => "Seine-et-Marne"),
        "78"  => array("region" => "11", "code" => "78", "label" => "Yvelines"),
        "79"  => array("region" => "72", "code" => "79", "label" => "Deux-Sèvres"),
        "80"  => array("region" => "22", "code" => "80", "label" => "Somme"),
        "81"  => array("region" => "73", "code" => "81", "label" => "Tarn"),
        "82"  => array("region" => "73", "code" => "82", "label" => "Tarn-et-Garonne"),
        "83"  => array("region" => "93", "code" => "83", "label" => "Var"),
        "84"  => array("region" => "93", "code" => "84", "label" => "Vaucluse"),
        "85"  => array("region" => "52", "code" => "85", "label" => "Vendée"),
        "86"  => array("region" => "72", "code" => "86", "label" => "Vienne"),
        "87"  => array("region" => "72", "code" => "87", "label" => "Haute-Vienne"),
        "88"  => array("region" => "21", "code" => "88", "label" => "Vosges"),
        "89"  => array("region" => "26", "code" => "89", "label" => "Yonne"),
        "90"  => array("region" => "26", "code" => "90", "label" => "Territoire de Belfort"),
        "91"  => array("region" => "11", "code" => "91", "label" => "Essonne"),
        "92"  => array("region" => "11", "code" => "92", "label" => "Hauts-de-Seine"),
        "93"  => array("region" => "11", "code" => "93", "label" => "Seine-Saint-Denis"),
        "94"  => array("region" => "11", "code" => "94", "label" => "Val-de-Marne"),
        "95"  => array("region" => "11", "code" => "95", "label" => "Val-d'Oise"),
        "971" => array("region" => "1", "code" => "971", "label" => "Guadeloupe"),
        "972" => array("region" => "2", "code" => "972", "label" => "Martinique"),
        "973" => array("region" => "3", "code" => "973", "label" => "Guyane"),
        "974" => array("region" => "4", "code" => "974", "label" => "La Réunion"),
        "975" => array("region" => null, "code" => "975", "label" => "Saint-Pierre-et-Miquelon"),
        "976" => array("region" => "6", "code" => "976", "label" => "Mayotte"),
        "977" => array("region" => "1", "code" => "977", "label" => "Saint-Barthélemy"),
        "978" => array("region" => "1", "code" => "978", "label" => "Saint-Martin"),
        "984" => array("region" => null, "code" => "984", "label" => "Terres Australes et Antarctiques"),
        "986" => array("region" => null, "code" => "986", "label" => "Wallis et Futuna"),
        "987" => array("region" => null, "code" => "987", "label" => "Polynésie-Française"),
        "988" => array("region" => null, "code" => "988", "label" => "Nouvelle-Calédonie"),
    );

    const REGIONS_SIB = array(
        "Auvergne-Rhône-Alpes" => "1",
        "Bourgogne-Franche-Comté" => "2",
        "Bretagne" => "3",
        "Centre-Val de Loire" => "4",
        "Corse" => "5",
        "Grand Est" => "6",
        "Hauts-de-France" => "7",
        "Île-de-France" => "8",
        "Normandie" => "9",
        "Nouvelle-Aquitaine" => "10",
        "Occitanie" => "11",
        "Pays de la Loire" => "12",
        "Provence-Alpes-Côte d'Azur" => "13",
        "Guadeloupe" => "14",
        "Martinique" => "15",
        "Guyane" => "16",
        "La Réunion" => "17",
        "Mayotte" => "18",
    );

    protected $urlWSAdresse;
    protected $client;
    protected $cle;

    /**
     * @var Transporter
     */
    private $transporter;

    /** @var array */
    private $ugas;

    private bool $geocodageActive;

    public function __construct($urlWSAdresse, Transporter $transporter, $ugas, bool $geocodageActive) {
        $this->urlWSAdresse = $urlWSAdresse;
        $this->transporter = $transporter;
        $this->ugas = $ugas;
        $this->geocodageActive = $geocodageActive;
    }

    /**
     * Geocode Adresse
     * @return array
     */
    public function getGeocodeAdresse($adresse) {
        // désactiver en mode test
        if (!$this->geocodageActive) {
            return ["adresse" => []];
        }
        $url = sprintf("%s?%s", $this->urlWSAdresse, http_build_query(
            array(
                "q" => $adresse,
                "limit" => 1,
                "autocomplete" => 0
            )
        ));
        $string = @file_get_contents($url);

        $result = array(
            "adresse" => array()
        );
        if ($string === false) {
            return $result;
        }

        $apiResult = json_decode($string, true);

        if (count($apiResult["features"]) > 0 && isset($apiResult["features"][0]["geometry"]["coordinates"])) {
            $coordinates = $apiResult["features"][0]["geometry"]["coordinates"];
            $result["adresse"][] =  array(
                "coordonnees" => sprintf("%s,%s", $coordinates[1], $coordinates[0])
            );
        }

        return $result;
    }

    /**
     * @param $zipCode
     * @return mixed|null
     */
    static public function getRegionName($zipCode)
    {
        $dptCode = self::extractDptCode($zipCode);
        if ($dptCode && array_key_exists($dptCode, self::DEPARTEMENTS) && !is_null(self::DEPARTEMENTS[$dptCode]["region"])) {
            return self::REGIONS[self::DEPARTEMENTS[$dptCode]["region"]];
        }
        return null;
    }

    /**
     * @param $zipCode
     * @return mixed|null
     */
    static public function getRegionCode($zipCode)
    {
        $dptCode = self::extractDptCode($zipCode);
        if ($dptCode && array_key_exists($dptCode, self::DEPARTEMENTS) && !is_null(self::DEPARTEMENTS[$dptCode]["region"])) {
            return self::DEPARTEMENTS[$dptCode]["region"];
        }
        return null;
    }
    
    /**
     * @param $zipCode
     * @return mixed|null
     */
    static public function getRegionCodeSib($zipCode)
    {
        $regionName = self::getRegionName($zipCode);   
        return isset(self::REGIONS_SIB[$regionName]) ? self::REGIONS_SIB[$regionName] : null;
    }

    /**
     * @param $zipCode
     * @return mixed|null
     */
    static public function getDepartementName($zipCode)
    {
        $dptCode = self::extractDptCode($zipCode);
        if ($dptCode && array_key_exists($dptCode, self::DEPARTEMENTS)) {
            return self::DEPARTEMENTS[$dptCode]["label"];
        }
        return null;
    }

    /**
     * @return array
     */
    static public function getDepartementCode($label)
    {
        foreach (self::DEPARTEMENTS as $code => $departement) {
            if ($label === $departement["label"]) {
                return $code;
            }
        }
        return null;
    }

    /**
     * @return array
     */
    static public function getRegionsLabel()
    {
        return array_values(self::REGIONS);
    }

    /**
     * @return array
     */
    static public function getDepartementsLabel()
    {
        return array_column(array_values(self::DEPARTEMENTS), "label");
    }

    /**
     * @return array
     */
    static public function getDepartementsCodes()
    {
        return array_column(array_values(self::DEPARTEMENTS), "code");
    }

    /**
     * @return array
     */
    static public function getDepartementsCodeLabel()
    {
        return array_map(function($label) {
            return sprintf("%s - %s", self::getDepartementCode($label), $label);
        }, array_column(array_values(self::DEPARTEMENTS), "label"));
    }

    /**
     * @param $zipCode
     * @return bool|string
     */
    public static function extractDptCode($zipCode)
    {
        $dptCode = substr($zipCode, 0, 2);
        if (in_array($dptCode, array("20", "97", "98"))) {
            $dptCode = substr($zipCode, 0, 3);
        }
        return $dptCode;
    }

    /**
     * @param $localization
     * @param string $distance
     * @return array
     */
    public function getNearbyFormationIds($localization, $distance = "50km") {
        if ($distance != null && strpos($distance, "km") == false)
        {
            $distance = $distance . "km";
        }
        $index = $this->transporter->getIndexType("formation");

        $query = [
            "query" => [
                "bool" => [
                    "must" => [
                        "match_all" => new \stdClass
                    ],
                    "filter" => [
                        "geo_distance" => [
                            "distance" => $distance,
                            "localization" => $localization
                        ]
                    ]
                ]
            ],
            "_source" => false,
            "size" => 10000
        ];

        try {
            $result = $index->request('_search', Request::POST, json_encode($query));
        } catch (\Exception $exception) {
            return array();
        }
        if ($result->getData() && isset ($result->getData()["hits"]["hits"])) {
            return array_map(function($hit) {
                return $hit["_id"];
            }, $result->getData()["hits"]["hits"]);
        }
        return array();
    }

    static public function loadUgas($projectDir) {
        $ugaFile = sprintf("%s/var/UGA.json", $projectDir);
        $ugas = array();

        if ($ugaFile && file_exists($ugaFile)) {
            $data = json_decode(file_get_contents($ugaFile), true);

            foreach ($data["features"] as $index => $uga) {
                $ugas[$uga["properties"]["Id"]] = array(
                    "id"    => $uga["properties"]["Id"],
                    "name"  => $uga["properties"]["Name"],
                    "zipCodes"  => $uga["zipCodes"] ?? array(),
                    "label" => sprintf("%s - %s", $uga["properties"]["Id"], $uga["properties"]["Name"]),
                );
            }

            ksort($ugas);
        }

        return $ugas;
    }

    /**
     * Effectue une requête elasticsearch pour trouver l'UGA associé à une adresse
     * @param array $coordinates
     * @return array
     */
    public function getAddressUGA($address, $zipCode, $city, array $coordinates = null) {
        if($uga = $this->findUgaByZipCode($zipCode)) {
            return $uga;
        }
        if ($coordinates !== null) {
            return $this->findUga($coordinates);
        }
        $fullAddress = trim($address . " " . ltrim($zipCode, '0') . " " . $city);
        $result = $this->getGeocodeAdresse($fullAddress);
        if (count($result["adresse"])) {
            $coordinates = explode(",", $result["adresse"][0]["coordonnees"]);
            return $this->findUga(array($coordinates[1], $coordinates[0]));
        }
        return null;
    }

    /**
     * @param $zipCode
     * @return mixed
     */
    public function findUgaByZipCode($zipCode) {
        foreach ($this->ugas as $uga) {
            foreach ($uga["zipCodes"] as $zip) {
                if (strpos($zipCode, $zip) === 0) {
                    return $uga;
                }
            }
        }
        return null;
    }

    /**
     * Effectue une requête elasticsearch pour trouver l'UGA associé à des coordonnées géographiques
     * @param array $coordinates
     * @return array
     */
    public function findUga(array $coordinates) {
        $index = $this->transporter->getIndexType("uga");

        $coordinates = array_map(function($coordinate) {
            return (float) $coordinate;
        }, $coordinates);

        $query = [
            "size" => 1,
            "query" => [
                "bool" => [
                    "must" => [
                        "match_all" => new \stdClass
                    ],
                    "filter" => [
                        "geo_shape" => [
                            "shape" => [
                                "relation" => "contains",
                                "shape" => [
                                    "coordinates" => $coordinates,
                                    "type" => "point"
                                ]
                            ]
                        ]
                    ]
                ]
            ],
        ];

        try {
            $result = $index->request('_search', Request::POST, json_encode($query));
            if ($result->getData() && isset ($result->getData()["hits"]["hits"])) {
                return $result->getData()["hits"]["hits"][0]["_source"];
            }
        } catch (\Exception $exception) {
            return null;
        }
        return null;
    }


}