<?php

namespace Eduprat\DomainBundle\Services\Populator;

use Alienor\ElasticBundle\Services\PopulatorInterface;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Entity\PersonSearch;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Symfony\Contracts\Translation\TranslatorInterface;

class CoordinatorPopulator implements PopulatorInterface
{

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * EvaluationPopulator constructor.
     * @param EntityManagerInterface $entityManager
     * @param ParticipationAccessManager $accessManager
     * @param TranslatorInterface $translator
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Dans le cas ou cette méthode retourne true, fetchDatas sera executée pour récupérer les données
     * @param $type
     * @return mixed
     */
    public function hasCustomFetch($type)
    {
        return $type === "coordinator";
    }

    /**
     * @return mixed
     */
    public function hasBulkFetch()
    {
        return true;
    }

    /**
     * Ne sera executé que dans le cas ou hasCustomFetch($type) renvoie true
     * Doit retourner un tableau d'entité correspondant au type passé en paramètre.
     * Dans le cas ou hasCustomFetch retourne false, un simple fetchAll sera exécuté
     * @param string $type
     * @return array
     */
    public function fetchDatas($type)
    {
        return array();
    }

    public function fetchDatasBulk(string $type, int $bulkSize, callable $callback = null): void
    {
        $page = 1;
        $ended = false;
        $count = 0;

        while (!$ended) {
            $persons = $this->entityManager->getRepository(Person::class)->findSearchResults($this->getSearch(), $page, $bulkSize);

            if (count($persons) > 0) {
                if ($callback) {
                    $callback($persons, $count += count($persons));
                }
            } else {
                $ended = true;
            }

            $this->entityManager->clear();
            $page++;
        }
    }

    public function countResults($type): int
    {
        return $this->entityManager->getRepository(Person::class)->countSearchResults($this->getSearch());
    }

    /**
     * Permet de synhroniser les données qui ont était ajoutées en base depuis le début de l'éxécution de la commande pour éviter les pertes
     * A implémenter si nécessaire
     * @param array $types
     * @param \DateTimeInterface $now
     * @return mixed
     */
    public function synchronizeUpdatedDatas(array $types, \DateTimeInterface $now)
    {
    }

    /**
     * @return PersonSearch
     */
    protected function getSearch(): PersonSearch
    {
        $search = new PersonSearch();
        $search->setRole("ROLE_COORDINATOR");
        return $search;
    }

    public function hasCustomProcessAvance(): bool
    {
        return false;
    }
}