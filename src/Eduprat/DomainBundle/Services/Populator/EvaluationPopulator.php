<?php

namespace Eduprat\DomainBundle\Services\Populator;

use Alienor\ElasticBundle\Services\PopulatorInterface;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\ApiBundle\Services\Serializer;
use Eduprat\AuditBundle\Services\EvaluationGlobalManager;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer;
use Eduprat\DomainBundle\Entity\EvaluationGlobalQuestion;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Contracts\Translation\TranslatorInterface;

class EvaluationPopulator implements PopulatorInterface
{

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var ParticipationAccessManager
     */
    private $accessManager;

    /**
     * @var TranslatorInterface
     */
    private $translator;
    /**
     * @var \DateTime
     */
    private $evaluationMigrationDate;

    /**
     * @var Serializer
     */
    private $serializer;

    /**
     * EvaluationPopulator constructor.
     * @param EntityManagerInterface $entityManager
     * @param ParticipationAccessManager $accessManager
     * @param TranslatorInterface $translator
     * @param Serializer $serializer
     * @param \DateTime $evaluationMigrationDate
     * @throws \Exception
     */
    public function __construct(EntityManagerInterface $entityManager, ParticipationAccessManager $accessManager, TranslatorInterface $translator, Serializer $serializer, $evaluationMigrationDate)
    {
        $this->entityManager = $entityManager;
        $this->accessManager = $accessManager;
        $this->translator = $translator;
        $this->evaluationMigrationDate = new \DateTime($evaluationMigrationDate);
        $this->serializer = $serializer;
    }

    /**
     * Dans le cas ou cette méthode retourne true, fetchDatas sera executée pour récupérer les données
     * @param $type
     * @return mixed
     */
    public function hasCustomFetch($type)
    {
        return $type === "evaluation";
    }

    /**
     * @return mixed
     */
    public function hasBulkFetch()
    {
        return true;
    }

    /**
     * Ne sera executé que dans le cas ou hasCustomFetch($type) renvoie true
     * Doit retourner un tableau d'entité correspondant au type passé en paramètre.
     * Dans le cas ou hasCustomFetch retourne false, un simple fetchAll sera exécuté
     * @param string $type
     * @return array
     */
    public function fetchDatas($type)
    {
        return array();
    }

    public function fetchDatasBulk(string $type, int $bulkSize, callable $callback = null): void
    {
        $page = 1;
        $ended = false;
        $count = 0;

        while (!$ended) {
            $formations = $this->entityManager->getRepository(Formation::class)->findClosed($this->evaluationMigrationDate, $page, $bulkSize);

            $evaluations = array();
            foreach ($formations as $formation) {
                try {
                    $evaluations = array_merge($evaluations, $this->extractEvaluations($formation));
                } catch (\Exception $exception) {
                    var_dump($exception->getFile(), $exception->getLine(), $exception->getMessage());
                }
            }

            if (count($formations) > 0) {
                if ($callback) {
                    $callback($evaluations, $count += count($formations));
                }
            } else {
                $ended = true;
            }

            unset($formations);
            unset($evaluations);
            $this->entityManager->clear();

            $page++;
        }
    }

    public function countResults($type): int
    {
        return $this->entityManager->getRepository(Formation::class)->countClosed($this->evaluationMigrationDate);
    }

    public function extractEvaluations(Formation $formation) {

        $answers = $formation->getEvaluationAnswers();

        $formationBase = $this->serializer->normalizeGroup($formation, 'elastic_evaluation');

        $items = array();

        $questionsParticipant = EvaluationGlobalManager::getAllQuestionsForRole($formation, Person::ROLE_PARTICIPANT);
        $questionsCoordinator = EvaluationGlobalManager::getAllQuestionsForRole($formation, Person::ROLE_COORDINATOR);
        $questionsFormer = EvaluationGlobalManager::getAllQuestionsForRole($formation, Person::ROLE_FORMER);

        $answersParticipants = array();
        $answersCoordinators = array();
        $answersFormers = array();

        $vars = array(
            "questionsParticipant" => "answersParticipants",
            "questionsCoordinator" => "answersCoordinators",
            "questionsFormer" => "answersFormers",
        );

        foreach ($vars as $q => $a) {
            $$a = array_reduce($$q->toArray(), function($acc, EvaluationGlobalQuestion $question) {
                $acc["answers_questionId"][] = $this->translator->trans($question->getIndex());
                $acc["answers_targets"] = array_merge($acc["answers_targets"], EvaluationGlobalManager::getQuestionTargets($question->getIndex()));
                $acc["answers_question"][] = $this->translator->trans($question->getLabel());
                $acc["answers_group"][] = $this->translator->trans($question->getGroup());
                return $acc;
            }, array(
                "answers_questionId" => array(),
                "answers_targets" => array(),
                "answers_question" => array(),
                "answers_group" => array(),
            ));
            $b = $$a;
            $b["answers_questionId"] = array_values(array_filter(array_unique($b["answers_questionId"])));
            $b["answers_targets"] = array_values(array_filter(array_unique($b["answers_targets"])));
            $b["answers_question"] = array_values(array_filter(array_unique($b["answers_question"])));
            $b["answers_group"] = array_values(array_filter(array_unique($b["answers_group"])));
            $$a = $b;
        }

        $formerReferentIds = array();

        /** @var Formateur $formateur */
        foreach ($formation->getFormateurs() as $formateur) {
            $referent = $this->entityManager->getRepository(EvaluationGlobalAnswer::class)->isFormerReferent($formateur);
            if ($referent) {
                $formerReferentIds[] = $formateur->getPerson()->getId();
            }
        }

        $coordinatorNames = array();
        $supervisorNames = array();


        /** @var Coordinator $coordinator */
        foreach ($formation->getCoordinators() as $coordinator) {

            $coordinatorNames[] = $coordinator->getPerson()->getFullname();
            if ($coordinator->getPerson()->getSupervisor()) {
                $supervisorNames[] = $coordinator->getPerson()->getSupervisor()->getFullname();
            }

            if (!isset($items[$coordinator->getPerson()->getId()])) {
                $items[$coordinator->getPerson()->getId()] = array_merge($formationBase, array(
                    "formationId" => $formation->getId(),
                    "answered" => 0,
                    "completed" => 0,
                    "userId" => $coordinator->getPerson()->getId(),
                    "origin" => "coordinator",
                    "originLabel" => "Coordinateur",
                    "formerReferentIds" => $formerReferentIds,
                    "answers" => array(),
                    "answers_questionId" => $answersCoordinators["answers_questionId"],
                    "answers_targets" => $answersCoordinators["answers_targets"],
                    "answers_question" => $answersCoordinators["answers_question"],
                    "answers_group" => $answersCoordinators["answers_group"],
                ));
            }
        }

        /** @var Participation $participation */
        foreach ($formation->getParticipations() as $participation) {
            $participant = $participation->getParticipant();
            if ($participant->getUser() && !isset($items[$participant->getUser()->getId()])) {
                $associatedCoordinator = $participation->getAssociatedCoordinator();

                $items[$participant->getUser()->getId()] = array_merge($formationBase, array(
                    "coordinatorName" => $associatedCoordinator ? $associatedCoordinator->getPerson()->getFullname() : null,
                    "supervisorName" => $associatedCoordinator && $associatedCoordinator->getPerson()->getSupervisor() ? $associatedCoordinator->getPerson()->getSupervisor()->getFullname() : null,
                    "formationId" => $formation->getId(),
                    "answered" => 0,
                    "completed" => 0,
                    "programmeAnsweredId" => null,
                    "userId" => $participant->getUser()->getId(),
                    "category" => $participant->getCategory(),
                    "speciality" => $participant->getSpeciality(),
                    "origin" => "participant",
                    "originLabel" => "Participant",
                    "formerReferentIds" => $formerReferentIds,
                    "answers" => array(),
                    "answers_questionId" => $answersParticipants["answers_questionId"],
                    "answers_targets" => $answersParticipants["answers_targets"],
                    "answers_question" => $answersParticipants["answers_question"],
                    "answers_group" => $answersParticipants["answers_group"],
                ));
            }
        }

        /** @var Formateur $formateur */
        foreach ($formation->getFormateurs() as $formateur) {
            if (!isset($items[$formateur->getPerson()->getId()])) {
                $items[$formateur->getPerson()->getId()] = array_merge($formationBase, array(
                    "coordinatorName" => array_values(array_unique($coordinatorNames)),
                    "supervisorName" => array_values(array_unique($supervisorNames)),
                    "formationId" => $formation->getId(),
                    "answered" => 0,
                    "completed" => 0,
                    "userId" => $formateur->getPerson()->getId(),
                    "origin" => "former",
                    "originLabel" => "Formateur",
                    "formerReferentIds" => $formerReferentIds,
                    "answers" => array(),
                    "answers_questionId" => $answersFormers["answers_questionId"],
                    "answers_targets" => $answersFormers["answers_targets"],
                    "answers_question" => $answersFormers["answers_question"],
                    "answers_group" => $answersFormers["answers_group"],
                ));
            }
        }

        $arr = array();
        /**
         * @var  $key
         * @var EvaluationGlobalAnswer $item
         */
        foreach($answers as $key => $item) {
            $arr[$item->getPerson()->getId()][$key] = $item;
        }
        ksort($arr, SORT_NUMERIC);

        /** @var EvaluationGlobalAnswer[] $item */
        foreach ($arr as $item) {
            /** @var EvaluationGlobalAnswer $answer */
            $answer = array_values($item)[0];
            $userId = $answer->getPerson()->getId();

            if (!isset($items[$userId])) {
                continue;
            }

            if ($answer->getPerson()->isFormer() && $items[$userId]["origin"] === "participant") {
                /** @var EvaluationGlobalAnswer $a */
                foreach ($item as $a) {
                    if ($a->getQuestion() === EvaluationGlobalManager::ACCUEIL_QUESTIONS_FORMER[0]) {
                        $items[$userId]["origin"] = "former";
                        $items[$userId]["originLabel"] = "Formateur";
                        break;
                    }
                }
            }

            $items[$userId] = array_merge($items[$userId], array(
                "answered" => 1,
                //"completed" => $this->accessManager->isEvaluationCompleted($answer->getFormation(), $answer->getPerson()) ? 1 : 0,
                "completed" => 0,
                "userId" => $userId,
                "programmeAnsweredId" => $answer->getFormation()->getProgramme()->getId(),
                "formationAnsweredId" => $answer->getFormation()->getId(),
                "answers" => array_values(array_map(function(EvaluationGlobalAnswer $answer) use ($items, $userId, $formation, $formerReferentIds) {
                    $identifier = explode("_", $answer->getQuestion());

                    $answerData = array(
                        "id" => $answer->getId(),
                        "questionId" => $answer->getQuestion(),
                        "question" => $this->translator->trans("evaluation.global." . $identifier[0] . ".questions." . $identifier[1] . ".label"),
                        "answer" => (int) $answer->getAnswer(),
                        "group" => $this->translator->trans("evaluation.global." . $identifier[0] . ".title"),
                        "origin" => $items[$userId]["origin"],
                        "originLabel" => $items[$userId]["originLabel"],
                        "targets" => EvaluationGlobalManager::getQuestionTargets($answer->getQuestion()),
                        "title" => $formation->getProgramme()->getTitle(),
                        "formationAnsweredId" => $answer->getFormation()->getId(),
                    );

                    if (isset($items[$userId]["coordinatorName"])) {
                        $answerData["coordinatorName"] = $items[$userId]["coordinatorName"];
                    }

                    if (isset($items[$userId]["supervisorName"])) {
                        $answerData["supervisorName"] = $items[$userId]["supervisorName"];
                    }

                    /** @var Formateur $former */
                    $former = $answer->getFormer();
                    if ($former) {
                        $answerData = array_merge($answerData, array(
                            "formerCivility" => $former->getPerson()->getCivility(),
                            "formerName" => $former->getPerson()->getFullname(),
                            "formerJob" => $former->getPerson()->getJob(),
                            "formerAddress" => $former->getPerson()->getFullAdress(),
                            "formerReferent" => in_array($former->getPerson()->getId(), $formerReferentIds) ? 1 : 0,
                        ));
                    }

                    return $answerData;
                }, $item))
            ));

        }

        $items = array_map(function ($elem) {
            $elem['exercisesMode'] = array_values($elem['exercisesMode']);
            return $elem;
        }, $items);
        return array_values($items);
    }

    /**
     * Permet de synhroniser les données qui ont était ajoutées en base depuis le début de l'éxécution de la commande pour éviter les pertes
     * A implémenter si nécessaire
     * @param array $types
     * @param \DateTimeInterface $now
     * @return mixed
     */
    public function synchronizeUpdatedDatas(array $types, \DateTimeInterface $now)
    {
    }

    public function hasCustomProcessAvance(): bool
    {
        return true;
    }
}