<?php

namespace Eduprat\DomainBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Repository\ParticipationLogRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Bundle\SecurityBundle\Security;
use Eduprat\AuditBundle\Services\CourseManager;

class ParticipationLogger
{
    public const ACTIVE_ITEM_KEY = "p_log_active";
    public const ACTIVE_ITEM_ELEARNING = "p_log_elearning";
    private EntityManager $entityManager;
    private ParticipationLogRepository $repository;
    private RequestStack $requestStack;
    private Security $security;
    private CourseManager $courseManager;

    /**
     * InvoiceManager constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(Security $security, EntityManagerInterface $entityManager, RequestStack $requestStack, CourseManager $courseManager)
    {
        $this->entityManager = $entityManager;
        $this->repository = $entityManager->getRepository(ParticipationLog::class);
        $this->requestStack = $requestStack;
        $this->security = $security;
        $this->courseManager = $courseManager;
    }

    public function getActiveItemKey() {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            return $session->get(self::ACTIVE_ITEM_ELEARNING) ? $session->get(self::ACTIVE_ITEM_ELEARNING) : $session->get(self::ACTIVE_ITEM_KEY);
        }
    }

    public function setActiveItem($key): void
    {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            if ($key === null) {
                $session->remove(self::ACTIVE_ITEM_KEY);
            } else {
                $session->set(self::ACTIVE_ITEM_KEY, array(
                    "key" => $key,
                    "route" => $this->requestStack->getMainRequest()->attributes->get("_route")
                ));
            }
        }
    }

    public function getActiveItem() {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            $item = $session->get(self::ACTIVE_ITEM_KEY);
            if (isset($item["key"]) && $session->has($item["key"])) {
                return $this->repository->find($session->get($item["key"]));
            }
        }
        return null;
    }

    public function getSessionKey(Participation $participation, $action, $key = null) {
        return $key ?: sprintf("p_log_%s_%s", $participation->getId(), $action);
    }

    public function getSessionItem(Participation $participation, $action, $key = null) {
        if ($this->requestStack->getMainRequest()?->hasSession()) {
            $session = $this->requestStack->getSession();
            $key = $this->getSessionKey($participation, $action, $key);
            if ($session->has($key)) {
                return $session->get($key);
            }
        }
        return null;
    }

    public function getExistingLog(Participation $participation, $action, $key = null): ?ParticipationLog
    {
        $itemId = $this->getSessionItem($participation, $action, $key);
        if ($itemId) {
            return $this->repository->find($itemId);
        }
        return null;
    }

    public function startLog(Participation $participation, $action, $key = null, $progression = 0, $stringProgression = null, $activity = null): ?ParticipationLog
     {
        if ($participation->getFormation()->getFormClosingDate() < new \DateTime('today midnight')) {
            return null;
        }
        $item = $this->getExistingLog($participation, $action, $key);
        $key = $this->getSessionKey($participation, $action, $key);
        $this->setActiveItem($key);

        $step = $this->courseManager->findStepIdModule($this->courseManager->getCourse($participation), $action, true);

        if ($item === null) {
            $log = $this->addLog($participation, $action, $progression, $stringProgression, $activity, $step);
            if ($this->requestStack->getMainRequest()?->hasSession()) {
                $session = $this->requestStack->getSession();
                $session->set($key, $log->getId());
            }
        }
        return $item;
    }

    public function endLog(Participation $participation, $action, $key = null, $progression = null, $stringProgression = null) {
        $item = $this->getExistingLog($participation, $action, $key);
        if ($item) {
            $item->setEndDate(new \DateTime());
            if ($progression) {
                $item->setProgression($progression);
            }
            if ($stringProgression) {
                $item->setStringProgression($stringProgression);
            }
            $this->entityManager->persist($item);
            $this->entityManager->flush();
            if ($progression === 100) {
                $this->setActiveItem(null);
                if ($this->requestStack->getMainRequest()?->hasSession() && $key) {
                    $session = $this->requestStack->getSession();
                    $session->remove($key);
                }
            }
        } else { // Maj de la progression si le log se fait fermer par autre chose
            $log = $this->repository->findLastByAction($participation, $action);
            if ($log && $log->getProgression($progression) != $progression && $log->getProgression($progression) < $progression) {
                $log->setProgression($progression);
                $log->setStringProgression($stringProgression);
                $this->entityManager->persist($log);
                $this->entityManager->flush();
            }
        }
        return $item;
    }

    public function endCurrentLog(): void
    {
        $info = $this->getActiveItemKey();
        $item = $this->getActiveItem();
        if ($info && $item) {
            $item->setEndDate(new \DateTime());
            $this->entityManager->persist($item);
            $this->entityManager->flush();
        }
    }

    public function addLog(Participation $participation, $action, $progression = 0, $stringProgression = null, $activity = null, $step = null): ParticipationLog
    {
        $log = new ParticipationLog($participation, $action);
        $log->setProgression($progression);
        $log->setStringProgression($stringProgression);
        $log->setStep($step);
        if ($activity) {
            $log->setLesson($activity->getLesson()->getId());
            $log->setActivity($activity->getId());
        }
        $log->setIp($_SERVER['REMOTE_ADDR']);
        $this->entityManager->persist($log);
        $this->entityManager->flush();
        return $log;
    }

    public function handleRequest(Request $request): void
    {
        $info = $this->getActiveItemKey();
        $currentRoute = $this->requestStack->getMainRequest()->attributes->get("_route");
        if ($info && ($currentRoute !== $info["route"]
                || strpos($info["key"], 'p_log_elearning') !== false)
            && !in_array($currentRoute, ["eduprat_activity_pdf_file", "participant_date_download", "eduprat_audit_formation_topo", "eduprat_audit_formation_topo_tool_programme", "eduprat_audit_formation_documents_pedagogiques"])
            && !$this->tcsToTcs($currentRoute, $info)
        ) {
            $this->endCurrentLog();
            $this->setActiveItem(null);
            if ($this->requestStack->getMainRequest()?->hasSession()) {
                $session = $this->requestStack->getSession();
                $session->remove($info["key"]);
            }
        }
    }

    public function tcsToTcs($currentRoute, $info) {
        return str_contains($currentRoute, "tcs") && str_contains($info["key"], "tcs");
    }

}
