<?php

namespace Eduprat\DomainBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\ProgrammesAssocies;

class ProgrammeAssocieManager
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function synchroAssociation(Programme $programme, ?ProgrammesAssocies $programmeAssocieClonePreUpdate = null): void
    {
        // En cas de modification/suppression de la valeur du programme associé
        // on met à jour l'ancien programme associé
        if ($programmeAssocieClonePreUpdate) {
            if ($programmeAssocieClonePreUpdate->getProgrammeElearning() !== null) {
                if (!$programme->getProgrammesAssocies()->getProgrammeElearning() // suppression
                    || ($programme->getProgrammesAssocies()->getProgrammeElearning() != $programmeAssocieClonePreUpdate->getProgrammeElearning()) //modification
                ) {
                    $programmeAssocieClonePreUpdate->getProgrammeElearning()->setProgrammesAssocies(null);
                    $this->entityManager->persist($programmeAssocieClonePreUpdate->getProgrammeElearning());
                }
            }
            if ($programmeAssocieClonePreUpdate->getProgrammeClasseVirtuelle() !== null
                && (!$programme->getProgrammesAssocies()->getProgrammeClasseVirtuelle() // suppression
                    || $programme->getProgrammesAssocies()->getProgrammeClasseVirtuelle() != $programmeAssocieClonePreUpdate->getProgrammeClasseVirtuelle()) //modification
            ) {
                $programmeAssocieClonePreUpdate->getProgrammeClasseVirtuelle()->setProgrammesAssocies(null);
                $this->entityManager->persist($programmeAssocieClonePreUpdate->getProgrammeClasseVirtuelle());
            }
            if ($programmeAssocieClonePreUpdate->getProgrammeSurSite() !== null
                && (!$programme->getProgrammesAssocies()->getProgrammeSurSite() // suppression
                    || $programme->getProgrammesAssocies()->getProgrammeSurSite() != $programmeAssocieClonePreUpdate->getProgrammeSurSite()) //modification
            ) {
                $programmeAssocieClonePreUpdate->getProgrammeSurSite()->setProgrammesAssocies(null);
                $this->entityManager->persist($programmeAssocieClonePreUpdate->getProgrammeSurSite());
            }
        }

        // On assigne le programme modifié au ProgrammesAssocies
        if (!$programme->getProgrammesAssocies()) {
            $programme->setProgrammesAssocies(new ProgrammesAssocies());
        }
        switch ($programme->getPresence()) {
            case Programme::PRESENCE_ELEARNING;
                $programme->getProgrammesAssocies()->setProgrammeElearning($programme);
            break;
            case Programme::PRESENCE_VIRTUELLE;
                $programme->getProgrammesAssocies()->setProgrammeClasseVirtuelle($programme);
            break;
            case Programme::PRESENCE_SITE;
                $programme->getProgrammesAssocies()->setProgrammeSurSite($programme);
                break;
        }

        if ($programme->getProgrammesAssocies()->hasMin2Programme()) {
            $programme->getProgrammesAssocies()->synchro();
        } else {
            $this->entityManager->remove($programme->getProgrammesAssocies());
            $programme->setProgrammesAssocies(null);
        }
    }
}