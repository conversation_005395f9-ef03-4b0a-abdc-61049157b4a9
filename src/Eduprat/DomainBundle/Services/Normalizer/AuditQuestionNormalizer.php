<?php

namespace Eduprat\DomainBundle\Services\Normalizer;

use Alienor\ElasticBundle\Services\Normalizer\ObjectNormalizer;
use Eduprat\DomainBundle\Entity\AuditQuestion;

class AuditQuestionNormalizer extends ObjectNormalizer
{

    public function supportsDenormalization($data, $type, $format = null, array $context = []): bool
    {
        return $type == AuditQuestion::class;
    }

    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        return $data instanceof AuditQuestion;
    }

    /**
     * @param AuditQuestion $object
     */
    public function normalize($object, ?string $format = null, array $context = []): array
    {
        $data = parent::normalize($object, $format, $context);
        if (in_array("api", $context["groups"])) {
            $data["type"] = "radio";
            if ($object->getDisplayedAnswer() !== null) {
                $data["answer"] = $object->getDisplayedAnswer();
            }
        }
        return $data;
    }


    /**
     * @param AuditQuestion $object
     * @param mixed $data
     * @param string|null $format
     * @param array $context
     * @return AuditQuestion
     */
    protected function postDenormalize($object, $data, $format = null, array $context = array())
    {
        return $object;
    }

    public function getClass()
    {
        return AuditQuestion::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return array(AuditQuestion::class => false);
    }
}