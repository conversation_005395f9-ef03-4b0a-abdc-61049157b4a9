<?php

namespace Eduprat\DomainBundle\Services\Normalizer;

use Alienor\ElasticBundle\Services\Normalizer\ObjectNormalizer;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactoryInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;
use Symfony\Component\Asset\Packages;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

abstract class DefaultNormalizer extends ObjectNormalizer
{

    /**
     * @var UploaderHelper
     */
    protected $uploaderHelper;

    /**
     * @var Packages
     */
    protected $packages;

    public function __construct(Packages $packages, UploaderHelper $uploaderHelper, ClassMetadataFactoryInterface $classMetadataFactory = null, NameConverterInterface $nameConverter = null, PropertyAccessorInterface $propertyAccessor = null)
    {
        parent::__construct($classMetadataFactory, $nameConverter, $propertyAccessor);
        $this->uploaderHelper = $uploaderHelper;
        $this->packages = $packages;
    }
}