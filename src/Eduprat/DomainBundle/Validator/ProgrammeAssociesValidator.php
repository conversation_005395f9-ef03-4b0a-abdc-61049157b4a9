<?php

namespace Eduprat\DomainBundle\Validator;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\ProgrammesAssocies;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class ProgrammeAssociesValidator extends ConstraintValidator
{
    protected $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @param ProgrammesAssocies $value
     */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof ProgrammeAssociesContraint) {
            throw new UnexpectedTypeException($constraint, ProgrammeAssociesContraint::class);
        }

        $programmeAssocieEL = $value->getProgrammeElearning();
        $programmeAssocieCV = $value->getProgrammeClasseVirtuelle();
        $programmeAssocieSS = $value->getProgrammeSurSite();

        // On vérifie le typage des champs
        if ($programmeAssocieEL !== null && !$programmeAssocieEL->isElearning()) {
            $this->context->buildViolation($constraint->messageELRequired)->atPath('programmeElearning')->addViolation();
        }
        if ($programmeAssocieCV !== null && !$programmeAssocieCV->isClasseVirtuelle()) {
            $this->context->buildViolation($constraint->messageCVRequired)->atPath('programmeClasseVirtuelle')->addViolation();
        }
        if ($programmeAssocieSS !== null && !$programmeAssocieSS->isSurSite()) {
            $this->context->buildViolation($constraint->messageSSRequired)->atPath('programmeSurSite')->addViolation();
        }
    }
}