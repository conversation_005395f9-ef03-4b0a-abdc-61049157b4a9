<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\DomainBundle\Entity\AuditCategory;
use <PERSON><PERSON>rat\DomainBundle\Repository\AuditCategoryRepository;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<AuditCategory>
 *
 * @method        AuditCategory|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static AuditCategory|Proxy                              createOne(array $attributes = [])
 * @method static AuditCategory|Proxy                              find(object|array|mixed $criteria)
 * @method static AuditCategory|Proxy                              findOrCreate(array $attributes)
 * @method static AuditCategory|Proxy                              first(string $sortedField = 'id')
 * @method static AuditCategory|Proxy                              last(string $sortedField = 'id')
 * @method static AuditCategory|Proxy                              random(array $attributes = [])
 * @method static AuditCategory|Proxy                              randomOrCreate(array $attributes = [])
 * @method static AuditCategoryRepository|ProxyRepositoryDecorator repository()
 * @method static AuditCategory[]|Proxy[]                          all()
 * @method static AuditCategory[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static AuditCategory[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static AuditCategory[]|Proxy[]                          findBy(array $attributes)
 * @method static AuditCategory[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static AuditCategory[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        AuditCategory&Proxy<AuditCategory> create(array|callable $attributes = [])
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> createOne(array $attributes = [])
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> find(object|array|mixed $criteria)
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> findOrCreate(array $attributes)
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> first(string $sortedField = 'id')
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> last(string $sortedField = 'id')
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> random(array $attributes = [])
 * @phpstan-method static AuditCategory&Proxy<AuditCategory> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<AuditCategory, EntityRepository> repository()
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> all()
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> findBy(array $attributes)
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<AuditCategory&Proxy<AuditCategory>> randomSet(int $number, array $attributes = [])
 */
final class AuditCategoryFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();
    }

    protected function defaults(): array|callable
    {
        return [
            'name' => self::faker()->unique()->randomElement([
                'Addictologie',
                'Aromathérapie',
                'Autres',
                'Bilan pré-thérapeutique',
                'Biologiste',
                'Cardiologie',
                'Chirurgie',
                'COMPLICATIONS - EVOLUTION',
                'Connaître la physiopathologie et les symptômes',
                'Conseils',
                'Définitions',
                'Dentiste',
                'Dépistage',
                'Dermatologie',
                'Douleur',
                'Education thérapeutique',
                'Endocrinologie',
                'Ergothérapie',
                'Ethique',
                'Etre capable de reconnaître les effets secondaires et complications liées aux traitements',
                'Etre en mesure d\'assurer un suivi adapté et personnalisé',
                'Evaluation et examen clinique',
                'Gastro-Hépato-Entéro',
                'Gériatrie',
                'Gynécologie',
                'Hématologie',
                'Hypnose',
                'IDE',
                'Prescription/conseils',
                'Psychiatrie',
                'Radiologie',
                'Recommandations / vaccinologie',
                'Repérer les évolutions de la maladie et les pathologies associées',
                'Rhumatologie',
                'Santé et sécurité au travail',
                'Savoir expliquer et adapter les traitements médicamenteux',
                'Savoir prescrire les examens complémentaires pertinents',
                'Schémas vaccinal',
                'Suivi - Thérapeutique',
                'Suivi, Orientation évolution',
                'Technicien de laboratoire médical',
                'Télémédecine',
                'Tests - Examens complémentaires',
                'Thérapeutique - ETP',
                'Urgences',
                'Urologie',
            ]),
            'color' => self::faker()->hexColor(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(AuditCategory $auditCategory): void {})
        ;
    }

    public static function class(): string
    {
        return AuditCategory::class;
    }
}
