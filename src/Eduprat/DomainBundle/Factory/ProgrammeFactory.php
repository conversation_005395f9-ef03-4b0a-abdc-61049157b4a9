<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Ed<PERSON>rat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Repository\ProgrammeRepository;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zenstruck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<Programme>
 *
 * @method        Programme|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static Programme|Proxy                              createOne(array $attributes = [])
 * @method static Programme|Proxy                              find(object|array|mixed $criteria)
 * @method static Programme|Proxy                              findOrCreate(array $attributes)
 * @method static Programme|Proxy                              first(string $sortedField = 'id')
 * @method static Programme|Proxy                              last(string $sortedField = 'id')
 * @method static Programme|Proxy                              random(array $attributes = [])
 * @method static Programme|Proxy                              randomOrCreate(array $attributes = [])
 * @method static ProgrammeRepository|ProxyRepositoryDecorator repository()
 * @method static Programme[]|Proxy[]                          all()
 * @method static Programme[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static Programme[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static Programme[]|Proxy[]                          findBy(array $attributes)
 * @method static Programme[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static Programme[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        Programme&Proxy<Programme> create(array|callable $attributes = [])
 * @phpstan-method static Programme&Proxy<Programme> createOne(array $attributes = [])
 * @phpstan-method static Programme&Proxy<Programme> find(object|array|mixed $criteria)
 * @phpstan-method static Programme&Proxy<Programme> findOrCreate(array $attributes)
 * @phpstan-method static Programme&Proxy<Programme> first(string $sortedField = 'id')
 * @phpstan-method static Programme&Proxy<Programme> last(string $sortedField = 'id')
 * @phpstan-method static Programme&Proxy<Programme> random(array $attributes = [])
 * @phpstan-method static Programme&Proxy<Programme> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<Programme, EntityRepository> repository()
 * @phpstan-method static list<Programme&Proxy<Programme>> all()
 * @phpstan-method static list<Programme&Proxy<Programme>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<Programme&Proxy<Programme>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<Programme&Proxy<Programme>> findBy(array $attributes)
 * @phpstan-method static list<Programme&Proxy<Programme>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<Programme&Proxy<Programme>> randomSet(int $number, array $attributes = [])
 */
class ProgrammeFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            'title' => self::faker()->text(),
            'reference' => self::faker()->numerify('###########'),
            'startDate' => new \DateTime(),
            'endDate' => new \DateTime('today + 2 months'),
            'resume' => self::faker()->text(),
            'createdAt' => new \DateTime('today - 1 day'),
            'address' => self::faker()->streetAddress(),
            'address2' => self::faker()->streetAddress(),
            'city' => self::faker()->city(),
            'zipCode' => self::faker()->postcode(),
            'duration' => self::faker()->numberBetween(0, 10),
            'cout' => 'Nous contacter : <EMAIL> ou 05 56 51 65 14',
            'prerequis' => self::faker()->text(),
            'certifying' => self::faker()->boolean(),
            'region' => 'Nouvelle-Aquitaine',
            'year' => self::faker()->year(),
            'presence' => self::faker()->randomElement(['Sur site', 'E-Learning', 'Classe virtuelle']),
            'format' => self::faker()->randomElement(['Mixte', 'Présentiel', 'Elearning']),
            'category' => AuditCategoryFactory::new(),
            'durationPresentielle' => self::faker()->numberBetween(0, 10),
            'durationNotPresentielle' => self::faker()->numberBetween(0, 10),
            'durationNotPresentielleActalians' => self::faker()->numberBetween(0, 10),
            'formType' => self::faker()->randomElement(['survey', 'audit', 'predefined', 'vignette', 'vignette_audit', null]),
            'sessionType' => self::faker()->randomElement(['formation_presentielle', 'formation_vignette_audit', 'formation_elearning', 'formation_audit', 'formation_vignette', 'formation_congres', 'formation_powerpoint', 'formation_sedd', null]),
        ];
    }

    public function elearningTwoUnities()
    {
        return $this->with([
            'sessionType' => Formation::TYPE_ELEARNING,
            'presence' => Programme::PRESENCE_SITE,
            'unities' => [
                UnityFormationFactory::new()->elearning()->create(),
                UnityFormationFactory::new()->surSite()->create(),
            ]
        ]);
    }

    public function surSiteOneUnity()
    {
        return $this->with([
            'presence' => Programme::PRESENCE_SITE,
            'unities' => [
                UnityFormationFactory::new()->surSite()->create(),
            ]
        ]);
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Programme $programme): void {})
        ;
    }

    public static function class(): string
    {
        return Programme::class;
    }
}
