<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\DomainBundle\Entity\FormationElearning;
use Ed<PERSON>rat\DomainBundle\Repository\FormationElearningRepository;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<FormationElearning>
 *
 * @method        FormationElearning|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static FormationElearning|Proxy                              createOne(array $attributes = [])
 * @method static FormationElearning|Proxy                              find(object|array|mixed $criteria)
 * @method static FormationElearning|Proxy                              findOrCreate(array $attributes)
 * @method static FormationElearning|Proxy                              first(string $sortedField = 'id')
 * @method static FormationElearning|Proxy                              last(string $sortedField = 'id')
 * @method static FormationElearning|Proxy                              random(array $attributes = [])
 * @method static FormationElearning|Proxy                              randomOrCreate(array $attributes = [])
 * @method static FormationElearningRepository|ProxyRepositoryDecorator repository()
 * @method static FormationElearning[]|Proxy[]                          all()
 * @method static FormationElearning[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static FormationElearning[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static FormationElearning[]|Proxy[]                          findBy(array $attributes)
 * @method static FormationElearning[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static FormationElearning[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        FormationElearning&Proxy<FormationElearning> create(array|callable $attributes = [])
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> createOne(array $attributes = [])
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> find(object|array|mixed $criteria)
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> findOrCreate(array $attributes)
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> first(string $sortedField = 'id')
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> last(string $sortedField = 'id')
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> random(array $attributes = [])
 * @phpstan-method static FormationElearning&Proxy<FormationElearning> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<FormationElearning, EntityRepository> repository()
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> all()
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> findBy(array $attributes)
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<FormationElearning&Proxy<FormationElearning>> randomSet(int $number, array $attributes = [])
 */
final class FormationElearningFactory extends FormationFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(FormationElearning $formationElearning): void {})
        ;
    }

    public static function class(): string
    {
        return FormationElearning::class;
    }
}
