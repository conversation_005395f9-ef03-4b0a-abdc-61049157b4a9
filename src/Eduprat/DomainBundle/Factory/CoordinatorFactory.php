<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Repository\CoordinatorRepository;
use <PERSON><PERSON><PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<Coordinator>
 *
 * @method        Coordinator|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static Coordinator|Proxy                              createOne(array $attributes = [])
 * @method static Coordinator|Proxy                              find(object|array|mixed $criteria)
 * @method static Coordinator|Proxy                              findOrCreate(array $attributes)
 * @method static Coordinator|Proxy                              first(string $sortedField = 'id')
 * @method static Coordinator|Proxy                              last(string $sortedField = 'id')
 * @method static Coordinator|Proxy                              random(array $attributes = [])
 * @method static Coordinator|Proxy                              randomOrCreate(array $attributes = [])
 * @method static CoordinatorRepository|ProxyRepositoryDecorator repository()
 * @method static Coordinator[]|Proxy[]                          all()
 * @method static Coordinator[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static Coordinator[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static Coordinator[]|Proxy[]                          findBy(array $attributes)
 * @method static Coordinator[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static Coordinator[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        Coordinator&Proxy<Coordinator> create(array|callable $attributes = [])
 * @phpstan-method static Coordinator&Proxy<Coordinator> createOne(array $attributes = [])
 * @phpstan-method static Coordinator&Proxy<Coordinator> find(object|array|mixed $criteria)
 * @phpstan-method static Coordinator&Proxy<Coordinator> findOrCreate(array $attributes)
 * @phpstan-method static Coordinator&Proxy<Coordinator> first(string $sortedField = 'id')
 * @phpstan-method static Coordinator&Proxy<Coordinator> last(string $sortedField = 'id')
 * @phpstan-method static Coordinator&Proxy<Coordinator> random(array $attributes = [])
 * @phpstan-method static Coordinator&Proxy<Coordinator> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<Coordinator, EntityRepository> repository()
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> all()
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> findBy(array $attributes)
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<Coordinator&Proxy<Coordinator>> randomSet(int $number, array $attributes = [])
 */
final class CoordinatorFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'person' => PersonFactory::new(),
            'formation' => FormationFactory::new(),
            'initiator' => self::faker()->boolean(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Coordinator $coordinator): void {})
        ;
    }

    public static function class(): string
    {
        return Coordinator::class;
    }
}
