<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON>rat\DomainBundle\Entity\UnitySession;
use Ed<PERSON>rat\DomainBundle\Repository\UnitySessionRepository;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<UnitySession>
 *
 * @method        UnitySession|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static UnitySession|Proxy                              createOne(array $attributes = [])
 * @method static UnitySession|Proxy                              find(object|array|mixed $criteria)
 * @method static UnitySession|Proxy                              findOrCreate(array $attributes)
 * @method static UnitySession|Proxy                              first(string $sortedField = 'id')
 * @method static UnitySession|Proxy                              last(string $sortedField = 'id')
 * @method static UnitySession|Proxy                              random(array $attributes = [])
 * @method static UnitySession|Proxy                              randomOrCreate(array $attributes = [])
 * @method static UnitySessionRepository|ProxyRepositoryDecorator repository()
 * @method static UnitySession[]|Proxy[]                          all()
 * @method static UnitySession[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static UnitySession[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static UnitySession[]|Proxy[]                          findBy(array $attributes)
 * @method static UnitySession[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static UnitySession[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        UnitySession&Proxy<UnitySession> create(array|callable $attributes = [])
 * @phpstan-method static UnitySession&Proxy<UnitySession> createOne(array $attributes = [])
 * @phpstan-method static UnitySession&Proxy<UnitySession> find(object|array|mixed $criteria)
 * @phpstan-method static UnitySession&Proxy<UnitySession> findOrCreate(array $attributes)
 * @phpstan-method static UnitySession&Proxy<UnitySession> first(string $sortedField = 'id')
 * @phpstan-method static UnitySession&Proxy<UnitySession> last(string $sortedField = 'id')
 * @phpstan-method static UnitySession&Proxy<UnitySession> random(array $attributes = [])
 * @phpstan-method static UnitySession&Proxy<UnitySession> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<UnitySession, EntityRepository> repository()
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> all()
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> findBy(array $attributes)
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<UnitySession&Proxy<UnitySession>> randomSet(int $number, array $attributes = [])
 */
final class UnitySessionFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            'openingDate' => new \DateTime(),
            'closingDate' => new \DateTime(),
            'formation' => FormationFactory::new(),
            'unitySessionDates' => [
//                UnitySessionDateFactory::new(),
            ],
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(UnitySession $unitySession): void {})
        ;
    }

    public static function class(): string
    {
        return UnitySession::class;
    }
}
