<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\Formateur;
use Ed<PERSON>rat\DomainBundle\Repository\FormateurRepository;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenst<PERSON>ck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<Formateur>
 *
 * @method        Formateur|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static Formateur|Proxy                              createOne(array $attributes = [])
 * @method static Formateur|Proxy                              find(object|array|mixed $criteria)
 * @method static Formateur|Proxy                              findOrCreate(array $attributes)
 * @method static Formateur|Proxy                              first(string $sortedField = 'id')
 * @method static Formateur|Proxy                              last(string $sortedField = 'id')
 * @method static Formateur|Proxy                              random(array $attributes = [])
 * @method static Formateur|Proxy                              randomOrCreate(array $attributes = [])
 * @method static FormateurRepository|ProxyRepositoryDecorator repository()
 * @method static Formateur[]|Proxy[]                          all()
 * @method static Formateur[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static Formateur[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static Formateur[]|Proxy[]                          findBy(array $attributes)
 * @method static Formateur[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static Formateur[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        Formateur&Proxy<Formateur> create(array|callable $attributes = [])
 * @phpstan-method static Formateur&Proxy<Formateur> createOne(array $attributes = [])
 * @phpstan-method static Formateur&Proxy<Formateur> find(object|array|mixed $criteria)
 * @phpstan-method static Formateur&Proxy<Formateur> findOrCreate(array $attributes)
 * @phpstan-method static Formateur&Proxy<Formateur> first(string $sortedField = 'id')
 * @phpstan-method static Formateur&Proxy<Formateur> last(string $sortedField = 'id')
 * @phpstan-method static Formateur&Proxy<Formateur> random(array $attributes = [])
 * @phpstan-method static Formateur&Proxy<Formateur> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<Formateur, EntityRepository> repository()
 * @phpstan-method static list<Formateur&Proxy<Formateur>> all()
 * @phpstan-method static list<Formateur&Proxy<Formateur>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<Formateur&Proxy<Formateur>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<Formateur&Proxy<Formateur>> findBy(array $attributes)
 * @phpstan-method static list<Formateur&Proxy<Formateur>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<Formateur&Proxy<Formateur>> randomSet(int $number, array $attributes = [])
 */
final class FormateurFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        $programme = ProgrammeFactory::new();
        return [
            'honorary' => self::faker()->randomFloat(),
            'programme' => $programme,
            'formation' => FormationFactory::new([
                'programme' => $programme,
            ]),
            'person' => PersonFactory::new(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(Formateur $formateur): void {})
        ;
    }

    public static function class(): string
    {
        return Formateur::class;
    }
}
