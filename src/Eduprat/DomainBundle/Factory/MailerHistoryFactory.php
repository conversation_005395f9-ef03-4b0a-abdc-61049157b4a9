<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\MailerHistory;
use Ed<PERSON>rat\DomainBundle\Repository\MailerHistoryRepository;
use Eduprat\DomainBundle\Services\Email\EmailFormationConvocation;
use Eduprat\DomainBundle\Services\Email\EmailFormationConvocationCoordinateur;
use Eduprat\DomainBundle\Services\Email\EmailFormationJP1;
use Eduprat\DomainBundle\Services\Email\EmailFormationJP1EmptyCoordinator;
use Zenstruck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zenstruck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<MailerHistory>
 *
 * @method        MailerHistory|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static MailerHistory|Proxy                              createOne(array $attributes = [])
 * @method static MailerHistory|Proxy                              find(object|array|mixed $criteria)
 * @method static MailerHistory|Proxy                              findOrCreate(array $attributes)
 * @method static MailerHistory|Proxy                              first(string $sortedField = 'id')
 * @method static MailerHistory|Proxy                              last(string $sortedField = 'id')
 * @method static MailerHistory|Proxy                              random(array $attributes = [])
 * @method static MailerHistory|Proxy                              randomOrCreate(array $attributes = [])
 * @method static MailerHistoryRepository|ProxyRepositoryDecorator repository()
 * @method static MailerHistory[]|Proxy[]                          all()
 * @method static MailerHistory[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static MailerHistory[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static MailerHistory[]|Proxy[]                          findBy(array $attributes)
 * @method static MailerHistory[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static MailerHistory[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        MailerHistory&Proxy<MailerHistory> create(array|callable $attributes = [])
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> createOne(array $attributes = [])
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> find(object|array|mixed $criteria)
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> findOrCreate(array $attributes)
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> first(string $sortedField = 'id')
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> last(string $sortedField = 'id')
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> random(array $attributes = [])
 * @phpstan-method static MailerHistory&Proxy<MailerHistory> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<MailerHistory, EntityRepository> repository()
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> all()
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> findBy(array $attributes)
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<MailerHistory&Proxy<MailerHistory>> randomSet(int $number, array $attributes = [])
 */
final class MailerHistoryFactory extends PersistentProxyObjectFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function defaults(): array|callable
    {
        return [
            // TODO add your default values here (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#model-factories)
            'createdAt' => new \DateTime(), // TODO add DATETIME ORM type manually
            'subject' => self::faker()->text(),
            'type' => MailerHistory::TYPE_AUTO,
//            'sendEvent' => \Swift_Events_SendEvent::RESULT_SUCCESS,
            'sendEvent' => 0x0010,
            'alias' => self::faker()->randomElement([EmailFormationConvocation::ALIAS, EmailFormationConvocationCoordinateur::ALIAS, EmailFormationJP1::ALIAS, EmailFormationJP1EmptyCoordinator::ALIAS]),
            'sender' => PersonFactory::new(),
            'destinataire' => PersonFactory::new(),
            'session' => FormationFactory::new(),
            'participation' => ParticipationFactory::new(),
        ];
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(MailerHistory $mailerHistory): void {})
        ;
    }

    public static function class(): string
    {
        return MailerHistory::class;
    }
}
