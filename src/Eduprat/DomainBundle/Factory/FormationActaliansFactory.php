<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\FormationActalians;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zenst<PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zen<PERSON><PERSON>ck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<FormationActalians>
 *
 * @method        FormationActalians|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static FormationActalians|Proxy                              createOne(array $attributes = [])
 * @method static FormationActalians|Proxy                              find(object|array|mixed $criteria)
 * @method static FormationActalians|Proxy                              findOrCreate(array $attributes)
 * @method static FormationActalians|Proxy                              first(string $sortedField = 'id')
 * @method static FormationActalians|Proxy                              last(string $sortedField = 'id')
 * @method static FormationActalians|Proxy                              random(array $attributes = [])
 * @method static FormationActalians|Proxy                              randomOrCreate(array $attributes = [])
 * @method static ProxyRepositoryDecorator                              repository()
 * @method static FormationActalians[]|Proxy[]                          all()
 * @method static FormationActalians[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static FormationActalians[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static FormationActalians[]|Proxy[]                          findBy(array $attributes)
 * @method static FormationActalians[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static FormationActalians[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        FormationActalians&Proxy<FormationActalians> create(array|callable $attributes = [])
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> createOne(array $attributes = [])
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> find(object|array|mixed $criteria)
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> findOrCreate(array $attributes)
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> first(string $sortedField = 'id')
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> last(string $sortedField = 'id')
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> random(array $attributes = [])
 * @phpstan-method static FormationActalians&Proxy<FormationActalians> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<FormationActalians, EntityRepository> repository()
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> all()
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> findBy(array $attributes)
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<FormationActalians&Proxy<FormationActalians>> randomSet(int $number, array $attributes = [])
 */
final class FormationActaliansFactory extends FormationFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(FormationActalians $FormationActalians): void {})
            ;
    }

    public static function class(): string
    {
        return FormationActalians::class;
    }
}
