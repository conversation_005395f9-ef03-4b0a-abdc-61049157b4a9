<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON><PERSON>\DomainBundle\Entity\FormationVignetteAudit;
use <PERSON>st<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use <PERSON>st<PERSON><PERSON>\Foundry\Persistence\ProxyRepositoryDecorator;
use Zen<PERSON><PERSON>ck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<FormationVignetteAudit>
 *
 * @method        FormationVignetteAudit|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static FormationVignetteAudit|Proxy                              createOne(array $attributes = [])
 * @method static FormationVignetteAudit|Proxy                              find(object|array|mixed $criteria)
 * @method static FormationVignetteAudit|Proxy                              findOrCreate(array $attributes)
 * @method static FormationVignetteAudit|Proxy                              first(string $sortedField = 'id')
 * @method static FormationVignetteAudit|Proxy                              last(string $sortedField = 'id')
 * @method static FormationVignetteAudit|Proxy                              random(array $attributes = [])
 * @method static FormationVignetteAudit|Proxy                              randomOrCreate(array $attributes = [])
 * @method static ProxyRepositoryDecorator repository()
 * @method static FormationVignetteAudit[]|Proxy[]                          all()
 * @method static FormationVignetteAudit[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static FormationVignetteAudit[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static FormationVignetteAudit[]|Proxy[]                          findBy(array $attributes)
 * @method static FormationVignetteAudit[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static FormationVignetteAudit[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        FormationVignetteAudit&Proxy<FormationVignetteAudit> create(array|callable $attributes = [])
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> createOne(array $attributes = [])
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> find(object|array|mixed $criteria)
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> findOrCreate(array $attributes)
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> first(string $sortedField = 'id')
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> last(string $sortedField = 'id')
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> random(array $attributes = [])
 * @phpstan-method static FormationVignetteAudit&Proxy<FormationVignetteAudit> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<FormationVignetteAudit, EntityRepository> repository()
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> all()
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> findBy(array $attributes)
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<FormationVignetteAudit&Proxy<FormationVignetteAudit>> randomSet(int $number, array $attributes = [])
 */
final class FormationVignetteAuditFactory extends FormationFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(FormationVignetteAudit $FormationVignetteAudit): void {})
            ;
    }

    public static function class(): string
    {
        return FormationVignetteAudit::class;
    }
}
