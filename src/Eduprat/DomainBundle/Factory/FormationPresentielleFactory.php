<?php

namespace Eduprat\DomainBundle\Factory;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\DomainBundle\Entity\FormationPresentielle;
use Ed<PERSON>rat\DomainBundle\Repository\FormationPresentielleRepository;
use Zenst<PERSON>ck\Foundry\Persistence\PersistentProxyObjectFactory;
use Zen<PERSON><PERSON>ck\Foundry\Persistence\ProxyRepositoryDecorator;
use Zenstruck\Foundry\Proxy;

/**
 * @extends PersistentProxyObjectFactory<FormationPresentielle>
 *
 * @method        FormationPresentielle|\Zenstruck\Foundry\Persistence\Proxy                              create(array|callable $attributes = [])
 * @method static FormationPresentielle|Proxy                              createOne(array $attributes = [])
 * @method static FormationPresentielle|Proxy                              find(object|array|mixed $criteria)
 * @method static FormationPresentielle|Proxy                              findOrCreate(array $attributes)
 * @method static FormationPresentielle|Proxy                              first(string $sortedField = 'id')
 * @method static FormationPresentielle|Proxy                              last(string $sortedField = 'id')
 * @method static FormationPresentielle|Proxy                              random(array $attributes = [])
 * @method static FormationPresentielle|Proxy                              randomOrCreate(array $attributes = [])
 * @method static FormationPresentielleRepository|ProxyRepositoryDecorator repository()
 * @method static FormationPresentielle[]|Proxy[]                          all()
 * @method static FormationPresentielle[]|Proxy[]                          createMany(int $number, array|callable $attributes = [])
 * @method static FormationPresentielle[]|Proxy[]                          createSequence(iterable|callable $sequence)
 * @method static FormationPresentielle[]|Proxy[]                          findBy(array $attributes)
 * @method static FormationPresentielle[]|Proxy[]                          randomRange(int $min, int $max, array $attributes = [])
 * @method static FormationPresentielle[]|Proxy[]                          randomSet(int $number, array $attributes = [])
 *
 * @phpstan-method        FormationPresentielle&Proxy<FormationPresentielle> create(array|callable $attributes = [])
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> createOne(array $attributes = [])
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> find(object|array|mixed $criteria)
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> findOrCreate(array $attributes)
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> first(string $sortedField = 'id')
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> last(string $sortedField = 'id')
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> random(array $attributes = [])
 * @phpstan-method static FormationPresentielle&Proxy<FormationPresentielle> randomOrCreate(array $attributes = [])
 * @phpstan-method static ProxyRepositoryDecorator<FormationPresentielle, EntityRepository> repository()
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> all()
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> createMany(int $number, array|callable $attributes = [])
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> createSequence(iterable|callable $sequence)
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> findBy(array $attributes)
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> randomRange(int $min, int $max, array $attributes = [])
 * @phpstan-method static list<FormationPresentielle&Proxy<FormationPresentielle>> randomSet(int $number, array $attributes = [])
 */
final class FormationPresentielleFactory extends FormationFactory
{
    public function __construct()
    {
        parent::__construct();

        // TODO inject services if required (https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#factories-as-services)
    }

    protected function initialize(): static
    {
        // see https://symfony.com/bundles/ZenstruckFoundryBundle/current/index.html#initialization
        return $this
            // ->afterInstantiate(function(FormationPresentielle $FormationPresentielle): void {})
            ;
    }

    public static function class(): string
    {
        return FormationPresentielle::class;
    }
}
