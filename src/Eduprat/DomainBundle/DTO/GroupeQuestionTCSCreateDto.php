<?php

namespace Eduprat\DomainBundle\DTO;

use Ed<PERSON><PERSON>\DomainBundle\Entity\TCS\QuestionnaireTCS;
use Symfony\Component\Validator\Constraints as Assert;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

#[Vich\Uploadable()]
class GroupeQuestionTCSCreateDto
{
    #[Assert\NotBlank()]
    public $description;

    public $syntheseEducative;
    public $descriptionImage;
    public $syntheseEducativeImage;

    #[Vich\UploadableField(mapping: 'person_avatar', fileNameProperty: "descriptionImage")]
    public $descriptionImageFile;

    #[Vich\UploadableField(mapping: 'person_avatar', fileNameProperty: "syntheseEducativeImage")]
    public $syntheseEducativeImageFile;

    public function __construct(
        public QuestionnaireTCS $questionnaireTC
    )
    {
    }
}
