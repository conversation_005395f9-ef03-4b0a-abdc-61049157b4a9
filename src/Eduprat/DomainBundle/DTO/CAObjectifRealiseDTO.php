<?php

namespace Eduprat\DomainBundle\DTO;

use Eduprat\AdminBundle\Entity\CoordinatorPerson;

class CAObjectifRealiseDTO
{
    public ?float $CARealise = null;
    public ?float $CAObjectif = null;
    public ?float $cumulMargeRealise = null;
    public ?float $cumulMargeObjectif = null;

    public ?float $primeAnnuelleRealise = null;
    public ?float $primeAnnuelleObjectif = null;

    public ?float $tauxPrimeRealise = null;
    public ?float $tauxPrimeObjectif = null;

    public ?float $nextPalierEcart = null;

    public ?int $nbParticipationsToNextPalier = null;

    public function __construct(CoordinatorPerson $coordinatorPerson, int $year, array $total, int $participations)
    {
        $this->CARealise = $coordinatorPerson->getAllCoordinatorCaSecteurByCoordinator($year);
        $this->CARealise = 240000;
        $this->CAObjectif = $coordinatorPerson->getObjetifCAOf($year);

        $this->calculateCumulMargeObjectif($coordinatorPerson, $year);
        $this->calculateCumulMargeRealise($coordinatorPerson, $year, $total['marges']);

        $this->tauxPrimeRealise = $this->getTauxPrime($this->cumulMargeRealise);
        $this->tauxPrimeObjectif = $this->getTauxPrime($this->cumulMargeObjectif);

        $this->primeAnnuelleRealise = $this->getPrime($this->tauxPrimeRealise, $this->cumulMargeRealise);
        $this->primeAnnuelleObjectif = $this->getPrime($this->tauxPrimeObjectif, $this->cumulMargeObjectif);

        $this->nextPalierEcart = $this->calculateNextPalierEcart();
        $this->calculateNbParticipationsToNextPalier($participations);
    }

    public function getCAPercentObjectif(): ?float
    {
        if (null === $this->CAObjectif) {
            return null;
        }
        return min(100, $this->CARealise / $this->CAObjectif * 100);
    }

    public function getCumulMargePercentObjectif(): ?float
    {
        if (null === $this->cumulMargeObjectif) {
            return null;
        }
        return min(100, $this->cumulMargeRealise / $this->cumulMargeObjectif * 100);
    }

    public function getPrimeAnnuellePercentObjectif(): ?float
    {
        if (null === $this->primeAnnuelleObjectif) {
            return null;
        }
        return min(100, $this->primeAnnuelleRealise / $this->primeAnnuelleObjectif * 100);
    }

    public function getTauxPrimePercentObjectif(): ?float
    {
        if (null === $this->tauxPrimeRealise) {
            return null;
        }
        return min(100, $this->tauxPrimeObjectif / $this->tauxPrimeRealise * 100);
    }

    private function calculateCumulMargeObjectif(CoordinatorPerson $coordinatorPerson, $year): void
    {
        $this->cumulMargeObjectif = $this->CAObjectif * $coordinatorPerson->getTauxCumulMargeOf($year) / 100;
    }

    private function calculateCumulMargeRealise (CoordinatorPerson $coordinatorPerson, $year, $marges): void
    {
        $this->cumulMargeRealise = !$coordinatorPerson->crIsSalarie() ? $marges : $this->CARealise * ($coordinatorPerson->getTauxCumulMargeOf($year) / 100);
    }

    private function calculateNbParticipationsToNextPalier (int $participations): void {
        $nextPalierEcart = $this->calculateNextPalierEcart();
        $forfaitMoyenParticipation = $this->CARealise && $participations ? $this->CARealise / $participations : 0;
        $this->nbParticipationsToNextPalier = $nextPalierEcart && $forfaitMoyenParticipation ? round($nextPalierEcart / $forfaitMoyenParticipation) : null;
    }

    private function getTauxPrime($marge): float
    {
        $paliers = $this->getPaliers();

        // Taux de base pour les montants inférieurs au premier palier
        $tauxBase = 4;

        foreach ($paliers as $seuil => $info) {
            if ($marge < $seuil) {
                return $tauxBase;
            }
            $tauxBase = $info['taux'];
        }

        // Taux pour les montants supérieurs au dernier palier
        return 10;
    }

    private function getPrime(float $tauxPrimeRealise, float $cumulMargeRealise): float
    {
        return $cumulMargeRealise * $tauxPrimeRealise / 100;
    }

    /**
     * Retourne le montant qu'il manque pour atteindre le palier suivant
     */
    public function calculateNextPalierEcart(): ?float
    {
        if ($this->cumulMargeRealise === null) {
            return null;
        }

        $paliers = $this->getPaliers();

        foreach ($paliers as $seuil => $info) {
            if ($this->cumulMargeRealise < $seuil) {
                return $seuil - $this->cumulMargeRealise;
            }
        }

        // Si on est déjà au palier maximum
        return null;
    }

    /**
     * Retourne la liste des paliers restants (incluant le palier en cours) avec leurs tranches et taux
     * Format: [seuil => ["tranche" => "...", "taux" => ..., "value" => ...], ...]
     */
    public function getPaliersRestants(): array
    {
        if ($this->cumulMargeRealise === null) {
            return [];
        }

        $paliers = $this->getPaliers();
        $paliersRestants = [];
        $palierActuelInclus = false;

        foreach ($paliers as $seuil => $info) {
            // Inclure le palier actuel (celui dans lequel on se trouve)
            if (!$palierActuelInclus && $this->cumulMargeRealise >= $seuil) {
                // On continue pour trouver le bon palier actuel
                continue;
            }

            // Une fois qu'on a dépassé notre position, on inclut ce palier et tous les suivants
            if (!$palierActuelInclus && $this->cumulMargeRealise < $seuil) {
                // Inclure d'abord le palier précédent (palier actuel) s'il existe
                $paliersPrecedents = array_keys($paliers);
                $indexActuel = array_search($seuil, $paliersPrecedents);

                if ($indexActuel > 0) {
                    $seuilPrecedent = $paliersPrecedents[$indexActuel - 1];
                    $infoPrecedente = $paliers[$seuilPrecedent];
                    $paliersRestants[$seuilPrecedent] = [
                        "tranche" => $infoPrecedente['tranche'],
                        "taux" => $infoPrecedente['taux'],
                        "value" => $seuilPrecedent
                    ];
                }
                $palierActuelInclus = true;
            }

            // Inclure ce palier et tous les suivants
            if ($this->cumulMargeRealise < $seuil) {
                $paliersRestants[$seuil] = [
                    "tranche" => $info['tranche'],
                    "taux" => $info['taux'],
                    "value" => $seuil
                ];
            }
        }

        return $paliersRestants;
    }

    private function getPaliers(): array
    {
        return [
            160000 => ['taux' => 4, 'tranche' => '160 -<br>179k€'],
            180000 => ['taux' => 4.5, 'tranche' => '180 -<br>199k€'],
            200000 => ['taux' => 5, 'tranche' => '200 -<br>219k€'],
            220000 => ['taux' => 5.5, 'tranche' => '220 -<br>239k€'],
            240000 => ['taux' => 6, 'tranche' => '240 -<br>259k€'],
            260000 => ['taux' => 6.5, 'tranche' => '260 -<br>279k€'],
            280000 => ['taux' => 7, 'tranche' => '280 -<br>319k€'],
            320000 => ['taux' => 8, 'tranche' => '320 -<br>339k€'],
            340000 => ['taux' => 8.5, 'tranche' => '340 -<br>359k€'],
            360000 => ['taux' => 9, 'tranche' => '360 -<br>379k€'],
            380000 => ['taux' => 9.5, 'tranche' => '380k€<br>et plus'],
        ];
    }


}