<?php

namespace Eduprat\DomainBundle\Controller;

use Alienor\ApiBundle\Services\FlashMessages;
use Ed<PERSON>rat\DomainBundle\Services\SearchHandler;
use Ed<PERSON>rat\DomainBundle\Services\SendinBlueManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

abstract class EdupratController extends AbstractController
{
    /**
     * @var FlashMessages
     */
    public $flashMessages;

    /**
     * @var SearchHandler
     */
    protected SearchHandler $searchHandler;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler)
    {
        $this->flashMessages = $flashMessages;
        $this->searchHandler = $searchHandler;
    }

    #[Route(path: '/sib/webhook/desinscription', name: 'admin_sib_webhook_desinscription')]
    public function sibDesinscriptionWebHook(Request $request, SendinBlueManager $sendinBlueManager): JsonResponse {

        $content = $request->getContent();
        $response = $sendinBlueManager->desinscription(json_decode($content));

        if ($response) {
            return new JsonResponse("Ok");
        }
        return new JsonResponse("Participant innexistant");
    }
}