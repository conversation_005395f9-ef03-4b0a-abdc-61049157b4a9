<?php

namespace Eduprat\DomainBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ObjectifsSalarieType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('objectifCA', NumberType::class, [
                'label' => 'Objectif de CA',
                'translation_domain' => false,
            ])
            ->add('tauxCumulMarge', NumberType::class, [
                'label' => 'Taux de cumul de marge (en %)',
                'translation_domain' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ObjectifsSalarieDTO::class,
        ]);
    }
}
