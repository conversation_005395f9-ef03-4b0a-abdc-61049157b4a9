<?php

namespace Eduprat\DomainBundle\Form;

use Eduprat\AdminBundle\Entity\CoordinatorPerson;
use Eduprat\DomainBundle\Entity\Objectif\ObjectifsSalarie;

class ObjectifsSalarieDTO
{

    public function __construct(
        public CoordinatorPerson $person,
        public int $year,
        public ?float $objectifCA = null,
        public ?float $tauxCumulMarge = null,
    )
    {
    }

    public function updateObjectifsSalarie(ObjectifsSalarie $objectifsSalarie): void
    {
        $objectifsSalarie->setObjectifCA($this->objectifCA);
        $objectifsSalarie->setTauxCumulMarge($this->tauxCumulMarge);
    }
}