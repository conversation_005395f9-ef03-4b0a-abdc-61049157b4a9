<?php

namespace Eduprat\DomainBundle\Form;


use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Vich\UploaderBundle\Form\Type\VichImageType;


class PriseEnChargeType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', null, array(
                'label' => 'admin.priseEnCharge.form.name',
            ))
            ->add('priseEnChargePictureFile', VichImageType::class, array(
                'label' => false,
                'required' => false,
                'allow_delete' => true,
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400)
            ))
            ->add('priseEnChargePictureFrom', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false
            ))
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\PriseEnCharge'
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_prise_en_charge';
    }
}
