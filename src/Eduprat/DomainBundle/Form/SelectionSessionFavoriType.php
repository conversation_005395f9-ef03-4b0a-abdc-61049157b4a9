<?php

namespace Eduprat\DomainBundle\Form;

use Ed<PERSON>rat\DomainBundle\Entity\SelectionFormation;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Regex;


class SelectionSessionFavoriType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nomFavori', TextType::class, array(
                'label' => false,
                'attr' => array(
                    'class' => 'mrs',
                    'maxlength' => 50,
                )
            ))
            ->add('nomFavori2', TextType::class, array(
                'label' => false,
                'mapped' => false,
                'constraints' => [
                    new Regex('/^[0-9A-z :\-_]+$/m')
                ],
                'attr' => array(
                    'class' => 'mrs',
                    'maxlength' => 50,
                )
            ))
            ->add('envoyer', SubmitType::class, [
                'label' => 'admin.global.save',
                'attr' => array(
                    'class' => 'btn btn-eduprat saveFavori btn-large'
                )
            ])
            ->add('enregistrer_sous', SubmitType::class, [
                'label' => 'admin.global.save_as',
                'attr' => array(
                    'class' => 'btn btn-eduprat saveFavori btn-large',
                    'data-saveas' => true,
                )
            ])
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => SelectionFormation::class
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_selectionsession_favori';
    }
}
