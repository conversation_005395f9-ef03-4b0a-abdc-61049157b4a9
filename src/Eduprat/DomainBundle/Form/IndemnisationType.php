<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Indemnisation;
use Ed<PERSON>rat\DomainBundle\Entity\IndemnisationPriseEnCharge;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class IndemnisationType extends AbstractType
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        if ($options["group"]) {
            $builder
                ->add('group', TextType::class, array(
                    'attr' => array(
                        'readonly' => true,
                    ),
                    'label' => 'admin.indemnisation.group',
                    'data' => $options["group"],
                    'constraints' => array(
                        new NotBlank()
                    )
                ));
        } else {
            $builder
                ->add('group', TextType::class, array(
                    'label' => 'admin.indemnisation.group',
                    'attr' => array(
                        'readonly' => true,
                    ),
                    'constraints' => array(
                        new NotBlank()
                    )
                ));
        }

        $builder
            ->add('format', ChoiceType::class, array(
                'label' => 'admin.indemnisation.format',
                'choices'  => ProgrammeSearchType::getFormatByDurations(),
                'constraints' => array(
                    new NotBlank()
                )
            ))
            ->add('presence', ChoiceType::class, array(
                'required' => true,
                'label' => 'admin.programme.presence',
                'choices'  => ProgrammeType::getPresences(),
                'constraints' => array(
                    new NotBlank()
                )
            ))
            ->add('formType', ChoiceType::class, array(
                'label' => 'admin.programme.formType.label',
                'required' => true,
                'choices' => ProgrammeType::getFormTypes("admin.indemnisation.formTypes."),
                'constraints' => array(
                    new NotBlank()
                )
            ))
            ->add('nbHours', TextType::class, array(
                'label' => 'admin.indemnisation.nbHours',
                'attr' => array("class" => "validate-number"),
                'constraints' => array(
                    new NotBlank()
                )
            ))
            ->add('indemnisationParticipant', TextType::class, array(
                'required' => false,
                'label' => 'admin.indemnisation.indemnisationParticipant',
                'attr' => array("class" => "validate-number"),
                'constraints' => array(
                    new NotBlank()
                )
            ))
            ->add('priseEnCharges', CollectionType::class, array(
                'entry_type'   => IndemnisationPriseEnChargeType::class,
                'allow_add' => false,
                'required' => false
            ))
        ;

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            /** @var Indemnisation|null $data */
            $data = $event->getData();
            if (!$data) {
                return;
            }

            $existing = new ArrayCollection();
            foreach ($data->getPriseEnCharges() as $item) {
                $existing->add($item->getPriseEnCharge());
            }

            $prisesEnCharges  = $this->entityManager->getRepository(PriseEnCharge::class)->findAll();
            foreach ($prisesEnCharges as $prisesEnCharge) {
                if (!$existing->contains($prisesEnCharge)) {
                    $data->addPriseEnCharge(new IndemnisationPriseEnCharge($data, $prisesEnCharge));
                }
            }
        });
    }
    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => Indemnisation::class,
            'group' => null
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_indemnisation';
    }


}
