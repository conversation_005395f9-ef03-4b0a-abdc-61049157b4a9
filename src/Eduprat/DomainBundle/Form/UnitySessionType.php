<?php

namespace Eduprat\DomainBundle\Form;

use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Ed<PERSON>rat\DomainBundle\Entity\UnitySession;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;

class UnitySessionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('openingDate', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
                'label' => 'admin.unitySession.openingDate',
                'required' => true,
            ))
            ->add('closingDate', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
                'label' => 'admin.unitySession.closingDate',
                'required' => true,
            ))
            ->add('unitySessionDates', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => UnitySessionDateType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
                'required' => true,
           ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UnitySession::class,
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_unity_session';
    }
}
