<?php

namespace Eduprat\DomainBundle\Form;

use Eduprat\DomainBundle\Entity\ModuleTimes;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ModuleTimesVfcType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('video_presession', IntegerType::class, array(
                'label' => 'front.course.steps.video',
                'required' => false,
            ))
            ->add('form_presession', IntegerType::class, array(
                'label' => 'front.course.steps.form_presession',
                'required' => false,
            ))
            ->add('prerestitution', IntegerType::class, array(
                'label' => $options['isVignette'] ? 'restitution.scoring' : 'front.course.steps.prerestitution',
                'required' => false,
            ))
            ->add('etutorat_1', IntegerType::class, array(
                'label' => 'front.course.steps.etutorat_1',
                'required' => false,
            ))
            ->add('fiche_action_1', IntegerType::class, array(
                'label' => 'front.course.steps.fiche_action_1',
                'required' => false,
            ))
            ->add('synthese', IntegerType::class, array(
                'label' => 'front.course.steps.synthese',
                'required' => false,
            ))
            ->add('etutorat_3', IntegerType::class, array(
                'label' => 'front.course.steps.etutorat_3',
                'required' => false,
            ))
            ->add('topos', IntegerType::class, array(
                'label' => 'front.course.steps.topos',
                'required' => false,
            ))
            ->add('tool_box', IntegerType::class, array(
                'label' => 'front.course.steps.tool_box',
                'required' => false,
            ))
            ->add('documents_pedagogiques', IntegerType::class, array(
                'label' => 'front.course.steps.documents_pedagogiques',
                'required' => false,
            ))
            ->add('end', IntegerType::class, array(
                'label' => 'front.course.steps.end',
                'required' => false,
            ))
            ->add('form_evaluation', IntegerType::class, array(
                'label' => 'front.course.steps.form_evaluation',
                'required' => false,
            ))
            // ->add('fiche_action_2', IntegerType::class, array(
            //     'label' => 'front.course.steps.fiche_action_2',
            //     'required' => false,
            // ))
            // ->add('video_postsession', IntegerType::class, array(
            //     'label' => 'front.course.steps.video_postsession',
            //     'required' => false,
            // ))
            // ->add('form_postsession', IntegerType::class, array(
            //     'label' => 'front.course.steps.form_postsession',
            //     'required' => false,
            // ))
            // ->add('etutorat_2', IntegerType::class, array(
            //     'label' => 'front.course.steps.etutorat_2',
            //     'required' => false,
            // ))
            // ->add('restitution', IntegerType::class, array(
            //     'label' => 'front.course.steps.restitution',
            //     'required' => false,
            // ))
            // ->add('progression', IntegerType::class, array(
            //     'label' => 'front.course.steps.progression',
            //     'required' => false,
            // ))
            
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ModuleTimes::class,
            'isVignette' => false,
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_module_times';
    }
}
