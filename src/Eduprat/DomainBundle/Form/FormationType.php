<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Ed<PERSON>rat\DomainBundle\Entity\Participant;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;

class FormationType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('financeModes', EntityType::class, array(
                'label' => 'admin.formation.financeMode.title',
                'class' => FinanceMode::class,
                'choice_label' => 'name',
                'placeholder' => 'admin.formation.financeMode.empty',
                'expanded' => false,
                'multiple' => true,
                'required' => true,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('f')
                        ->orderBy('f.name', 'ASC');
                },
            ))
            ->add('financeSousModes', EntityType::class, array(
                'label' => 'admin.formation.financeSousMode.title',
                'class' => FinanceSousMode::class,
                'choice_label' => function (FinanceSousMode $financeSousMode) {
                    return $financeSousMode->getIdentifiant() . ' - ' . $financeSousMode->getName() . ' (' . $financeSousMode->getPriseEnCharge() .')';
                },
                'placeholder' => 'admin.formation.financeMode.empty',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('f')
                        ->orderBy('f.identifiant', 'ASC');
                },
                'expanded' => false,
                'multiple' => true,
                'required' => true,
                'group_by' => function (FinanceSousMode $financeSousMode) {
                    return $financeSousMode->getFinanceMode()->getName();
                },
            ))
            ->add('nombrePlacesInitiales', IntegerType::class, [
                'label' => 'admin.formation.nombrePlacesInitiales',
                'required' => false,
            ])
            ->add('sessionNumber', null, array(
                'required' => false,
                'label' => 'admin.formation.sessionNumber',
            ))
            ->add('commentaireNotif', null, array(
                'required' => false,
                'label' => 'admin.formation.commentaireNotifSession',
            ))
            ->add('noMailing', CheckboxType::class, array(
                'label' => 'admin.formation.noMailing',
                'required' => false
            ))
            ->add('expert', CheckboxType::class, array(
                'label' => 'admin.formation.expert',
                'required' => false
            ))
            ->add('manualParticipantCount', null, array(
                'label' => 'admin.formation.manualParticipantCount',
                'required' => false,
                'attr' => array('actalians' => true),
            ))
            ->add('cost', null, array(
                'label' => 'admin.formation.cost',
                'required' => false,
                'attr' => array('actalians' => true, 'actalians-end' => true)
            ))
            ->add('coordinators', CollectionType::class, array(
                'required' => true,
                'label' => false,
                'entry_options' => array(
                    'label' => false,
                    'locked' => $options["locked"],
                    'formation' => $options['data'],
                ),
                'entry_type' => CoordinatorType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
           ))
            ->add('formateurs', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false,
                    'locked' => $options["locked"]
                ),
                'entry_type' => FormateurType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
            ))
            ->add('costKilometres', null, array(
                'required' => false,
                'label' => 'admin.formation.costKilometresLabel',
                'disabled' => $options["locked"],
            ))
            ->add('costBadges', null, array(
                'required' => false,
                'label' => 'admin.formation.costBadges',
                'disabled' => $options["locked"],
            ))
            ->add('costRetrocessions', null, array(
                'required' => false,
                'label' => 'admin.formation.costRetrocessions',
                'disabled' => $options["locked"],
            ))
            ->add('costMateriel', null, array(
                'required' => false,
                'label' => 'admin.formation.costMateriel',
                'disabled' => $options["locked"],
            ))
            ->add('costDivers', null, array(
                'required' => false,
                'label' => 'admin.formation.costDivers',
                'disabled' => $options["locked"],
            ))
            ->add('address', null, array(
                'required' => false,
                'label' => 'admin.formation.address.title',
            ))
            ->add('address2', null, array(
                'required' => false,
                'label' => 'admin.formation.address2.title',
            ))
            ->add('city', null, array(
                'required' => false,
                'label' => 'admin.formation.city.empty',
            ))
            ->add('zipCode', null, array(
                'required' => false,
                'label' => 'admin.formation.zipCode.empty',
            ))
            ->add('zoomId', null, array(
                'required' => false,
                'label' => 'admin.formation.zoomId',
            ))
            ->add('zoomLink', null, array(
                'required' => false,
                'label' => 'admin.formation.zoomLink',
            ))
            ->add('accessibilite', null, array(
                'label' => 'admin.formation.accessibilite',
            ))
            ->add('commentaire', null, array(
                'label' => 'admin.formation.commentaire',
            ))
            ->add('outil', null, array(
                'label' => 'admin.formation.outil',
                'required' => false,
            ))
            ->add('private', null, array(
                'label' => 'admin.formation.private',
            ))
            ->add('partenariat', ChoiceType::class, array(
                'label' => 'admin.participant.partenariat',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getPartenariats(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('ots', ChoiceType::class, array(
                'label' => 'admin.participant.ots',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getOts(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('unities', CollectionType::class, array(
                'required' => false,
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => UnitySessionType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'error_bubbling' => false,
           ))
           ->add('moduleTimes', CollectionType::class, array(
            'required' => false,
            'label' => false,
            'entry_options' => array(
                'label' => false,
                'isVignette' => $options['isVignette'],
            ),
            'entry_type' => ModuleTimesType::class,
            'allow_add'    => true,
            'allow_delete'    => true,
            'by_reference' => false,
            'error_bubbling' => false,
           ))
           ->add('moduleMinTimes', CollectionType::class, array(
            'required' => false,
            'label' => false,
            'entry_options' => array(
                'label' => false,
                'isVignette' => $options['isVignette'],
                'programme' => $options['programme'],
            ),
            'entry_type' => ModuleMinTimesType::class,
            'allow_add'    => true,
            'allow_delete'    => true,
            'by_reference' => false,
            'error_bubbling' => false,
           ))
           ->add('applyTimesToAll', CheckboxType::class, array(
            'label' => 'admin.formation.applyModuleTimesToAll',
            'required' => false,
           ))
        ;


         $builder->addEventListener(
             FormEvents::PRE_SET_DATA,
             function (FormEvent $event) {
                 /** @var Formation $data */
                 $data = $event->getData();
                 $form = $event->getForm();
                 if ($data->getProgramme()->isClasseVirtuelle()) {
                     $form->add('national', ChoiceType::class, array(
                         'required' => true,
                         'label' => 'admin.formation.national',
                         'choices' => array(
                             "Nationale" => 1,
                             "Régionale" => 0
                         )
                     ));
                 }
                 if ($data->getProgramme()->isSurSite()) {
                    $form->add('formatPeriod', ChoiceType::class, array(
                        'label' => 'admin.formation.formatPeriod',
                        'expanded' => false,
                        'multiple' => false,
                        'required' => false,
                        'choices'  => self::getFormatPeriods(),
                        'placeholder' => 'admin.global.select',
                    ));
                 }
             }
         );
    }

    public static function getPartenariats() : array
    {
        $partenariats = array (
            Participant::PART_GPM,
            Participant::PART_MSOIGNER,
            Participant::PART_MFM,
            Participant::PART_PODOLOGUE,
            Participant::PART_SITE_INTERNET,
            Participant::PART_LBI,
            Participant::PART_PHARMAZON
        );
        return array_combine($partenariats, $partenariats);
    }
    public static function getFormatPeriods() : array
    {
        $formatPeriods = array (
            Formation::FORMAT_PERIOD_JOURNEE,
            Formation::FORMAT_PERIOD_CONGRES,
        );
        return array_combine($formatPeriods, $formatPeriods);
    }


    public static function getOts() : array
    {
        $ots = array (
            Participant::OTS_CPTS,
            Participant::OTS_MSP,
            Participant::OTS_CDS,
            Participant::OTS_CH,
            Participant::OTS_CLINIQUE,
            Participant::OTS_ASSOCIATION
        );
        return array_combine($ots, $ots);
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\Formation',
            'exercisesMode' => null,
            'locked' => false,
            'isVignette' => false,
            'programme' => false,
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_formation';
    }

    /**
     * @return array
     */
    public static function getExerciseModes()
    {
        return array(
            'Monomode' => array(
                'Libéral' => 'Libéral',
                'Salarié' => 'Salarié',
                'Salarié en centre de santé conventionné' => 'Salarié en centre de santé conventionné',
                'Salarié hospitalier' => 'Salarié hospitalier',
                'Salarié de l’industrie' => 'Salarié de l’industrie'
            ),
            'Monomode 2017' => array(
                'Mixte' => 'Mixte',
                'Service de Santé des Armées' => 'Service de Santé des Armées'
            )
        );
    }

}
