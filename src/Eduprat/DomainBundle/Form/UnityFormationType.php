<?php

namespace Eduprat\DomainBundle\Form;

use Ed<PERSON><PERSON>\DomainBundle\Entity\Programme;
use Ed<PERSON>rat\DomainBundle\Entity\UnityFormation;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use <PERSON>ymfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UnityFormationType extends AbstractType
{
    
    private ParameterBagInterface $parameterBag;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $this->parameterBag = $parameterBag;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $guidedType = $options["guidedType"];
        $builder
            ->add('method', ChoiceType::class, array(
                'label' => 'admin.unityFormation.method',
                'required' => true,
                'choices'  => array_flip(Programme::METHODS),
                'placeholder' => 'admin.unityFormation.empty',
                'disabled' => $guidedType
            ))
            ->add('presence', ChoiceType::class, array(
                'label' => 'admin.unityFormation.presence',
                'required' => true,
                'choices'  => ProgrammeType::getPresences(),
                'placeholder' => 'admin.unityFormation.empty',
                'attr' => array(
                    'class' => 'presence'
                ),
                'choice_attr' => function($c) {
                    return $this->getPresencesInfos($c);
                },
                'disabled' => $guidedType

            ))
            ->add('descriptif', null, array(
                'label' => 'admin.unityFormation.descriptif',
                'required' => false,
                'attr' => array(
                    'class' => 'tinymce',
                )
            ))
            ->add('descriptifConnected', null, array(
                'label' => 'admin.unityFormation.descriptifConnected',
                'required' => false,
                'attr' => array(
                    'class' => 'tinymce',
                )
            ))
            ->add('descriptifOffline', null, array(
                'label' => 'admin.unityFormation.descriptifOffline',
                'required' => false,
                'attr' => array(
                    'class' => 'tinymce descriptifOffline',
                )
            ))
            ->add('nbHoursConnected', null, array(
                'label' => 'admin.unityFormation.nbHoursConnected',
                'required' => false,
                'attr' => array(
                    'class' => 'elearning-hours',
                )
            ))
            ->add('nbHoursOffline', null, array(
                'label' => 'admin.unityFormation.nbHoursOffline',
                'required' => false,
                'attr' => array(
                    'class' => 'elearning-hours',
                )
            ))
            ->add('minTimeConnected', null, array(
                'label' => 'admin.unityFormation.minTimeConnected',
                'required' => false
            ))
            ->add('nbDays', null, array(
                'label' => 'admin.unityFormation.nbDays',
                'required' => false
            ))
            ->add('nbHours', null, array(
                'label' => 'admin.unityFormation.nbHours',
                'required' => false,
                'attr' => array(
                    'class' => 'hours',
                )
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UnityFormation::class,
            'guidedType' => false,
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_unity_formation';
    }

    /**
     * @return array
     */
    public function getPresencesInfos($presence)
    {
        $infos = array();
        switch ($presence) {
            case Programme::PRESENCE_SITE:
                $infos["additionalInfos"] = '';
                $infos["firstAdditionalInfosPictureFrom"] = null;
                $infos["secondAdditionalInfosPictureFrom"] = null;
                $infos["firstAdditionalInfosPictureLink"] = null;
                $infos["secondAdditionalInfosPictureLink"] = null;
                break;
            case Programme::PRESENCE_VIRTUELLE:
                $infos["additionalInfos"] = '<span style="font-weight: bold">LES CLASSES VIRTUELLES EDUPRAT</span><br>
                1- Je télécharge et j\'installe ZOOM (Sur ordinateur ou sur tablette)<br>
                2- Le jour de ma formation, réception d\'un mail,avec les informations de connexion<br>
                <u>Au moment de la formation</u> : Ouvrir Zoom > Saisir l\'ID de réunion > Saisir votre NOM Prénom > <span style="font-weight: bold">LA FORMATION
                LIVE PEUT COMMENCER</span>';
        }
        return array(
            "data-infos" => json_encode($infos)
        );
    }
}
