<?php

namespace Eduprat\DomainBundle\Form\DataTransformer;

use Doctrine\ORM\EntityManagerInterface;
use <PERSON><PERSON>rat\DomainBundle\Entity\Programme;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;

class ReferenceToProgrammeTransformer implements DataTransformerInterface
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Transforms an object (programme) to a string (number).
     *
     * @param ?Programme $programme
     */
    public function transform($programme): string
    {
        if (null === $programme) {
            return '';
        }

        return $programme->getReference();
    }

    /**
     * Transforms a string (number) to an object (issue).
     *
     * @param string $programmeReference
     * @throws TransformationFailedException if object (issue) is not found.
     */
    public function reverseTransform($programmeReference): ?Programme
    {
        // no issue number? It's optional, so that's ok
        if (!$programmeReference) {
            return null;
        }

        $programme = $this->entityManager
            ->getRepository(Programme::class)
            ->findOneBy(['reference' => $programmeReference]);

        if (null === $programme) {
            // causes a validation error
            // this message is not shown to the user
            // see the invalid_message option
            throw new TransformationFailedException(sprintf(
                'Aucun programme ne possède cette référence ("%s").',
                $programmeReference
            ));
        }

        return $programme;
    }
}