<?php

namespace Eduprat\DomainBundle\Form\TCS;

use Ed<PERSON><PERSON>\DomainBundle\DTO\QuestionTCSCreateDto;
use Ed<PERSON>rat\DomainBundle\Entity\SurveyQuestionCategory;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class QuestionTCSType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('libelleSiVousPensiez', TextareaType::class, [
                'label' => 'Libellé "si vous pensez"',
                "required" => true,
                'attr' => [
                    'class' => 'tinymce',
                ],
            ])
            ->add('libelleEtQuAlorsVousTrouvez', TextareaType::class, [
                'label' => 'Libellé "et qu\'alors vous trouvez"',
                "required" => true,
                'attr' => [
                    'class' => 'tinymce',
                ],
            ])
            ->add('category', EntityType::class, [
                'class' => SurveyQuestionCategory::class,
                'choice_label' => 'name',
                'placeholder' => 'Choisir une catégorie',
                'label' => 'Catégorie',
            ])
            ->add('reponses', CollectionType::class, [
                'entry_type' => ReponseTCSType::class,
                'disabled' => true,
                'allow_add' => false,
                'allow_delete' => false,
                'label' => 'Réponses',
            ])
        ;

        $builder->addEventListener(FormEvents::POST_SUBMIT, function ($event) {
            // On supprime le tag <p> créé par ce petit malin de tinyMce
            /** @var QuestionTCSCreateDto $entity */
            $entity = $event->getData(); // The Form Object
            $libelleSiVousPensiez = str_replace(["<p>", "</p>"], ['',''], $entity->libelleSiVousPensiez);
            $entity->libelleSiVousPensiez = $libelleSiVousPensiez;

            $libelleEtQuAlorsVousTrouvez = str_replace(["<p>", "</p>"], ['',''], $entity->libelleEtQuAlorsVousTrouvez);
            $entity->libelleEtQuAlorsVousTrouvez = $libelleEtQuAlorsVousTrouvez;
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => QuestionTCSCreateDto::class,
        ]);
    }
}
