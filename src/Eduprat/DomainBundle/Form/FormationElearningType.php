<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\ORM\EntityRepository;
use Ed<PERSON>rat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\Elearning;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\Survey;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FormationElearningType extends FormationType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder->addEventListener(
            FormEvents::PRE_SET_DATA,
            array($this, 'presetData')
        );

    }

    /**
     * @param FormEvent $event
     */
    public function preSetData(FormEvent $event) {
        $form = $event->getForm();
        /** @var FormationElearning $data */
        $data = $event->getData();

        $form
            ->add('formOpeningDate', DateTimeType::class, array(
                'required' => true,
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
                'label' => 'admin.formation.formOpeningDate',
            ))
            ->add('formClosingDate', DateTimeType::class, array(
                'required' => true,
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
                'label' => 'admin.formation.formClosingDate',
            ))
        ;
        
        if ($data->isFormVignetteAudit()) {
            if ($data->isVignetteFirst()) {
                // if (is_null($data->getAudit()) || !$data->formAlreadyAnswered()) {
                    $canEdit = is_null($data->getAudit()) || !$data->formAlreadyAnswered();
                    $form->remove("audit");
                    $form->add('audit', EntityType::class, array(
                        'label' =>'admin.formation.vignette_audit.vignettePre',
                        'class' => Audit::class,
                        'disabled' => !$canEdit,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type = :vignette')->setParameter('vignette', Audit::TYPE_VIGNETTE)
                                ->andWhere('q.vignetteType = :vignetteType')->setParameter('vignetteType', 'Pré')
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                // }

                // if (is_null($data->getAudit2()) || !$data->form2AlreadyAnswered()) {
                    $canEdit2 = is_null($data->getAudit2()) || !$data->form2AlreadyAnswered();
                    $typeForm = Audit::TYPE_AUDIT;
                    $form->add('audit2', EntityType::class, array(
                        'label' =>'admin.formation.vignette_audit.auditPost',
                        'class' => Audit::class,
                        'disabled' => !$canEdit2,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data, $typeForm) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type IN (:types)')->setParameter('types', array($typeForm))
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if ($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                // }
            } else {
                // if (is_null($data->getAudit()) || !$data->formAlreadyAnswered()) {
                    $canEdit = is_null($data->getAudit()) || !$data->formAlreadyAnswered();
                    $form->remove("audit");
                    $typeForm = Audit::TYPE_AUDIT;
                    $form->add('audit', EntityType::class, array(
                        'label' =>'admin.formation.vignette_audit.auditPre',
                        'class' => Audit::class,
                        'disabled' => !$canEdit,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data, $typeForm) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type IN (:types)')->setParameter('types', array($typeForm))
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if ($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                // }

                // if (is_null($data->getAudit2()) || !$data->form2AlreadyAnswered()) {
                    $canEdit2 = is_null($data->getAudit2()) || !$data->form2AlreadyAnswered();
                    $form->add('audit2', EntityType::class, array(
                        'label' =>'admin.formation.vignette_audit.vignettePost',
                        'class' => Audit::class,
                        'disabled' => !$canEdit2,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type = :vignette')->setParameter('vignette', 'vignette')
                                ->andWhere('q.vignetteType = :vignetteType')->setParameter('vignetteType', 'Post')
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                // }
            }
        } else {
            // if (is_null($data->getQuestionnaire()) || !$data->formAlreadyAnswered()) {
                if($data->getFormType() == "survey") {
                    $canEdit = is_null($data->getQuestionnaire()) || !$data->formAlreadyAnswered();
                    $form->add('questionnaire', EntityType::class, array(
                        'label' => 'admin.formation.presentielle.title',
                        'class' => Survey::class,
                        'disabled' => !$canEdit,
                        'choice_label' => 'label',
                        'placeholder' => 'admin.formation.presentielle.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data) {
                            // On ne veut pas les questionnaires qui n'ont pas de questions
                            $er = $er->createQueryBuilder('q')->leftJoin('q.category', 'category')->innerJoin('q.questions', 'questions')->orderBy('category.name')->addOrderBy('q.label', 'ASC');
                            if($data->getProgramme()->getCategory()) {
                                $er->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $er;
                        },
                        'group_by' => 'category.name',
                    ));
                } elseif ($data->getProgramme()->getFormType() == Formation::FORM_TYPE_VIGNETTE) {
                    // if (is_null($data->getAudit()) || !$data->formAlreadyAnswered()) {
                        $canEdit = is_null($data->getAudit()) || !$data->formAlreadyAnswered();
                        $form->remove("audit");
                        $form->add('audit', EntityType::class, array(
                            'label' => 'admin.formation.vignette.audit1',
                            'class' => Audit::class,
                            'disabled' => !$canEdit,
                            'choice_label' => function($audit) {
                                $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                                return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                            },
                            'placeholder' => 'admin.formation.audit.empty',
                            'required' => false,
                            'query_builder' => function (EntityRepository $er) use ($data) {
                                // On ne veut pas les audits qui n'ont pas de questions
                                $qb = $er->createQueryBuilder('q');
                                $qb->leftJoin('q.category', 'category')
                                    ->leftJoin('q.questions', 'questions')
                                    ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                    ->where($qb->expr()->orX(
                                        $qb->expr()->isNotNull('questions.id'),
                                        $qb->expr()->isNotNull('surveyQuestions.id')
                                    ))
                                    ->andWhere('q.archived = false')
                                    ->andWhere('q.type = :vignette')->setParameter('vignette', Audit::TYPE_VIGNETTE)
                                    ->andWhere('q.vignetteType = :vignetteType')->setParameter('vignetteType', 'Pré')
                                    ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                    ->orderBy('category.name')
                                    ->addOrderBy('q.label', 'ASC');
                                if($data->getProgramme()->getCategory()) {
                                    $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                                }
                                return $qb;
                            },
                            'group_by' => 'category.name',
                        ));
                    // }
                }
                
                else {
                    $canEdit = is_null($data->getAudit()) || !$data->formAlreadyAnswered();
                    $typeForm = $data->getProgramme()->getFormType() == Formation::FORM_TYPE_PREDEFINED ? Audit::TYPE_PREDEFINED : Audit::TYPE_AUDIT;
                    $form->add('audit', EntityType::class, array(
                        'label' => $data->getProgramme()->getFormType() == Formation::FORM_TYPE_PREDEFINED  ? 'admin.formation.audit.titlePredefined' : 'admin.formation.audit.title',
                        'class' => Audit::class,
                        'disabled' => !$canEdit,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data, $typeForm) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type IN (:types)')->setParameter('types', array($typeForm))
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if ($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                }
            // }

            if ($data->getProgramme()->getFormType() == Formation::FORM_TYPE_VIGNETTE) {
                // if (is_null($data->getAudit2()) || !$data->form2AlreadyAnswered()) {
                    $canEdit2 = is_null($data->getAudit2()) || !$data->form2AlreadyAnswered();
                    $form->add('audit2', EntityType::class, array(
                        'label' => 'admin.formation.vignette.audit2',
                        'class' => Audit::class,
                        'disabled' => !$canEdit2,
                        'choice_label' => function($audit) {
                            $date = $audit->getUpdatedAt() ? $audit->getUpdatedAt()->format("d/m/Y") : $audit->getCreatedAt()->format("d/m/Y");
                            return $audit->getLabel() . " (Date de dernière validation : " . $date . ")";
                        },
                        'placeholder' => 'admin.formation.audit.empty',
                        'required' => false,
                        'query_builder' => function (EntityRepository $er) use ($data) {
                            // On ne veut pas les audits qui n'ont pas de questions
                            $qb = $er->createQueryBuilder('q');
                            $qb->leftJoin('q.category', 'category')
                                ->leftJoin('q.questions', 'questions')
                                ->leftJoin('q.surveyQuestions', 'surveyQuestions')
                                ->where($qb->expr()->orX(
                                    $qb->expr()->isNotNull('questions.id'),
                                    $qb->expr()->isNotNull('surveyQuestions.id')
                                ))
                                ->andWhere('q.archived = false')
                                ->andWhere('q.type = :vignette')->setParameter('vignette', 'vignette')
                                ->andWhere('q.vignetteType = :vignetteType')->setParameter('vignetteType', 'Post')
                                ->andWhere('q.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear())
                                ->orderBy('category.name')
                                ->addOrderBy('q.label', 'ASC');
                            if($data->getProgramme()->getCategory()) {
                                $qb->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                            }
                            return $qb;
                        },
                        'group_by' => 'category.name',
                    ));
                // }
            }
        }

        $canChangeElearning = true;
        foreach ($data->getParticipations() as $participation) {
            if ($participation->getElearningCourse() !== []) {
                $canChangeElearning = false;
            }
        }

        if ($canChangeElearning) {
            $form->add('elearning', EntityType::class, array(
                'label' => 'admin.formation.presentielle.elearning',
                'class' => Elearning::class,
                'choice_label' => 'label',
                'placeholder' => 'admin.formation.presentielle.empty',
                'required' => false,
                'query_builder' => function (EntityRepository $er) use ($data) {
                    $er = $er->createQueryBuilder('e')->leftJoin('e.category', 'category')->orderBy('category.name')->addOrderBy('e.label', 'ASC');
                    if($data->getProgramme()->getCategory()) {
                        $er->andWhere('category = :category')->setParameter('category', $data->getProgramme()->getCategory()->getId());
                    }
                    $er->andWhere('e.year = :programmeYear')->setParameter('programmeYear', $data->getProgramme()->getYear());
                    $er->andWhere('e.archived = false');
                    return $er;
                },
                'group_by' => 'category.name',
            ));
        }

    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\FormationElearning',
            'exercisesMode' => null,
            'locked' => false,
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_formation';
    }
}
