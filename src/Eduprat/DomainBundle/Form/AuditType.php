<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;


use Symfony\Component\OptionsResolver\OptionsResolver;

class AuditType extends AbstractType
{
    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @param TranslatorInterface $translator
     */
    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if(isset($options['data']) && $options['data']->getIntroduction() != '' ) {
            $intro = $options['data']->getIntroduction();
        }
        else {
            $intro = $this->translator->trans('audit.notice.more').$this->translator->trans('audit.notice.list.1').$this->translator->trans('audit.notice.list.2').$this->translator->trans('audit.notice.list.3');
        }

        $builder
            ->add('label', null, array(
                'label' => 'admin.audit.label',
            ))
            ->add('pdfFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => true,
                'translation_domain' => 'messages',
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'application/pdf', 'application/x-pdf'
                        )
                    )
                ),
                'attr' => array(
                    'class' => 'activity-content audit-pdfFile'
                )
            ))
            ->add('type', HiddenType::class, array(
                'data' => $options['data']->getType()
            ));

        if ($options['data']->isDefaultType()) {
            $builder->add('nbPatients', null, array(
                'label' => 'admin.audit.nbPatients',
            ));
        }

        $builder
            ->add('questions', CollectionType::class, array(
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => AuditQuestionType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
            ))
            ->add('introduction', null, array(
                'label' => 'admin.audit.introduction',
                'data' => $intro,
                'attr' => array(
                    'class' => 'tinymce',
                ),
            ))
            ->add('category', EntityType::class, array(
                'class' => 'Eduprat\DomainBundle\Entity\AuditCategory',
                'label'    => 'admin.audit.category.label',
                'choice_label' => 'name',
                'expanded' => false,
                'multiple' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('c')
                        ->orderBy('c.name', 'ASC');
                },
            ))
            ->add('year', ChoiceType::class, array(
                'label' => 'admin.programme.year',
                'required' => true,
                'choices'  => self::getYears(),
                'placeholder' => 'admin.global.select',
            ));

    }
    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\Audit',
            'type' => null
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_audit';
    }

    public function getYears() {
        $yearArray = [];
        $year = 2017;
        while ($year <= date("Y")+1) {
            $yearArray[$year] = $year;
            $year++;
        }
        return $yearArray;
    }


}
