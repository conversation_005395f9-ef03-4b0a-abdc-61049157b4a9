<?php

namespace Eduprat\DomainBundle\Form;

use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PatientDescriptionPredefinedType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('description', null, array(
                'label' => 'admin.audit.' . ($options["type"] === "predefined" ? "patientDescription" : "vignetteDescription"),
            ))
            ->add('images', CollectionType::class, array(
                'entry_type'   		=> ImageType::class,
                'prototype'			=> true,
                'allow_add'			=> true,
                'allow_delete'		=> true,
                'by_reference' 		=> false,
                'required'			=> false,
                'label'			=> false,
                'entry_options' => array(
                    'label' => false
                ),
                'attr' => array(
                    'class' => 'images'
                ),
                'prototype_name' => '__image__',
            ))
            ->add('patient', HiddenType::class, array(
                'label' => 'admin.question.patient',
                'attr' => array(
                    'class' => 'audit-patient-description'
                )
            ))
        ;
    }
    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\PatientDescriptionPredefined',
            'type' => null,
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_patient_description';
    }

}
