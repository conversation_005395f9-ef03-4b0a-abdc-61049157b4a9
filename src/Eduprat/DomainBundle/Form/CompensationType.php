<?php

namespace Eduprat\DomainBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CompensationType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        if ($options["profession"]) {
            $builder
                ->add('profession', TextType::class, array(
                    'attr' => array(
                        'readonly' => true,
                    ),
                    'label' => 'admin.compensation.profession',
                    'data' => $options["profession"],
                ));
        }

        $builder
            ->add('nbHours', TextType::class, array(
                'label' => 'admin.compensation.nbHours',
            ))
            ->add('compensationParticiant', TextType::class, array(
                'required' => false,
                'label' => 'admin.compensation.compensationParticiant',
            ))
            ->add('price', TextType::class, array(
                'label' => 'admin.compensation.price',
            ))
            ->add('budgetCR', TextType::class, array(
                'required' => false,
                'label' => 'admin.compensation.budgetCR',
            ));
    }
    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\Compensation',
            'profession' => null
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_compensation';
    }


}
