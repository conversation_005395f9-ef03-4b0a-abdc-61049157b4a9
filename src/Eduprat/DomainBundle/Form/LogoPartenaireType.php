<?php

namespace Eduprat\DomainBundle\Form;

use Ed<PERSON><PERSON>\DomainBundle\Entity\LogoPartenaire;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;

class LogoPartenaireType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('logoName', TextType::class, [
                'label' => 'admin.logoPartenaire.form.logoName',
            ])
            ->add('file', FileType::class, array(
                'label' 	=> 'admin.logoPartenaire.form.file',
                'required' 	=> !$options['edit'],
                'constraints' => array(
                    new File(
                        array(
                            'mimeTypes' => array(
                                'image/jpeg', 'image/png'
                            ),
                            "mimeTypesMessage" => 'admin.logoPartenaire.mimeTypes',
                        )
                    ),
                ),
            ));
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LogoPartenaire::class,
            'edit' => false
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_logo_partenaire';
    }
}