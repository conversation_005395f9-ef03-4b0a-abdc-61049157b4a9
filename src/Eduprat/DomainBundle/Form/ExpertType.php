<?php

namespace Eduprat\DomainBundle\Form;

use <PERSON><PERSON><PERSON>\DomainBundle\DTO\ExpertDto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ExpertType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstName', TextType::class, ['label' => 'admin.tcs.expert.first_name'])
            ->add('lastName', TextType::class, ['label' => 'admin.tcs.expert.last_name'])
            ->add('email', EmailType::class, ['label' => 'admin.tcs.expert.email'])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ExpertDto::class,
        ]);
    }
}
