<?php

namespace Eduprat\DomainBundle\Form;

use Ed<PERSON><PERSON>\DomainBundle\Entity\IndemnisationPriseEnCharge;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

class IndemnisationPriseEnChargeType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        $builder
            ->add('price', TextType::class, array(
                'label' => false,
                'required' => false,
                'attr' => array("class" => "validate-number")
            ))
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => IndemnisationPriseEnCharge::class,
            'group' => null
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function finishView(FormView $view, FormInterface $form, array $option): void
    {
        $entity = $form->getData();
        $view->vars['label'] = $entity->getPriseEnCharge()->getName();
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_indemnisationPriseEnCharge';
    }


}
