<?php

namespace Eduprat\DomainBundle\DataFixtures;

use Eduprat\DomainBundle\Entity\AuditCategory;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Repository\AuditCategoryRepository;

class ProgrammeFixtures extends AbstractDependantEntityFixtures
{
    const REFERENCE_ENTITY1 = '57202325690';
    public function getDatasToLoad(): array
    {
        /** @var AuditCategoryRepository $auditCategoryRepo */
        $auditCategoryRepo = $this->doctrine->getRepository(AuditCategory::class);
        $firstEntityData = self::getFirstEntityData();
        $firstEntityData['category'] = $auditCategoryRepo->findOneBy(['name' => AuditCategoryFixtures::CARDIOLOGIE]);
        return [
            $firstEntityData,
        ];
    }

    protected function getEntityClass(): string
    {
        return Programme::class;
    }

    public static function getFirstEntityData(): array
    {
        return array(
            'title' => 'Classe virtuelle - La dyspnée chez l’enfant - COPIE',
            'picture' => '6421f3129b51d696335217.jpeg',
            'certifying' => '0',
            'reference' => self::REFERENCE_ENTITY1,
            'categories' => ["Médecin"],
            'specialities' => ["Médecine générale"],
            'exercisesMode' => ["Libéral", "Salarié", "Salarié en centre de santé conventionné", "Salarié hospitalier"],
            'nationalOrientation' => '<p>M&eacute;decins sp&eacute;cialis&eacute;s en m&eacute;decine g&eacute;n&eacute;rale :</p>',
            'durationPresentielle' => '3',
            'durationNotPresentielle' => '5',
            'year' => '2023',
            'presence' => 'Sur site',
            'cout' => 'Nous contacter : <EMAIL> ou 05 56 51 65 14.',
            'prerequis' => 'Aucun',
            'sessionType' => 'formation_vignette_audit',
            'andpcStatus' => 'published',
            'method' => 'integre',
            'format' => 'Mixte',
            'category' => '17',
            'firstAdditionalInfosPicture' => '63ebc58275277154990702.png',
            'secondAdditionalInfosPicture' => '63ebc58275938479970790.png',
            'firstAdditionalInfosPictureLink' => 'https://play.google.com/store/apps/details?id=fr.heurisko.eduprat',
            'secondAdditionalInfosPictureLink' => 'https://apps.apple.com/us/app/eduprat-formations/id1501527019#?platform=iphone',
            'additionalInfos' => '',
            'formType' => 'vignette_audit',
        );
    }

    public function getDependencies(): array
    {
        return [
            ResetAutoIncrementFixtures::class,
            AuditCategoryFixtures::class,
        ];
    }
}