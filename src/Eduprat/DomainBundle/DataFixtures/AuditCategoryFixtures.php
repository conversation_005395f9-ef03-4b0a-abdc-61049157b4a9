<?php

namespace Eduprat\DomainBundle\DataFixtures;

use Eduprat\DomainBundle\Entity\AuditCategory;

class AuditCategoryFixtures extends AbstractDependantEntityFixtures
{
    const CARDIOLOGIE = 'Cardiologie';
    const PEDIATRIE = 'Pédiatrie';
    public function getDatasToLoad(): array
    {
        return [
            ['name' => self::CARDIOLOGIE,'color' => '#c61010'],
            ['name' => 'Dermatologie',],
            ['name' => 'Urgences',],
            ['name' => 'Ostéopathie',],
            ['name' => 'Rhumatologie',],
            ['name' => 'Psychiatrie',],
            ['name' => 'Angiologie',],
            ['name' => 'Endocrinologie',],
            ['name' => 'Prise en charge des pathologies de l\'appareil locomoteur',],
            ['name' => 'Médecine interne',],
            ['name' => 'Gynécologie',],
            ['name' => self::PEDIATRIE,],
            ['name' => '<PERSON><PERSON><PERSON>trie',],
            ['name' => 'Hématologie',],
            ['name' => 'Podologie',],
            ['name' => 'Oncologie',],
            ['name' => 'Ophtalmologie',],
            ['name' => 'Chirurgie',],
            ['name' => 'Pneumologie',],
            ['name' => 'Médecine infectieuse',],
            ['name' => 'Télémédecine',],
            ['name' => 'Immunologie',],
            ['name' => 'Gastro-Hépato-Entéro',],
            ['name' => 'Douleur',],
            ['name' => 'ORL',],
            ['name' => 'Neurologie',],
            ['name' => 'Orthopédie',],
            ['name' => 'Urologie',],
            ['name' => 'Infectiologie','color' => '#084605'],
            ['name' => 'Néphrologie',],
            ['name' => 'Médecine du sport',],
            ['name' => 'Hypnose',],
            ['name' => 'Addictologie',],
            ['name' => 'Interrogatoire - Épidemiologie',],
            ['name' => 'Biologiste',],
            ['name' => 'Aromathérapie',],
            ['name' => 'Autres',],
            ['name' => 'Technicien de laboratoire médical',],
            ['name' => 'IDE',],
            ['name' => 'Education thérapeutique',],
            ['name' => 'Dentiste',],
            ['name' => 'Radiologie',],
            ['name' => 'Nutrition',],
            ['name' => 'LBI',],
            ['name' => 'Management',],
            ['name' => 'Innovation','color' => '#fff0f0'],
        ];
    }

    protected function getEntityClass(): string
    {
        return AuditCategory::class;
    }


    public function getDependencies(): array
    {
        return [
            ResetAutoIncrementFixtures::class,
        ];
    }
}