<?php

namespace Eduprat\DomainBundle\DataFixtures;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Repository\ProgrammeRepository;

class FormationFixtures extends AbstractDependantEntityFixtures
{
    public function getDatasToLoad(): array
    {
        /** @var ProgrammeRepository $programmeRepo */
        $programmeRepo = $this->doctrine->getRepository(Programme::class);


        return [
            [
                "reference" => "23.001",
                "nombrePlacesInitiales" => 43,
                "startDate" => (new \DateTime())->setDate(2023, 4, 22)->setTime(0,0,0),
                "openingDate" => (new \DateTime())->setDate(2023, 4, 22)->setTime(0,0,0),
                "closingDate" => (new \DateTime())->setDate(2023, 6, 6)->setTime(0,0,0),
                "endDate" => (new \DateTime())->setDate(2023, 6, 6)->setTime(0,0,0),
                "address" => null,
                "address2" => null,
                "city" => null,
                "zipCode" => null,
                "region" => null,
                "departement" => null,
                'programme' => $programmeRepo->findOneBy(['reference' => ProgrammeFixtures::REFERENCE_ENTITY1]),
            ]
        ];
    }

    protected function getEntityClass(): string
    {
        return Formation::class;
    }

    public function getDependencies(): array
    {
        return [
            ResetAutoIncrementFixtures::class,
            ProgrammeFixtures::class,
        ];
    }
}