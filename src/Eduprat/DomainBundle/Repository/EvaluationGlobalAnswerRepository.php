<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\EvaluationGlobalManager;
use Eduprat\DomainBundle\Entity\EvaluationGlobalQuestion;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\Coordinator;
use Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSearch;

/**
 * EvaluationGlobalAnswerRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class EvaluationGlobalAnswerRepository extends EntityRepository
{
    /**
     * @param Programme $programme
     * @param $role
     * @return array
     */
    public function findByProgrammeRole(Programme $programme, $role) {
        $qb = $this->createQueryBuilder("e")
            ->innerJoin('e.formation', 'f')
            ->innerJoin('e.person', 'pe')
            ->leftJoin('pe.participant', 'pa');
        $qb->where('f.programme = :programme')->setParameter('programme', $programme->getId());
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $this->byRole($qb, $role);
        return $qb->getQuery()->getResult();
    }

    public function globalAverageAndCountParticipant(array $formations = []) : array
    {
        $questions = [];
        array_map(function($formation) use (&$questions) {
            $tmpQuestions = EvaluationGlobalManager::getAllQuestionsForRole($formation, Person::ROLE_PARTICIPANT)->toArray();
            foreach ($tmpQuestions as $tmpQuestion) {
                /** @var EvaluationGlobalQuestion $tmpQuestion */
                if (in_array($tmpQuestion->getType(), [EvaluationGlobalQuestion::TYPE_NOTE_OPTIONAL, EvaluationGlobalQuestion::TYPE_NOTE])) {
                    $questions[] = '"'.$tmpQuestion->getIndex().'"';
                }
            }
        }, $formations);
        $questions = array_unique($questions);

        if (!isset($formations[0]) || empty($questions)) {
            return [
                'votants' => null,
                'average' => null,
            ];
        }

        $conn = $this->getEntityManager()->getConnection();
        $sql = 'SELECT COUNT(DISTINCT (p0_.id)) AS votants, AVG(e1_.answer) AS moyenne 
                FROM evaluation_global_answer e1_ 
                INNER JOIN person p0_ ON e1_.person = p0_.id 
                INNER JOIN participant p2_ ON p0_.id = p2_.user_id  
                INNER JOIN formation f3_ ON e1_.formation = f3_.id 
                INNER JOIN participation p3_ ON p3_.participant = p2_.id AND p3_.formation = f3_.id
                WHERE f3_.programme = :programmeId 
                  AND f3_.archived = false
                  AND p0_.isArchived = false
                  AND p3_.archived = false
                  AND e1_.question in ('.implode(',', $questions).')
                  AND e1_.answer <> 0';
        $stmt = $conn->prepare($sql);
        $qb = $stmt->executeQuery([
            'programmeId' => $formations[0]->getProgramme()->getId(),
        ]);
        $res = $qb->fetchAllAssociative();

        return [
            'votants' => $res[0]['votants'] ?? null,
            'average' => $res[0]['moyenne'] ? round($res[0]['moyenne'], 2) : null,
        ];
    }

    /**
     * @param Formation $formation
     * @param $role
     * @return array
     */
    public function findByFormationRole(Formation $formation, $role) {
        $qb = $this->createQueryBuilder("e")
            ->innerJoin('e.formation', 'f')
            ->innerJoin('e.person', 'pe')
            ->leftJoin('pe.participant', 'pa');
        $qb->where('f.id = :formation')->setParameter('formation', $formation->getId());
        $this->byRole($qb, $role);
        return $qb->getQuery()->getResult();
    }

    /**
     * @todo delete
     * @param Programme $programme
     * @param Person $person
     * @return array
     */
    public function findByProgrammePerson(Programme $programme, Person $person)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->where('e.programme = :programme')->setParameter('programme', $programme->getId());
        $qb->andWhere('e.person = :person')->setParameter('person', $person->getId());
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Formation $formation
     * @param Person $person
     * @return array
     */
    public function findByFormationPerson(Formation $formation, Person $person)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->where('e.formation = :formation')->setParameter('formation', $formation->getId());
        $qb->andWhere('e.person = :person')->setParameter('person', $person->getId());
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Formateur $former
     * @return array
     */
    public function findByFormer(Formateur $former, $role = null)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->innerJoin('e.formation', 'f')
            ->innerJoin('e.person', 'pe')
            ->leftJoin('pe.participant', 'pa');
        $qb->where('e.former = :former')->setParameter('former', $former->getId());
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        if ($role) {
            $this->byRole($qb, $role);
        }
        return $qb->getQuery()->getResult();
    }

    /**
     * @param QueryBuilder $qb
     * @param $year
     */
    public function byYear(QueryBuilder $qb, $year) {
        $qb->andWhere('f.startDate BETWEEN :start AND :end')
            ->setParameter('start', (new \DateTime("First day of January " . $year))->format('Y-m-d'))
            ->setParameter('end', (new \DateTime("First day of January " . ($year + 1)))->format('Y-m-d'))
        ;
    }

    /**
     * @param QueryBuilder $qb
     * @param $role
     */
    public function byRole(QueryBuilder $qb, $role) {
        if ($role === Person::ROLE_PARTICIPANT) {
            $qb->andWhere('pa.user is not null');
            $qb->leftJoin('f.participations', 'par')
                ->andWhere('par.archived = false');
        } else {
            $qb->andWhere('JSON_SEARCH(pe.roles, \''. JsonSearch::MODE_ONE.'\', '. ':role) is not null');
            $qb->setParameter('role',  $role);
        }
    }

    /**
     * @param Person $person
     * @param $year
     * @return array
     */
    public function findByFormerYear(Person $person, $year)
    {
        $qb = $this->createQueryBuilder("e")
            ->innerJoin('e.formation', 'f')
            ->innerJoin('e.former', 'fo')
            ->where('fo.person = :person')->setParameter("person", $person->getId());
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $this->byYear($qb, $year);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Person $person
     * @param $year
     * @return array
     */
    public function findByCoordinatorYear(Person $person, $year)
    {
        $qb = $this->getCoordinatorQb();

        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->like('pe.roles', $qb->expr()->literal("%" . Person::ROLE_FORMER . "%")),
                $qb->expr()->orX(
                    $qb->expr()->andX(
                        $qb->expr()->isNotNull('pa.user'),
                        $qb->expr()->isNull('pas.coordinator'),
                        $qb->expr()->eq('c1.person', $person->getId())
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->isNotNull('pa.user'),
                        $qb->expr()->isNotNull('pas.coordinator'),
                        $qb->expr()->eq('c2.person', $person->getId())
                    )
                )
            )
        );

        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $this->byYear($qb, $year);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Person $person
     * @param Formation|null $formation
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     */
    public function findByCoordinatorParticipantBase(Person $person, Formation $formation = null, \DateTime $startDate = null, \DateTime $endDate = null)
    {
        $qb = $this->getCoordinatorQb();

        $qb
            ->andWhere('pas.formation = f.id')
            ->andWhere('pas.archived = false')
        ;
        
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->andX(
                    $qb->expr()->isNotNull('pa.user'),
                    $qb->expr()->isNull('pas.coordinator'),
                    $qb->expr()->eq('c1.person', $person->getId())
                ),
                $qb->expr()->andX(
                    $qb->expr()->isNotNull('pa.user'),
                    $qb->expr()->isNotNull('pas.coordinator'),
                    $qb->expr()->eq('c2.person', $person->getId())
                )
            )
        );

        if ($formation) {
            $qb->andWhere('f.id = :formation')->setParameter('formation', $formation->getId());
        }

        if ($startDate && $endDate) {
            $qb->andWhere('f.startDate BETWEEN :start AND :end')
                ->setParameter('start', ($startDate)->format('Y-m-d'))
                ->setParameter('end', ($endDate)->format('Y-m-d'))
            ;
        }

        $qb->andWhere($qb->expr()->like('pe.roles', "'[]'"));

        return $qb->getQuery()->getResult();
    }

    /**
     * @param Coordinator $coordinator
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     */
    public function findByCoordinatorParticipant(Coordinator $coordinator)
    {
        return $this->findByCoordinatorParticipantBase($coordinator->getPerson(), $coordinator->getFormation());
    }

    /**
     * @param Person $person
     * @param $year
     * @return array
     * @throws \Exception
     */
    public function findByCoordinatorYearParticipant(Person $person, $year)
    {
        $startDate = (new \DateTime("First day of January " . $year));
        $endDate = (new \DateTime("First day of January " . ($year + 1)));
        return $this->findByCoordinatorParticipantBase($person, null, $startDate, $endDate);
    }

    /**
     * @param Person $person
     * @param $year
     * @return array
     */
    public function findByCoordinatorYearFormer(Person $person, $year)
    {
        $qb = $this->getCoordinatorQb();
        $qb->andWhere("c1.person = :person")->setParameter("person", $person->getId());
        $qb->andWhere($qb->expr()->like('pe.roles', $qb->expr()->literal("%" . Person::ROLE_FORMER . "%")));
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $this->byYear($qb, $year);
        return $qb->getQuery()->getResult();
    }

    /**
     * @param Person $person
     * @param \DateTime|null $startDate
     * @param \DateTime|null $endDate
     * @return array
     */
    public function findByCoordinatorFormer(Coordinator $coordinator)
    {
        $qb = $this->getCoordinatorQb();
        $qb->andWhere($qb->expr()->like('pe.roles', $qb->expr()->literal("%" . Person::ROLE_FORMER . "%")));
        $qb->andWhere("c1.person = :person")->setParameter("person", $coordinator->getPerson()->getId());
        $qb->andWhere('f.id = :formation')->setParameter('formation', $coordinator->getFormation()->getId());
        return $qb->getQuery()->getResult();
    }

    /**
     * @param $title
     * @param $year
     * @param $role
     * @return array
     */
    public function findByTitleYearRole($title, $year, $role) {
        $qb = $this->createQueryBuilder("e")
            ->innerJoin('e.formation', 'f')
            ->innerJoin('f.programme', 'p')
            ->innerJoin('e.person', 'pe')
            ->leftJoin('pe.participant', 'pa')
            ->andWhere('p.title LIKE :title')->setParameter('title', $title);
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $this->byYear($qb, $year);
        $this->byRole($qb, $role);
        return $qb->getQuery()->getResult();
    }

    /**
     * @return QueryBuilder
     */
    protected function getCoordinatorQb()
    {
        $qb = $this->createQueryBuilder("e")
            ->innerJoin('e.formation', 'f')
            ->innerJoin('f.coordinators', 'c1')
            ->innerJoin('e.person', 'pe')
            ->leftJoin('pe.participant', 'pa')
            ->leftJoin('pa.participations', 'pas')
            ->leftJoin('pas.coordinator', 'c2');
        return $qb;
    }

    /**
     * @param Formateur $former
     * @return array
     */
    public function isFormerReferent(Formateur $former)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->innerJoin('e.former', 'f');
        $qb->select('e.answer');
        $qb->where('f.person = :person')->setParameter('person', $former->getPerson()->getId());
        $qb->andWhere('e.question LIKE :question')->setParameter('question', EvaluationGlobalManager::FORMER_RECOMMANDE_QUESTION);
        $qb->andWhere('e.answer LIKE :answer')->setParameter('answer', '1');
        return count($qb->getQuery()->getScalarResult()) > 0;
    }

    public function findByPeriod($start, $end) {
        $qb = $this->createQueryBuilder('e');
        $qb->innerJoin('e.formation', 'f');

        $qb->where(
            $qb->expr()->orX(
            $qb->expr()->gte('f.querySearchStartDate', ':start')
                )
        );
        $qb->setParameter(':start', $start);

        $end->setTime(23, 59, 59);
        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->lte('f.querySearchEndDate', ':end')
            )
        );
        $qb->andWhere('f.archived = :archived')->setParameter('archived', false);
        $qb->setParameter(':end', $end);

        return $qb->getQuery()->getResult();
    }
}
