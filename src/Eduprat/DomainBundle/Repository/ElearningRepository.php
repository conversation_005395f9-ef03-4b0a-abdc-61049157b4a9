<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\QueryBuilder;
use Alienor\ApiBundle\Repository\PageableAbstractRepository;
use Eduprat\AdminBundle\Entity\ElearningSearch;

/**
 * AuditRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ElearningRepository extends PageableAbstractRepository
{
    public function getQueryBuilder()
    {
        return $this->createQueryBuilder('a');
    }

    public function countQueryBuilder()
    {
        return $this->getQueryBuilder()->select('count(a)');
    }

    public function listing($page, $nombre){
        return $this->getQueryBuilder()
            ->setFirstResult(($page-1) * $nombre)
            ->setMaxResults($nombre);
    }

    public function countSearchResults(ElearningSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->select('COUNT(a.id)')
            ->getQuery()->getSingleScalarResult()
            ;
    }

    public function findSearchResults(ElearningSearch $search, $page, $number, $sortBy = null, $order = "ASC") {
        $qb = $this->createSearchResultsQueryBuilder($search);

        if ($sortBy) {
            $qb->addOrderBy($sortBy, $order);
        }

        return $qb->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)
            ->getQuery()->getResult();
    }

    /**
     * @param ElearningSearch $search
     * @return QueryBuilder
     */
    public function createSearchResultsQueryBuilder(ElearningSearch $search) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->leftJoin('a.category', 'c');

        if ($search->getCategory()) {
            $queryBuilder->andWhere('c.id = :id')->setParameter('id', $search->getCategory());
        }

        if ($search->getLabel()) {
            $queryBuilder->andWhere('a.label like :label')->setParameter('label', "%" . $search->getLabel() . "%");
        }

        if ($search->getYear()) {
            $queryBuilder->andWhere('a.year = :year')->setParameter('year', $search->getYear());
        }

        if ($search->getCategory()) {
            $queryBuilder->andWhere('c.id = :id')->setParameter('id', $search->getCategory());
        }

        $queryBuilder->andWhere('a.archived = :archived')->setParameter('archived', $search->getArchived() ? true : false);

        return $queryBuilder;
    }
}
