<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\ApiBundle\Model\CatalogueSearch;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\DomainBundle\Entity\FinanceMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPowerpoint;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Entity\FormationTcs;
use Eduprat\DomainBundle\Entity\FormationVfc;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Model\SessionSearch;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Model\PlaquetteSearch;
use Scienta\DoctrineJsonFunctions\Query\AST\Functions\Mysql\JsonSearch;

/**
 * FormationRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class FormationRepository extends EntityRepository
{
    /**
     * @param SessionSearch                         $search
     * @param Person|null $user
     * @return QueryBuilder
     */
    public function createSearchResultsQueryBuilder($search, Person $user = null) {
        $queryBuilder = $this->createQueryBuilder('f')
            ->select('f')
            ->distinct()
            ->leftJoin('f.coordinators', 'coordinator')
            ->leftJoin('coordinator.person', 'co')
            ->leftJoin('f.formateurs', 'former')
            ->leftJoin('former.person', 'fp')
            ->leftJoin('f.programme', 'p')
            ;

        if ($user && $user->isCoordinator()) {
            $queryBuilder->where('coordinator.person = :id')->setParameter('id', $user->getId());
        }

        if ($user && $user->isSupervisor()) {
            $queryBuilder->where('co.supervisor = :spId')->setParameter('spId', $user->getId());
        }

        if (!is_null($search->supervisor)) {
            $queryBuilder->leftJoin('co.supervisor', 's');
            $queryBuilder->andWhere('s.id = :supervisor')->setParameter('supervisor', $search->supervisor);
        }

        if (!is_null($search->programme)) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('p.id', ':id'))
                ->setParameter(':id', $search->programme);
        }

        if ($search->key) {
            $queryBuilder->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->like('p.reference', ':key'),
                $queryBuilder->expr()->like('p.title', ':key'),
                $queryBuilder->expr()->like('f.zipCode', ':key'),
                $queryBuilder->expr()->like('co.lastname', ':key'),
                $queryBuilder->expr()->like('co.firstname', ':key'),
                $queryBuilder->expr()->like('fp.lastname', ':key'),
                $queryBuilder->expr()->like('fp.firstname', ':key')
            ));
            $words = explode(' ', $search->key);
            foreach($words as $word) {
                $queryBuilder->setParameter('key', '%'.$word.'%');
            }
        }

        if ($search->closed === "oui") {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('f.closed', ':closed'))
                ->setParameter(':closed', true);
        } else if ($search->closed === "non") {
            $queryBuilder->andWhere($queryBuilder->expr()->neq('f.closed', ':opened'))
                ->setParameter(':opened', true);
        }

        if ($search->nbSession) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('f.sessionNumber', ':sessionNumber'))
                ->setParameter(':sessionNumber', $search->nbSession);
        }

        if ($search->start) {
            $queryBuilder->andWhere($queryBuilder->expr()->gte('f.startDate', ':start'))
                ->setParameter(':start', $search->start);
        }

        if ($search->end) {
            $search->end->setTime(23, 59, 59);
            $queryBuilder->andWhere($queryBuilder->expr()->lte('f.endDate', ':end'))
                ->setParameter(':end', $search->end);
        }

        if ($search instanceof SessionSearch || $search instanceof ComptabiliteSearch ||
        $search instanceof \Eduprat\DomainBundle\Model\ComptabiliteSearch || $search instanceof AttestationComptabiliteSearch) {
            if ($search->querySearchStartDate) {
                $queryBuilder->andWhere($queryBuilder->expr()->gte('f.querySearchStartDate', ':start'))
                    ->setParameter(':start', $search->querySearchStartDate);
            }

            if ($search->querySearchEndDate) {
                $search->querySearchEndDate->setTime(23, 59, 59);
                $queryBuilder->andWhere($queryBuilder->expr()->lte('f.querySearchEndDate', ':end'))
                    ->setParameter(':end', $search->querySearchEndDate);
            }
        }

        if ($search->startCreationDate) {
            $queryBuilder->andWhere($queryBuilder->expr()->gte('f.createdAt', ':startCreationDate'))
                ->setParameter(':startCreationDate', $search->startCreationDate);
        }

        if ($search->endCreationDate) {
            $search->endCreationDate->setTime(23, 59, 59);
            $queryBuilder->andWhere($queryBuilder->expr()->lte('f.createdAt', ':endCreationDate'))
                ->setParameter(':endCreationDate', $search->endCreationDate);
        }
        if ($search->startUpdateDate && $search->endUpdateDate) {
            $queryBuilder->andWhere($queryBuilder->expr()->gte('f.updatedAt', ':startUpdateDate'))
                ->setParameter(':startUpdateDate', $search->startUpdateDate);

                $search->endUpdateDate->setTime(23, 59, 59);
                $queryBuilder->andWhere($queryBuilder->expr()->lte('f.updatedAt', ':endUpdateDate'))
                ->setParameter(':endUpdateDate', $search->endUpdateDate);

            $queryBuilder->andWhere('f.updatedAt IS NOT NULL');
        }

        if ($search->city) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('f.city', ':city'))->setParameter('city', $search->city);
        }
        if ($search->zip) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('f.zipCode', ':zip'))->setParameter('zip', $search->zip);
        }

        if ($search->type) {
            if ($search->type === "predefined") {
                $queryBuilder->leftJoin(FormationAudit::class, 'wffas', 'WITH', 'wffas.id = f.id');
                $queryBuilder->leftJoin(Audit::class, 'au', 'WITH', 'au.id = wffas.audit');
                $queryBuilder->andWhere("au.type LIKE :auditType")->setParameter('auditType', 'predefined');
            } else if ($search->type === FormationAudit::class) {
                $queryBuilder->leftJoin(FormationAudit::class, 'wffas', 'WITH', 'wffas.id = f.id');
                $queryBuilder->leftJoin(Audit::class, 'au', 'WITH', 'au.id = wffas.audit');
                $queryBuilder->leftJoin(Programme::class, 'pg', 'WITH', 'f.programme = pg.id');
                $queryBuilder->andWhere($queryBuilder->expr()->isInstanceOf('f', $search->type));
                $queryBuilder->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq("au.type", ":auditType"),
                    $queryBuilder->expr()->isNull("wffas.audit")
                ))->setParameter('auditType', 'default');
                $queryBuilder->andWhere("pg.formType NOT LIKE :formType")->setParameter('formType', 'predefined');
                $queryBuilder->andWhere($queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('f', FormationVignette::class)));
            } else if ($search->type === "lbi") {
                $queryBuilder->andWhere("co.roles like '%ROLE_COORDINATOR_LBI%'");
            } else if ($search->type === FormationActalians::class) {
                $queryBuilder->andWhere("f.actaliansPdf = 1");
            } else if ($search->type === FormationPresentielle::class || $search->type === FormationElearning::class) {
                $excludedClasses = array();
                if ($search->type === FormationPresentielle::class) {
                    $excludedClasses = array(FormationElearning::class, FormationSedd::class);
                } else if ($search->type === FormationElearning::class) {
                    $excludedClasses = array(FormationPowerpoint::class);
                }
                $queryBuilder->andWhere($queryBuilder->expr()->isInstanceOf('f', $search->type));
                foreach ($excludedClasses as $class) {
                    $queryBuilder->andWhere($queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('f', $class)));
                }
            } else if ($search->type === FormationVignette::class) {
                $queryBuilder->andWhere($queryBuilder->expr()->isInstanceOf('f', $search->type));
                $queryBuilder->andWhere($queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('f', FormationVignetteAudit::class)));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->isInstanceOf('f', $search->type));
            }
        }

        $comptaSearch = property_exists($search, 'paidFormer');

        if (!$comptaSearch) {
            if ($search->financeMode) {
                $queryBuilder->innerJoin('f.financeModes', 'fm');
                $queryBuilder->andWhere("fm.id = :fm")->setParameter("fm", $search->financeMode);
            }
            if ($search->financeSousMode) {
                $queryBuilder->innerJoin('f.financeSousModes', 'fsm');
                $queryBuilder->andWhere('fsm.id = :fsm')->setParameter('fsm', $search->financeSousMode);
            }
            if ($search->withFormer) {
                switch ($search->withFormer) {
                    case "with_former":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.id'));
                        break;
                    case "without_former":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNull('fp.id'));
                        break;
                    case "with_siret":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.siret'));
                        break;
                    case "without_siret":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.id'));
                        $queryBuilder->andWhere($queryBuilder->expr()->isNull('fp.siret'));
                        break;
                }
            }
        }

        if ($comptaSearch && $search->accounted !== null) {
            if ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_NON_COMPTABILISEE) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->orX(
                        $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('f.accounted', 'false'),
                            $queryBuilder->expr()->eq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        ),
                        // pluri qui ont débuté sur l'année N et qui n'ont pas terminé le mi-parcours
                        $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('f.accountedMidCourse', 'false'),
                            $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                            $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                        )
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_COMPTABILISEE) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->orX(
                        $queryBuilder->expr()->eq('f.accounted', 'true'),
                        // pluri qui ont débuté sur l'année N et qui n'ont pas terminé le mi-parcours
                        $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('f.accountedMidCourse', 'true'),
                            $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                            $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                        )
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_NON_COMPTABILISEE_MI_PARCOURS) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accountedMidCourse', 'false'),
                        $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_COMPTABILISEE_MI_PARCOURS) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accountedMidCourse', 'true'),
                        $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_ANNUELLE_COMPTABILISEE) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accounted', 'true'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', 'YEAR(f.closingDate)')
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_ANNUELLE_NON_COMPTABILISEE) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accounted', 'false'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', 'YEAR(f.closingDate)')
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_PLURIANNUELLE_NON_COMPTABILISEE_EN_N1) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accounted', 'false'),
                        $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                    )
                );
            } elseif ($search->accounted === \Eduprat\DomainBundle\Model\ComptabiliteSearch::SESSION_PLURIANNUELLE_COMPTABILISEE_EN_N1) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq('f.accounted', 'true'),
                        $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        $queryBuilder->expr()->eq('YEAR(f.openingDate)', $search->yearForComptabilite),
                    )
                );
            }
        }

        if ($comptaSearch) {

            if ($search->financeModes) {
                $queryBuilder->innerJoin('f.financeModes', 'fm');
                $queryBuilder->andWhere("fm.id IN (:fm)")->setParameter("fm", $search->financeModes->map(function($item) {
                    return $item->getId();
                })->toArray());
            }
            if ($search->financeSousModes) {
                $queryBuilder->innerJoin('f.financeSousModes', 'fsm');
                $queryBuilder->andWhere('fsm.id IN (:fsm)')->setParameter('fsm', $search->financeSousModes->map(function($item) {
                    return $item->getId();
                })->toArray());
            }


            if ($search->withFormer) {
                switch ($search->withFormer) {
                    case "with_former":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.id'));
                        break;
                    case "without_former":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNull('fp.id'));
                        break;
                }
            }

            if ($search->withSiret) {
                switch ($search->withSiret) {
                    case "with_siret":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.siret'));
                        break;
                    case "without_siret":
                        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('fp.id'));
                        $queryBuilder->andWhere($queryBuilder->expr()->isNull('fp.siret'));
                        break;
                }
            }


            if($search->paidFormer || $search->remunerateFormateur) {
                $queryBuilder->innerJoin('f.formateurs', 'frm');
            }

            if ($search->paidFormer) { // Frm Payé/NonPayé
                $queryBuilder->andWhere("frm.isPaid = :fmp")->setParameter("fmp", $search->paidFormer == "paid_former" ? 1 : 0);
            }

            if ($search->remunerateFormateur) {
                if($search->remunerateFormateur == "UnRemunerateFormateur") { // Uniquement les Frm non rémunérés
                    $queryBuilder->andWhere("frm.honorary = :hn");
                } else if ($search->remunerateFormateur == "RemunerateFormateur") {
                    $queryBuilder->andWhere("frm.honorary > :hn");
                }
                $queryBuilder->setParameter("hn", 0);
            }

            if($search->paidCoordinator || $search->formationCoordinator)
            {
                $queryBuilder->innerJoin('f.coordinators', 'fc');
            }

            if ($search->paidCoordinator) {
                if ($search->paidCoordinator == "paid_coordinator") {
                    $queryBuilder->andWhere(
                        $queryBuilder->expr()->orX(
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->eq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                                $queryBuilder->expr()->eq('fc.isPaid', 1)
                            ),
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                                $queryBuilder->expr()->eq('fc.isPaidMidCourse', 1),
                                $queryBuilder->expr()->eq('fc.isPaid', 1)
                            )
                        )
                    );
                } else {
                    $queryBuilder->andWhere(
                        $queryBuilder->expr()->orX(
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->eq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                                $queryBuilder->expr()->eq('fc.isPaid', 0)
                            ),
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                                $queryBuilder->expr()->orX(
                                    $queryBuilder->expr()->eq('fc.isPaidMidCourse', 0),
                                    $queryBuilder->expr()->eq('fc.isPaid', 0)
                                ),
                            )
                        )
                    );
                }
                $search->formationCoordinator = "regionalCoordinator";
            }

            if ($search->formationCoordinator) {
                $queryBuilder->innerJoin('fc.person', 'per');
                $queryBuilder->andWhere("per.crStatus = :crs");
                $queryBuilder->setParameter("crs", $search->formationCoordinator == "formationCoordinator" ? Person::CR_STATUS_SALARIE : Person::CR_STATUS_INDEPENDANT);
            }
        }

        if ($search->billingStatus) {

            $closingDateExpired = (new \DateTime())->modify('- 1 day')->setTime(0, 0, 0)->format('Y-m-d');

            switch ($search->billingStatus) {
                case "waiting":
                    $queryBuilder->innerJoin('f.factureState', 'fcs');
                    $queryBuilder->andWhere("fcs.isWaiting = true"); // Calculé par le FacturationStateManager
                    break;
                case "chargeable":
                    $queryBuilder->innerJoin('f.factureState', 'fcst');
                    $queryBuilder->andWhere("fcst.isChargeable = true"); // Calculé par le FacturationStateManager

                    if ($search->withParticipant === null) {
                        $search->withParticipant = "oui";
                    }
                    break;
                case "billed":
                    // Session possédant au moins un mode de financement en état "facturé" (case à cocher check)
                    $queryBuilder->andWhere('f.oneFinanceSousModeFactured = :oneFinanceSousModeFactured')->setParameter("oneFinanceSousModeFactured", true);
                    break;
            }
        }

        if ($search->fileStatus) {
            $queryBuilder->andWhere('f.fileCompleted = :fileCompleted')->setParameter("fileCompleted", $search->fileStatus === "complete");
        }

        if ($search->withForm === "oui") {
            self::withForm($queryBuilder, 'f');
        } else if ($search->withForm === "non") {
            $queryBuilder->leftJoin(FormationAudit::class, 'woffa', 'WITH', 'woffa.id = f.id');
            $queryBuilder->leftJoin(FormationVignette::class, 'woffv', 'WITH', 'woffv.id = f.id');
            $queryBuilder->leftJoin(FormationPresentielle::class, 'woffpr', 'WITH', 'woffpr.id = f.id');
            $queryBuilder->leftJoin(FormationActalians::class, 'woffac', 'WITH', 'woffac.id = f.id');
            $queryBuilder->leftJoin(FormationElearning::class, 'woffel', 'WITH', 'woffel.id = f.id');
            $queryBuilder->leftJoin(FormationVfc::class, 'wffvfc', 'WITH', 'wffvfc.id = f.id');
            $queryBuilder->leftJoin(FormationTcs::class, 'wfftcs', 'WITH', 'wfftcs.id = f.id');
            $queryBuilder
                ->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationAudit::class),
                        $queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('f', FormationVignette::class)),
                        $queryBuilder->expr()->isNull('woffa.audit')
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationVignette::class),
                        $queryBuilder->expr()->orX(
                            $queryBuilder->expr()->isNull('woffv.audit'),
                            $queryBuilder->expr()->isNull('woffv.audit2'),
                        ),
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationPresentielle::class),
                        $queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('f', FormationElearning::class)),
                        $queryBuilder->expr()->isNull('woffpr.questionnaire')
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationActalians::class),
                        $queryBuilder->expr()->isNull('woffac.questionnaire')
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationElearning::class),
                        $queryBuilder->expr()->andX( // les elearnings sont soit avec questionnaire soit avec audit
                            $queryBuilder->expr()->isNull('woffel.questionnaire'),
                            $queryBuilder->expr()->isNull('woffel.audit'),
                        )
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationVfc::class),
                        $queryBuilder->expr()->isNull('wffvfc.audit')
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isInstanceOf('f', FormationTcs::class),
                        $queryBuilder->expr()->isNull('wfftcs.audit')
                    )
                ))
            ;
        }
        if ($search->withTopo === "oui") {
            $queryBuilder
                ->innerJoin('f.topoFiles', 't');
        } else if($search->withTopo === "non") {
            $queryBuilder
                    ->leftJoin('f.topoFiles', 't');
            $queryBuilder->andWhere($queryBuilder->expr()->isNull('t.id'));
        }

        if ($search->withParticipant === "oui") {
            $queryBuilder->innerJoin('f.participations', 'par');
            $queryBuilder->andWhere('par.archived = false');
        } else if($search->withParticipant === "non") {
            $queryBuilder->leftJoin('f.participations', 'par');
            // Si tous les participants sont archivés ou si il n'y a pas de participants
            $queryBuilder->having($queryBuilder->expr()->eq("(SELECT count(par2.id) FROM ".Participation::class." par2 WHERE par2.archived = 1 AND par2.formation = f.id)", "count(par.id)"));
            $queryBuilder->andHaving("count(par.id) > 0");
            $queryBuilder->orHaving($queryBuilder->expr()->eq("count(par)", 0));
            $queryBuilder->groupBy("f.id");
        }

        if ($search->presence) {
            $queryBuilder->andWhere('p.presence = :presence')->setParameter('presence', $search->presence);
        }

        if($search->status && $search->status === "future") {
            $queryBuilder->andWhere('f.startDate > :now')->setParameter('now', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'))
                        ->andWhere('f.closed = :closed')->setParameter('closed', false);
        }

        if ($search->partenariat) {
            if($search->partenariat == "Tous partenariats") {
                $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('f.partenariat'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->eq('f.partenariat', ':partenariat'))
                    ->setParameter(':partenariat', $search->partenariat);
            }
        }

        if ($search->ots) {
            if($search->ots == "Tous OTS") {
                $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('f.ots'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->eq('f.ots', ':ots'))
                    ->setParameter(':ots', $search->ots);
            }
        }

        if ($search->archived === "oui") {
            $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', true);
        } else if ($search->archived === "non") {
            $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', false);
        }

        $queryBuilder->orderBy("f.startDate", "DESC");
        return $queryBuilder;
    }

    public function countSearchResults($search, Person $user = null) {
        $queryBuilder = $this->createSearchResultsQueryBuilder($search, $user)->select('COUNT(DISTINCT f.id)');
        if ($search->withParticipant === "non") {
            return count($queryBuilder->distinct(false)->getQuery()->getScalarResult());
        }
        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function findSearchResults($search, $page, $number, Person $user = null) {
        return $this->createSearchResultsQueryBuilder($search, $user)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)->getQuery()->getResult();
    }

    public function countPlaquetteSearchResults($search, Person $user = null) {
        $queryBuilder = $this->createPlaquetteSearchResultsQueryBuilder($search, $user)->select('COUNT(DISTINCT f.id)');
        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function findPlaquetteSearchResults($search, $page, $number, Person $user = null, $forCountSelection = false) {
        $seachResult = $this->createPlaquetteSearchResultsQueryBuilder($search, $user);
        if ($forCountSelection) {
            return $seachResult->getQuery()->getArrayResult();
        } else {
            return $seachResult->setFirstResult(($page-1) * $number)->setMaxResults($number)->getQuery()->getResult();
        }
    }

    public function findPlaquetteSearchResultsByIds($page, $number, $forCountSelection, $ids, $count = false) {
        $seachResult = $this->findSearchByIds($ids);
        if ($count) {
            return $seachResult->select('COUNT(DISTINCT f.id)')->getQuery()->getSingleScalarResult();
        } elseif ($forCountSelection) {
            return $seachResult->getQuery()->getArrayResult();
        } else {
            return $seachResult->setFirstResult(($page-1) * $number)->setMaxResults($number)->getQuery()->getResult();
        }
    }

    public function findSearchByIds($sessions)
    {
        $qb = $this->createQueryBuilder('f')
                    ->leftJoin('f.programme', 'p')
                    ->orderBy("p.presence", "ASC")
                    ->addOrderBy("f.startDate", "ASC")
                    ;

        $qb
            ->andWhere('f.id in (:idSessionInclude)')
            ->setParameter('idSessionInclude', $sessions);
        return $qb;
    }

    public function findElearnings($exclusions) {
        // select id from formation inner join programme where programme is elearning and formation.id in $exclusions
        $qb = $this->createQueryBuilder('f')
        ->select('f')
        ->innerJoin('f.programme', 'p')
        ->where('p.presence = :presence')->setParameter('presence', Programme::PRESENCE_ELEARNING)
        ->andWhere('f.id IN (:ids)')->setParameter(':ids', $exclusions);
        ;

        return $qb->getQuery()->getResult();
    }

    public function findProgrammesSons($programmes) {
        // select id from formation inner join programme where programme is elearning and formation.id in $exclusions
        $qb = $this->createQueryBuilder('f')
        ->select('f.id')
        ->innerJoin('f.programme', 'p')
        ->andWhere('p.id IN (:ids)')->setParameter(':ids', $programmes);
        ;

        return $qb->getQuery()->getResult();
    }

    public function findPlaquetteProgrammes($sessions, $startDateOnly = false): array
    {
        $qb = $this->createQueryBuilder('f')
                    ->innerJoin('f.programme', 'p');

        if($startDateOnly) {
            $qb->orderBy("f.startDate", "ASC");
        } else {
            $qb->orderBy("p.presence", "DESC")
                ->addOrderBy("f.startDate", "ASC");
        }

        $qb
            ->andWhere('f.id in (:idSessionInclude)')
            ->setParameter('idSessionInclude', $sessions);
        return $qb->getQuery()->getResult();
    }

    public function findPlaquetteSearchIds($search, Person $user = null, $exclusion = null): array
    {
        $qb = $this->createPlaquetteSearchResultsQueryBuilder($search, $user)
            ->select('f.id');

        if ($exclusion) {
            $qb
            ->andWhere('f.id <> :idSessionInclude')
            ->setParameter('idSessionInclude', $exclusion);
        }
        $res = $qb
            ->getQuery()
            ->getArrayResult();
        return array_column($res, "id");
    }

    /**
     * @param PlaquetteSearch                         $search
     * @param Person|null $user
     * @return QueryBuilder
     */
    public function createPlaquetteSearchResultsQueryBuilder(PlaquetteSearch $search, Person $user = null, $forceForElearning = false) {
        $queryBuilder = $this->createQueryBuilder('f');
        $queryBuilder
            ->distinct()
            ->leftJoin('f.programme', 'p')
            ->leftJoin('f.coordinators', 'crd')
            ;

        if ($search->regions) {
            $orExpr = $queryBuilder->expr()->orX();
            $orExpr->add($queryBuilder->expr()->in('f.region', ':regions'));
            $queryBuilder->andWhere($orExpr)->setParameter('regions', $search->regions);
        }

        if ($search->departements) {
            $orExpr = $queryBuilder->expr()->orX();
            foreach ($search->departements as $index => $departement) {
                $orExpr->add($queryBuilder->expr()->like('f.zipCode', ':zip' . $index));
                $queryBuilder->setParameter('zip' . $index, $departement . '%');
            }
            $queryBuilder->andWhere($orExpr);
        }

        if ($search->categories) {
            $andExpr = $queryBuilder->expr()->andX();
            foreach ($search->categories as $index => $category) {
                $andExpr->add('JSON_SEARCH(p.categories, \''. JsonSearch::MODE_ONE.'\', '. ':cat' . $index.') is not null');
                $queryBuilder->setParameter('cat' . $index, $category);
            }
            $queryBuilder->andWhere($andExpr);
        }

        if ($search->specialities) {
            $andExpr = $queryBuilder->expr()->andX();
            foreach ($search->specialities as $index => $speciality) {
                $andExpr->add('JSON_SEARCH(p.specialities, \''. JsonSearch::MODE_ONE.'\', '. ':spe' . $index.') is not null');
                $queryBuilder->setParameter('spe' . $index, $speciality);
            }
            $queryBuilder->andWhere($andExpr);
        }

        if ($search->key) {
            $queryBuilder->leftJoin('f.coordinators', 'coordinator')
                ->leftJoin('coordinator.person', 'co')
                ->leftJoin('f.formateurs', 'former')
                ->leftJoin('former.person', 'fp');
            $queryBuilder->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->like('p.reference', ':key'),
                $queryBuilder->expr()->like('p.title', ':key'),
                $queryBuilder->expr()->like('f.zipCode', ':key'),
                $queryBuilder->expr()->like('co.lastname', ':key'),
                $queryBuilder->expr()->like('co.firstname', ':key'),
                $queryBuilder->expr()->like('fp.lastname', ':key'),
                $queryBuilder->expr()->like('fp.firstname', ':key')
            ));
            $words = explode(' ', $search->key);
            foreach($words as $word) {
                $queryBuilder->setParameter('key', '%'.$word.'%');
            }
        }

        if ($search->nbSession) {
            $queryBuilder->andWhere($queryBuilder->expr()->eq('f.sessionNumber', ':sessionNumber'))
                ->setParameter(':sessionNumber', $search->nbSession);
        }

        if ($search->start) {
            $queryBuilder->andWhere($queryBuilder->expr()->gte('f.querySearchStartDate', ':start'))
                ->setParameter(':start', $search->start);
        }

        if ($search->end) {
            $search->end->setTime(23, 59, 59);
            $queryBuilder->andWhere($queryBuilder->expr()->lte('f.querySearchEndDate', ':end'))
                ->setParameter(':end', $search->end);
        }

        if ($search->presence || $forceForElearning) {
            $presence = $forceForElearning ? Programme::PRESENCE_ELEARNING : $search->presence;
            $queryBuilder->andWhere('p.presence = :presence')->setParameter('presence', $presence);
        } else {
            $queryBuilder->andWhere('p.presence != :presence')->setParameter('presence', Programme::PRESENCE_ELEARNING);
        }

        if ($search->financeModes) {
            $queryBuilder->innerJoin('f.financeModes', 'fm');
            $queryBuilder->andWhere("fm.id IN (:fm)")->setParameter("fm", $search->financeModes);
        }

        if ($search->prisesEnCharge) {
            $queryBuilder->innerJoin('p.prisesEnCharge', 'pec');
            $queryBuilder->andWhere("pec.id IN (:pec)")->setParameter("pec", $search->prisesEnCharge);
        }

        if ($search->thematiques) {
            $queryBuilder->innerJoin('p.category', 'pc');
            $queryBuilder->andWhere("pc.id IN (:pc)")->setParameter("pc", $search->thematiques);
        }


        $orExpr = $queryBuilder->expr()->orX();
        $orExpr->add($queryBuilder->expr()->eq("crd.person", $user->getId()));

        if ($search->virtualUnrelated === 'unrelated') {
            $orExpr->add(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->neq("crd.person", $user->getId()),
                    $queryBuilder->expr()->eq("p.presence", ':presvirtual'),
                )
            );
            $queryBuilder->setParameter('presvirtual', Programme::PRESENCE_VIRTUELLE);
        }

        if ($search->elearningUnrelated === 'unrelated') {
            $orExpr->add(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->neq("crd.person", $user->getId()),
                    $queryBuilder->expr()->eq("p.presence", ':preselearning'),
                )
            );
            $queryBuilder->setParameter('preselearning', Programme::PRESENCE_ELEARNING);
        }

        $queryBuilder->andWhere($orExpr);

        if ($search->exclusions) {
            $queryBuilder->andWhere('f.id NOT IN (:ids)')
                    ->setParameter(':ids', $search->exclusions);
        }

        $tomorrow = (new \DateTime())->setTime(0, 0, 0)->modify("+ 1 day");
        $queryBuilder
            ->andWhere('f.querySearchStartDate >= :date')->setParameter('date', $tomorrow->format('Y-m-d H:i:s'))
            ->andWhere('f.archived = :archived')->setParameter('archived', false);

        $queryBuilder->andWhere($queryBuilder->expr()->orX(
            $queryBuilder->expr()->like('f.formatPeriod', ':formatPeriodJournee'),
            $queryBuilder->expr()->isNull('f.formatPeriod'),
        ))->setParameter('formatPeriodJournee', Formation::FORMAT_PERIOD_JOURNEE);

        $queryBuilder->orderBy("p.presence", "ASC");
        $queryBuilder->addOrderBy("f.startDate", "ASC");

        return $queryBuilder;
    }

    /**
     * @param CatalogueSearch                         $search
     * @param Person|null $user
     * @return QueryBuilder
     */
    public function createCatalogueResultsQueryBuilder(CatalogueSearch $search, Person $user = null, $notFinished = false) {
        $qb = $this->createQueryBuilder('f')->select('f');
        $qb->innerJoin('f.programme', 'p');

        $qb->andWhere('f.private = false');

        if ($search->getTitle()) {
            $qb->leftJoin('p.tags', 't');
            $titleSearch = $search->getFormat() === "elearning" ? $search->getTitle() : "%" . $search->getTitle() . "%";
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like('p.title', ':title'),
                $qb->expr()->like('t.name', ':title')
            ))->setParameter('title', $titleSearch);
        }

        if ($search->getTitleContains()) {
            $qb->andWhere($qb->expr()->like('p.title', ':title'))->setParameter('title', "%" . $search->getTitleContains() . "%");
        }

        if ($search->getRegion()) {
            $orExpr = $qb->expr()->orX();
            $orExpr->add($qb->expr()->in('f.region', ':regions'));
            $orExpr->add($qb->expr()->eq("p.presence", ':classeVirtuelle_r'));
            $orExpr->add($qb->expr()->eq("p.format", ':elearning_r'));
            $qb->setParameter('classeVirtuelle_r', Programme::PRESENCE_VIRTUELLE);
            $qb->setParameter('elearning_r', Programme::FORMAT_ELEARNING);
            $qb->andWhere($orExpr)->setParameter('regions', $search->getRegion());
        }

        if ($search->getDepartement()) {
            $orExpr = $qb->expr()->orX();
            $orExpr->add($qb->expr()->in('f.departement', ':departements'));
            $orExpr->add($qb->expr()->eq("p.presence", ':classeVirtuelle_r'));
            $orExpr->add($qb->expr()->eq("p.format", ':elearning_r'));
            $qb->setParameter('classeVirtuelle_r', Programme::PRESENCE_VIRTUELLE);
            $qb->setParameter('elearning_r', Programme::FORMAT_ELEARNING);
            $qb->andWhere($orExpr)->setParameter('departements', $search->getDepartement());
        }

        if ($search->getPresence()) {
            $orExpr = $qb->expr()->orX();
            $orExpr->add($qb->expr()->in('p.presence', ':presence'));
            $qb->andWhere($orExpr)->setParameter('presence', $search->getPresence());
        }

        if ($search->getCategories()) {
            $orExpr = $qb->expr()->orX();
            foreach ($search->getCategories() as $index => $category) {
                $orExpr->add('JSON_SEARCH(p.categories, \''. JsonSearch::MODE_ONE.'\', '. ':cat' . $index.') is not null');
                $qb->setParameter('cat' . $index, $category);
            }
            $qb->andWhere($orExpr);
        }

        if ($search->getSpecialities()) {
            $orExpr = $qb->expr()->orX();
            foreach ($search->getSpecialities() as $index => $speciality) {
                $orExpr->add('JSON_SEARCH(p.specialities, \''. JsonSearch::MODE_ONE.'\', '. ':spe' . $index.') is not null');
                $qb->setParameter('spe' . $index, $speciality);
            }
            $qb->andWhere($orExpr);
        }

        if ($search->getVariety() && count($search->getVariety()) < 3) {
            $orExpr = $qb->expr()->orX();
            if (in_array(Formation::VARIETY_MIXED, $search->getVariety())) {
                $orExpr->add($qb->expr()->eq("p.format", ":formatMixed"));
                $qb->setParameter("formatMixed", Programme::FORMAT_MIXTE);
            }
            if (in_array(Formation::VARIETY_PRESENTIEL, $search->getVariety())) {
                $orExpr->add($qb->expr()->eq("p.format", ":formatPresentiel"));
                $qb->setParameter("formatPresentiel", Programme::FORMAT_PRESENTIEL);
            }
            if (in_array(Formation::VARIETY_ELEARNING, $search->getVariety())) {
                $orExpr->add($qb->expr()->eq("p.format", ":formatElearning"));
                $qb->setParameter("formatElearning", Programme::FORMAT_ELEARNING);
            }
            $qb->andWhere($orExpr);
        }

        if ($search->isCertifying() !== null) {
            $qb->andWhere($qb->expr()->eq('p.certifying', ':certifying'))->setParameter('certifying', $search->isCertifying());
        }

        if ($search->isExpert() !== null) {
            $qb->andWhere($qb->expr()->eq('f.expert', ':expert'))->setParameter('expert', $search->isExpert());
        }

        if ($search->getCategory()) {
            $qb->leftJoin(FormationPresentielle::class, 'fp', 'WITH', 'f.id = fp.id');
            $qb->leftJoin('fp.questionnaire', 'fpq');
            $qb->leftJoin('fpq.category', 'fpcq');
            $qb->leftJoin(FormationElearning::class, 'fe', 'WITH', 'f.id = fe.id');
            $qb->leftJoin('fe.questionnaire', 'feq');
            $qb->leftJoin('feq.category', 'fecq');
            $qb->leftJoin(FormationActalians::class, 'fac', 'WITH', 'f.id = fac.id');
            $qb->leftJoin('fac.questionnaire', 'facq');
            $qb->leftJoin('facq.category', 'faccq');
            $qb->leftJoin(FormationPowerpoint::class, 'fpo', 'WITH', 'f.id = fpo.id');
            $qb->leftJoin('fpo.questionnaire', 'fpoq');
            $qb->leftJoin('fpoq.category', 'fpocq');
            $qb->leftJoin(FormationAudit::class, 'fau', 'WITH', 'f.id = fau.id');
            $qb->leftJoin('fau.audit', 'fauq');
            $qb->leftJoin('fauq.category', 'faucq');
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like('fpcq.name', ':category'),
                $qb->expr()->like('fecq.name', ':category'),
                $qb->expr()->like('faccq.name', ':category'),
                $qb->expr()->like('fpocq.name', ':category'),
                $qb->expr()->like('faucq.name', ':category')
            ));
            $qb->setParameter(':category', "%" . $search->getCategory() . "%");
        }

        if ($search->getType()) {
            $this->byClass($qb, $search->getTypeClasses());
        }

        if ($search->getStatus()) {
            $this->byStatusCatalogue($qb, $search->getStatuses());
        }

        if ($search->getStartDate()) {
            $endDate = $search->getEndDate() ? $search->getEndDate() :(new \DateTime($search->getStartDate()))->modify('+ 1 day')->format('Y-m-d');
            $qb->andWhere($qb->expr()->between('f.startDate', ':start', ':end'));
            $qb->setParameter('start', $search->getStartDate());
            $qb->setParameter('end', $endDate);
        }

        if ($search->getFormationIds() !== null && (count ($search->getFormationIds()) > 0 || $search->getLocalization())) {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->in('f.id', ':ids'),
                $qb->expr()->eq("p.format", ':elearning'),
                $qb->expr()->eq("p.presence", ':classeVirtuelle')
            ));
            $qb->setParameter('ids', $search->getFormationIds());
            $qb->setParameter('elearning', Programme::FORMAT_ELEARNING);
            $qb->setParameter('classeVirtuelle', Programme::PRESENCE_VIRTUELLE);
        }

        if (!empty($search->getCoordinators())) {
            $qb->innerJoin('f.coordinators', 'c');
            $qb->andWhere($qb->expr()->in('c.person', ':coordinators'));
            $qb->setParameter('coordinators', $search->getCoordinators());
        }

        if (!empty($search->getFinanceModes())) {
            $qb2  = $this->_em->createQueryBuilder();
            $qb2->select('f_sub.id')
                ->from(Formation::class, 'f_sub')
                ->innerJoin('f_sub.financeModes', 'fm_sub')
                ->where($qb->expr()->in('fm_sub.name', $search->getFinanceModes()));

            $qb->andWhere($qb->expr()->in('f.id', $qb2->getQuery()->getDQL()));
        }

        if (!$notFinished) {
            // Pour l'appli : on exlut les formations elearning qui ne sont pas la prochaine session elearning de chaque programme
            if ($search->getFormat() !== "elearning") {

                $conn = $this->getEntityManager()->getConnection();
                $sql = '
                    SELECT  p.id as pid, f.id as fid, f.startDate as fStartDate
                    FROM programme p INNER JOIN
                    (
                        SELECT  programme, MIN(startDate) MaxDate
                        FROM    formation
                        WHERE startDate >= :date
                        GROUP BY programme
                    ) MaxDates ON p.id = MaxDates.programme
                    INNER JOIN formation f ON MaxDates.programme = f.programme AND MaxDates.MaxDate = f.startDate
                    AND MaxDates.MaxDate = f.startDate WHERE p.format = :format
                ';

                $stmt = $conn->prepare($sql);
                $stmt->execute(array('date' => (new \DateTime())->format("Y-m-d"), "format" => Programme::FORMAT_ELEARNING));
                $ids = array_column($stmt->fetchAllAssociative(), "fid");

                if($ids != []) {
                    $allElearning = $this->createQueryBuilder("fes");
                    $allElearning->innerJoin("fes.programme", "pe");
                    $allElearning->select("fes.id");
                    $allElearning->andWhere("fes.startDate > :dateElearning");
                    $allElearning->andWhere("pe.format = :formatElearning");

                    $qb->setParameter(":dateElearning", (new \DateTime())->format("Y-m-d"));
                    $qb->setParameter(":formatElearning", Programme::FORMAT_ELEARNING);
                    $qb->andWhere(
                        $qb->expr()->orX(
                            $qb->expr()->in("f.id", $ids),
                            $qb->expr()->notIn("f.id", $allElearning->getDQL())
                        )
                    );
                }
            }
        }

        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);

        $qb->orderBy('f.startDate', $search->getFormationOrder());

        return $qb;
    }

    public function countCatalogueResults(CatalogueSearch $search, Person $user = null) {
        return $this->createCatalogueResultsQueryBuilder($search, $user)
            ->select('COUNT(f.id)')
            ->getQuery()->getSingleScalarResult()
            ;
    }

    public function findCatalogueResults(CatalogueSearch $search, $page, $number, Person $user = null) {
        return $this->createCatalogueResultsQueryBuilder($search, $user)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)->getQuery()->getResult();
    }

    public function findComplex($year = null, array $options = [], $cms = null, array $classes = []) {
        $qb = $this->createQueryBuilder('f');
        $qb->innerJoin('f.programme', 'p');
        if (!is_null($cms)) {
            $this->includeCms($qb, $cms);
        }
        if (sizeof($classes)) {
            $this->byClass($qb, $classes);
        }
        if (!is_null($year)) {
            $this->byYear($qb, $year);
        }
        if (sizeof($options)) {
            $this->byStatus($qb, $options);
        }
        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);
        return $qb->getQuery()->getResult();
    }

    public function includeCms(QueryBuilder $qb, $include) {
        $qb->innerJoin('f.coordinators', 'co');
        $qb->innerJoin('co.person', 'pe');
        $qb->where('pe.cms = :cms')->setParameter('cms', $include);
    }

    public function byClass(QueryBuilder $qb, array $classes = []) {
        $ors = array_map(function($class) use ($qb) {
            return $qb->expr()->isInstanceOf('f', $class);
        }, $classes);
        $qb->andWhere(call_user_func_array(array($qb->expr(), "orX"), $ors));
    }

    public function byCriteria(QueryBuilder $qb, array $criterias = []) {
        foreach ($criterias as $key => $value) {
            $k = str_replace(".", "", $key);
            $qb->andWhere($qb->expr()->eq($key, ":$k"))->setParameter($k, $value);
        }
    }

    public function byYear(QueryBuilder $qb, $year = null) {
        if (!is_null($year)) {
            $start = new \DateTime('first day of January ' . $year);
            $end = clone $start;
            $end->modify('+ 1 year');
            $qb->andWhere('p.startDate BETWEEN :start AND :end')
                ->setParameter('start', $start->format('Y-m-d'))
                ->setParameter('end', $end->format('Y-m-d'));
        }
    }

    public function find30ByProgramme($programme, Person $user = null) {
        $queryBuilder = $this->createQueryBuilder('f')
            ->select('f')
            ->leftJoin('f.programme', 'p')
            ->distinct();

        if ($user && $user->isCoordinator()) {
            $queryBuilder
                ->leftJoin('f.coordinators', 'coordinator')
                ->leftJoin('coordinator.person', 'co')
            ;
            $queryBuilder->where('coordinator.person = :person')->setParameter('person', $user->getId());
        }

        $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', false);
        $queryBuilder->andWhere($queryBuilder->expr()->eq('p.id', ':id'))
            ->setParameter(':id', $programme)
            ->setMaxResults(30)
            ->orderBy('f.startDate', 'DESC');

        return $queryBuilder->getQuery()->getResult();
    }


    public function byStatus(QueryBuilder $qb, $statuses) {
        foreach ($statuses as $status => $value) {
            switch ($status) {
                case Formation::STATUS_CLOSED_ANDPC:
                    $qb->andWhere('f.closedAndpc = :closed')->setParameter('closed', $value);
                    break;
                case Formation::STATUS_CLOSED:
                    $qb->andWhere('f.closed = :closed')->setParameter('closed', $value);
                    break;
                case Formation::STATUS_OPENED:
                    $qb->andWhere('p.startDate <= :now')
                        ->andWhere('f.closed = :closed')->setParameter('closed', false)
                        ->setParameter('now', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'));
                    break;
                case Formation::STATUS_OPENED_ANDPC:
                    $qb->andWhere('p.startDate <= :now')
                        ->andWhere('f.closedAndpc = :closed')->setParameter('closed', false)
                        ->setParameter('now', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'));
                    break;
                case Formation::STATUS_FUTURE:
                    $qb->andWhere('p.startDate > :now')->setParameter('now', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'))
                        ->andWhere('f.closed = :closed')->setParameter('closed', false);
                    break;
                case Formation::STATUS_FUTURE_ANDPC:
                    $qb->andWhere('p.startDate > :now')->setParameter('now', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'))
                        ->andWhere('f.closedAndpc = :closed')->setParameter('closed', false);
                    break;
            }
        }
    }

    static public function byStatusCatalogue(QueryBuilder $qb, $statuses) {

        $ors = array();

        foreach ($statuses as $status => $value) {
            $or = null;
            switch ($status) {
                case Formation::STATUS_CLOSED:
                    $or = $qb->expr()->orX(
                        $qb->expr()->eq('f.closed', ':closed1'),
                        $qb->expr()->eq('f.closedAndpc', ':closed1')
                    );
                    $qb->setParameter('closed1', $value);
                    break;
                case Formation::STATUS_OPENED:
                    $or = $qb->expr()->andX(
                        $qb->expr()->lte('f.startDate', ':now2'),
                        $qb->expr()->eq('f.closed', ':closed2'),
                        $qb->expr()->eq('f.closedAndpc', ':closed2')
                    );
                    $qb->setParameter('closed2', false)->setParameter('now2', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'));
                    break;
                case Formation::STATUS_FUTURE:
                    $or = $qb->expr()->andX(
                        $qb->expr()->gt('f.startDate', ':now3'),
                        $qb->expr()->eq('f.closed', ':closed3'),
                        $qb->expr()->eq('f.closedAndpc', ':closed3')
                    );
                    $qb->setParameter('closed3', false)->setParameter('now3', (new \DateTime())->modify('+ 1 day')->setTime(0, 0, 0)->format('Y-m-d'));
                    break;
                case Formation::STATUS_ACCESSIBLE:
                    $or = $qb->expr()->gte('f.openingDate', ':now4');
                    $qb->setParameter('now4', false)->setParameter('now4', (new \DateTime())->setTime(0, 0, 0)->format('Y-m-d'));
                    break;
            }
            $ors[] = $or;
        }

        $qb->andWhere(call_user_func_array(array($qb->expr(), "orX"), $ors));
    }

    public function findForPdfGeneration() {
        $queryBuilder = $this->createQueryBuilder('f');
        $start = (new \DateTime())->modify('- 30 day')->format('Y-m-d 00:00:00');
        $end = (new \DateTime())->modify('+ 20 day')->format('Y-m-d 23:59:59');
        $queryBuilder->where("f.closingDate BETWEEN :start AND :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end);
        $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', false);
        return $queryBuilder->getQuery()->getResult();
    }

    public function findFormationWithoutForm(\DateTime $date = null) {
        $qb = $this->createQueryBuilder('f');
        $qb->innerJoin('f.programme', 'pg');

        if (!is_null($date)) {
            $qb->andWhere($qb->expr()->eq('pg.startDate', ':date'))
                ->setParameter('date', $date->format('Y-m-d'));
        }

        $this->byClass($qb, [
            FormationAudit::class,
            FormationPresentielle::class,
            FormationElearning::class,
            FormationActalians::class,
        ]);

        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);
        return $qb->getQuery()->getResult();
    }

    public function findByReference($reference, $session) {
        $qb = $this->createQueryBuilder('f');
        $qb->innerJoin('f.programme', 'p');
        $qb->innerJoin("f.financeModes", 'fm');
        $qb->where("p.reference = :reference")->setParameter("reference", $reference);
        $qb->andWhere("f.sessionNumber = :session")->setParameter("session", $session);
        $qb->andWhere("fm.name = :financeMode")->setParameter("financeMode", FinanceMode::DPC_NAME);
        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findPreventDuplicate($programme, $sessionNumber) {
        return $this->createQueryBuilder('f')
            ->select('f.id')
            ->where('f.programme = :programme')->setParameter('programme', $programme)
            ->andWhere('f.sessionNumber = :sessionNumber')->setParameter('sessionNumber', $sessionNumber)
            ->andWhere("f.archived = :archived")->setParameter('archived', false)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Formation
     */
    public function findFirst() {
        $q = $this->createQueryBuilder('f')
            ->orderBy('f.startDate', 'ASC')
            ->where("f.archived = :archived")->setParameter('archived', false)
            ->setMaxResults(1);
        return $q->getQuery()->getSingleResult();
    }

    public function findByQuarter($year = null, $quarter = null) {
        $qb = $this->createQueryBuilder('f');

        if($quarter) {
            $month = ($quarter - 1) * 3 + 1;
            $start = $year."-".sprintf("%02d", $month)."-01";
            $end = date("Y-m-t", strtotime($year."-".sprintf("%02d", $month+2 )."-"."01"));
        }
        else {
            $start = $year."-01-01";
            $end = date("Y-m-t", strtotime($year."-12-01"));
        }

         if (!is_null($year)) {
             $qb->andWhere('f.startDate BETWEEN :start AND :end')
                 ->setParameter('start', $start)
                 ->setParameter('end', $end);
         }

        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);

        return $qb->getQuery()->getResult();
    }

    public function findFormationClosedByPeriod($start, $end) {
        $queryBuilder = $this->createQueryBuilder('f');
        $queryBuilder->where(
                    $queryBuilder->expr()->isNull('f.quizsCompletedsEmailSended')
                )
                ->andWhere(
                    $queryBuilder->expr()->between('f.formClosingDate', ':start', ':end')
                );
        $queryBuilder->setParameter('start', $start->format('Y-m-d'))
            ->setParameter('end',$end->format('Y-m-d'));

        $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', false);

         return $queryBuilder->getQuery()->getResult();
    }

    public function findFormationWithoutTopo($start, $end) {
        $queryBuilder = $this->createQueryBuilder('f');

        $queryBuilder
                    ->leftJoin('f.topoFiles', 't')
                    ->leftJoin('f.coordinators', 'c')
                    ->leftJoin('c.person', 'p');

        $queryBuilder->andWhere($queryBuilder->expr()->isNull('t.id'));

        // where dateRéunion passée && diff entre Jour J et dateRéunion >= 4
        $queryBuilder->andWhere('f.endDate < :end');
        $queryBuilder->andWhere('f.endDate > :start');
        $queryBuilder->andWhere('DATE_DIFF(:end,f.endDate) >= 4');
        $queryBuilder->setParameter('end', $end->format('Y-m-d'));
        $queryBuilder->setParameter('start', $start->format('Y-m-d'));

        $queryBuilder->andWhere('f NOT INSTANCE OF '.FormationElearning::class);

        $queryBuilder->andWhere("p.roles not like '%ROLE_COORDINATOR_LBI%'");
        $queryBuilder->andWhere("f.archived = :archived")->setParameter('archived', false);

        return $queryBuilder->getQuery()->getResult();
    }

    public function countClosed(\DateTime $since) {
        $search = new SessionSearch();
        $search->closed = true;
        return $this->createSearchResultsQueryBuilder($search)
		    ->select('COUNT(DISTINCT f.id)')
	    ->andWhere("f.startDate > '2020-01-01'")
            ->getQuery()->getSingleScalarResult();
    }

    public function findClosed(\DateTime $since, $page, $number) {
        $search = new SessionSearch();
        $search->closed = true;
        return $this->createSearchResultsQueryBuilder($search)
            ->select('f')
            ->leftJoin('f.participations', 'pa')
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)
            ->getQuery()
            ->getResult();
    }

    public function findByStartDate(\DateTime $date, $hours = false) {
        $clonedDateStart = clone $date;
        if (!$hours) {
            $clonedDateStart->setTime(0, 0, 0);
        }
        $clonedDateEnd = clone $clonedDateStart;
        if (!$hours) {
            $clonedDateEnd->modify('+ 1 day');
        } else {
            $clonedDateEnd->modify('+ 1 hour');
        }
        $clonedDateEnd->modify('- 1 second');
        $qb = $this->createQueryBuilder('f');
        $qb->andWhere('f.startDate BETWEEN :start and :end')
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'))
        ;
        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);

        return $qb->getQuery()->getResult();
    }

    public function findByDate($field, \DateTime $date, $hours = false) {
        $clonedDateStart = clone $date;
        if (!$hours) {
            $clonedDateStart->setTime(0, 0, 0);
        }
        $clonedDateEnd = clone $clonedDateStart;
        if (!$hours) {
            $clonedDateEnd->modify('+ 1 day');
        } else {
            $clonedDateEnd->modify('+ 1 hour');
        }
        $clonedDateEnd->modify('- 1 second');
        $qb = $this->createQueryBuilder('f');
        $qb->andWhere('f.'.$field.' BETWEEN :start and :end')
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'))
        ;
        $qb->andWhere("f.archived = :archived")->setParameter('archived', false);

        return $qb->getQuery()->getResult();
    }

    /**
     * Les documents sont considérés comme manquants dès que la date de réunion est dépassée.
     * Pour les e-learning, à J+15 de la date d’ouverture.
     * @param Person|null $coordinator
     * @return QueryBuilder
     */
    public function getMissingFilesQuery(ComptabiliteSearch $search) {
        $start = (new \DateTime())->format("Y-m-d");
        $opening = (new \DateTime())->modify("- 15 days")->format("Y-m-d");

        $qb = $this->createQueryBuilder("f")
            ->select("DISTINCT f")
            ->innerJoin("f.programme", "p")
            ->leftJoin('f.topoFiles', 't')
            ->leftJoin('p.topoProgrammeFiles', 'tp')
            ->leftJoin("f.coordinators", "c")
            ->leftJoin("c.person", "pe")
        ;

        $qb->setParameter('start', $start);
        $qb->setParameter('opening', $opening);
        $qb->setParameter('elearning', Programme::FORMAT_ELEARNING);

        if ($search->querySearchStartDate) {
            $qb->andWhere($qb->expr()->gte('f.querySearchStartDate', ':startDate'))->setParameter('startDate', $search->querySearchStartDate);
        }

        if ($search->querySearchEndDate) {
            $search->querySearchEndDate->setTime(23, 59, 59);
            $qb->andWhere($qb->expr()->lte('f.querySearchEndDate', ':endDate'))->setParameter(':endDate', $search->querySearchEndDate);
        }

        $qb
            // ->andWhere(
            //     $qb->expr()->orX(
            //         $qb->expr()->eq("f.fileCompleted", 0),
            //         $qb->expr()->andX(
            //             $qb->expr()->notLike("p.format", ":elearning"),
            //             $qb->expr()->isNull('t.id'),
            //             $qb->expr()->isNull('tp.id'),
            //         ),
            //     )
            // )
            ->andWhere('f.fileCompleted = :fileCompleted')->setParameter("fileCompleted", 0)
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->andX(
                        $qb->expr()->like("p.format", ":elearning"),
                        $qb->expr()->lte("f.openingDate", ":opening")
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->notLike("p.format", ":elearning"),
                        $qb->expr()->lte("f.startDate", ":start")
                    ),
                )
            )
            ->orderBy("f.closingDate", "ASC")
        ;

        if ($search->coordinator) {
            $qb
                ->andWhere("c.person = :coordinator")
                ->setParameter("coordinator", $search->coordinator->getId());
        }

        if ($search->supervisor) {
            $qb
                ->andWhere("pe.supervisor = :supervisor")
                ->setParameter("supervisor", $search->supervisor->getId());
        }

        if ($search->webmaster) {
            $qb
                ->andWhere("pe.webmaster = :webmaster")
                ->setParameter("webmaster", $search->webmaster->getId());
        }

        $qb->andWhere('f.archived = :archived')->setParameter("archived", false);
        return $qb;
    }

    public function countMissingFiles(ComptabiliteSearch $search) {
        $queryBuilder = $this->getMissingFilesQuery($search)->select('COUNT(DISTINCT f.id)');
        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function findMissingFiles(ComptabiliteSearch $search, $page, $number) {
        return $this->getMissingFilesQuery($search)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)->getQuery()->getResult();
    }


    // Retourne les sous-modes de financements avec participations rattachées dont la formation associée a une date de réunion comprise entre $start et $end
    public function findByPeriodAndFinanceSousMode(\DateTime $start, \DateTime $end, FinanceSousMode $financeSousMode) {
        $queryBuilder = $this->createQueryBuilder('fo')->select('fo');
        $queryBuilder->innerJoin('fo.participations', 'par');
        $queryBuilder->innerJoin('par.financeSousMode', 'f');
        $queryBuilder->andWhere('par.archived = false');
        $queryBuilder->andWhere($queryBuilder->expr()->between('fo.querySearchStartDate', ':start', ':end'));
        $queryBuilder->andWhere('par.financeSousMode = :financeSousMode');
        $queryBuilder->setParameter('financeSousMode', $financeSousMode);
        $queryBuilder->setParameter('start',$start);
        $queryBuilder->setParameter('end', $end);
        return $queryBuilder->getQuery()->getResult();
    }

    public function findByDateAndClass($dateField, \DateTime $date, $classes, $hours = false, $ignoreArchived = false) {
        $clonedDateStart = clone $date;
        if (!$hours) {
            $clonedDateStart->setTime(0, 0, 0);
        }
        $clonedDateEnd = clone $clonedDateStart;
        if (!$hours) {
            $clonedDateEnd->modify('+ 1 day');
        } else {
            $clonedDateEnd->modify('+ 1 hour');
        }
        $clonedDateEnd->modify('- 1 second');

        $qb = $this->createQueryBuilder('f');
        $qb->andWhere(sprintf('%s BETWEEN :start and :end', $dateField))
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'));

        if ($ignoreArchived) {
            $qb->andWhere('f.archived = false');
        }
        self::byClass($qb, $classes);
        return $qb->getQuery()->getResult();
    }

    public function findByUnityOpeningDate(\DateTime $openingDate, $classes, $ignoreFormationArchived = false) {
        $clonedDateStart = clone $openingDate;
        $clonedDateStart->setTime(0, 0, 0);
        $clonedDateEnd = clone $clonedDateStart;
        $clonedDateEnd->modify('+ 1 day');
        $clonedDateEnd->modify('+ 1 hour');
        $clonedDateEnd->modify('- 1 second');

        $queryBuilder = $this->createQueryBuilder('f')->select('f');
        if ($ignoreFormationArchived) {
            $queryBuilder->andWhere('f.archived = false');
        }
        $queryBuilder->innerJoin('f.unities', 'u');
        $queryBuilder->andWhere('u.openingDate BETWEEN :start and :end')
            ->setParameter('start', $clonedDateStart->format('Y-m-d H:i:s'))
            ->setParameter('end', $clonedDateEnd->format('Y-m-d H:i:s'));

        self::byClass($queryBuilder, $classes);

        return $queryBuilder->getQuery()->getResult();
    }

    public function loadFormations($titles, CatalogueSearch $formationSearch)
    {
        if (!$titles) {
            return $titles;
        }
        $qb = $this->createCatalogueResultsQueryBuilder($formationSearch, null, true);
        $formations = $qb
            ->andWhere($qb->expr()->in('f.programme',
                array_map(function (Programme $programme) {
                    return $programme->getId();
                }, $titles)
            ))
            ->getQuery()
            ->getResult()
        ;
        $sortedFormations = [];
        /** @var Formation $formation */
        foreach ($formations as $formation) {
            if (!isset($sortedFormations[$formation->getProgramme()->getId()])) {
                $sortedFormations[$formation->getProgramme()->getId()] = [];
            }
            $sortedFormations[$formation->getProgramme()->getId()][] = $formation;
        }
        foreach ($sortedFormations as $idProgramme => $formations) {
            /** @var Programme $programme */
            foreach ($titles as &$programme) {
                if ($programme->getId() === $idProgramme) {
                    $programme->setFormations(new ArrayCollection($formations));
                }
            }
        }
        return $titles;
    }

    public function findAllByProgrammeNotArchived(Programme $programme)
    {
        return $this->createQueryBuilder('fo')
            ->select('fo')
            ->andWhere('fo.archived = 0')
            ->andWhere('fo.programme = :programme')
            ->setParameter('programme', $programme)
            ->getQuery()
            ->getResult()
        ;
    }

    public function hasParticipation(array $formations): array
    {
        $result = $this->createQueryBuilder('fo')
            ->select('COUNT(p.id) as participationCount, fo.id as formationId')
            ->innerJoin('fo.participations', 'p')
            ->andWhere('fo.id IN (:formations)')
            ->andWhere('p.archived = false')
            ->groupBy('fo.id')  // Nécessaire pour grouper les participations par formation
            ->setParameter(':formations', $formations)
            ->getQuery()
            ->getScalarResult()
        ;
        $keyValueArray = [];
        foreach ($result as $row) {
            $keyValueArray[$row['formationId']] = $row['participationCount'];
        }
        return $keyValueArray;
    }

    public static function withForm(QueryBuilder $queryBuilder, string $formationAlias = 'f'): void
    {
        $queryBuilder->leftJoin(FormationAudit::class, 'wffa', 'WITH', 'wffa.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationVignette::class, 'wffv', 'WITH', 'wffv.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationPresentielle::class, 'wffpr', 'WITH', 'wffpr.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationActalians::class, 'wffac', 'WITH', 'wffac.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationElearning::class, 'wffel', 'WITH', 'wffel.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationVfc::class, 'wffvfc', 'WITH', 'wffvfc.id = '.$formationAlias.'.id');
        $queryBuilder->leftJoin(FormationTcs::class, 'wfftcs', 'WITH', 'wfftcs.id = '.$formationAlias.'.id');
        $queryBuilder
            ->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationAudit::class),
                    $queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf($formationAlias, FormationVignette::class)),
                    $queryBuilder->expr()->isNotNull('wffa.audit')
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationVignette::class),
                    $queryBuilder->expr()->isNotNull('wffa.audit'),
                    $queryBuilder->expr()->isNotNull('wffv.audit2'),
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationPresentielle::class),
                    $queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf($formationAlias, FormationElearning::class)),
                    $queryBuilder->expr()->isNotNull('wffpr.questionnaire')
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationActalians::class),
                    $queryBuilder->expr()->isNotNull('wffac.questionnaire')
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationElearning::class),
                    $queryBuilder->expr()->orX( // les elearnings sont soit avec questionnaire soit avec audit
                        $queryBuilder->expr()->isNotNull('wffel.questionnaire'),
                        $queryBuilder->expr()->isNotNull('wffel.audit'),
                    )
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationVfc::class),
                    $queryBuilder->expr()->isNotNull('wffvfc.audit')
                ),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->isInstanceOf($formationAlias, FormationTcs::class),
                    $queryBuilder->expr()->isNotNull('wfftcs.audit')
                ),
            ));
    }
}
