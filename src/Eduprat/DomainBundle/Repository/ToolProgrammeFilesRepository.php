<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\TopoProgrammeFiles;

/**
 * ToolProgrammeFilesRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ToolProgrammeFilesRepository extends EntityRepository
{
    /**
     * @param Programme $programme
     * @return TopoProgrammeFiles
     */
    public function findByProgramme(Programme $programme)
    {
        return $this->findBy(array(
            'programme' => $programme
        ));
    }
}
