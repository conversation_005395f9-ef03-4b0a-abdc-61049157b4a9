<?php

namespace Eduprat\DomainBundle\Repository\TCS;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Eduprat\DomainBundle\DTO\QuestionnaireTCSSearch;
use Eduprat\DomainBundle\Entity\TCS\QuestionnaireTCS;

/**
 * @extends ServiceEntityRepository<QuestionnaireTCS>
 *
 * @method QuestionnaireTCS|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionnaireTCS|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionnaireTCS[]    findAll()
 * @method QuestionnaireTCS[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionnaireTCSRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionnaireTCS::class);
    }

    public function countSearchResults(QuestionnaireTCSSearch $search): int
    {
        return $this->createSearchResultsQueryBuilder($search)
            ->select('COUNT(q.id)')
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findSearchResults(QuestionnaireTCSSearch $search, int $page, int $nbPerPage, ?string $sortBy = null, string $order = null): array
    {
        $qb = $this->createSearchResultsQueryBuilder($search);

        if ($sortBy) {
            $qb->addOrderBy($sortBy, $order);
        }

        return $qb->setFirstResult(($page - 1) * $nbPerPage)
            ->setMaxResults($nbPerPage)
            ->getQuery()->getResult();
    }

    private function createSearchResultsQueryBuilder(QuestionnaireTCSSearch $search): QueryBuilder
    {
        $queryBuilder = $this->getQueryBuilder();

        if ($search->thematique) {
            $queryBuilder->leftJoin('q.thematique', 'c');
            $queryBuilder->andWhere('c.id = :id')->setParameter('id', $search->thematique->getId());
        }

        if ($search->label) {
            $queryBuilder->andWhere('q.label like :label')->setParameter('label', "%" . $search->label . "%");
        }

        $queryBuilder->andWhere('q.archived = :archived')->setParameter('archived', $search->archived);

        return $queryBuilder;
    }

    private function getQueryBuilder(): QueryBuilder
    {
        return $this->createQueryBuilder('q');
    }
}
