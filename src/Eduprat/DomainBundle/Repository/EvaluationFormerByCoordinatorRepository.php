<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\Coordinator;

/**
 * EvaluationFormerByCoordinatorRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class EvaluationFormerByCoordinatorRepository extends EntityRepository
{
    public function findByFormerProgrammeCoordinator(Person $former, Programme $programme, Coordinator $coordinator)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->join('e.programme', 'p');
        $qb->where('p.id = :id')->setParameter('id', $programme->getId());
        $qb->andWhere('e.coordinator = :coordinator')->setParameter('coordinator', $coordinator->getId());
        $qb->andWhere('e.former = :former')->setParameter('former', $former->getId());
        return $qb->getQuery()->getResult();
    }

    public function findByFormerProgramme(Person $former, Programme $programme)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->join('e.programme', 'p');
        $qb->where('p.id = :id')->setParameter('id', $programme->getId());
        $qb->andWhere('e.former = :former')->setParameter('former', $former->getId());
        return $qb->getQuery()->getResult();
    }

    public function findByProgrammeCoordinator(Programme $programme, Coordinator $coordinator)
    {
        $qb = $this->createQueryBuilder("e");
        $qb->join('e.programme', 'p');
        $qb->where('p.id = :id')->setParameter('id', $programme->getId());
        $qb->andWhere('e.coordinator = :coordinator')->setParameter('coordinator', $coordinator->getId());
        return $qb->getQuery()->getResult();
    }
}
