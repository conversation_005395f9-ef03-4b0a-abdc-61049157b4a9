<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;

class LogoPartenaireUsageRepository extends EntityRepository
{
    public function findByPeriod(\DateTime $start, \DateTime $end) {
        $queryBuilder = $this->createQueryBuilder('l')->select('l');
        $queryBuilder->innerJoin('l.formation', 'fo');
        $queryBuilder->andWhere($queryBuilder->expr()->between('fo.startDate', ':start', ':end'));
        $queryBuilder->setParameter('start',$start);
        $queryBuilder->setParameter('end', $end);
        return $queryBuilder->getQuery()->getResult();
    }
}
