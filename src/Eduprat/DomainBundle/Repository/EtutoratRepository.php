<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Eduprat\DomainBundle\Entity\Etutorat;

/**
 * @method Etutorat|null find($id, $lockMode = null, $lockVersion = null)
 * @method Etutorat|null findOneBy(array $criteria, array $orderBy = null)
 * @method Etutorat[]    findAll()
 * @method Etutorat[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EtutoratRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Etutorat::class);
    }

}
