<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;

/**
 * InvoiceRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class InvoiceProformaRepository extends EntityRepository
{

    public function findByPrefix($prefix)
    {
        $q = $this->createQueryBuilder('i')
            ->where('i.number LIKE :prefix')
            ->setParameter('prefix', $prefix . '%');
        return $q->getQuery()->getResult();
    }

}
