<?php

namespace Eduprat\DomainBundle\Repository;
use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\CoordinatorFiles;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;

/**
 * CoordinatorFilesRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class CoordinatorFilesRepository extends EntityRepository
{

    /**
     * @param Coordinator $coordinator
     * @param Formation $formation
     * @return CoordinatorFiles
     */
    public function findByCoordinatorFormation(Coordinator $coordinator, Formation $formation)
    {
        return $this->findBy(array(
            'formation' => $formation,
            'coordinator' => $coordinator
        ));
    }
}
