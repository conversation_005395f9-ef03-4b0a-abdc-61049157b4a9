<?php

namespace Eduprat\DomainBundle\Repository;
use Doctrine\ORM\EntityRepository;
use Eduprat\DomainBundle\Entity\ParticipantActionSheet;
use Eduprat\DomainBundle\Entity\Participation;

/**
 * ParticipantActionSheetRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ParticipantActionSheetRepository extends EntityRepository
{
    /**
     * @param Participation $participation
     * @return ParticipantActionSheet
     */
    public function findByparticipation(Participation $participation)
    {
        return $this->findBy(array(
            'participation' => $participation
        ));
    }
}
